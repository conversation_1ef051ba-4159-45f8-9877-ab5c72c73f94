<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Research v2 - Video Analyzer</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/static/favicon.svg">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- YouTube Research v2 Design System -->
    <link rel="stylesheet" href="/static/styles/youtube-research-v2-design-system.css?v=2025073002">
</head>
<body>
    <div class="app-layout">
        <!-- Premium Sidebar -->
        <aside class="app-sidebar" id="sidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <a href="/" class="sidebar-logo">
                    <div class="sidebar-logo-icon">YR</div>
                    <span class="sidebar-logo-text">YouTube Research</span>
                </a>
                <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                    <i data-lucide="sun" class="theme-icon-light"></i>
                    <i data-lucide="moon" class="theme-icon-dark" style="display: none;"></i>
                </button>
            </div>
            
            <!-- Navigation Menu -->
            <nav class="sidebar-nav">
                <!-- Overview Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Overview</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/" class="nav-item" data-page="dashboard">
                                <i data-lucide="layout-dashboard" class="nav-icon"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <a href="/analytics" class="nav-item" data-page="analytics">
                                <i data-lucide="bar-chart-3" class="nav-icon"></i>
                                <span>Analytics</span>
                            </a>
                        </li>
                        <li>
                            <a href="/reports" class="nav-item" data-page="reports">
                                <i data-lucide="folder" class="nav-icon"></i>
                                <span>My Reports</span>
                                <span class="nav-badge" id="reportsCount">0</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Analysis Tools Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">AI Analysis Tools</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/video-analyzer" class="nav-item active" data-page="video-analyzer">
                                <i data-lucide="video" class="nav-icon"></i>
                                <span>Video Analyzer</span>
                                <span class="nav-badge nav-badge-purple">6 AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/channel-analyzer" class="nav-item" data-page="channel-analyzer">
                                <i data-lucide="users" class="nav-icon"></i>
                                <span>Channel DNA</span>
                                <span class="nav-badge nav-badge-blue">5 AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/market-research" class="nav-item" data-page="market-research">
                                <i data-lucide="trending-up" class="nav-icon"></i>
                                <span>Market Research</span>
                                <span class="nav-badge nav-badge-green">AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/agent/comment" class="nav-item" data-page="comment-intelligence">
                                <i data-lucide="message-circle" class="nav-icon"></i>
                                <span>Comment Intel</span>
                                <span class="nav-badge nav-badge-orange">AI</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Account Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Account</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/settings" class="nav-item" data-page="settings">
                                <i data-lucide="settings" class="nav-icon"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li>
                            <a href="/help" class="nav-item" data-page="help">
                                <i data-lucide="help-circle" class="nav-icon"></i>
                                <span>Help & Support</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="sidebar-user">
                    <div class="sidebar-user-avatar">A</div>
                    <div class="sidebar-user-info">
                        <div class="sidebar-user-name">Ardi</div>
                        <div class="sidebar-user-plan">Premium Plan</div>
                    </div>
                </div>
                <div class="version-info">
                    <span class="version-label">YouTube Research v2</span>
                    <span class="version-number">2.0.0</span>
                </div>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="app-main">
            <!-- Header -->
            <header class="app-header">
                <div class="header-left">
                    <button class="btn btn-ghost btn-sm" id="sidebar-toggle">
                        <i data-lucide="menu" class="sidebar-nav-icon"></i>
                    </button>
                    <nav class="breadcrumb">
                        <span class="breadcrumb-item">Analysis Tools</span>
                        <i data-lucide="chevron-right" class="breadcrumb-separator"></i>
                        <span class="breadcrumb-item">Video Analyzer</span>
                    </nav>
                </div>
                <div class="header-right">
                    <div class="history-dropdown-container">
                        <button class="btn btn-ghost btn-sm" id="historyBtn" onclick="toggleHistoryDropdown()">
                            <i data-lucide="history" class="sidebar-nav-icon"></i>
                            <span>Recent</span>
                        </button>
                        <div class="history-dropdown" id="historyDropdown" style="display: none;">
                            <div class="history-header">Recent Video Analyses</div>
                            <div class="history-empty">No recent analyses</div>
                        </div>
                    </div>
                    <button class="btn btn-ghost btn-sm" id="exportBtn" style="display: none;">
                        <i data-lucide="download" class="sidebar-nav-icon"></i>
                        <span>Export</span>
                    </button>
                </div>
            </header>
            
            <!-- Content -->
            <div class="premium-content">
                <!-- Analysis Form -->
                <div class="analyzer-form-section">
                    <div class="welcome-section">
                        <div>
                            <div class="form-header">
                                <div class="market-icon">
                                    <i data-lucide="video"></i>
                                </div>
                                <h1>AI Video Analyzer</h1>
                                <p>Deep forensic analysis with 7 specialized AI agents. Uncover performance secrets, script psychology, SEO opportunities, and audience insights.</p>
                            </div>
                        </div>
                        <div class="welcome-stats">
                            <div class="premium-card glass-card">
                                <div class="stat-item">
                                    <div class="stat-value">7 AI</div>
                                    <div class="stat-label">Agents</div>
                                </div>
                            </div>
                            <div class="premium-card glass-card">
                                <div class="stat-item">
                                    <div class="stat-value">∞</div>
                                    <div class="stat-label">Videos</div>
                                </div>
                            </div>
                            <div class="premium-card glass-card">
                                <div class="stat-item">
                                    <div class="stat-value">100%</div>
                                    <div class="stat-label">Accuracy</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="premium-card">
                        <div class="premium-card-header">
                            <div>
                                <h2 class="premium-card-title">
                                    <i data-lucide="search" style="margin-right: var(--space-sm);"></i>
                                    Video Analysis Query
                                </h2>
                                <p class="premium-card-subtitle">Enter a YouTube video URL to begin comprehensive AI analysis</p>
                            </div>
                            <div class="agent-badge-large">
                                <i data-lucide="video" class="agent-icon-large"></i>
                                <span class="agent-label">AI Analysis</span>
                            </div>
                        </div>
                        <div class="premium-card-content">
                            <form class="analyzer-form" id="analysisForm">
                                <div class="form-group">
                                    <label class="form-label">YouTube Video URL</label>
                                    <input type="url" class="form-input" id="videoUrl" placeholder="https://www.youtube.com/watch?v=..." required>
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Video Script (Optional)</label>
                                    <textarea class="form-input" id="script" placeholder="Paste the video script here for deeper analysis..." rows="4" style="resize: vertical; font-family: 'JetBrains Mono', monospace;"></textarea>
                                </div>
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary btn-lg" id="analyzeBtn">
                                        <i data-lucide="play" class="btn-icon"></i>
                                        <span class="btn-text">Analyze Video</span>
                                        <div class="btn-spinner" style="display: none;">
                                            <div class="spinner"></div>
                                        </div>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Results Area -->
                <div id="resultsContainer" style="display: none;">
                    <!-- Results will be populated here -->
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Navigation Manager
        class NavigationManager {
            constructor() {
                this.currentTheme = localStorage.getItem('theme') || 'dark';
                this.init();
            }

            init() {
                this.setupThemeToggle();
                this.setupNavigation();
                this.updateActiveNavItem();
            }

            setupThemeToggle() {
                const themeToggle = document.getElementById('themeToggle');
                const lightIcon = themeToggle.querySelector('.theme-icon-light');
                const darkIcon = themeToggle.querySelector('.theme-icon-dark');

                // Apply saved theme
                document.documentElement.setAttribute('data-theme', this.currentTheme);
                this.updateThemeIcons(lightIcon, darkIcon);

                themeToggle.addEventListener('click', () => {
                    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
                    document.documentElement.setAttribute('data-theme', this.currentTheme);
                    localStorage.setItem('theme', this.currentTheme);
                    this.updateThemeIcons(lightIcon, darkIcon);

                    // Add theme transition effect
                    document.body.classList.add('theme-transitioning');
                    setTimeout(() => {
                        document.body.classList.remove('theme-transitioning');
                    }, 300);
                });
            }

            updateThemeIcons(lightIcon, darkIcon) {
                if (this.currentTheme === 'dark') {
                    lightIcon.style.display = 'none';
                    darkIcon.style.display = 'block';
                } else {
                    lightIcon.style.display = 'block';
                    darkIcon.style.display = 'none';
                }
            }

            setupNavigation() {
                // Add click handlers for navigation items
                const navItems = document.querySelectorAll('.nav-item');
                navItems.forEach(item => {
                    item.addEventListener('click', (e) => {
                        // Update active state
                        navItems.forEach(nav => nav.classList.remove('active'));
                        item.classList.add('active');

                        // Store active page
                        const page = item.getAttribute('data-page');
                        if (page) {
                            localStorage.setItem('activePage', page);
                        }
                    });
                });
            }

            updateActiveNavItem() {
                const activePage = localStorage.getItem('activePage') || 'video-analyzer';
                const currentPath = window.location.pathname;

                // Remove all active states
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });

                // Set active based on current path or stored page
                let activeItem = document.querySelector(`[data-page="${activePage}"]`);

                if (!activeItem) {
                    // Fallback to path matching
                    activeItem = document.querySelector(`[href="${currentPath}"]`);
                }

                if (activeItem) {
                    activeItem.classList.add('active');
                }
            }
        }

        // Initialize navigation when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.navigationManager = new NavigationManager();
        });
        
        // Sidebar toggle functionality
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('collapsed');
        });
        
        // Form submission
        document.getElementById('analysisForm').addEventListener('submit', function(e) {
            e.preventDefault();
            startAnalysis();
        });
        
        // Global variables
        let analysisData = null;
        window.analysisData = null; // Also make it accessible globally
        
        // Analysis functions
        function startAnalysis() {
            const videoUrl = document.getElementById('videoUrl').value;
            const script = document.getElementById('script').value;
            
            if (!videoUrl.trim()) {
                alert('Please enter a YouTube video URL');
                return;
            }
            
            showLoadingState();
            analyzeVideo(videoUrl, script);
        }
        
        function showLoadingState() {
            const analyzeBtn = document.getElementById('analyzeBtn');
            const btnText = analyzeBtn.querySelector('.btn-text');
            const btnSpinner = analyzeBtn.querySelector('.btn-spinner');
            
            btnText.style.display = 'none';
            btnSpinner.style.display = 'flex';
            analyzeBtn.disabled = true;
            
            document.getElementById('resultsContainer').style.display = 'block';
            document.getElementById('resultsContainer').innerHTML = `
                <div class="premium-card">
                    <div class="premium-card-header">
                        <h2 class="premium-card-title">Analysis in Progress</h2>
                        <div class="status-indicator processing">
                            <div class="status-spinner"></div>
                            <span>Analyzing...</span>
                        </div>
                    </div>
                    <div class="premium-card-content">
                        <div class="loading-placeholder">
                            <div class="placeholder-spinner">
                                <div class="spinner"></div>
                            </div>
                            <div class="placeholder-text">AI agents are analyzing your video...</div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        function hideLoadingState() {
            const analyzeBtn = document.getElementById('analyzeBtn');
            const btnText = analyzeBtn.querySelector('.btn-text');
            const btnSpinner = analyzeBtn.querySelector('.btn-spinner');
            
            btnText.style.display = 'inline';
            btnSpinner.style.display = 'none';
            analyzeBtn.disabled = false;
            btnText.textContent = 'Analyze Another Video';
        }
        
        function analyzeVideo(videoUrl, script) {
            const data = {
                video_url: videoUrl,
                script: script || ''
            };
            
            fetch('/analyze-video', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                
                displayResults(data);
                hideLoadingState();
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorState(error.message);
                hideLoadingState();
            });
        }
        
        function showErrorState(errorMessage) {
            document.getElementById('resultsContainer').innerHTML = `
                <div class="premium-card">
                    <div class="premium-card-header">
                        <h2 class="premium-card-title">Analysis Failed</h2>
                        <div class="status-indicator error">
                            <i data-lucide="alert-circle"></i>
                            <span>Error</span>
                        </div>
                    </div>
                    <div class="premium-card-content">
                        <div class="error-state">
                            <i data-lucide="alert-triangle"></i>
                            <h3>Analysis Failed</h3>
                            <p>${errorMessage}</p>
                            <button class="btn btn-secondary" onclick="location.reload()">
                                <i data-lucide="refresh-cw"></i>
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
            `;
            
            lucide.createIcons();
        }
        
        function displayResults(data) {
            console.log('Analysis data received:', data);
            
            // Store data globally for export functionality
            analysisData = data;
            window.analysisData = data;
            
            // Save to localStorage for history
            saveAnalysisToHistory(data);
            
            // Show export button
            showExportButton();
            
            const resultsContainer = document.getElementById('resultsContainer');
            
            // Video Overview Card
            let videoOverviewHTML = '';
            if (data.video_data) {
                const channelContext = data.video_data.channel_context || {};
                const performanceMetrics = data.video_data.performance_metrics || {};
                
                videoOverviewHTML = `
                    <div class="video-overview">
                        <div class="premium-card">
                            <div class="premium-card-header">
                                <div>
                                    <h2 class="premium-card-title">✅ Video Analysis Complete</h2>
                                    <p class="premium-card-subtitle">Analyzed: ${data.video_data.title || 'Video analysis completed successfully'}</p>
                                </div>
                                <div class="status-indicator completed">
                                    <i data-lucide="check-circle"></i>
                                    <span>Complete</span>
                                </div>
                            </div>
                            <div class="premium-card-content">
                                <div class="video-info">
                                    <div class="video-header">
                                        <div class="video-details">
                                            <h3 class="video-title">${data.video_data.title || 'Unknown Title'}</h3>
                                            <div class="video-stats">
                                                <span class="stat-item">
                                                    <i data-lucide="eye"></i>
                                                    ${formatNumber(data.video_data.views || 0)} views
                                                </span>
                                                <span class="stat-item">
                                                    <i data-lucide="thumbs-up"></i>
                                                    ${formatNumber(data.video_data.likes || 0)} likes
                                                </span>
                                                <span class="stat-item">
                                                    <i data-lucide="message-circle"></i>
                                                    ${formatNumber(data.video_data.comments || 0)} comments
                                                </span>
                                            </div>
                                            <div class="channel-info">
                                                <span class="channel-name">
                                                    <i data-lucide="user"></i>
                                                    ${channelContext.channel_name || 'Unknown Channel'}
                                                </span>
                                                <span class="channel-tier">
                                                    ${channelContext.channel_tier || 'Unknown Tier'}
                                                </span>
                                                ${performanceMetrics.performance_note ? `
                                                    <span class="performance-note ${performanceMetrics.performance_note.toLowerCase().replace(' ', '-')}">
                                                        ${performanceMetrics.performance_note}
                                                    </span>
                                                ` : ''}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }
            
            // Export Section
            const exportHTML = `
                <div class="export-section">
                    <div class="premium-card">
                        <div class="premium-card-header">
                            <div>
                                <h2 class="premium-card-title">📋 Export Your Report</h2>
                                <p class="premium-card-subtitle">Download your analysis in multiple formats</p>
                            </div>
                            <div class="export-status">
                                <span class="status-dot"></span>
                                <span>Ready to Export</span>
                            </div>
                        </div>
                        <div class="premium-card-content">
                            <div class="export-buttons">
                                <button class="btn btn-primary" onclick="exportToPDF()">
                                    <i data-lucide="file-text" class="btn-icon"></i>
                                    PDF Report
                                </button>
                                <button class="btn btn-secondary" onclick="exportToJSON()">
                                    <i data-lucide="database" class="btn-icon"></i>
                                    JSON Data
                                </button>
                                <button class="btn btn-secondary" onclick="exportToMarkdown()">
                                    <i data-lucide="hash" class="btn-icon"></i>
                                    Markdown
                                </button>
                                <button class="btn btn-secondary" onclick="exportToHTML()">
                                    <i data-lucide="globe" class="btn-icon"></i>
                                    HTML File
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Tab Navigation
            const tabNavigationHTML = `
                <div class="tab-navigation">
                    <div class="premium-card">
                        <div class="premium-card-header">
                            <h2 class="premium-card-title">🎯 Analysis Results</h2>
                            <div class="tab-counter">
                                <span id="activeTabIndicator">1</span> of 7 analyses
                            </div>
                        </div>
                        <div class="premium-card-content">
                            <div class="tab-buttons">
                                <button class="tab-button active" onclick="switchTab('performance')" data-tab="performance">
                                    <i data-lucide="zap" class="tab-icon"></i>
                                    <span>Performance Intelligence</span>
                                </button>
                                <button class="tab-button" onclick="switchTab('script')" data-tab="script">
                                    <i data-lucide="file-text" class="tab-icon"></i>
                                    <span>Script Forensics</span>
                                </button>
                                <button class="tab-button" onclick="switchTab('seo')" data-tab="seo">
                                    <i data-lucide="search" class="tab-icon"></i>
                                    <span>SEO & Discoverability</span>
                                </button>
                                <button class="tab-button" onclick="switchTab('psychology')" data-tab="psychology">
                                    <i data-lucide="brain" class="tab-icon"></i>
                                    <span>Audience Psychology</span>
                                </button>
                                <button class="tab-button" onclick="switchTab('comments')" data-tab="comments">
                                    <i data-lucide="message-circle" class="tab-icon"></i>
                                    <span>Comment Intelligence</span>
                                </button>
                                <button class="tab-button" onclick="switchTab('thumbnail')" data-tab="thumbnail">
                                    <i data-lucide="image" class="tab-icon"></i>
                                    <span>Thumbnail Analysis</span>
                                </button>
                                <button class="tab-button" onclick="switchTab('synthesis')" data-tab="synthesis">
                                    <i data-lucide="target" class="tab-icon"></i>
                                    <span>Strategic Synthesis</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // Tab Content
            const analysisTypes = [
                { key: 'performance_analysis', id: 'performance', title: 'Performance Intelligence', icon: 'zap' },
                { key: 'script_analysis', id: 'script', title: 'Script Forensics', icon: 'file-text' },
                { key: 'seo_analysis', id: 'seo', title: 'SEO & Discoverability', icon: 'search' },
                { key: 'psychology_analysis', id: 'psychology', title: 'Audience Psychology', icon: 'brain' },
                { key: 'comment_analysis', id: 'comments', title: 'Comment Intelligence', icon: 'message-circle' },
                { key: 'thumbnail_analysis', id: 'thumbnail', title: 'Thumbnail Analysis', icon: 'image' },
                { key: 'strategic_synthesis', id: 'synthesis', title: 'Strategic Synthesis', icon: 'target' }
            ];
            
            let tabContentHTML = '';
            analysisTypes.forEach((type, index) => {
                const isActive = index === 0 ? 'active' : '';
                const analysisData = data[type.key];
                
                let formattedAnalysis = 'No analysis available';
                if (analysisData) {
                    // Check for styled_html first, then formatted_html, then raw analysis
                    if (analysisData.styled_html) {
                        formattedAnalysis = analysisData.styled_html;
                    } else if (analysisData.formatted_html) {
                        formattedAnalysis = analysisData.formatted_html;
                    } else if (analysisData.analysis) {
                        formattedAnalysis = formatAnalysisText(analysisData.analysis);
                    }
                }
                
                tabContentHTML += `
                    <div class="tab-content ${isActive}" id="tab-${type.id}">
                        <div class="premium-card">
                            <div class="premium-card-header">
                                <div>
                                    <h2 class="premium-card-title">
                                        <i data-lucide="${type.icon}" class="premium-card-icon"></i>
                                        ${type.title}
                                    </h2>
                                    <p class="premium-card-subtitle">AI-powered analysis and insights</p>
                                </div>
                                <div class="status-indicator completed">
                                    <i data-lucide="check-circle"></i>
                                    <span>Complete</span>
                                </div>
                            </div>
                            <div class="premium-card-content">
                                <div class="analysis-content">
                                    ${formattedAnalysis}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            resultsContainer.innerHTML = videoOverviewHTML + exportHTML + tabNavigationHTML + tabContentHTML;
            
            // Re-initialize icons
            lucide.createIcons();
        }
        
        // Tab switching functionality
        function switchTab(tabId) {
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Add active class to clicked tab button
            document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');
            
            // Hide all tab content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(`tab-${tabId}`).classList.add('active');
            
            // Update tab counter
            const tabOrder = ['performance', 'script', 'seo', 'psychology', 'comments', 'thumbnail', 'synthesis'];
            const activeIndex = tabOrder.indexOf(tabId) + 1;
            document.getElementById('activeTabIndicator').textContent = activeIndex;
        }
        
        // Text formatting function
        function formatAnalysisText(text) {
            if (!text) return 'No analysis available';
            
            // Format the analysis text for better display
            let formattedText = text
                .replace(/^### (.*$)/gm, '<h3 class="analysis-header">$1</h3>')
                .replace(/^## (.*$)/gm, '<h2 class="analysis-header">$1</h2>')
                .replace(/^\*\*(.*?)\*\*/gm, '<strong>$1</strong>')
                .replace(/^\* (.*$)/gm, '<li>$1</li>')
                .replace(/\n\n/g, '</p><p>')
                .replace(/^(?!<h|<li|<p)(.+)$/gm, '<p>$1</p>');
            
            // Wrap lists
            formattedText = formattedText.replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>');
            
            return `<div class="analysis-text">${formattedText}</div>`;
        }
        
        // Export functions
        function exportToPDF() {
            if (!analysisData) {
                alert('No analysis data available for export');
                return;
            }
            window.print();
        }
        
        function exportToJSON() {
            if (!analysisData) {
                alert('No analysis data available for export');
                return;
            }
            const jsonString = JSON.stringify(analysisData, null, 2);
            downloadFile(jsonString, 'video-analysis.json', 'application/json');
        }
        
        function exportToMarkdown() {
            if (!analysisData) {
                alert('No analysis data available for export');
                return;
            }
            const data = analysisData;
            let markdown = `# Video Analysis Report\n\n`;
            markdown += `**Video:** ${data.video_data?.title || 'Unknown'}\n`;
            markdown += `**Generated:** ${new Date().toLocaleDateString()}\n\n`;
            
            const sections = [
                { key: 'performance_analysis', title: 'Performance Intelligence' },
                { key: 'script_analysis', title: 'Script Forensics' },
                { key: 'seo_analysis', title: 'SEO & Discoverability' },
                { key: 'psychology_analysis', title: 'Audience Psychology' },
                { key: 'comment_analysis', title: 'Comment Intelligence' },
                { key: 'thumbnail_analysis', title: 'Thumbnail Analysis' },
                { key: 'strategic_synthesis', title: 'Strategic Synthesis' }
            ];
            
            sections.forEach(section => {
                if (data[section.key]) {
                    markdown += `## ${section.title}\n\n`;
                    markdown += `${data[section.key].analysis || 'No analysis available'}\n\n`;
                }
            });
            
            downloadFile(markdown, 'video-analysis.md', 'text/markdown');
        }
        
        function exportToHTML() {
            if (!analysisData) {
                alert('No analysis data available for export');
                return;
            }
            const data = analysisData;
            let html = `<!DOCTYPE html>
<html><head><title>Video Analysis Report</title></head><body>
<h1>Video Analysis Report</h1>
<p><strong>Video:</strong> ${data.video_data?.title || 'Unknown'}</p>
<p><strong>Generated:</strong> ${new Date().toLocaleDateString()}</p>`;
            
            const sections = [
                { key: 'performance_analysis', title: 'Performance Intelligence' },
                { key: 'script_analysis', title: 'Script Forensics' },
                { key: 'seo_analysis', title: 'SEO & Discoverability' },
                { key: 'psychology_analysis', title: 'Audience Psychology' },
                { key: 'comment_analysis', title: 'Comment Intelligence' },
                { key: 'thumbnail_analysis', title: 'Thumbnail Analysis' },
                { key: 'strategic_synthesis', title: 'Strategic Synthesis' }
            ];
            
            sections.forEach(section => {
                if (data[section.key]) {
                    html += `<h2>${section.title}</h2>`;
                    html += `<div>${data[section.key].styled_html || data[section.key].analysis || 'No analysis available'}</div>`;
                }
            });
            
            html += '</body></html>';
            downloadFile(html, 'video-analysis.html', 'text/html');
        }
        
        function downloadFile(content, filename, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }
        
        // Utility function
        function formatNumber(num) {
            if (num >= 1000000000) {
                return (num / 1000000000).toFixed(1) + 'B';
            } else if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }
        
        // Tab switching functionality
        function switchVideoTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show the selected tab content
            document.getElementById('video-' + tabName).classList.add('active');
            
            // Add active class to the clicked tab button
            event.target.closest('.tab-button').classList.add('active');
        }
        
        // Additional utility functions
        function createFormattedDate() {
            return new Date().toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }
        
        // Analysis History Management
        function saveAnalysisToHistory(data) {
            try {
                const history = getAnalysisHistory();
                const report = {
                    id: Date.now() + '_video',
                    type: 'video',
                    title: data.video_data?.title || 'Unknown Video',
                    channel: data.video_data?.channel_title || data.video_data?.channel_context?.channel_name || 'Unknown Channel',
                    url: document.getElementById('videoUrl').value,
                    timestamp: new Date().toISOString(),
                    displayDate: new Date().toLocaleString(),
                    data: data
                };
                
                // Add to beginning of array
                history.unshift(report);
                
                // Keep only last 20 reports
                if (history.length > 20) {
                    history.splice(20);
                }
                
                localStorage.setItem('youtube_pulse_history', JSON.stringify(history));
                console.log('✅ Analysis saved to history:', report.title);
                
                // Update UI
                updateHistoryDropdown();
            } catch (error) {
                console.warn('Failed to save analysis to history:', error);
            }
        }
        
        function getAnalysisHistory() {
            try {
                const stored = localStorage.getItem('youtube_pulse_history');
                return stored ? JSON.parse(stored) : [];
            } catch (error) {
                console.warn('Failed to load analysis history:', error);
                return [];
            }
        }
        
        function loadAnalysisFromHistory(reportId) {
            const history = getAnalysisHistory();
            const report = history.find(r => r.id === reportId);
            
            if (report) {
                console.log('📋 Loading full analysis from history:', report.title);
                console.log('🔍 Report data:', report.data);
                
                // Populate form fields
                document.getElementById('videoUrl').value = report.url;
                if (report.data.script) {
                    document.getElementById('script').value = report.data.script;
                }
                
                // Store the data globally for export functionality
                analysisData = report.data;
                window.analysisData = report.data;
                
                // Show the results container
                document.getElementById('resultsContainer').style.display = 'block';
                
                // Display the full analysis results
                displayResults(report.data);
                
                // Show export button
                showExportButton();
                
                // Close the dropdown
                document.getElementById('historyDropdown').style.display = 'none';
                
                console.log('✅ Full analysis loaded from history:', report.title);
            } else {
                console.error('❌ Report not found:', reportId);
            }
        }
        
        function updateHistoryDropdown() {
            // Update history dropdown if it exists
            const historyDropdown = document.getElementById('historyDropdown');
            if (historyDropdown) {
                const history = getAnalysisHistory().filter(r => r.type === 'video').slice(0, 10);
                
                if (history.length === 0) {
                    historyDropdown.innerHTML = '<div class="history-empty">No recent analyses</div>';
                    return;
                }
                
                historyDropdown.innerHTML = history.map(report => `
                    <div class="history-item" onclick="loadAnalysisFromHistory('${report.id}')">
                        <div class="history-title" title="${report.title}">${report.title}</div>
                        <div class="history-meta">${report.channel} • ${new Date(report.timestamp).toLocaleDateString()}</div>
                    </div>
                `).join('');
            }
        }
        
        // History dropdown functionality
        function toggleHistoryDropdown() {
            const dropdown = document.getElementById('historyDropdown');
            if (dropdown.style.display === 'none') {
                updateHistoryDropdown();
                dropdown.style.display = 'block';
            } else {
                dropdown.style.display = 'none';
            }
        }
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const container = document.querySelector('.history-dropdown-container');
            const dropdown = document.getElementById('historyDropdown');
            
            if (container && !container.contains(event.target)) {
                dropdown.style.display = 'none';
            }
        });
        
        // Show export button when analysis is loaded
        function showExportButton() {
            document.getElementById('exportBtn').style.display = 'flex';
        }
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            updateHistoryDropdown();
        });
        
        // Clean up - this section was causing duplicates
    </script>
    
    <!-- Video Analyzer styles now in youtube-research-v2-design-system.css -->



</body>
</html>