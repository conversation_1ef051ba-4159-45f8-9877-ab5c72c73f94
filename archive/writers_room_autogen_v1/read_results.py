#!/usr/bin/env python3
"""
Simple reader for research results and workflow outputs
"""

import json
import os
from datetime import datetime

def read_latest_results():
    """Read and display the latest research results"""
    
    print("📚 RESEARCH RESULTS READER")
    print("=" * 50)
    
    # Check for workflow results
    workflow_dir = "outputs/complete_workflow"
    if os.path.exists(workflow_dir):
        files = [f for f in os.listdir(workflow_dir) if f.endswith('.json')]
        if files:
            latest_file = sorted(files)[-1]
            filepath = os.path.join(workflow_dir, latest_file)
            
            print(f"📄 Latest Results: {latest_file}")
            print(f"📍 Location: {filepath}")
            print("-" * 50)
            
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"🎯 Topic: {data['topic']}")
            print(f"⏱️  Total Time: {data['total_workflow_time']:.1f} seconds")
            print(f"📊 Quality Score: {data['writers_room_result']['quality_score']}/10")
            print(f"📝 Script Length: {len(data['writers_room_result']['final_script'])} characters")
            print(f"🔬 Research Content: {data['research_content_length']} characters")
            
            print(f"\n📋 RESEARCH BRIEF:")
            print("-" * 30)
            for category, items in data['research_brief'].items():
                print(f"• {category.replace('_', ' ').title()}: {', '.join(items)}")
            
            print(f"\n🎬 SCRIPT STRUCTURE:")
            print("-" * 30)
            structure = data['writers_room_result']['structure']
            # Show first 500 characters
            print(structure[:500] + "..." if len(structure) > 500 else structure)
            
            print(f"\n🪝 HOOKS:")
            print("-" * 30)
            hooks = data['writers_room_result']['hooks']
            for i, hook in enumerate(hooks[:3], 1):
                print(f"{i}. {hook[:100]}..." if len(hook) > 100 else f"{i}. {hook}")
            
            print(f"\n📄 FULL RESULTS LOCATION:")
            print(f"   File: {filepath}")
            print(f"   Open with: cat '{filepath}' | jq .")
            print(f"   Or: python -m json.tool '{filepath}'")
            
        else:
            print("❌ No workflow results found")
    else:
        print("❌ No outputs directory found")

def show_file_locations():
    """Show all available result files"""
    
    print(f"\n📁 ALL AVAILABLE RESULTS:")
    print("-" * 50)
    
    if os.path.exists("outputs"):
        for root, dirs, files in os.walk("outputs"):
            for file in files:
                if file.endswith('.json'):
                    filepath = os.path.join(root, file)
                    size = os.path.getsize(filepath)
                    mtime = datetime.fromtimestamp(os.path.getmtime(filepath))
                    print(f"📄 {filepath}")
                    print(f"   Size: {size:,} bytes")
                    print(f"   Modified: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")
                    print()

if __name__ == "__main__":
    read_latest_results()
    show_file_locations()