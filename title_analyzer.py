#!/usr/bin/env python3
"""
Title Analyzer - Advanced title optimization intelligence for Channel Analyzer
Analyzes title patterns, psychology, and performance correlation
"""

import re
from collections import Counter, defaultdict
from typing import Dict, List, Any, Tuple
import statistics
import logging

logger = logging.getLogger(__name__)

class TitleAnalyzer:
    """Advanced title analysis and optimization engine"""
    
    def __init__(self):
        # Psychology trigger words for different emotions
        self.psychology_triggers = {
            'urgency': ['now', 'today', 'urgent', 'limited', 'last chance', 'deadline', 'hurry', 'fast', 'quick'],
            'curiosity': ['secret', 'hidden', 'revealed', 'truth', 'mystery', 'unknown', 'discover', 'find out', 'what happens'],
            'fear': ['warning', 'danger', 'avoid', 'mistake', 'wrong', 'terrible', 'worst', 'never', 'scary'],
            'excitement': ['amazing', 'incredible', 'unbelievable', 'shocking', 'epic', 'insane', 'crazy', 'wild', 'extreme'],
            'exclusivity': ['exclusive', 'only', 'rare', 'special', 'unique', 'limited', 'private', 'vip', 'elite'],
            'social_proof': ['everyone', 'viral', 'trending', 'popular', 'famous', 'millions', 'thousands', 'best'],
            'curiosity_gap': ['this', 'what', 'how', 'why', 'when', 'where', 'will', 'can', 'should'],
            'numbers': ['\\d+', 'one', 'two', 'three', 'four', 'five', 'first', 'top', 'best']
        }
        
        # Common clickbait patterns
        self.clickbait_patterns = [
            r'\b(?:you )?won\'t believe\b',
            r'\bthis .* will (?:shock|amaze|surprise) you\b',
            r'\b\d+ .* that will (?:change|blow) your mind\b',
            r'\bwhat happens next (?:will )?(?:shock|amaze|surprise)\b',
            r'\byou\'ll never guess\b',
            r'\bthis is (?:why|how|what)\b',
            r'\bthe .* that everyone\b',
            r'\bno one talks about\b'
        ]
        
        # Title structure patterns
        self.title_structures = {
            'question': r'^(?:what|how|why|when|where|which|who|can|do|does|is|are)',
            'list': r'^(?:\d+|\w+) (?:ways|things|tips|secrets|reasons|facts)',
            'how_to': r'^how to',
            'vs_comparison': r'\bvs\.?\b|\bversus\b',
            'reaction': r'\breact(?:ing|ion)?\b|\bresponse\b',
            'challenge': r'\bchallenge\b|\btrying\b|\battempt',
            'story': r'^(?:my|our) (?:story|experience|journey)',
            'review': r'\breview\b|\btesting\b|\btried\b'
        }
    
    def analyze_channel_titles(self, video_library: List[Dict[str, Any]], channel_profile: Dict[str, Any]) -> Dict[str, Any]:
        """
        Comprehensive title analysis for a channel's video library
        
        Args:
            video_library: List of video data with titles and statistics
            channel_profile: Channel information for context
            
        Returns:
            Complete title intelligence report
        """
        
        if not video_library:
            return {'error': 'No video data available for title analysis'}
        
        # Extract titles and performance data
        title_data = []
        for video in video_library:
            title = video.get('title', '')
            if title:
                title_data.append({
                    'title': title,
                    'views': int(video.get('statistics', {}).get('viewCount', 0)),
                    'likes': int(video.get('statistics', {}).get('likeCount', 0)),
                    'comments': int(video.get('statistics', {}).get('commentCount', 0)),
                    'duration': video.get('duration', ''),
                    'published': video.get('publishedAt', ''),
                    'video_id': video.get('id', '')
                })
        
        if not title_data:
            return {'error': 'No valid titles found for analysis'}
        
        # Run comprehensive analysis
        analysis = {
            'title_performance_analysis': self._analyze_title_performance(title_data),
            'length_optimization': self._analyze_title_length(title_data),
            'psychology_triggers': self._analyze_psychology_triggers(title_data),
            'keyword_intelligence': self._analyze_keywords(title_data),
            'structure_patterns': self._analyze_title_structures(title_data),
            'optimization_recommendations': None  # Will be filled after analysis
        }
        
        # Generate optimization recommendations
        analysis['optimization_recommendations'] = self._generate_title_recommendations(analysis, channel_profile)
        
        return analysis
    
    def _analyze_title_performance(self, title_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze correlation between title characteristics and performance"""
        
        if not title_data:
            return {'error': 'No title data available'}
        
        # Sort by views to find top performers
        sorted_titles = sorted(title_data, key=lambda x: x['views'], reverse=True)
        top_performers = sorted_titles[:min(5, len(sorted_titles))]
        bottom_performers = sorted_titles[-min(5, len(sorted_titles)):]
        
        # Calculate average performance
        avg_views = statistics.mean([t['views'] for t in title_data])
        avg_engagement = statistics.mean([
            (t['likes'] + t['comments']) / max(t['views'], 1) * 100 
            for t in title_data
        ])
        
        return {
            'total_titles_analyzed': len(title_data),
            'average_views': int(avg_views),
            'average_engagement_rate': round(avg_engagement, 2),
            'top_performing_titles': [
                {
                    'title': t['title'],
                    'views': t['views'],
                    'engagement_rate': round((t['likes'] + t['comments']) / max(t['views'], 1) * 100, 2)
                }
                for t in top_performers
            ],
            'performance_insights': self._extract_performance_patterns(top_performers, bottom_performers)
        }
    
    def _analyze_title_length(self, title_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze optimal title length for performance"""
        
        length_performance = []
        for title in title_data:
            length = len(title['title'])
            length_performance.append({
                'length': length,
                'views': title['views'],
                'title': title['title']
            })
        
        # Group by length ranges
        length_groups = {
            'short': [t for t in length_performance if t['length'] <= 40],
            'medium': [t for t in length_performance if 40 < t['length'] <= 60],
            'long': [t for t in length_performance if 60 < t['length'] <= 80],
            'very_long': [t for t in length_performance if t['length'] > 80]
        }
        
        # Calculate performance by length group
        length_stats = {}
        for group_name, group_data in length_groups.items():
            if group_data:
                avg_views = statistics.mean([t['views'] for t in group_data])
                length_stats[group_name] = {
                    'count': len(group_data),
                    'average_views': int(avg_views),
                    'character_range': self._get_length_range(group_name),
                    'best_example': max(group_data, key=lambda x: x['views'])
                }
        
        # Find optimal length
        best_performing_group = max(length_stats.items(), key=lambda x: x[1]['average_views']) if length_stats else None
        
        # Calculate overall statistics
        all_lengths = [t['length'] for t in length_performance]
        avg_length = statistics.mean(all_lengths)
        
        return {
            'average_title_length': round(avg_length, 1),
            'length_distribution': length_stats,
            'optimal_length_range': best_performing_group[1]['character_range'] if best_performing_group else 'Unknown',
            'best_performing_length_group': best_performing_group[0] if best_performing_group else 'Unknown',
            'length_recommendations': self._generate_length_recommendations(length_stats, avg_length)
        }
    
    def _analyze_psychology_triggers(self, title_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze psychological triggers used in titles"""
        
        trigger_performance = defaultdict(list)
        trigger_usage = defaultdict(int)
        
        for title in title_data:
            title_lower = title['title'].lower()
            title_triggers = []
            
            # Check for each trigger type
            for trigger_type, trigger_words in self.psychology_triggers.items():
                trigger_count = 0
                for word in trigger_words:
                    if word.startswith('\\'):  # Regex pattern
                        if re.search(word, title_lower):
                            trigger_count += len(re.findall(word, title_lower))
                    else:
                        trigger_count += title_lower.count(word)
                
                if trigger_count > 0:
                    trigger_usage[trigger_type] += 1
                    trigger_performance[trigger_type].append(title['views'])
                    title_triggers.append(trigger_type)
        
        # Calculate performance by trigger type
        trigger_analysis = {}
        for trigger_type, views_list in trigger_performance.items():
            avg_views = statistics.mean(views_list)
            usage_count = trigger_usage[trigger_type]
            
            trigger_analysis[trigger_type] = {
                'usage_count': usage_count,
                'average_views': int(avg_views),
                'total_titles': len(title_data),
                'usage_percentage': round((usage_count / len(title_data)) * 100, 1)
            }
        
        # Find most effective triggers
        best_trigger = max(trigger_analysis.items(), key=lambda x: x[1]['average_views']) if trigger_analysis else None
        most_used_trigger = max(trigger_analysis.items(), key=lambda x: x[1]['usage_count']) if trigger_analysis else None
        
        return {
            'trigger_analysis': trigger_analysis,
            'most_effective_trigger': best_trigger[0] if best_trigger else 'None',
            'most_used_trigger': most_used_trigger[0] if most_used_trigger else 'None',
            'psychology_score': self._calculate_psychology_score(trigger_analysis),
            'trigger_recommendations': self._generate_trigger_recommendations(trigger_analysis)
        }
    
    def _analyze_keywords(self, title_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze keyword usage and performance"""
        
        # Extract keywords (2+ character words, excluding common words)
        stop_words = {'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'should', 'could', 'can', 'may', 'might', 'must', 'shall', 'a', 'an'}
        
        keyword_performance = defaultdict(list)
        keyword_count = defaultdict(int)
        
        for title in title_data:
            # Extract words
            words = re.findall(r'\b[a-zA-Z]{2,}\b', title['title'].lower())
            unique_words = set(words)
            
            for word in unique_words:
                if word not in stop_words:
                    keyword_performance[word].append(title['views'])
                    keyword_count[word] += 1
        
        # Calculate keyword statistics
        keyword_stats = {}
        for keyword, views_list in keyword_performance.items():
            if len(views_list) >= 2:  # Only keywords used in 2+ videos
                avg_views = statistics.mean(views_list)
                keyword_stats[keyword] = {
                    'usage_count': keyword_count[keyword],
                    'average_views': int(avg_views),
                    'total_views': sum(views_list),
                    'usage_percentage': round((keyword_count[keyword] / len(title_data)) * 100, 1)
                }
        
        # Sort by performance
        top_keywords = sorted(keyword_stats.items(), key=lambda x: x[1]['average_views'], reverse=True)[:10]
        most_used_keywords = sorted(keyword_stats.items(), key=lambda x: x[1]['usage_count'], reverse=True)[:10]
        
        return {
            'total_unique_keywords': len(keyword_stats),
            'top_performing_keywords': [
                {
                    'keyword': keyword,
                    'average_views': data['average_views'],
                    'usage_count': data['usage_count']
                }
                for keyword, data in top_keywords
            ],
            'most_used_keywords': [
                {
                    'keyword': keyword,
                    'usage_count': data['usage_count'],
                    'average_views': data['average_views']
                }
                for keyword, data in most_used_keywords
            ],
            'keyword_recommendations': self._generate_keyword_recommendations(top_keywords, most_used_keywords)
        }
    
    def _analyze_title_structures(self, title_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze title structure patterns and their performance"""
        
        structure_performance = defaultdict(list)
        structure_usage = defaultdict(int)
        
        for title in title_data:
            title_lower = title['title'].lower()
            
            # Check for each structure pattern
            for structure_type, pattern in self.title_structures.items():
                if re.search(pattern, title_lower):
                    structure_performance[structure_type].append(title['views'])
                    structure_usage[structure_type] += 1
        
        # Calculate performance by structure
        structure_analysis = {}
        for structure_type, views_list in structure_performance.items():
            if views_list:
                avg_views = statistics.mean(views_list)
                usage_count = structure_usage[structure_type]
                
                structure_analysis[structure_type] = {
                    'usage_count': usage_count,
                    'average_views': int(avg_views),
                    'usage_percentage': round((usage_count / len(title_data)) * 100, 1),
                    'total_views': sum(views_list)
                }
        
        # Find best performing structure
        best_structure = max(structure_analysis.items(), key=lambda x: x[1]['average_views']) if structure_analysis else None
        most_used_structure = max(structure_analysis.items(), key=lambda x: x[1]['usage_count']) if structure_analysis else None
        
        return {
            'structure_analysis': structure_analysis,
            'best_performing_structure': best_structure[0] if best_structure else 'None',
            'most_used_structure': most_used_structure[0] if most_used_structure else 'None',
            'structure_diversity_score': len(structure_analysis),
            'structure_recommendations': self._generate_structure_recommendations(structure_analysis)
        }
    
    def _generate_title_recommendations(self, analysis: Dict[str, Any], channel_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Generate actionable title optimization recommendations"""
        
        recommendations = {
            'immediate_actions': [],
            'optimization_opportunities': [],
            'title_templates': [],
            'psychology_enhancements': [],
            'strategic_insights': []
        }
        
        # Length recommendations
        length_data = analysis.get('length_optimization', {})
        optimal_length = length_data.get('optimal_length_range', '')
        if optimal_length and optimal_length != 'Unknown':
            recommendations['immediate_actions'].append(
                f"Optimize title length to {optimal_length} characters for best performance"
            )
        
        # Psychology trigger recommendations
        psychology_data = analysis.get('psychology_triggers', {})
        most_effective_trigger = psychology_data.get('most_effective_trigger', 'None')
        if most_effective_trigger != 'None':
            recommendations['psychology_enhancements'].append(
                f"Increase use of '{most_effective_trigger}' triggers - they drive higher engagement"
            )
        
        # Keyword recommendations
        keyword_data = analysis.get('keyword_intelligence', {})
        top_keywords = keyword_data.get('top_performing_keywords', [])[:3]
        if top_keywords:
            recommendations['optimization_opportunities'].append(
                f"Include high-performing keywords: {', '.join([k['keyword'] for k in top_keywords])}"
            )
        
        # Structure recommendations
        structure_data = analysis.get('structure_patterns', {})
        best_structure = structure_data.get('best_performing_structure', 'None')
        if best_structure != 'None':
            recommendations['optimization_opportunities'].append(
                f"Use '{best_structure}' title structure more often - shows best performance"
            )
        
        # Generate title templates
        recommendations['title_templates'] = self._create_title_templates(analysis)
        
        # Strategic insights
        performance_data = analysis.get('title_performance_analysis', {})
        avg_views = performance_data.get('average_views', 0)
        if avg_views > 0:
            recommendations['strategic_insights'].append(
                f"Current average: {avg_views:,} views per video - optimization could improve by 20-40%"
            )
        
        return recommendations
    
    def _create_title_templates(self, analysis: Dict[str, Any]) -> List[Dict[str, str]]:
        """Create title templates based on successful patterns"""
        
        templates = []
        
        # Best performing keywords and triggers
        keyword_data = analysis.get('keyword_intelligence', {})
        top_keywords = [k['keyword'] for k in keyword_data.get('top_performing_keywords', [])[:3]]
        
        psychology_data = analysis.get('psychology_triggers', {})
        best_trigger = psychology_data.get('most_effective_trigger', '')
        
        # Create templates
        if top_keywords:
            templates.append({
                'template': f"[NUMBER] {top_keywords[0].title()} [ACTION] That Will [RESULT]",
                'example': f"5 {top_keywords[0].title()} Tricks That Will Change Everything",
                'psychology': 'Numbers + Results-focused'
            })
        
        if best_trigger and best_trigger in ['curiosity', 'excitement']:
            templates.append({
                'template': "What Happens When [SCENARIO] (You Won't Believe This)",
                'example': "What Happens When I Try This Challenge (You Won't Believe This)",
                'psychology': 'Curiosity + Anticipation'
            })
        
        templates.append({
            'template': "I [ACTION] So You Don't Have To",
            'example': "I Tested This For 30 Days So You Don't Have To",
            'psychology': 'Social proof + Value delivery'
        })
        
        return templates
    
    # Helper methods for analysis
    def _extract_performance_patterns(self, top_performers: List[Dict], bottom_performers: List[Dict]) -> List[str]:
        """Extract patterns from top vs bottom performing titles"""
        patterns = []
        
        # Analyze length differences
        top_lengths = [len(t['title']) for t in top_performers]
        bottom_lengths = [len(t['title']) for t in bottom_performers]
        
        if top_lengths and bottom_lengths:
            avg_top_length = statistics.mean(top_lengths)
            avg_bottom_length = statistics.mean(bottom_lengths)
            
            if avg_top_length < avg_bottom_length:
                patterns.append("Top performers use shorter titles on average")
            elif avg_top_length > avg_bottom_length:
                patterns.append("Top performers use longer titles on average")
        
        return patterns
    
    def _get_length_range(self, group_name: str) -> str:
        """Get character range for length group"""
        ranges = {
            'short': '≤40 characters',
            'medium': '41-60 characters', 
            'long': '61-80 characters',
            'very_long': '80+ characters'
        }
        return ranges.get(group_name, 'Unknown')
    
    def _generate_length_recommendations(self, length_stats: Dict, avg_length: float) -> List[str]:
        """Generate recommendations based on length analysis"""
        recommendations = []
        
        if length_stats:
            best_group = max(length_stats.items(), key=lambda x: x[1]['average_views'])
            recommendations.append(f"Optimal range: {best_group[1]['character_range']}")
            
            if avg_length > 70:
                recommendations.append("Consider shorter titles for better engagement")
            elif avg_length < 30:
                recommendations.append("Consider longer titles for more context")
        
        return recommendations
    
    def _calculate_psychology_score(self, trigger_analysis: Dict) -> int:
        """Calculate overall psychology effectiveness score"""
        if not trigger_analysis:
            return 0
        
        # Score based on variety and effectiveness of triggers used
        trigger_count = len(trigger_analysis)
        avg_effectiveness = statistics.mean([t['average_views'] for t in trigger_analysis.values()])
        
        # Normalize to 0-100 scale
        score = min(100, (trigger_count * 10) + (avg_effectiveness / 1000000 * 20))
        return int(score)
    
    def _generate_trigger_recommendations(self, trigger_analysis: Dict) -> List[str]:
        """Generate psychology trigger recommendations"""
        recommendations = []
        
        if not trigger_analysis:
            recommendations.append("Add emotional triggers to increase engagement")
            return recommendations
        
        # Find underused but effective triggers
        effective_triggers = [
            name for name, data in trigger_analysis.items() 
            if data['average_views'] > 0 and data['usage_percentage'] < 30
        ]
        
        if effective_triggers:
            recommendations.append(f"Underutilized effective triggers: {', '.join(effective_triggers[:3])}")
        
        return recommendations
    
    def _generate_keyword_recommendations(self, top_keywords: List, most_used_keywords: List) -> List[str]:
        """Generate keyword usage recommendations"""
        recommendations = []
        
        if top_keywords:
            recommendations.append(f"High-impact keywords to use more: {', '.join([k[0] for k in top_keywords[:3]])}")
        
        if most_used_keywords:
            overused = [k for k in most_used_keywords if k[1]['usage_percentage'] > 50]
            if overused:
                recommendations.append("Diversify keyword usage to avoid repetition")
        
        return recommendations
    
    def _generate_structure_recommendations(self, structure_analysis: Dict) -> List[str]:
        """Generate title structure recommendations"""
        recommendations = []
        
        if structure_analysis:
            best_structure = max(structure_analysis.items(), key=lambda x: x[1]['average_views'])
            recommendations.append(f"Most effective structure: {best_structure[0]} format")
            
            underused_structures = [
                name for name, data in structure_analysis.items()
                if data['usage_percentage'] < 20 and data['average_views'] > 0
            ]
            
            if underused_structures:
                recommendations.append(f"Experiment with: {', '.join(underused_structures)}")
        
        return recommendations

# Create global title analyzer instance
title_analyzer = TitleAnalyzer()