#!/usr/bin/env python3
"""
Complete Workflow Test: Research → Preprocessing → Writers' Room → Script
Demonstrates the full pipeline with cost optimization
"""

import sys
import os
import json
import time

# Add system paths
sys.path.append('/Users/<USER>/Documents/CrewAI/youtube-research-v2/writers_room/core_system')

from research_preprocessor import ResearchPreprocessor, ProcessedResearch
from fixed_autogen_writers_room import FixedAutoGenWritersRoom, WritersRoomInput

def load_phoenix_research():
    """Load the Phoenix water crisis research"""
    
    # Sample Phoenix research focused on Rome-Sasanid parallels
    research_text = """
    PHOENIX WATER CRISIS ANALYSIS - Historical Parallels:
    
    1. MODERN PHOENIX SITUATION:
    - Phoenix consumes 2x NYC water despite desert location (89 gallons/person/day)
    - Colorado River allocation based on 1922 compact during unusually wet period
    - Lake Mead dropping 140 feet since 2000, approaching "dead pool" status
    - $60B semiconductor fabrication needs massive water (1 chip = 10 gallons)
    - Saudi company Fondomonte pumping groundwater for alfalfa, shipping to Saudi Arabia
    
    2. ROME-SASANID WATER WARS (224-628 CE):
    - 400-year conflict over Mesopotamian water control
    - Euphrates/Tigris system fed 50 million people in antiquity
    - Sasanid qanat system: 50,000 miles of underground channels
    - Roman siege of Ctesiphon (283 CE): Diocletian diverted Tigris, starved city
    - Julian's campaign (363 CE): Destroyed irrigation, created 1000-year desert
    
    3. KEY PARALLELS:
    - Legal frameworks: Diocletian's water laws → 1922 Colorado Compact
    - Over-allocation during abundance → Crisis during scarcity
    - Foreign exploitation: Saudi alfalfa farms → Roman water theft
    - Technology hubris: Phoenix semiconductors → Sasanid mega-irrigation
    - Climate ignorance: Building in deserts → Inevitable collapse
    
    4. THE FATAL PATTERN:
    - Sasanid irrigation fed 20 million but needed constant maintenance
    - One cut aqueduct in 627 CE → 500,000 died in 3 months
    - Roman general Heraclius identified weakest points, surgical strikes
    - Modern parallel: Glen Canyon Dam = single point of failure
    
    5. SHOCKING REVELATIONS:
    - Phoenix water rights based on same Roman "first in time" principle
    - Saudi farms use more water than entire city of Phoenix
    - Ancient Mesopotamia never recovered - still 90% desert today
    - Colorado River hasn't reached ocean since 1998
    - By 2030, 40 million Americans face water cutoffs
    """
    
    return research_text

def test_complete_workflow():
    """Test the full workflow from research to script"""
    
    print("🚀 COMPLETE WORKFLOW TEST: Research → Preprocessing → Writers' Room")
    print("=" * 80)
    
    # API key
    api_key = "********************************************************************************************************************************************************************"
    
    # Step 1: Load research
    print("\n📚 STEP 1: Loading Research Document")
    research_text = load_phoenix_research()
    print(f"✓ Loaded {len(research_text.split())} words of research")
    
    # Step 2: Preprocess research
    print("\n🔬 STEP 2: Preprocessing Research (One-Time Cost)")
    preprocessor = ResearchPreprocessor(model="gpt-3.5-turbo", api_key=api_key)
    
    # Calculate cost savings
    word_count = len(research_text.split())
    cost_estimate = preprocessor.calculate_cost(word_count)
    print(f"💰 Preprocessing cost: ${cost_estimate['total_cost']:.4f}")
    print(f"💸 Savings vs 6 agents reading 4 times: ${cost_estimate['cost_per_agent_saved']:.2f}")
    
    # Process research
    start_time = time.time()
    processed_research = preprocessor.process_research(research_text)
    preprocess_time = time.time() - start_time
    
    print(f"✓ Preprocessing complete in {preprocess_time:.1f}s")
    print(f"  - Director insights: {len(processed_research.director_insights)} chars")
    print(f"  - Hooksmith patterns: {len(processed_research.hooksmith_patterns)} chars")
    print(f"  - Narrative elements: {len(processed_research.narrative_elements)} chars")
    
    # Step 3: Create Writers' Room input
    print("\n📝 STEP 3: Preparing Writers' Room Brief")
    
    # Extract key nuggets from processed research
    golden_nuggets = [
        "Rome and Sasanid Persia fought 400 years over water - same pattern as Phoenix today",
        "Sasanid irrigation collapse killed 500,000 in 3 months when Romans cut water",
        "Phoenix uses Roman water law from Diocletian creating modern crisis",
        "Saudi farms pump more water than entire Phoenix - modern water colonialism",
        "Colorado River hasn't reached ocean since 1998 - civilization-ending trajectory"
    ]
    
    writers_input = WritersRoomInput(
        topic="The Ancient Water War That's Destroying Phoenix",
        golden_nuggets=golden_nuggets,
        persona={
            "channel": "Frontier",
            "voice": "Shocked investigator uncovering conspiracy",
            "style": "Fast-paced revelations building to existential dread"
        },
        research_context=processed_research.raw_summary,  # Preprocessed summary
        target_length="8-10 minutes",
        max_rounds=20  # Reduced for testing
    )
    
    # Step 4: Run Writers' Room
    print("\n🎬 STEP 4: Running 6-Agent Writers' Room")
    print(f"🤖 Using GPT-3.5-turbo for cost efficiency")
    
    writers_room = FixedAutoGenWritersRoom(
        model="gpt-3.5-turbo",  # Cheap model for testing
        api_key=api_key,
        test_mode=True
    )
    
    # Run collaboration
    start_time = time.time()
    try:
        output = writers_room.run_collaborative_session(writers_input)
        writers_time = time.time() - start_time
        
        print(f"\n✅ Writers' Room complete in {writers_time:.1f}s")
        print(f"📊 Collaboration Metrics:")
        print(f"  - Total rounds: {output.collaboration_metrics.total_rounds}")
        print(f"  - Quality score: {output.collaboration_metrics.final_quality_score:.1f}/10")
        print(f"  - Script length: {len(output.final_script.split())} words")
        
        # Step 5: Save outputs
        print("\n💾 STEP 5: Saving Results")
        output_dir = "/Users/<USER>/Documents/CrewAI/youtube-research-v2/writers_room/outputs/complete_workflow"
        os.makedirs(output_dir, exist_ok=True)
        
        # Save preprocessed research
        with open(f"{output_dir}/preprocessed_research.json", 'w') as f:
            json.dump({
                "director_insights": processed_research.director_insights,
                "hooksmith_patterns": processed_research.hooksmith_patterns,
                "narrative_elements": processed_research.narrative_elements,
                "emotional_triggers": processed_research.emotional_triggers,
                "production_notes": processed_research.production_notes,
                "closing_strategies": processed_research.closing_strategies,
                "preprocessing_time": preprocess_time,
                "preprocessing_cost": cost_estimate['total_cost']
            }, f, indent=2)
        
        # Save final script
        writers_room.save_output(output, f"{output_dir}/rome_sasanid_phoenix_script.json")
        
        # Save just the script text
        with open(f"{output_dir}/final_script.txt", 'w') as f:
            f.write(f"TITLE: {writers_input.topic}\n")
            f.write(f"LENGTH: {writers_input.target_length}\n")
            f.write(f"QUALITY SCORE: {output.collaboration_metrics.final_quality_score}/10\n")
            f.write("=" * 80 + "\n\n")
            f.write(output.final_script)
        
        # Step 6: Cost Analysis
        print("\n💰 STEP 6: Total Cost Analysis")
        print(f"  - Research preprocessing: ${cost_estimate['total_cost']:.4f}")
        print(f"  - Writers' Room (est): $0.10-0.30")  # GPT-3.5 estimate
        print(f"  - Total (est): $0.15-0.35")
        print(f"  - Previous cost: $5-10 (95% reduction!)")
        
        print("\n✅ WORKFLOW COMPLETE!")
        print(f"📁 Results saved to: {output_dir}")
        
        # Show sample of final script
        print("\n📜 SCRIPT PREVIEW:")
        print("-" * 80)
        preview = output.final_script[:500] + "..." if len(output.final_script) > 500 else output.final_script
        print(preview)
        
    except Exception as e:
        print(f"\n❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_complete_workflow()