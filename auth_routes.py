"""
YouTube Research v2 - Authentication Routes
FastAPI routes for user authentication and management
"""

from fastapi import APIRouter, HTTPException, Depends, status
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr
from typing import Optional, Dict, Any
import jwt
import bcrypt
from datetime import datetime, timedelta
import logging

from supabase_integration import (
    supabase_manager, 
    get_current_user, 
    get_current_user_optional,
    UserProfile, 
    UsageStats,
    SUPABASE_ENABLED
)

logger = logging.getLogger(__name__)

# Create router
auth_router = APIRouter(prefix="/api/auth", tags=["authentication"])
user_router = APIRouter(prefix="/api/user", tags=["user"])

# Security
security = HTTPBearer()

# Request/Response Models
class SignUpRequest(BaseModel):
    email: EmailStr
    password: str
    full_name: Optional[str] = None

class SignInRequest(BaseModel):
    email: EmailStr
    password: str

class AuthResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    user: UserProfile
    expires_in: int = 3600

class PasswordResetRequest(BaseModel):
    email: EmailStr

class PasswordUpdateRequest(BaseModel):
    current_password: str
    new_password: str

class ProfileUpdateRequest(BaseModel):
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None

# Authentication Routes
@auth_router.post("/signup", response_model=AuthResponse)
async def sign_up(request: SignUpRequest):
    """Register a new user"""
    if not SUPABASE_ENABLED:
        raise HTTPException(
            status_code=503, 
            detail="User registration is currently disabled"
        )
    
    try:
        # Use Supabase Auth to create user
        auth_response = supabase_manager.client.auth.sign_up({
            "email": request.email,
            "password": request.password,
            "options": {
                "data": {
                    "full_name": request.full_name
                }
            }
        })
        
        if auth_response.user is None:
            raise HTTPException(
                status_code=400,
                detail="Failed to create user account"
            )
        
        # Get user profile (should be created automatically by trigger)
        user_profile = await supabase_manager.get_user_profile(auth_response.user.id)
        
        if not user_profile:
            # Fallback: create profile manually
            user_profile = await supabase_manager.create_user_profile(
                auth_response.user.id,
                request.email,
                request.full_name
            )
        
        return AuthResponse(
            access_token=auth_response.session.access_token,
            user=user_profile,
            expires_in=auth_response.session.expires_in or 3600
        )
        
    except Exception as e:
        logger.error(f"Signup error: {e}")
        raise HTTPException(
            status_code=400,
            detail="Failed to create account. Email may already be registered."
        )

@auth_router.post("/signin", response_model=AuthResponse)
async def sign_in(request: SignInRequest):
    """Sign in existing user"""
    if not SUPABASE_ENABLED:
        raise HTTPException(
            status_code=503,
            detail="User authentication is currently disabled"
        )
    
    try:
        # Use Supabase Auth to sign in
        auth_response = supabase_manager.client.auth.sign_in_with_password({
            "email": request.email,
            "password": request.password
        })
        
        if auth_response.user is None:
            raise HTTPException(
                status_code=401,
                detail="Invalid email or password"
            )
        
        # Get user profile
        user_profile = await supabase_manager.get_user_profile(auth_response.user.id)
        
        if not user_profile:
            raise HTTPException(
                status_code=404,
                detail="User profile not found"
            )
        
        return AuthResponse(
            access_token=auth_response.session.access_token,
            user=user_profile,
            expires_in=auth_response.session.expires_in or 3600
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Signin error: {e}")
        raise HTTPException(
            status_code=401,
            detail="Invalid email or password"
        )

@auth_router.post("/signout")
async def sign_out(current_user: UserProfile = Depends(get_current_user)):
    """Sign out current user"""
    if not SUPABASE_ENABLED:
        return {"message": "Signed out"}
    
    try:
        supabase_manager.client.auth.sign_out()
        return {"message": "Successfully signed out"}
    except Exception as e:
        logger.error(f"Signout error: {e}")
        return {"message": "Signed out"}

@auth_router.post("/reset-password")
async def reset_password(request: PasswordResetRequest):
    """Send password reset email"""
    if not SUPABASE_ENABLED:
        raise HTTPException(
            status_code=503,
            detail="Password reset is currently disabled"
        )
    
    try:
        supabase_manager.client.auth.reset_password_email(request.email)
        return {"message": "Password reset email sent"}
    except Exception as e:
        logger.error(f"Password reset error: {e}")
        return {"message": "If the email exists, a reset link has been sent"}

@auth_router.get("/me", response_model=UserProfile)
async def get_me(current_user: UserProfile = Depends(get_current_user)):
    """Get current user profile"""
    return current_user

# User Management Routes
@user_router.get("/profile", response_model=UserProfile)
async def get_profile(current_user: UserProfile = Depends(get_current_user)):
    """Get user profile"""
    return current_user

@user_router.put("/profile", response_model=UserProfile)
async def update_profile(
    request: ProfileUpdateRequest,
    current_user: UserProfile = Depends(get_current_user)
):
    """Update user profile"""
    if not SUPABASE_ENABLED:
        raise HTTPException(
            status_code=503,
            detail="Profile updates are currently disabled"
        )
    
    try:
        update_data = {}
        if request.full_name is not None:
            update_data["full_name"] = request.full_name
        if request.avatar_url is not None:
            update_data["avatar_url"] = request.avatar_url
        
        if update_data:
            response = supabase_manager.client.table("user_profiles").update(
                update_data
            ).eq("id", current_user.id).execute()
            
            if response.data:
                updated_profile = UserProfile(**response.data[0])
                return updated_profile
        
        return current_user
        
    except Exception as e:
        logger.error(f"Profile update error: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to update profile"
        )

@user_router.get("/usage", response_model=UsageStats)
async def get_usage_stats(current_user: UserProfile = Depends(get_current_user)):
    """Get user usage statistics"""
    return await supabase_manager.get_usage_stats(current_user.id)

@user_router.get("/reports")
async def get_user_reports(
    limit: int = 50,
    current_user: UserProfile = Depends(get_current_user)
):
    """Get user's analysis reports"""
    reports = await supabase_manager.get_user_reports(current_user.id, limit)
    return {"reports": reports, "total": len(reports)}

@user_router.post("/upgrade")
async def upgrade_subscription(current_user: UserProfile = Depends(get_current_user)):
    """Upgrade user to pro subscription (placeholder for payment integration)"""
    if not SUPABASE_ENABLED:
        raise HTTPException(
            status_code=503,
            detail="Subscription management is currently disabled"
        )
    
    # This would integrate with Stripe or other payment processor
    # For now, just return upgrade information
    return {
        "message": "Subscription upgrade initiated",
        "current_tier": current_user.subscription_tier,
        "upgrade_url": "https://your-payment-processor.com/upgrade",
        "pricing": {
            "pro": {
                "price": "$99/month",
                "credits": "Unlimited",
                "features": [
                    "Unlimited AI analysis",
                    "Advanced market research",
                    "Priority support",
                    "Export capabilities",
                    "API access"
                ]
            }
        }
    }

# Health check for authentication system
@auth_router.get("/health")
async def auth_health():
    """Check authentication system health"""
    return {
        "status": "healthy" if SUPABASE_ENABLED else "disabled",
        "supabase_enabled": SUPABASE_ENABLED,
        "features": {
            "user_registration": SUPABASE_ENABLED,
            "user_authentication": SUPABASE_ENABLED,
            "report_storage": SUPABASE_ENABLED,
            "usage_tracking": SUPABASE_ENABLED
        }
    }

# Middleware for optional authentication
async def optional_auth_middleware(
    current_user: Optional[UserProfile] = Depends(get_current_user_optional)
):
    """Middleware that provides optional authentication"""
    return current_user

# Credit check decorator
def require_credits(credits_needed: int = 1):
    """Decorator to check if user has enough credits"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            current_user = kwargs.get('current_user')
            if current_user and current_user.credits_remaining < credits_needed:
                raise HTTPException(
                    status_code=402,
                    detail=f"Insufficient credits. Need {credits_needed}, have {current_user.credits_remaining}"
                )
            return await func(*args, **kwargs)
        return wrapper
    return decorator
