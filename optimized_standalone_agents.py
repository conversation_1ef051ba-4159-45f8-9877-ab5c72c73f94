"""
Optimized Standalone YouTube Analysis Agents
With caching, batch processing, and cost optimization
"""

import os
import re
import logging
from typing import Dict, Any, Optional, List
from functools import wraps
from concurrent.futures import ThreadPoolExecutor, as_completed
from crewai import Agent, Crew, Task
from crewai.llm import LLM
from crewai.tools import BaseTool
from pydantic import BaseModel, Field

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# OPTIMIZATION STEP 1: Enable LiteLLM caching for all agents
import litellm
litellm.enable_cache(type="local", supported_call_types=["completion", "acompletion"])
logger.info("🚀 LiteLLM caching enabled for standalone agents - automatic cost reduction active!")

# OPTIMIZATION STEP 2: Import transcript and cost tracking
from simple_transcript_cache import transcript_cache
from cost_tracker import cost_tracker

# OPTIMIZATION STEP 3: Shared LLM instance for all agents
_shared_llm = None
def get_shared_llm():
    """Get or create shared LLM instance for better caching"""
    global _shared_llm
    if _shared_llm is None:
        _shared_llm = LLM(model="gemini/gemini-2.5-flash")
    return _shared_llm

class ThinkingInput(BaseModel):
    """Input schema for thinking tool"""
    question: str = Field(..., description="The question or problem to think through")
    context: str = Field(default="", description="Additional context for the question")

class ThinkingTool(BaseTool):
    name: str = "sequential_thinking"
    description: str = "Engage in structured sequential thinking to break down complex problems"
    args_schema: type[BaseModel] = ThinkingInput

    def _run(self, question: str, context: str = "") -> str:
        """Execute thinking tool synchronously"""
        return f"""
## Sequential Thinking Framework Applied

**Question:** {question}
**Context:** {context}

### Step-by-Step Reasoning Process:

**1. Understanding the Core Question**
- Breaking down what we need to analyze
- Identifying the key components and variables
- Setting clear analytical objectives

**2. Information Processing & Pattern Recognition**  
- Systematically examining all available data
- Identifying patterns and correlations
- Cross-referencing with known successful examples

**3. Cause-Effect Analysis**
- Determining what drives the observed outcomes
- Understanding the mechanics behind success
- Isolating the most impactful factors

**4. Strategic Intelligence Generation**
- Converting observations into actionable insights
- Developing replication strategies
- Identifying optimization opportunities

**5. Replication Strategy Development**
- Creating step-by-step implementation guides
- Adapting strategies for different contexts
- Risk assessment and mitigation planning

**6. Future Scenario Analysis**
- Predicting likely outcomes of different approaches
- Identifying potential roadblocks and solutions
- Long-term strategic planning considerations
"""

def track_agent_performance(agent_name: str):
    """Decorator to track agent performance and costs"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            
            # Run the agent
            result = func(*args, **kwargs)
            
            # Track performance
            execution_time = time.time() - start_time
            logger.info(f"⚡ {agent_name} completed in {execution_time:.2f}s")
            
            # Note: Actual token tracking would happen in LiteLLM callbacks
            # This is a placeholder for the pattern
            
            return result
        return wrapper
    return decorator

class OptimizedBaseStandaloneAgent:
    """Optimized base class with caching and cost tracking"""
    
    def __init__(self, use_sequential_thinking: bool = True):
        # Use shared LLM instance for better caching
        self.llm_pro = get_shared_llm()
        self.use_sequential_thinking = use_sequential_thinking
        self.thinking_tool = ThinkingTool() if use_sequential_thinking else None
        
    def _create_enhanced_agent(self, role: str, goal: str, standard_backstory: str, 
                             enhanced_backstory_suffix: str = '') -> Agent:
        """Create an enhanced agent with optional sequential thinking"""
        tools = [self.thinking_tool] if self.thinking_tool else []
        
        enhanced_role = f"Enhanced {role} with Sequential Thinking" if self.use_sequential_thinking else role
        enhanced_backstory = standard_backstory + enhanced_backstory_suffix if self.use_sequential_thinking else standard_backstory
        
        return Agent(
            role=enhanced_role,
            goal=goal,
            backstory=enhanced_backstory,
            llm=self.llm_pro,
            tools=tools,
            verbose=True
        )
    
    def _get_thinking_instruction(self, question: str, context: str) -> str:
        """Get thinking instruction for sequential thinking agents"""
        if not self.use_sequential_thinking:
            return ""
        
        return f"""Use your sequential_thinking tool to structure your analysis:

THINKING FRAMEWORK:
Question: "{question}"
Context: "{context}"

Apply your 6-step sequential thinking process before providing your final analysis.

"""

    def _prepare_script_insights(self, script_analysis: Dict[str, Any]) -> str:
        """Extract key insights from script analysis to avoid passing full transcript"""
        if not script_analysis or 'analysis' not in script_analysis:
            return "No script insights available"
        
        analysis = script_analysis['analysis']
        
        # Extract key sections instead of full analysis
        insights = []
        
        # Look for key markers in the analysis
        if "SCRIPT METRICS" in analysis:
            metrics_start = analysis.find("SCRIPT METRICS")
            metrics_end = analysis.find("\n\n", metrics_start + 200)
            if metrics_end > metrics_start:
                insights.append(analysis[metrics_start:metrics_end])
        
        if "hook" in analysis.lower():
            hook_context = self._extract_context(analysis, "hook", 200)
            if hook_context:
                insights.append(f"Hook Analysis: {hook_context}")
        
        if "psychological trigger" in analysis.lower():
            psych_context = self._extract_context(analysis, "psychological trigger", 200)
            if psych_context:
                insights.append(f"Psychological Triggers: {psych_context}")
        
        # If no specific insights found, provide a summary
        if not insights:
            insights.append(f"Script Analysis Summary (first 500 chars): {analysis[:500]}...")
        
        return "\n\n".join(insights)
    
    def _extract_context(self, text: str, keyword: str, context_size: int = 200) -> str:
        """Extract context around a keyword"""
        lower_text = text.lower()
        keyword_lower = keyword.lower()
        
        pos = lower_text.find(keyword_lower)
        if pos == -1:
            return ""
        
        start = max(0, pos - context_size // 2)
        end = min(len(text), pos + context_size // 2)
        
        return text[start:end].strip()


class OptimizedPerformanceAgent(OptimizedBaseStandaloneAgent):
    """Optimized Performance Intelligence Analyst"""
    
    @track_agent_performance("Performance Agent")
    def analyze(self, video_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run performance analysis with caching"""
        # Agent implementation remains the same, but benefits from:
        # 1. Shared LLM instance with caching
        # 2. Performance tracking
        # 3. Cost monitoring through LiteLLM
        
        # [Rest of the original PerformanceAgent.analyze implementation]
        # Not duplicating the full implementation to save space
        # The key is that it now uses the optimized base class
        
        return {"analysis": "Performance analysis with caching enabled"}


class OptimizedStandaloneAgentInterface:
    """Optimized interface with batch processing and caching"""
    
    def __init__(self, use_sequential_thinking: bool = True):
        self.use_sequential_thinking = use_sequential_thinking
        
        # Initialize all agents with shared resources
        self.performance_agent = OptimizedPerformanceAgent(use_sequential_thinking)
        # Add other agents similarly...
        
        logger.info(f"✅ Optimized standalone agents initialized (Sequential Thinking: {use_sequential_thinking})")
    
    def run_agents_batch(self, video_data: Dict[str, Any], script: str, 
                        agent_names: List[str]) -> Dict[str, Any]:
        """Run multiple agents in batch for efficiency"""
        results = {}
        
        # OPTIMIZATION: Use transcript cache
        video_id = video_data.get('video_id', 'unknown')
        if script:
            cache_key = transcript_cache.store_transcript(video_id, script)
            script_metadata = transcript_cache.get_metadata(cache_key)
            logger.info(f"📝 Transcript cached: {script_metadata['word_count']} words")
        
        # OPTIMIZATION: Run independent agents in parallel
        independent_agents = []
        dependent_agents = []
        
        for agent_name in agent_names:
            if agent_name in ['performance', 'script']:
                independent_agents.append(agent_name)
            else:
                dependent_agents.append(agent_name)
        
        # Run independent agents in parallel
        if independent_agents:
            with ThreadPoolExecutor(max_workers=2) as executor:
                futures = {}
                
                if 'performance' in independent_agents:
                    futures['performance'] = executor.submit(
                        self.performance_agent.analyze, video_data
                    )
                
                if 'script' in independent_agents:
                    futures['script'] = executor.submit(
                        self.script_agent.analyze, video_data, script
                    )
                
                # Collect results
                for agent_name, future in futures.items():
                    try:
                        results[f'{agent_name}_analysis'] = future.result()
                        logger.info(f"✅ {agent_name} agent completed (parallel)")
                    except Exception as e:
                        logger.error(f"❌ {agent_name} agent failed: {e}")
                        results[f'{agent_name}_analysis'] = {'error': str(e)}
        
        # Run dependent agents sequentially with optimized data passing
        if 'script' in results and dependent_agents:
            # OPTIMIZATION: Pass script insights instead of full transcript
            script_insights = self._prepare_script_insights(results.get('script_analysis', {}))
            
            for agent_name in dependent_agents:
                try:
                    if agent_name == 'seo':
                        results['seo_analysis'] = self.seo_agent.analyze(
                            video_data, script_insights, 
                            results.get('performance_analysis', {})
                        )
                    # Add other dependent agents...
                    
                    logger.info(f"✅ {agent_name} agent completed")
                except Exception as e:
                    logger.error(f"❌ {agent_name} agent failed: {e}")
                    results[f'{agent_name}_analysis'] = {'error': str(e)}
        
        # Print cost summary
        cost_tracker.print_summary()
        
        return results
    
    def estimate_cost(self, agent_names: List[str], has_transcript: bool) -> Dict[str, Any]:
        """Estimate cost for running selected agents"""
        # Base estimates (can be refined with actual data)
        agent_costs = {
            'performance': 0.02,
            'script': 0.05 if has_transcript else 0.01,
            'seo': 0.03,
            'psychology': 0.03,
            'comment': 0.04,
            'synthesis': 0.06
        }
        
        # Apply caching discount (assuming 30% cache hit rate)
        cache_discount = 0.7
        
        estimated_cost = sum(agent_costs.get(agent, 0.03) for agent in agent_names)
        estimated_cost_with_cache = estimated_cost * cache_discount
        
        return {
            'estimated_cost': estimated_cost,
            'estimated_cost_with_cache': estimated_cost_with_cache,
            'potential_savings': estimated_cost - estimated_cost_with_cache,
            'cache_benefit': f"{(1 - cache_discount) * 100:.0f}% savings from caching"
        }


# Example usage demonstrating optimizations
if __name__ == "__main__":
    import time
    
    # Test data
    test_video_data = {
        'video_id': 'test123',
        'title': 'Test Video',
        'views': 1000000,
        'likes': 50000,
        'comments': 5000,
        'channel_name': 'Test Channel',
        'channel_subscribers': 100000
    }
    
    test_script = "This is a test script " * 1000  # Simulate large transcript
    
    # Initialize optimized interface
    interface = OptimizedStandaloneAgentInterface(use_sequential_thinking=False)
    
    # Estimate costs
    agents_to_run = ['performance', 'script', 'seo']
    cost_estimate = interface.estimate_cost(agents_to_run, has_transcript=True)
    print(f"💰 Cost Estimate: ${cost_estimate['estimated_cost_with_cache']:.4f}")
    print(f"   {cost_estimate['cache_benefit']}")
    
    # Run agents with optimizations
    print("\n🚀 Running optimized agents...")
    start_time = time.time()
    
    results = interface.run_agents_batch(test_video_data, test_script, agents_to_run)
    
    execution_time = time.time() - start_time
    print(f"\n⚡ Total execution time: {execution_time:.2f}s")
    print(f"✅ Completed {len(results)} agent analyses")