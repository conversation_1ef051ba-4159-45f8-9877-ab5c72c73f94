<!-- 
Professional Tool Layout Components
Unified design system for all analysis tools
Replaces the heavy purple gradient forms with clean, enterprise-grade components
-->

<!-- Tool Introduction Section -->
<div class="tool-intro-section">
    <div class="tool-hero">
        <div class="tool-hero-content">
            <div class="tool-header">
                <div class="tool-icon-wrapper" data-tool-color="{TOOL_COLOR}">
                    <i data-lucide="{TOOL_ICON}" class="tool-icon"></i>
                </div>
                <div class="tool-info">
                    <h1 class="tool-title">{TOOL_TITLE}</h1>
                    <p class="tool-subtitle">{TOOL_SUBTITLE}</p>
                    <div class="tool-badges">
                        <span class="tool-badge" data-badge-type="agents">{AGENT_COUNT} AI Agents</span>
                        <span class="tool-badge" data-badge-type="time">~{ANALYSIS_TIME} Analysis</span>
                        <span class="tool-badge" data-badge-type="enterprise">Enterprise Grade</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Capabilities Grid -->
<div class="capabilities-section">
    <div class="capabilities-container">
        <div class="capabilities-header">
            <h2 class="capabilities-title">What This Tool Delivers</h2>
            <p class="capabilities-subtitle">Professional-grade analysis powered by specialized AI agents</p>
        </div>
        <div class="capabilities-grid">
            <!-- Capability items will be inserted here per tool -->
            {CAPABILITIES_CONTENT}
        </div>
    </div>
</div>

<!-- Professional Analysis Form -->
<div class="analysis-form-section">
    <div class="analysis-form-container">
        <div class="form-header">
            <div class="form-header-content">
                <h3 class="form-title">Start Analysis</h3>
                <div class="form-status">
                    <div class="status-indicator ready">
                        <div class="status-dot"></div>
                        <span class="status-text">Ready</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="form-content">
            <form class="professional-form" id="analysisForm">
                {FORM_FIELDS}
                <div class="form-actions">
                    <button type="submit" class="btn-analyze" id="analyzeBtn">
                        <div class="btn-content">
                            <i data-lucide="play" class="btn-icon"></i>
                            <span class="btn-text">{ANALYZE_BUTTON_TEXT}</span>
                        </div>
                        <div class="btn-loading" style="display: none;">
                            <div class="loading-spinner"></div>
                            <span class="loading-text">Analyzing...</span>
                        </div>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Progress Indicator (shown during analysis) -->
<div class="analysis-progress" id="analysisProgress" style="display: none;">
    <div class="progress-container">
        <div class="progress-header">
            <h3 class="progress-title">Analysis in Progress</h3>
            <div class="progress-time" id="progressTime">Estimated: 2-3 minutes</div>
        </div>
        <div class="progress-steps">
            <div class="progress-step active" data-step="1">
                <div class="step-icon">
                    <i data-lucide="download" class="step-icon-svg"></i>
                </div>
                <div class="step-content">
                    <div class="step-title">Data Collection</div>
                    <div class="step-status">Fetching video data...</div>
                </div>
            </div>
            <div class="progress-step" data-step="2">
                <div class="step-icon">
                    <i data-lucide="brain" class="step-icon-svg"></i>
                </div>
                <div class="step-content">
                    <div class="step-title">AI Analysis</div>
                    <div class="step-status">Running {AGENT_COUNT} specialized agents...</div>
                </div>
            </div>
            <div class="progress-step" data-step="3">
                <div class="step-icon">
                    <i data-lucide="file-text" class="step-icon-svg"></i>
                </div>
                <div class="step-content">
                    <div class="step-title">Report Generation</div>
                    <div class="step-status">Compiling insights...</div>
                </div>
            </div>
        </div>
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill" style="width: 0%"></div>
        </div>
    </div>
</div>

<!-- Common Capability Item Template -->
<template id="capabilityItemTemplate">
    <div class="capability-item" data-capability="{CAPABILITY_ID}">
        <div class="capability-icon-wrapper">
            <i data-lucide="{CAPABILITY_ICON}" class="capability-icon"></i>
        </div>
        <div class="capability-content">
            <h4 class="capability-title">{CAPABILITY_TITLE}</h4>
            <p class="capability-description">{CAPABILITY_DESCRIPTION}</p>
            <div class="capability-features">
                {CAPABILITY_FEATURES}
            </div>
        </div>
    </div>
</template>

<!-- Form Field Templates -->
<template id="urlFieldTemplate">
    <div class="form-field">
        <label class="field-label">
            <span class="label-text">{FIELD_LABEL}</span>
            <span class="label-required">*</span>
        </label>
        <div class="field-input-wrapper">
            <div class="input-icon">
                <i data-lucide="link" class="input-icon-svg"></i>
            </div>
            <input 
                type="url" 
                class="field-input" 
                id="{FIELD_ID}"
                placeholder="{FIELD_PLACEHOLDER}"
                required
                autocomplete="off"
            >
            <div class="input-validation">
                <i data-lucide="check" class="validation-icon success"></i>
                <i data-lucide="x" class="validation-icon error"></i>
            </div>
        </div>
        <div class="field-help">{FIELD_HELP_TEXT}</div>
    </div>
</template>

<template id="textareaFieldTemplate">
    <div class="form-field">
        <label class="field-label">
            <span class="label-text">{FIELD_LABEL}</span>
            <span class="label-optional">Optional</span>
        </label>
        <div class="field-input-wrapper">
            <textarea 
                class="field-textarea" 
                id="{FIELD_ID}"
                placeholder="{FIELD_PLACEHOLDER}"
                rows="4"
            ></textarea>
        </div>
        <div class="field-help">{FIELD_HELP_TEXT}</div>
    </div>
</template>

<style>
/* Professional Tool Layout Styles */
.tool-intro-section {
    margin-bottom: 2rem;
}

.tool-hero {
    background: linear-gradient(135deg, 
        var(--bg-elevated) 0%, 
        var(--bg-secondary) 100%);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
}

.tool-hero-content {
    max-width: 100%;
}

.tool-header {
    display: flex;
    align-items: flex-start;
    gap: 1.5rem;
}

.tool-icon-wrapper {
    width: 4rem;
    height: 4rem;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--purple-light);
    border: 2px solid var(--purple-primary);
    flex-shrink: 0;
}

.tool-icon {
    width: 2rem;
    height: 2rem;
    color: var(--purple-primary);
}

.tool-info {
    flex: 1;
}

.tool-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
    line-height: 1.2;
}

.tool-subtitle {
    font-size: var(--text-lg);
    color: var(--text-secondary);
    margin: 0 0 1rem 0;
    line-height: 1.4;
}

.tool-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tool-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-secondary);
}

.tool-badge[data-badge-type="agents"] {
    background: var(--purple-light);
    color: var(--purple-primary);
    border-color: var(--purple-primary);
}

/* Capabilities Section */
.capabilities-section {
    margin-bottom: 2rem;
}

.capabilities-container {
    background: var(--bg-elevated);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
}

.capabilities-header {
    text-align: center;
    margin-bottom: 2rem;
}

.capabilities-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
}

.capabilities-subtitle {
    font-size: var(--text-base);
    color: var(--text-secondary);
    margin: 0;
}

.capabilities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.capability-item {
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    transition: all var(--transition-fast);
}

.capability-item:hover {
    border-color: var(--purple-primary);
    box-shadow: var(--shadow-sm);
    transform: translateY(-1px);
}

.capability-icon-wrapper {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-md);
    background: var(--purple-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.capability-icon {
    width: 1.5rem;
    height: 1.5rem;
    color: var(--purple-primary);
}

.capability-title {
    font-size: var(--text-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
}

.capability-description {
    font-size: var(--text-base);
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
}

/* Professional Form */
.analysis-form-section {
    margin-bottom: 2rem;
}

.analysis-form-container {
    background: var(--bg-elevated);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.form-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-light);
    padding: 1.5rem 2rem;
}

.form-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.form-title {
    font-size: var(--text-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.375rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
}

.status-indicator.ready {
    background: var(--success-light);
    color: var(--success);
}

.status-dot {
    width: 0.5rem;
    height: 0.5rem;
    border-radius: 50%;
    background: currentColor;
}

.form-content {
    padding: 2rem;
}

.professional-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-field {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.field-label {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: var(--text-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.label-required {
    color: var(--error);
}

.label-optional {
    color: var(--text-muted);
    font-weight: var(--font-weight-normal);
}

.field-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    left: 0.75rem;
    z-index: 2;
    color: var(--text-muted);
}

.input-icon-svg {
    width: 1rem;
    height: 1rem;
}

.field-input, .field-textarea {
    width: 100%;
    padding: 0.75rem 0.75rem 0.75rem 2.5rem;
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--text-base);
    transition: all var(--transition-fast);
}

.field-input:focus, .field-textarea:focus {
    outline: none;
    border-color: var(--purple-primary);
    box-shadow: 0 0 0 3px var(--purple-light);
}

.field-textarea {
    padding-left: 0.75rem;
    resize: vertical;
    font-family: var(--font-mono);
}

.field-help {
    font-size: var(--text-sm);
    color: var(--text-muted);
}

/* Analyze Button */
.btn-analyze {
    width: 100%;
    background: var(--purple-primary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: 1rem 2rem;
    font-size: var(--text-lg);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
}

.btn-analyze:hover {
    background: var(--purple-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-analyze:active {
    transform: translateY(0);
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-icon {
    width: 1.25rem;
    height: 1.25rem;
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.loading-spinner {
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Progress Indicator */
.analysis-progress {
    margin-bottom: 2rem;
}

.progress-container {
    background: var(--bg-elevated);
    border: 1px solid var(--border-light);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-sm);
}

.progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.progress-title {
    font-size: var(--text-xl);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin: 0;
}

.progress-time {
    font-size: var(--text-sm);
    color: var(--text-muted);
}

.progress-steps {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.progress-step {
    display: flex;
    align-items: center;
    gap: 1rem;
    opacity: 0.5;
    transition: all var(--transition-fast);
}

.progress-step.active {
    opacity: 1;
}

.step-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background: var(--bg-tertiary);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.progress-step.active .step-icon {
    background: var(--purple-primary);
    color: white;
}

.step-icon-svg {
    width: 1.25rem;
    height: 1.25rem;
}

.step-content {
    flex: 1;
}

.step-title {
    font-size: var(--text-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.step-status {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

.progress-bar {
    width: 100%;
    height: 0.5rem;
    background: var(--bg-tertiary);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--purple-gradient);
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
}

/* Responsive Design */
@media (max-width: 768px) {
    .tool-header {
        flex-direction: column;
        text-align: center;
    }
    
    .capabilities-grid {
        grid-template-columns: 1fr;
    }
    
    .form-header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .progress-steps {
        gap: 1rem;
    }
    
    .progress-step {
        flex-direction: column;
        text-align: center;
    }
}
</style>
