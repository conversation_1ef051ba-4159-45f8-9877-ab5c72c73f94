"""
Working CrewAI YouTube Research v2 App
TABBED INTERFACE - Professional $99/Month SaaS Version
"""

import os
import uuid
import json
from datetime import datetime, timezone
from typing import Dict, Any
from fastapi import FastAPI, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import logging

# CrewAI imports
from crewai import Agent, Crew, Task, Process
from crewai.llm import LLM

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="YouTube Research v2 - CrewAI")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple in-memory storage
analysis_store = {}

# Import our YouTube tools
from src.tools.youtube_tools import YouTubeChannelAnalysisTool

# Pydantic models
class AnalysisRequest(BaseModel):
    channel_id: str
    max_videos: int = 20
    max_comments: int = 50

class VideoAnalysisRequest(BaseModel):
    video_url: str
    script: str

class AnalysisResponse(BaseModel):
    analysis_id: str
    status: str
    message: str

class MarketResearchCrewAI:
    """CrewAI implementation of Market Research for AI Channel Creation"""
    
    def __init__(self):
        # Initialize Gemini models
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if gemini_api_key:
            # Set environment variable for LiteLLM
            os.environ['GEMINI_API_KEY'] = gemini_api_key
            
            self.llm_pro = LLM(
                model="gemini/gemini-1.5-pro"
            )
            self.llm_flash = LLM(
                model="gemini/gemini-1.5-flash"
            )
            self.ai_enabled = True
        else:
            self.llm_pro = None
            self.llm_flash = None
            self.ai_enabled = False
            
        # Initialize YouTube tools
        youtube_api_key = os.getenv('YOUTUBE_API_KEY')
        if youtube_api_key:
            self.youtube_tool = YouTubeChannelAnalysisTool(
                youtube_api_key=youtube_api_key,
                supadata_api_key=os.getenv('SUPADATA_API_KEY')
            )
            self.tools_available = True
        else:
            self.youtube_tool = None
            self.tools_available = False
    
    def create_agents(self):
        """Create the 6 Market Research agents"""
        
        if not self.ai_enabled:
            # Create simple agents without LLM
            return [
                Agent(
                    role="Market Opportunity Scout",
                    goal="Find content gaps and underserved audiences for new AI-generated channels",
                    backstory="Expert at identifying blue ocean opportunities in saturated markets",
                    verbose=True
                ),
                Agent(
                    role="Content Strategy Architect", 
                    goal="Extract viral patterns and content formats that work",
                    backstory="Specializes in reverse-engineering successful content strategies",
                    verbose=True
                ),
                Agent(
                    role="Audience Intelligence Analyst",
                    goal="Mine comments for unmet needs and content requests",
                    backstory="Expert at finding 'I wish someone would make...' opportunities in audience feedback",
                    verbose=True
                ),
                Agent(
                    role="Competitive Landscape Mapper",
                    goal="Benchmark performance and identify positioning opportunities",
                    backstory="Analyzes competitive dynamics and market positioning strategies",
                    verbose=True
                ),
                Agent(
                    role="Production Intelligence Agent",
                    goal="Assess AI feasibility and production complexity",
                    backstory="Evaluates content for automation potential and production efficiency",
                    verbose=True
                ),
                Agent(
                    role="Launch Strategy Synthesizer",
                    goal="Create comprehensive go-to-market plans for new channels",
                    backstory="Develops strategic launch plans combining all intelligence gathered",
                    verbose=True
                )
            ]
        
        # Full agents with LLM
        agents = [
            Agent(
                role="Market Opportunity Scout",
                goal="Find content gaps and underserved audiences for new AI-generated channels",
                backstory="""You are an expert market researcher specializing in identifying blue ocean opportunities 
                in saturated content markets. Your focus is finding untapped niches where new AI-generated channels 
                can succeed without competing directly with established creators.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_flash,
                verbose=True
            ),
            
            Agent(
                role="Content Strategy Architect",
                goal="Extract viral patterns and content formats that work",
                backstory="""You specialize in reverse-engineering successful content strategies. You analyze 
                what makes content go viral, identify repeatable patterns, and extract actionable insights 
                for creating similar but improved content.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_pro,
                verbose=True
            ),
            
            Agent(
                role="Audience Intelligence Analyst", 
                goal="Mine comments for unmet needs and content requests",
                backstory="""You are an expert at finding hidden opportunities in audience feedback. You excel 
                at identifying 'I wish someone would make...' comments, recurring complaints, and unmet needs 
                that represent content opportunities.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_flash,
                verbose=True
            ),
            
            Agent(
                role="Competitive Landscape Mapper",
                goal="Benchmark performance and identify positioning opportunities", 
                backstory="""You analyze competitive dynamics and market positioning. You understand how to 
                position new channels to avoid direct competition while capturing market share through 
                strategic differentiation.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_flash,
                verbose=True
            ),
            
            Agent(
                role="Production Intelligence Agent",
                goal="Assess AI feasibility and production complexity for content automation",
                backstory="""You evaluate content for automation potential using AI tools. You understand 
                production workflows, identify what can be automated vs requires human creativity, and 
                calculate ROI for AI-generated content strategies.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_pro,
                verbose=True
            ),
            
            Agent(
                role="Launch Strategy Synthesizer",
                goal="Create comprehensive go-to-market plans for new channels",
                backstory="""You synthesize all market intelligence into actionable launch strategies. 
                You create detailed plans that combine content strategy, audience targeting, competitive 
                positioning, and production workflows into cohesive channel launch blueprints.""",
                llm=self.llm_pro,
                verbose=True
            )
        ]
        
        return agents
    
    def create_tasks(self, agents, channel_data):
        """Create tasks for market research analysis"""
        
        tasks = [
            Task(
                description=f"""Analyze this YouTube channel data to identify market opportunities for new AI channels:
                
                Channel: {channel_data.get('channel', {}).get('title', 'Unknown')}
                Videos analyzed: {len(channel_data.get('videos', []))}
                
                Your mission: Find content gaps, underserved audiences, and blue ocean opportunities where a new 
                AI-generated channel could succeed. Look for:
                
                1. Content types that are popular but have low competition
                2. Audience segments that seem underserved 
                3. Geographic or demographic gaps
                4. Trending topics with room for new players
                5. Format innovations that could disrupt the space
                
                Focus on opportunities that favor AI-generated content (scripted formats, educational content, 
                list-based videos, etc.)
                
                Data: {json.dumps(channel_data, indent=2)}""",
                agent=agents[0],
                expected_output="Detailed market opportunity analysis with specific content gaps and target audiences"
            ),
            
            Task(
                description=f"""Extract viral content patterns and strategies from this channel data:
                
                Analyze the most successful videos to understand:
                1. What content formats work best (tutorials, lists, stories, etc.)
                2. Optimal video length and structure patterns  
                3. Title formulas and thumbnail strategies
                4. Content themes that generate high engagement
                5. Upload timing and frequency patterns
                
                Create actionable insights for replicating success with AI-generated content.
                
                Data: {json.dumps(channel_data, indent=2)}""",
                agent=agents[1], 
                expected_output="Viral content strategy blueprint with specific patterns and formulas"
            ),
            
            Task(
                description=f"""Mine the comments data to find unmet audience needs:
                
                Look for:
                1. "I wish someone would make..." type comments
                2. Recurring questions that aren't being answered
                3. Complaints about existing content 
                4. Requests for different formats or topics
                5. Geographic or language gaps mentioned
                
                Identify specific content opportunities hiding in audience feedback.
                
                Comments data: {json.dumps(channel_data.get('comments', {}), indent=2)}""",
                agent=agents[2],
                expected_output="List of unmet audience needs and content opportunities from comment analysis"
            ),
            
            Task(
                description=f"""Map the competitive landscape and positioning opportunities:
                
                Analyze this channel's performance against typical benchmarks:
                1. Engagement rates vs subscriber count
                2. View consistency across videos
                3. Growth trajectory indicators
                4. Content saturation in this niche
                5. Opportunities for strategic differentiation
                
                Identify how a new channel could position itself competitively.
                
                Data: {json.dumps(channel_data, indent=2)}""",
                agent=agents[3],
                expected_output="Competitive analysis with strategic positioning recommendations"
            ),
            
            Task(
                description=f"""Assess AI production feasibility for this content niche:
                
                Evaluate:
                1. How easily this content type can be automated with AI
                2. Production complexity and resource requirements  
                3. Quality expectations vs AI capabilities
                4. ROI potential for AI-generated versions
                5. Tools and workflows needed for automation
                
                Rate feasibility from 1-100 and provide specific recommendations.
                
                Content analysis: {json.dumps(channel_data.get('videos', [])[:5], indent=2)}""",
                agent=agents[4],
                expected_output="AI production feasibility assessment with automation recommendations"
            ),
            
            Task(
                description="""Synthesize all intelligence into a comprehensive launch strategy:
                
                Using insights from the previous analyses, create a detailed plan for launching a new AI-generated 
                channel in this space including:
                
                1. Recommended content pillars and format strategy
                2. Target audience and positioning approach
                3. Competitive differentiation tactics
                4. Production workflow and tool recommendations
                5. Launch timeline and milestone targets
                6. Success metrics and optimization approach
                
                Make this actionable and specific.""",
                agent=agents[5],
                expected_output="Comprehensive channel launch strategy with specific recommendations"
            )
        ]
        
        return tasks
    
    def run_analysis(self, channel_id: str, max_videos: int = 20, max_comments: int = 50):
        """Run the complete market research analysis"""
        
        try:
            # Get channel data
            if self.tools_available:
                logger.info(f"Fetching data for channel: {channel_id}")
                channel_data = self.youtube_tool.get_comprehensive_analysis(
                    channel_id=channel_id,
                    max_videos=max_videos, 
                    max_comments=max_comments
                )
                
                if 'error' in channel_data:
                    return {'error': channel_data['error']}
            else:
                logger.info("Using mock data - tools not available")
                channel_data = self._get_mock_data(channel_id)
            
            # Create agents and tasks
            agents = self.create_agents()
            tasks = self.create_tasks(agents, channel_data)
            
            # Run CrewAI analysis if AI is enabled
            crew_result = None
            if self.ai_enabled and len(agents) > 0:
                logger.info("Starting CrewAI analysis...")
                crew = Crew(
                    agents=agents,
                    tasks=tasks,
                    process=Process.sequential,
                    verbose=True
                )
                
                crew_result = crew.kickoff()
                logger.info("CrewAI analysis completed")
            
            # Format results for UI
            results = self._format_results(channel_data, crew_result)
            
            return results
            
        except Exception as e:
            logger.error(f"Analysis failed: {str(e)}")
            return {'error': str(e)}
    
    def _format_results(self, channel_data, crew_result):
        """Format analysis results for the UI"""
        
        # Extract and deeply analyze channel data
        channel_info = channel_data.get('channel', {})
        videos = channel_data.get('videos', [])
        comments_data = channel_data.get('comments', {})
        
        # DEEP CHANNEL PERFORMANCE ANALYSIS
        channel_analysis = self._analyze_channel_performance(channel_info, videos)
        
        # DEEP COMMENT ANALYSIS 
        comment_analysis = self._analyze_comments_deeply(comments_data, videos)
        
        # CONTENT GAP ANALYSIS from real data
        content_gaps = self._identify_real_content_gaps(comments_data, videos)
        
        # VIDEO PERFORMANCE BREAKDOWN
        video_performance = self._analyze_video_performance(videos)
        
        # AI PRODUCTION FEASIBILITY ASSESSMENT
        ai_feasibility = self._assess_ai_production_feasibility(videos, channel_info)
        
        results = {
            'channel_metrics': channel_analysis['metrics'],
            'channel_performance': channel_analysis['performance'],
            'comment_analysis': comment_analysis,
            'content_gaps': content_gaps,
            'video_performance': video_performance,
            'ai_feasibility': ai_feasibility,
            'market_intelligence': content_gaps,
            'content_strategy': video_performance,
            'audience_intelligence': comment_analysis,
            'production_score': ai_feasibility['score'],
            'production_analysis': ai_feasibility,
            'production_launch': {
                'strategy_points': content_gaps['launch_strategy']
            },
            'competition_level': channel_analysis['competition_level'],
            'video_ideas_count': len(content_gaps.get('opportunities', [])),
            'roi_rating': ai_feasibility['roi_rating'],
            'time_to_market': ai_feasibility['time_to_market'],
            'metadata': {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'analysis_type': 'Professional Market Research Report',
                'ai_enabled': self.ai_enabled,
                'tools_available': self.tools_available,
                'total_videos_analyzed': len(videos),
                'total_comments_analyzed': sum(len(comments) for comments in comments_data.values()),
                'raw_result': str(crew_result) if crew_result else None
            }
        }
        
        return results
    
    def _analyze_channel_performance(self, channel_info, videos):
        """Deep channel performance analysis"""
        stats = channel_info.get('statistics', {})
        subscriber_count = int(stats.get('subscriberCount', 0))
        
        # Calculate comprehensive metrics
        total_views = sum(int(v.get('statistics', {}).get('viewCount', 0)) for v in videos)
        total_likes = sum(int(v.get('statistics', {}).get('likeCount', 0)) for v in videos)
        total_comments = sum(int(v.get('statistics', {}).get('commentCount', 0)) for v in videos)
        
        avg_views = total_views // len(videos) if videos else 0
        avg_likes = total_likes // len(videos) if videos else 0
        avg_comments = total_comments // len(videos) if videos else 0
        
        engagement_rate = (total_likes / total_views * 100) if total_views > 0 else 0
        views_per_subscriber = total_views / subscriber_count if subscriber_count > 0 else 0
        
        # Performance categorization
        if views_per_subscriber > 10:
            performance_tier = "Exceptional"
        elif views_per_subscriber > 5:
            performance_tier = "Strong"
        elif views_per_subscriber > 2:
            performance_tier = "Average"
        else:
            performance_tier = "Below Average"
            
        # Competition level assessment
        if subscriber_count > 1000000 and engagement_rate > 5:
            competition_level = "High (Established Market Leader)"
        elif subscriber_count > 100000 and engagement_rate > 3:
            competition_level = "Medium-High (Strong Competitor)"
        elif subscriber_count > 10000:
            competition_level = "Medium (Growing Channel)"
        else:
            competition_level = "Low (Emerging Channel)"
        
        return {
            'metrics': {
                'subscribers': f"{subscriber_count:,}",
                'videos': len(videos),
                'avg_views': f"{avg_views:,}",
                'engagement_rate': f"{engagement_rate:.1f}%",
                'views_per_subscriber': f"{views_per_subscriber:.1f}",
                'total_views': f"{total_views:,}",
                'avg_likes': f"{avg_likes:,}",
                'avg_comments': f"{avg_comments:,}"
            },
            'performance': {
                'tier': performance_tier,
                'views_per_sub_ratio': views_per_subscriber,
                'engagement_strength': 'High' if engagement_rate > 5 else 'Medium' if engagement_rate > 2 else 'Low',
                'content_consistency': 'Analyzing upload patterns...'
            },
            'competition_level': competition_level
        }
    
    def _analyze_comments_deeply(self, comments_data, videos):
        """Deep comment analysis with sentiment and insights"""
        all_comments = []
        for video_id, comments in comments_data.items():
            all_comments.extend(comments)
        
        if not all_comments:
            return {
                'sentiment': {'positive': 0, 'negative': 0, 'neutral': 100},
                'common_themes': ['No comments available for analysis'],
                'content_requests': ['No specific requests found'],
                'questions': ['No questions identified'],
                'positive_feedback': ['No positive feedback available'],
                'negative_feedback': ['No negative feedback available']
            }
        
        # Basic sentiment analysis (simplified)
        positive_keywords = ['great', 'awesome', 'love', 'amazing', 'excellent', 'perfect', 'fantastic', 'helpful', 'thank you', 'best']
        negative_keywords = ['bad', 'terrible', 'hate', 'worst', 'boring', 'sucks', 'disappointed', 'confused', 'unclear']
        request_keywords = ['please', 'can you', 'could you', 'make a video', 'do a tutorial', 'i wish', 'would love', 'next video']
        question_keywords = ['?', 'how', 'what', 'when', 'where', 'why', 'which']
        
        positive_comments = []
        negative_comments = []
        request_comments = []
        question_comments = []
        
        for comment in all_comments:
            if isinstance(comment, dict):
                text = comment.get('text', '').lower()
            else:
                text = str(comment).lower()
            
            # Sentiment classification
            if any(keyword in text for keyword in positive_keywords):
                positive_comments.append(text[:100] + "..." if len(text) > 100 else text)
            elif any(keyword in text for keyword in negative_keywords):
                negative_comments.append(text[:100] + "..." if len(text) > 100 else text)
            
            # Content requests
            if any(keyword in text for keyword in request_keywords):
                request_comments.append(text[:150] + "..." if len(text) > 150 else text)
            
            # Questions
            if any(keyword in text for keyword in question_keywords):
                question_comments.append(text[:150] + "..." if len(text) > 150 else text)
        
        total_comments = len(all_comments)
        positive_percent = (len(positive_comments) / total_comments * 100) if total_comments > 0 else 0
        negative_percent = (len(negative_comments) / total_comments * 100) if total_comments > 0 else 0
        neutral_percent = 100 - positive_percent - negative_percent
        
        return {
            'total_comments_analyzed': total_comments,
            'sentiment': {
                'positive': round(positive_percent, 1),
                'negative': round(negative_percent, 1), 
                'neutral': round(neutral_percent, 1)
            },
            'content_requests': request_comments[:10],  # Top 10 requests
            'questions': question_comments[:15],  # Top 15 questions
            'positive_feedback': positive_comments[:8],  # Top 8 positive
            'negative_feedback': negative_comments[:8],  # Top 8 negative
            'engagement_quality': 'High' if total_comments / len(videos) > 50 else 'Medium' if total_comments / len(videos) > 20 else 'Low'
        }
    
    def _identify_real_content_gaps(self, comments_data, videos):
        """Identify content gaps from actual comment analysis"""
        all_comments = []
        for video_id, comments in comments_data.items():
            all_comments.extend(comments)
        
        # Extract content gap indicators from comments
        gap_indicators = []
        tutorial_requests = []
        topic_requests = []
        
        for comment in all_comments:
            if isinstance(comment, dict):
                text = comment.get('text', '').lower()
            else:
                text = str(comment).lower()
            
            # Content gap patterns
            if 'tutorial on' in text or 'how to' in text:
                tutorial_requests.append(text[:100])
            elif 'video about' in text or 'make one about' in text:
                topic_requests.append(text[:100])
            elif 'i wish' in text or 'would love to see' in text:
                gap_indicators.append(text[:100])
        
        return {
            'opportunities': gap_indicators + tutorial_requests + topic_requests,
            'tutorial_gaps': tutorial_requests[:10],
            'topic_gaps': topic_requests[:10],
            'audience_requests': gap_indicators[:10],
            'launch_strategy': [
                "Focus on highly requested tutorial topics",
                "Address frequently asked questions in comments",
                "Create beginner-friendly versions of popular content",
                "Develop series based on audience requests",
                "Implement Q&A format videos"
            ]
        }
    
    def _analyze_video_performance(self, videos):
        """Analyze video performance patterns"""
        if not videos:
            return {'viral_patterns': ['No video data available']}
        
        # Sort videos by view count
        sorted_videos = sorted(videos, key=lambda x: int(x.get('statistics', {}).get('viewCount', 0)), reverse=True)
        
        top_performers = sorted_videos[:5]
        bottom_performers = sorted_videos[-3:]
        
        # Analyze top performers for patterns
        viral_patterns = []
        for video in top_performers:
            title = video.get('title', '')
            views = int(video.get('statistics', {}).get('viewCount', 0))
            likes = int(video.get('statistics', {}).get('likeCount', 0))
            
            viral_patterns.append(f"'{title[:50]}...' - {views:,} views, {likes:,} likes")
        
        return {
            'viral_patterns': viral_patterns,
            'top_performers': [
                {
                    'title': v.get('title', '')[:60],
                    'views': f"{int(v.get('statistics', {}).get('viewCount', 0)):,}",
                    'likes': f"{int(v.get('statistics', {}).get('likeCount', 0)):,}",
                    'engagement': f"{(int(v.get('statistics', {}).get('likeCount', 0)) / max(int(v.get('statistics', {}).get('viewCount', 1)), 1) * 100):.2f}%"
                } for v in top_performers
            ],
            'performance_insights': [
                f"Top video has {int(top_performers[0].get('statistics', {}).get('viewCount', 0)):,} views",
                f"Average top 5 performance: {sum(int(v.get('statistics', {}).get('viewCount', 0)) for v in top_performers) // 5:,} views",
                f"Performance range: {int(top_performers[0].get('statistics', {}).get('viewCount', 0)) // int(bottom_performers[0].get('statistics', {}).get('viewCount', 1)):.1f}x difference between best and worst"
            ]
        }
    
    def _assess_ai_production_feasibility(self, videos, channel_info):
        """Assess AI production feasibility with detailed breakdown"""
        
        # Analyze content types for AI feasibility
        tutorial_count = sum(1 for v in videos if 'tutorial' in v.get('title', '').lower() or 'how to' in v.get('title', '').lower())
        educational_count = sum(1 for v in videos if any(word in v.get('title', '').lower() for word in ['explain', 'guide', 'learn', 'tips']))
        
        tutorial_ratio = tutorial_count / len(videos) if videos else 0
        educational_ratio = educational_count / len(videos) if videos else 0
        
        # AI feasibility scoring
        base_score = 70
        if tutorial_ratio > 0.5:
            base_score += 15  # High tutorial content
        if educational_ratio > 0.3:
            base_score += 10  # Educational focus
        
        feasibility_score = min(base_score, 95)
        
        return {
            'score': feasibility_score,
            'content_analysis': {
                'tutorial_percentage': f"{tutorial_ratio * 100:.1f}%",
                'educational_percentage': f"{educational_ratio * 100:.1f}%",
                'ai_friendly_content': f"{(tutorial_ratio + educational_ratio) * 100:.1f}%"
            },
            'automation_breakdown': {
                'script_generation': 'High' if educational_ratio > 0.4 else 'Medium',
                'voice_synthesis': 'High' if tutorial_ratio > 0.3 else 'Medium',
                'video_editing': 'Medium' if tutorial_ratio > 0.5 else 'Low',
                'thumbnail_creation': 'High'
            },
            'roi_rating': 'High' if feasibility_score > 85 else 'Medium' if feasibility_score > 70 else 'Low',
            'time_to_market': '2-3wks' if feasibility_score > 85 else '4-6wks' if feasibility_score > 70 else '8-12wks',
            'feasibility': f'Score: {feasibility_score}/100 - {"High" if feasibility_score > 85 else "Medium" if feasibility_score > 70 else "Low"} automation potential'
        }

    def _get_mock_data(self, channel_id):
        """Generate mock data when tools aren't available"""
        return {
            'channel': {
                'id': channel_id,
                'title': f'Mock Channel {channel_id}',
                'description': 'Mock channel for testing',
                'statistics': {'subscriberCount': '100000'}
            },
            'videos': [
                {
                    'id': f'video_{i}',
                    'title': f'Mock Video {i}',
                    'statistics': {'viewCount': str(1000 * i), 'likeCount': str(10 * i)}
                } for i in range(1, 6)
            ],
            'comments': {
                'video_1': ['Great video!', 'Very helpful'],
                'video_2': ['Love this content', 'More please!']
            }
        }

class VideoAnalysisCrewAI:
    """CrewAI implementation for single video analysis"""
    
    def __init__(self):
        # Initialize Gemini models
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if gemini_api_key:
            os.environ['GEMINI_API_KEY'] = gemini_api_key
            
            self.llm_pro = LLM(model="gemini/gemini-1.5-pro")
            self.llm_flash = LLM(model="gemini/gemini-1.5-flash")
            self.ai_enabled = True
        else:
            self.llm_pro = None
            self.llm_flash = None
            self.ai_enabled = False
    
    def analyze_video(self, video_url: str, script: str):
        """Run comprehensive video analysis"""
        try:
            # Extract video data from YouTube API
            video_data = self._get_video_data_from_url(video_url)
            if 'error' in video_data:
                return {'error': video_data['error']}
            
            # Run Agent 1: Performance Intelligence Analyst
            agent1_result = self._run_performance_agent(video_data)
            
            # Run Agent 2: Script Forensics Specialist  
            agent2_result = self._run_script_agent(video_data, script, agent1_result)
            
            return {
                'video_data': video_data,
                'performance_analysis': agent1_result,
                'script_analysis': agent2_result,
                'metadata': {
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'analysis_type': 'Professional Video Analysis Report',
                    'ai_enabled': self.ai_enabled
                }
            }
            
        except Exception as e:
            logger.error(f"Video analysis failed: {str(e)}")
            return {'error': str(e)}
    
    def _get_video_data_from_url(self, video_url: str):
        """Extract video ID and get data from YouTube API"""
        import re
        from googleapiclient.discovery import build
        
        # Extract video ID from URL
        video_id_match = re.search(r'(?:v=|\/)([0-9A-Za-z_-]{11}).*', video_url)
        if not video_id_match:
            return {'error': 'Invalid YouTube URL'}
        
        video_id = video_id_match.group(1)
        
        # Get video data from YouTube API
        youtube_api_key = os.getenv('YOUTUBE_API_KEY')
        if not youtube_api_key:
            return {'error': 'YouTube API key not configured'}
        
        try:
            youtube = build('youtube', 'v3', developerKey=youtube_api_key)
            
            request = youtube.videos().list(
                part='snippet,statistics,contentDetails',
                id=video_id
            )
            response = request.execute()
            
            if not response.get('items'):
                return {'error': 'Video not found'}
            
            video = response['items'][0]
            snippet = video['snippet']
            stats = video['statistics']
            content = video['contentDetails']
            
            # Parse duration
            duration_iso = content['duration']
            duration_match = re.match(r'PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?', duration_iso)
            if duration_match:
                hours = int(duration_match.group(1) or 0)
                minutes = int(duration_match.group(2) or 0)
                seconds = int(duration_match.group(3) or 0)
                total_minutes = hours * 60 + minutes
            else:
                total_minutes, seconds = 0, 0
            
            # Parse published date
            published_at = snippet['publishedAt']
            pub_date = datetime.fromisoformat(published_at.replace('Z', '+00:00'))
            days_ago = (datetime.now().astimezone() - pub_date).days
            
            return {
                'video_id': video_id,
                'title': snippet['title'],
                'views': int(stats.get('viewCount', 0)),
                'likes': int(stats.get('likeCount', 0)),
                'comments': int(stats.get('commentCount', 0)),
                'duration_minutes': total_minutes,
                'duration_seconds': seconds,
                'published_date': published_at,
                'days_since_published': days_ago
            }
            
        except Exception as e:
            return {'error': f'Failed to fetch video data: {str(e)}'}
    
    def _run_performance_agent(self, video_data):
        """Run Agent 1: Performance Intelligence Analyst"""
        if not self.ai_enabled:
            return {'error': 'AI not enabled'}
        
        agent1 = Agent(
            role='Performance Intelligence Analyst',
            goal='Conduct comprehensive performance audit using calculable metrics and YouTube benchmarks',
            backstory='''You are the world's leading YouTube Performance Intelligence Analyst with 15+ years of experience analyzing viral content. You've consulted for top creators, providing the data insights that drove their explosive growth.

Your expertise: Forensic performance analysis using actual calculable metrics and your deep knowledge of YouTube performance standards.''',
            llm=self.llm_pro,
            verbose=True
        )
        
        task1 = Task(
            description=f'''Conduct a comprehensive performance audit of this YouTube video using ONLY the data provided and your training knowledge of YouTube performance benchmarks. Deliver professional-grade analysis that justifies a $99/month subscription.

## DATA PROVIDED
- Video Title: {video_data['title']}
- Views: {video_data['views']:,}
- Likes: {video_data['likes']:,}
- Comments: {video_data['comments']:,}
- Duration: {video_data['duration_minutes']}:{video_data['duration_seconds']:02d}
- Published Date: {video_data['published_date']}
- Days Since Published: {video_data['days_since_published']}

## REQUIRED ANALYSIS (Complete all sections)

### 1. PERFORMANCE METRICS CALCULATION
Calculate and interpret these metrics using your YouTube knowledge:

**Essential Metrics:**
- Views Per Day = Total Views ÷ Days Since Published
- Engagement Rate = (Likes ÷ Views) × 100
- Comment Rate = (Comments ÷ Views) × 100
- Like-to-Comment Ratio = Likes ÷ Comments
- Views per Minute of Content = Views ÷ Duration in Minutes

For each metric: Calculate the number, provide YouTube industry context, explain what it reveals.

### 2. PERFORMANCE CONTEXTUALIZATION
Use your YouTube training knowledge to assess:
- Where each metric ranks in YouTube's performance distribution
- Performance tier classification (top 1%, 5%, 10%, 25%, average, below average)
- What these numbers indicate about audience satisfaction and content effectiveness
- Content lifecycle stage analysis based on age and metrics

### 3. PERFORMANCE INSIGHTS
Explain what these metrics reveal about:
- Audience engagement quality and satisfaction
- Content discoverability and shareability signals
- Community building effectiveness
- Long-term performance potential

### 4. STRATEGIC RECOMMENDATIONS
Based on calculated metrics, provide specific guidance for:
- Replicating this performance level
- Optimizing future content based on these patterns
- Leveraging high-performing elements identified

## OUTPUT FORMAT

Produce TWO outputs:

### OUTPUT 1: USER-FACING REPORT (Beautiful HTML formatting)
```html
<div class="performance-analysis">
  <div class="analysis-header">
    <h2>📊 Performance Intelligence Analysis</h2>
    <div class="overall-grade">Grade: [A+ to F]</div>
  </div>
  
  <div class="metrics-dashboard">
    <div class="metric-card">
      <div class="metric-value">[calculated value]</div>
      <div class="metric-label">[metric name]</div>
      <div class="benchmark">[YouTube benchmark context]</div>
    </div>
    <!-- Repeat for all 5 metrics -->
  </div>
  
  <div class="insights-section">
    <h3>🎯 Key Performance Insights</h3>
    <div class="insight-grid">
      <!-- Performance insights with explanations -->
    </div>
  </div>
  
  <div class="recommendations-section">
    <h3>📈 Strategic Recommendations</h3>
    <ul class="recommendation-list">
      <!-- Specific actionable recommendations -->
    </ul>
  </div>
</div>
```

### OUTPUT 2: STRUCTURED DATA (For other agents)
```json
{{
  "performance_metrics": {{
    "views_per_day": [number],
    "engagement_rate": [number],
    "comment_rate": [number],
    "like_comment_ratio": [number],
    "views_per_minute": [number],
    "performance_tier": "[top_1_percent/top_5_percent/etc]",
    "overall_grade": "[A+ to F]"
  }},
  "key_insights": {{
    "audience_satisfaction": "[very_high/high/medium/low]",
    "engagement_quality": "[exceptional/strong/average/weak]",
    "viral_potential": "[high/medium/low]",
    "content_effectiveness": "[description]"
  }},
  "performance_factors": [
    "list of factors that drove this performance"
  ],
  "recommendations": [
    "list of specific recommendations for other agents"
  ]
}}
```

Deliver comprehensive analysis that covers all calculable metrics with appropriate depth based on data richness.''',
            agent=agent1,
            expected_output='Comprehensive performance analysis with HTML report and structured data output'
        )
        
        crew = Crew(agents=[agent1], tasks=[task1], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': str(result)}
    
    def _run_script_agent(self, video_data, script, performance_data):
        """Run Agent 2: Script Forensics Specialist"""
        if not self.ai_enabled:
            return {'error': 'AI not enabled'}
        
        agent2 = Agent(
            role='Script Forensics Specialist',
            goal='Perform forensic analysis of video script to identify psychological triggers and structural elements',
            backstory='''You are a Hollywood script doctor turned YouTube script forensics expert with 12+ years reverse-engineering viral content. You've analyzed scripts for creators like MrBeast, Marques Brownlee, and Ali Abdaal, identifying the exact psychological triggers and structural elements that drive massive engagement.

Your expertise: Dissecting scripts word-by-word to reveal the hidden psychology behind viral success.''',
            llm=self.llm_pro,
            verbose=True
        )
        
        task2 = Task(
            description=f'''Perform forensic analysis of this video script to identify exactly what psychological triggers, structural elements, and pacing decisions drove its performance. Deliver insights that justify a $99/month professional subscription.

## DATA PROVIDED
- Full Video Script: {script}
- Video Duration: {video_data['duration_minutes']}:{video_data['duration_seconds']:02d}
- Performance Data: {performance_data}
- Video Title: {video_data['title']}

## IMPORTANT: Use the performance data to correlate script elements with results, but DO NOT repeat the performance analysis in your output. Focus on SCRIPT FORENSICS ONLY.

## REQUIRED ANALYSIS

### 1. SCRIPT METRICS CALCULATION
Calculate these measurable script elements:

**Pacing Analysis:**
- Total Word Count
- Speaking Pace (WPM) = Word Count ÷ Duration in Minutes
- Pace Category (Slow <120 WPM, Normal 120-150, Fast 150-180, Hyper >180)
- Sentence Length Average (words per sentence)
- Paragraph Length Average (sentences per paragraph)

**Structure Metrics:**
- Hook Length (first 30-50 words)
- Introduction Length (opening section word count)
- Main Content Sections (count major topics/transitions)
- Conclusion Length (closing section word count)
- Call-to-Action Count and Positions

### 2. HOOK FORENSICS (First 30-50 words)
Analyze the opening with surgical precision:

**Hook Type Identification:**
- Question Hook ("Have you ever wondered...")
- Problem Hook ("Most people struggle with...")
- Story Hook ("Last week something crazy happened...")
- Statistic Hook ("90% of people don't know...")
- Promise Hook ("I'll show you exactly how to...")
- Controversy Hook ("Everyone is wrong about...")

**Hook Effectiveness Elements:**
- Time to main promise/value proposition
- Curiosity gap creation strength
- Personal connection establishment
- Immediate relevance to target audience
- Emotional engagement level

### 3. SCRIPT STRUCTURE ANALYSIS
Map the entire script architecture and analyze psychological triggers, content delivery methods, and retention optimization techniques.

Produce comprehensive analysis with HTML report and structured data for other agents.''',
            agent=agent2,
            expected_output='Comprehensive script analysis with HTML report and structured data output'
        )
        
        crew = Crew(agents=[agent2], tasks=[task2], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': str(result)}
    
    def _run_seo_agent(self, video_data, script, performance_data):
        """Run Agent 3: SEO & Discoverability Expert"""
        if not self.ai_enabled:
            return {'error': 'AI not enabled'}
        
        agent3 = Agent(
            role='SEO & Discoverability Expert',
            goal='Analyze video SEO optimization, keyword strategy, and script-title alignment for maximum discoverability',
            backstory='''You are YouTube's leading SEO strategist with 10+ years optimizing videos for discovery. You've helped creators achieve 10M+ views through strategic SEO implementation, working with top channels to dominate search results.

Your expertise: Reverse-engineering viral video SEO patterns, analyzing keyword strategies, and optimizing script content for YouTube's algorithm.''',
            llm=self.llm_pro,
            verbose=True
        )
        
        task3 = Task(
            description=f'''Conduct comprehensive SEO and discoverability analysis of this video, focusing on title optimization, keyword strategy, and critical script-keyword alignment. Deliver professional-grade insights that justify a $99/month subscription.

## DATA PROVIDED
- Video Title: {video_data['title']}
- Full Script: {script}
- Performance Data: {performance_data}
- Views: {video_data['views']:,} | Likes: {video_data['likes']:,} | Comments: {video_data['comments']:,}
- Duration: {video_data['duration_minutes']}:{video_data['duration_seconds']:02d}

## REQUIRED ANALYSIS

### 1. TITLE SEO ANALYSIS
Extract and evaluate title optimization:

**Keyword Extraction:**
- Primary keywords (1-3 main terms)
- Secondary keywords (supporting terms)  
- Long-tail phrases
- Emotional triggers/power words
- Character count and optimization

**Title Effectiveness Scoring:**
- Search volume potential (based on keyword strength)
- Click-through rate optimization elements
- Algorithm-friendly structure
- Competitive advantage assessment

### 2. SCRIPT-KEYWORD ALIGNMENT ANALYSIS ⭐ CRITICAL
Analyze relationship between title keywords and script content:

**Keyword Consistency:**
- Count mentions of each primary keyword in script
- Calculate keyword density percentages
- Identify keyword placement (intro/middle/conclusion)
- Natural vs forced keyword integration

**Semantic Relevance:**
- Does script fulfill title promise?
- Topic consistency throughout content
- Related keyword coverage (semantic SEO)
- Search intent fulfillment analysis

**Authority Signals in Script:**
- Expertise demonstration through specific details
- Credible information sources mentioned
- Technical depth appropriate for topic
- Original insights vs rehashed content

### 3. DISCOVERABILITY OPTIMIZATION
Assess factors affecting video discovery:

**Algorithm Signals:**
- Title-content matching strength
- Topic authority indicators in script
- Engagement-driving elements
- Watch time optimization factors

**Search Capture Potential:**
- Long-tail keyword coverage in script
- Related topic mentions for broader reach
- Trending topic connections
- Niche authority building elements

### 4. OPTIMIZATION RECOMMENDATIONS
Provide specific SEO improvement strategies:

**Title Optimization:**
- Alternative title suggestions
- Keyword positioning improvements
- CTR enhancement tactics
- A/B testing recommendations

**Script Enhancement:**
- Strategic keyword placement suggestions  
- Related keyword integration opportunities
- Authority-building content additions
- Search intent optimization improvements

Deliver comprehensive SEO analysis that reveals exactly how to optimize this video for maximum discoverability and search performance.''',
            agent=agent3,
            expected_output='Comprehensive SEO analysis with keyword-script alignment and optimization recommendations'
        )
        
        crew = Crew(agents=[agent3], tasks=[task3], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': str(result)}
    
    def _run_psychology_agent(self, video_data, script, performance_data):
        """Run Agent 4: Audience Psychology Analyst"""
        if not self.ai_enabled:
            return {'error': 'AI not enabled'}
        
        agent4 = Agent(
            role='Audience Psychology Analyst', 
            goal='Analyze viewer psychology, emotional triggers, and audience behavior patterns to understand what drives engagement',
            backstory='''You are a behavioral psychologist specializing in digital media consumption with a PhD in Consumer Psychology. You've spent 8+ years analyzing viewer behavior for top YouTubers, identifying the psychological patterns that create addictive content and drive massive engagement.

Your expertise: Reverse-engineering the psychological mechanics behind viral content, understanding viewer motivations, and predicting audience behavior patterns.''',
            llm=self.llm_pro,
            verbose=True
        )
        
        task4 = Task(
            description=f'''Conduct deep psychological analysis of this video's audience appeal, emotional triggers, and viewer behavior patterns. Deliver insights that justify a $99/month professional subscription.

## DATA PROVIDED
- Video Title: {video_data['title']}
- Full Script: {script}
- Performance Metrics: Views: {video_data['views']:,} | Likes: {video_data['likes']:,} | Comments: {video_data['comments']:,}
- Engagement Rate: {performance_data.get('engagement_rate', 'N/A')}%
- Duration: {video_data['duration_minutes']}:{video_data['duration_seconds']:02d}

## REQUIRED ANALYSIS

### 1. PSYCHOLOGICAL TRIGGER IDENTIFICATION
Analyze content elements that activate psychological responses:

**Core Emotional Triggers:**
- Curiosity gaps and information seeking behavior
- Authority and credibility signals
- Social proof and validation needs
- Fear of missing out (FOMO) elements
- Achievement and status psychology
- Tribal identity and belonging signals

**Cognitive Engagement Patterns:**
- Mental model building (how content changes thinking)
- Cognitive load management (information processing ease)
- Pattern recognition satisfaction
- Problem-solution psychology
- Learning reward mechanisms

### 2. AUDIENCE MOTIVATION ANALYSIS
Identify why viewers are drawn to this content:

**Primary Viewer Motivations:**
- Knowledge acquisition (learning new information)
- Entertainment value (enjoyment and escapism)
- Social currency (shareable insights for status)
- Personal relevance (connection to viewer's life)
- Validation seeking (confirmation of existing beliefs)

**Psychological Needs Fulfilled:**
- Competence (feeling smarter/more informed)
- Autonomy (making independent judgments)
- Relatedness (connection to community/creator)
- Security (understanding complex world)
- Stimulation (intellectual challenge)

### 3. ENGAGEMENT PSYCHOLOGY ANALYSIS
Examine psychological factors driving high engagement:

**Attention Mechanisms:**
- Hook psychology and attention capture
- Attention maintenance strategies throughout video
- Cognitive load distribution (easy vs challenging content)
- Surprise and novelty elements
- Pattern breaks and attention resets

**Interaction Psychology:**
- Comment-driving psychological triggers
- Like/dislike motivation factors
- Sharing psychology (why viewers recommend)
- Parasocial relationship building elements
- Community belonging signals

### 4. DEMOGRAPHIC PSYCHOLOGY INSIGHTS
Analyze psychological patterns suggesting audience characteristics:

**Likely Demographics (based on content psychology):**
- Age groups attracted to this content style
- Educational background indicators
- Interest categories and psychographics
- Personality types most engaged
- Cultural and geographic appeal factors

**Behavioral Patterns:**
- Binge-watching likelihood
- Repeat viewing probability
- Cross-content consumption patterns
- Platform behavior tendencies
- Social sharing motivations

### 5. PSYCHOLOGICAL OPTIMIZATION RECOMMENDATIONS
Provide psychology-based improvement strategies:

**Engagement Enhancement:**
- Psychological trigger strengthening opportunities
- Emotional journey optimization
- Attention span management improvements
- Community building psychology tactics

**Audience Expansion:**
- Psychological broadening strategies
- Cross-demographic appeal methods
- Viral psychology activation techniques
- Long-term loyalty building approaches

Analyze this video's psychological appeal with scientific precision, identifying exactly what mental triggers drive its success.''',
            agent=agent4,
            expected_output='Comprehensive audience psychology analysis with behavioral insights and optimization recommendations'
        )
        
        crew = Crew(agents=[agent4], tasks=[task4], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': str(result)}

# Initialize CrewAI instances
crew_ai = None
video_ai = None

def get_crewai():
    """Get or create CrewAI instance"""
    global crew_ai
    if crew_ai is None:
        crew_ai = MarketResearchCrewAI()
    return crew_ai

def get_video_ai():
    """Get or create Video Analysis CrewAI instance"""
    global video_ai
    if video_ai is None:
        video_ai = VideoAnalysisCrewAI()
    return video_ai

# Routes
@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve web interface"""
    return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YoutubePulse - AI Market Research</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0b14;
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 24px;
        }
        
        header {
            background: rgba(15, 16, 23, 0.95);
            backdrop-filter: blur(12px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            padding: 1.25rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ff1744, #d50000);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 4px 16px rgba(255, 23, 68, 0.3);
        }
        
        .logo-text {
            font-size: 1.75rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ff1744, #ff6b6b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-menu {
            display: flex;
            gap: 0.75rem;
        }
        
        .nav-item {
            padding: 0.75rem 1.25rem;
            background: rgba(255, 255, 255, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 12px;
            color: #e2e8f0;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .nav-item:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 23, 68, 0.5);
            transform: translateY(-2px);
        }
        
        .main-content {
            padding: 2.5rem 0;
        }
        
        /* Hero Section */
        .hero-section {
            background: linear-gradient(135deg, rgba(255, 23, 68, 0.15), rgba(213, 0, 0, 0.1));
            border: 1px solid rgba(255, 23, 68, 0.2);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 30%, rgba(255, 23, 68, 0.05) 70%);
            pointer-events: none;
        }
        
        .hero-title {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #ffffff, #cbd5e1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
        }
        
        .hero-subtitle {
            font-size: 1.125rem;
            color: #94a3b8;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }
        
        .search-form {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr auto;
            gap: 1rem;
            align-items: end;
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 0.5rem;
            color: #cbd5e1;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .form-group input {
            padding: 1rem;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            color: #ffffff;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #ff1744;
            box-shadow: 0 0 0 3px rgba(255, 23, 68, 0.2);
            background: rgba(255, 255, 255, 0.12);
        }
        
        .btn {
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #ff1744, #d50000);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 16px rgba(255, 23, 68, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(255, 23, 68, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        /* Tabbed Interface */
        .tab-container {
            margin-top: 2rem;
        }
        
        .tab-navigation {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            overflow-x: auto;
            padding-bottom: 1rem;
        }
        
        .tab-button {
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 12px 12px 0 0;
            color: #94a3b8;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 140px;
            justify-content: center;
        }
        
        .tab-button:hover {
            background: rgba(255, 255, 255, 0.12);
            color: #e2e8f0;
        }
        
        .tab-button.active {
            background: linear-gradient(135deg, #ff1744, #d50000);
            border-color: #ff1744;
            color: #ffffff;
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(255, 23, 68, 0.3);
        }
        
        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .tab-icon {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }
        
        /* Analysis sections */
        .analysis-section {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .section-icon {
            width: 32px;
            height: 32px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            color: currentColor;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ffffff;
        }
        
        .section-subtitle {
            font-size: 1rem;
            color: #94a3b8;
            margin-top: 0.25rem;
        }
        
        .content-grid {
            display: grid;
            gap: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .content-grid.two-column {
            grid-template-columns: 1fr 1fr;
        }
        
        .content-grid.three-column {
            grid-template-columns: 1fr 1fr 1fr;
        }
        
        .content-grid.four-column {
            grid-template-columns: repeat(4, 1fr);
        }
        
        .content-card {
            background: rgba(255, 255, 255, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 16px;
            padding: 1.5rem;
        }
        
        .content-card h4 {
            color: #ffffff;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .metric-item {
            text-align: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.06);
            border-radius: 12px;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ffffff;
            display: block;
        }
        
        .metric-label {
            font-size: 0.75rem;
            color: #94a3b8;
            margin-top: 0.25rem;
        }
        
        .content-list {
            list-style: none;
            padding: 0;
        }
        
        .content-list li {
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            color: #cbd5e1;
        }
        
        .content-list li:last-child {
            border-bottom: none;
        }
        
        .loading {
            text-align: center;
            padding: 3rem;
            color: #cbd5e1;
        }
        
        .spinner {
            display: inline-block;
            width: 24px;
            height: 24px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ff1744;
            animation: spin 1s linear infinite;
            margin-right: 0.75rem;
        }
        
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
        
        .error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(220, 38, 38, 0.1));
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #fca5a5;
            padding: 2rem;
            border-radius: 16px;
            margin: 2rem 0;
            text-align: center;
        }
        
        .success {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(16, 185, 129, 0.1));
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #86efac;
            padding: 2rem;
            border-radius: 16px;
            margin: 2rem 0;
            text-align: center;
        }
        
        @media (max-width: 1200px) {
            .content-grid.four-column {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .content-grid.two-column,
            .content-grid.three-column,
            .content-grid.four-column {
                grid-template-columns: 1fr;
            }
            
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-section {
                padding: 2rem;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-menu {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .tab-navigation {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                        </svg>
                    </div>
                    <div class="logo-text">YoutubePulse</div>
                </div>
                <nav class="nav-menu">
                    <a href="#" class="nav-item active" onclick="switchMode('channel'); return false;" id="nav-channel">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                        </svg>
                        Channel Analysis
                    </a>
                    <a href="#" class="nav-item" onclick="switchMode('video'); return false;" id="nav-video">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                        </svg>
                        Video Analysis
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container">
        <main class="main-content">
            <!-- CHANNEL ANALYSIS SECTION -->
            <section class="hero-section" id="channel-section">
                <h1 class="hero-title">AI-Powered YouTube Channel Analysis</h1>
                <p class="hero-subtitle">Discover content gaps, analyze competitors, and find profitable niches for AI-generated channels</p>
                
                <form class="search-form" onsubmit="startChannelAnalysis(event)">
                    <div class="form-group">
                        <label for="channel_id">YouTube Channel ID or Handle</label>
                        <input type="text" id="channel_id" name="channel_id" placeholder="@mrbeast or UCX6OQ3DkcsbYNE6H8uQQuVA" required>
                    </div>
                    <div class="form-group">
                        <label for="max_videos">Max Videos</label>
                        <input type="number" id="max_videos" name="max_videos" value="20" min="1" max="100">
                    </div>
                    <div class="form-group">
                        <label for="max_comments">Max Comments</label>
                        <input type="number" id="max_comments" name="max_comments" value="50" min="1" max="200">
                    </div>
                    <button type="submit" class="btn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                        Analyze Channel
                    </button>
                </form>
            </section>

            <!-- VIDEO ANALYSIS SECTION -->
            <section class="hero-section" id="video-section" style="display: none;">
                <h1 class="hero-title">AI-Powered YouTube Video Analysis</h1>
                <p class="hero-subtitle">Deep forensic analysis of individual videos - performance, script, SEO, and optimization insights</p>
                
                <form class="search-form" onsubmit="startVideoAnalysis(event)">
                    <div class="form-group">
                        <label for="video_url">YouTube Video URL</label>
                        <input type="url" id="video_url" name="video_url" placeholder="https://www.youtube.com/watch?v=..." required>
                    </div>
                    <div class="form-group" style="grid-column: 1 / -1;">
                        <label for="video_script">Video Script/Transcript</label>
                        <textarea id="video_script" name="video_script" placeholder="Paste the complete video script or transcript here..." 
                                style="width: 100%; height: 120px; padding: 1rem; background: rgba(255, 255, 255, 0.08); border: 1px solid rgba(255, 255, 255, 0.15); border-radius: 12px; color: #ffffff; font-size: 1rem; resize: vertical;" 
                                required></textarea>
                    </div>
                    <button type="submit" class="btn" style="grid-column: 1 / -1; justify-self: center; max-width: 300px;">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                        </svg>
                        Analyze Video
                    </button>
                </form>
            </section>

            <div id="results-area"></div>
        </main>
    </div>

    <script>
        let currentAnalysisId = null;
        let currentMode = 'channel';

        // MODE SWITCHING
        function switchMode(mode) {
            currentMode = mode;
            
            // Update navigation
            document.getElementById('nav-channel').classList.toggle('active', mode === 'channel');
            document.getElementById('nav-video').classList.toggle('active', mode === 'video');
            
            // Update sections
            document.getElementById('channel-section').style.display = mode === 'channel' ? 'block' : 'none';
            document.getElementById('video-section').style.display = mode === 'video' ? 'block' : 'none';
            
            // Clear results
            document.getElementById('results-area').innerHTML = '';
        }

        // CHANNEL ANALYSIS
        async function startChannelAnalysis(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const data = {
                channel_id: formData.get('channel_id'),
                max_videos: parseInt(formData.get('max_videos')),
                max_comments: parseInt(formData.get('max_comments'))
            };

            const resultsArea = document.getElementById('results-area');
            resultsArea.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    Starting comprehensive market analysis for ${data.channel_id}...
                </div>
            `;

            try {
                const response = await fetch('/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (result.status === 'started') {
                    currentAnalysisId = result.analysis_id;
                    pollForResults(result.analysis_id);
                } else {
                    throw new Error(result.message || 'Failed to start analysis');
                }
            } catch (error) {
                resultsArea.innerHTML = `
                    <div class="error">
                        <h3>Analysis Failed</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // VIDEO ANALYSIS
        async function startVideoAnalysis(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const data = {
                video_url: formData.get('video_url'),
                script: formData.get('video_script')
            };

            const resultsArea = document.getElementById('results-area');
            resultsArea.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    Starting comprehensive video analysis...
                </div>
            `;

            try {
                const response = await fetch('/analyze-video', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (result.error) {
                    throw new Error(result.error);
                } else {
                    displayVideoResults(result);
                }
            } catch (error) {
                resultsArea.innerHTML = `
                    <div class="error">
                        <h3>Video Analysis Failed</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        function displayVideoResults(data) {
            const resultsArea = document.getElementById('results-area');
            
            // Debug: Log the data structure
            console.log('Video Analysis Data:', data);
            console.log('Performance Analysis:', data.performance_analysis);
            console.log('Script Analysis:', data.script_analysis);
            console.log('SEO Analysis:', data.seo_analysis);
            console.log('Psychology Analysis:', data.psychology_analysis);
            
            // Helper function to extract HTML from agent response
            function extractHTML(agentResponse) {
                if (!agentResponse || !agentResponse.analysis) return 'Analysis not available';
                
                const text = agentResponse.analysis;
                // Look for HTML content between ```html and ``` tags
                const htmlMatch = text.match(/```html\s*([\s\S]*?)\s*```/);
                if (htmlMatch) {
                    return htmlMatch[1].trim();
                }
                
                // If no HTML tags found, format as basic HTML
                return `<div style="white-space: pre-wrap; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;">${text}</div>`;
            }
            
            resultsArea.innerHTML = `
                <div class="success">
                    <h3>✅ Video Analysis Complete</h3>
                    <p>Analyzed video: ${data.video_data?.title || 'Unknown'}</p>
                </div>
                
                <div class="tab-container">
                    <div class="tab-navigation">
                        <button class="tab-button active" onclick="switchVideoTab('overview')">
                            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                            </svg>
                            Overview
                        </button>
                        <button class="tab-button" onclick="switchVideoTab('performance')">
                            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z"/>
                            </svg>
                            Performance Intelligence
                        </button>
                        <button class="tab-button" onclick="switchVideoTab('script')">
                            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/>
                            </svg>
                            Script Forensics
                        </button>
                        <button class="tab-button" onclick="switchVideoTab('seo')" style="opacity: 0.5;">
                            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                            </svg>
                            SEO (Coming Soon)
                        </button>
                        <button class="tab-button" onclick="switchVideoTab('audience')" style="opacity: 0.5;">
                            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                            </svg>
                            Audience (Coming Soon)
                        </button>
                    </div>

                    <!-- OVERVIEW TAB -->
                    <div id="video-overview" class="tab-content active">
                        <div class="analysis-section">
                            <div class="section-header">
                                <div class="section-icon">📊</div>
                                <div>
                                    <div class="section-title">Video Analysis Summary</div>
                                    <div class="section-subtitle">Key metrics and insights</div>
                                </div>
                            </div>
                            
                            <div class="metric-grid">
                                <div class="metric-item">
                                    <span class="metric-value">${data.video_data?.views?.toLocaleString() || 'N/A'}</span>
                                    <span class="metric-label">Views</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.video_data?.likes?.toLocaleString() || 'N/A'}</span>
                                    <span class="metric-label">Likes</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.video_data?.comments?.toLocaleString() || 'N/A'}</span>
                                    <span class="metric-label">Comments</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.video_data?.duration_minutes || 0}:${String(data.video_data?.duration_seconds || 0).padStart(2, '0')}</span>
                                    <span class="metric-label">Duration</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- PERFORMANCE TAB -->
                    <div id="video-performance" class="tab-content">
                        <div class="analysis-section">
                            <div class="section-header">
                                <div class="section-icon">📈</div>
                                <div>
                                    <div class="section-title">Performance Intelligence Analysis</div>
                                    <div class="section-subtitle">Deep performance metrics and benchmarks</div>
                                </div>
                            </div>
                            <div style="color: #e2e8f0; line-height: 1.6;">
                                ${extractHTML(data.performance_analysis)}
                            </div>
                        </div>
                    </div>

                    <!-- SCRIPT TAB -->
                    <div id="video-script" class="tab-content">
                        <div class="analysis-section">
                            <div class="section-header">
                                <div class="section-icon">📝</div>
                                <div>
                                    <div class="section-title">Script Forensics Analysis</div>
                                    <div class="section-subtitle">Psychological triggers and structural elements</div>
                                </div>
                            </div>
                            <div style="color: #e2e8f0; line-height: 1.6;">
                                ${extractHTML(data.script_analysis)}
                            </div>
                        </div>
                    </div>

                    <!-- SEO TAB -->
                    <div id="video-seo" class="tab-content">
                        <div class="analysis-section">
                            <div style="text-align: center; padding: 3rem; color: #64748b;">
                                <h3>SEO Analysis Coming Soon</h3>
                                <p>Advanced SEO optimization analysis will be available in the next update.</p>
                            </div>
                        </div>
                    </div>

                    <!-- AUDIENCE TAB -->
                    <div id="video-audience" class="tab-content">
                        <div class="analysis-section">
                            <div style="text-align: center; padding: 3rem; color: #64748b;">
                                <h3>Audience Analysis Coming Soon</h3>
                                <p>Deep audience psychology analysis will be available in the next update.</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function switchVideoTab(tabName) {
            // Hide all video tab contents
            document.querySelectorAll('[id^="video-"]').forEach(content => {
                if (content.classList.contains('tab-content')) {
                    content.classList.remove('active');
                }
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(`video-${tabName}`).classList.add('active');
            
            // Add active class to clicked button
            event.target.closest('.tab-button').classList.add('active');
        }

        async function pollForResults(analysisId) {
            const resultsArea = document.getElementById('results-area');
            
            const pollInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/results/${analysisId}`);
                    const result = await response.json();
                    
                    if (result.status === 'completed') {
                        clearInterval(pollInterval);
                        displayResults(result.data);
                    } else if (result.status === 'failed') {
                        clearInterval(pollInterval);
                        resultsArea.innerHTML = `
                            <div class="error">
                                <h3>Analysis Failed</h3>
                                <p>${result.message || 'Unknown error occurred'}</p>
                            </div>
                        `;
                    } else {
                        resultsArea.innerHTML = `
                            <div class="loading">
                                <div class="spinner"></div>
                                Running 6-agent market research analysis... This may take 2-3 minutes.
                            </div>
                        `;
                    }
                } catch (error) {
                    clearInterval(pollInterval);
                    resultsArea.innerHTML = `
                        <div class="error">
                            <h3>Connection Error</h3>
                            <p>${error.message}</p>
                        </div>
                    `;
                }
            }, 3000);
        }

        function displayResults(data) {
            const resultsArea = document.getElementById('results-area');
            
            resultsArea.innerHTML = `
                <div class="success">
                    <h3>✅ Professional Market Analysis Complete</h3>
                    <p>Analyzed ${data.metadata?.total_videos_analyzed || 0} videos and ${data.metadata?.total_comments_analyzed || 0} comments</p>
                </div>
                
                <div class="tab-container">
                    <div class="tab-navigation">
                        <button class="tab-button active" onclick="switchTab('overview')">
                            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                            </svg>
                            Overview
                        </button>
                        <button class="tab-button" onclick="switchTab('channel-analysis')">
                            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                            Channel DNA
                        </button>
                        <button class="tab-button" onclick="switchTab('comment-analysis')">
                            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12H7v-2h6v2zm3-4H7V8h9v2z"/>
                            </svg>
                            Comment Intelligence
                        </button>
                        <button class="tab-button" onclick="switchTab('video-performance')">
                            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M18 3v2h-2V3H8v2H6V3H4v18h2v-2h2v2h8v-2h2v2h2V3h-2z"/>
                            </svg>
                            Video Performance
                        </button>
                        <button class="tab-button" onclick="switchTab('market-opportunities')">
                            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                            Market Opportunities
                        </button>
                        <button class="tab-button" onclick="switchTab('ai-production')">
                            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                            </svg>
                            AI Production
                        </button>
                        <button class="tab-button" onclick="switchTab('launch-strategy')">
                            <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                            Launch Strategy
                        </button>
                    </div>

                    <!-- OVERVIEW TAB -->
                    <div id="overview" class="tab-content active">
                        <div class="analysis-section">
                            <div class="section-header">
                                <div class="section-icon">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                                    </svg>
                                </div>
                                <div>
                                    <div class="section-title">Executive Summary</div>
                                    <div class="section-subtitle">Key insights and market entry recommendations</div>
                                </div>
                            </div>
                            
                            <div class="metric-grid">
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_metrics?.subscribers || 'N/A'}</span>
                                    <span class="metric-label">Subscribers</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_metrics?.avg_views || 'N/A'}</span>
                                    <span class="metric-label">Avg Views</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_metrics?.engagement_rate || 'N/A'}</span>
                                    <span class="metric-label">Engagement Rate</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.production_score || '85'}/100</span>
                                    <span class="metric-label">AI Feasibility</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.video_ideas_count || '0'}</span>
                                    <span class="metric-label">Opportunities</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.roi_rating || 'High'}</span>
                                    <span class="metric-label">ROI Potential</span>
                                </div>
                            </div>
                            
                            <div class="content-grid two-column" style="margin-top: 2rem;">
                                <div class="content-card">
                                    <h4>Top Market Opportunities</h4>
                                    <ul class="content-list">
                                        ${(data.content_gaps?.opportunities || ['No opportunities identified']).slice(0, 8).map(opp => `<li>• ${opp}</li>`).join('')}
                                    </ul>
                                </div>
                                
                                <div class="content-card" style="text-align: center;">
                                    <h4>Quick Assessment</h4>
                                    <div style="margin: 1.5rem 0;">
                                        <div style="font-size: 2.5rem; font-weight: 900; color: #22c55e;">
                                            ${data.production_score || '85'}/100
                                        </div>
                                        <p style="color: #94a3b8; margin-top: 0.5rem;">AI Production Score</p>
                                    </div>
                                    <div style="text-align: left; font-size: 0.9rem; color: #cbd5e1;">
                                        <div style="margin-bottom: 0.5rem;">Competition: ${(data.competition_level || 'Medium').split('(')[0]}</div>
                                        <div style="margin-bottom: 0.5rem;">Time to Market: ${data.time_to_market || '2-4wks'}</div>
                                        <div>ROI Rating: <span style="color: #22c55e;">${data.roi_rating || 'High'}</span></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CHANNEL ANALYSIS TAB -->
                    <div id="channel-analysis" class="tab-content">
                        ${generateChannelAnalysisTab(data)}
                    </div>

                    <!-- COMMENT ANALYSIS TAB -->
                    <div id="comment-analysis" class="tab-content">
                        ${generateCommentAnalysisTab(data)}
                    </div>

                    <!-- VIDEO PERFORMANCE TAB -->
                    <div id="video-performance" class="tab-content">
                        ${generateVideoPerformanceTab(data)}
                    </div>

                    <!-- MARKET OPPORTUNITIES TAB -->
                    <div id="market-opportunities" class="tab-content">
                        ${generateMarketOpportunitiesTab(data)}
                    </div>

                    <!-- AI PRODUCTION TAB -->
                    <div id="ai-production" class="tab-content">
                        ${generateAIProductionTab(data)}
                    </div>

                    <!-- LAUNCH STRATEGY TAB -->
                    <div id="launch-strategy" class="tab-content">
                        ${generateLaunchStrategyTab(data)}
                    </div>
                </div>
            `;
        }

        function switchTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked button
            event.target.closest('.tab-button').classList.add('active');
        }

        function generateChannelAnalysisTab(data) {
            return `
                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div>
                            <div class="section-title">Channel DNA Deep Dive</div>
                            <div class="section-subtitle">Comprehensive performance analysis and competitive positioning</div>
                        </div>
                    </div>
                    
                    <div class="content-grid two-column">
                        <div class="content-card">
                            <h4>Performance Metrics</h4>
                            <div class="metric-grid">
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_metrics?.subscribers || 'N/A'}</span>
                                    <span class="metric-label">Subscribers</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_metrics?.total_views || 'N/A'}</span>
                                    <span class="metric-label">Total Views</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_metrics?.avg_views || 'N/A'}</span>
                                    <span class="metric-label">Average Views</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_metrics?.engagement_rate || 'N/A'}</span>
                                    <span class="metric-label">Engagement Rate</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_metrics?.views_per_subscriber || 'N/A'}</span>
                                    <span class="metric-label">Views per Subscriber</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_performance?.tier || 'N/A'}</span>
                                    <span class="metric-label">Performance Tier</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="content-card">
                            <h4>Competitive Assessment</h4>
                            <div style="padding: 1rem 0;">
                                <div style="margin-bottom: 1rem;">
                                    <strong style="color: #ffffff;">Market Position:</strong>
                                    <div style="color: #cbd5e1; margin-top: 0.5rem;">${data.competition_level || 'Medium competition level'}</div>
                                </div>
                                <div style="margin-bottom: 1rem;">
                                    <strong style="color: #ffffff;">Performance Tier:</strong>
                                    <div style="color: #cbd5e1; margin-top: 0.5rem;">${data.channel_performance?.tier || 'Average'} performance relative to channel size</div>
                                </div>
                                <div style="margin-bottom: 1rem;">
                                    <strong style="color: #ffffff;">Engagement Strength:</strong>
                                    <div style="color: #cbd5e1; margin-top: 0.5rem;">${data.channel_performance?.engagement_strength || 'Medium'} audience interaction quality</div>
                                </div>
                                <div>
                                    <strong style="color: #ffffff;">Growth Opportunity:</strong>
                                    <div style="color: #22c55e; margin-top: 0.5rem;">Multiple content gaps identified for market entry</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateCommentAnalysisTab(data) {
            return `
                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12H7v-2h6v2zm3-4H7V8h9v2z"/>
                            </svg>
                        </div>
                        <div>
                            <div class="section-title">Comment Intelligence Analysis</div>
                            <div class="section-subtitle">${data.comment_analysis?.total_comments_analyzed || 0} comments analyzed for market insights</div>
                        </div>
                    </div>
                    
                    <div class="content-grid three-column" style="margin-bottom: 2rem;">
                        <div class="content-card" style="background: rgba(34, 197, 94, 0.15); border-color: rgba(34, 197, 94, 0.3);">
                            <h4 style="color: #22c55e;">Positive Sentiment</h4>
                            <div style="text-align: center;">
                                <div style="font-size: 2.5rem; font-weight: 900; color: #22c55e;">${data.comment_analysis?.sentiment?.positive || 0}%</div>
                                <div style="color: #86efac;">Positive feedback</div>
                            </div>
                        </div>
                        <div class="content-card" style="background: rgba(239, 68, 68, 0.15); border-color: rgba(239, 68, 68, 0.3);">
                            <h4 style="color: #ef4444;">Negative Sentiment</h4>
                            <div style="text-align: center;">
                                <div style="font-size: 2.5rem; font-weight: 900; color: #ef4444;">${data.comment_analysis?.sentiment?.negative || 0}%</div>
                                <div style="color: #fca5a5;">Critical feedback</div>
                            </div>
                        </div>
                        <div class="content-card" style="background: rgba(156, 163, 175, 0.15); border-color: rgba(156, 163, 175, 0.3);">
                            <h4 style="color: #9ca3af;">Neutral Sentiment</h4>
                            <div style="text-align: center;">
                                <div style="font-size: 2.5rem; font-weight: 900; color: #9ca3af;">${data.comment_analysis?.sentiment?.neutral || 0}%</div>
                                <div style="color: #d1d5db;">Neutral comments</div>
                            </div>
                        </div>
                    </div>

                    <div class="content-grid two-column">
                        <div class="content-card">
                            <h4>Content Requests from Audience</h4>
                            <div style="max-height: 400px; overflow-y: auto; background: rgba(0,0,0,0.3); padding: 1rem; border-radius: 8px; margin-top: 1rem;">
                                ${(data.comment_analysis?.content_requests || ['No content requests found']).map(req => `
                                    <div style="padding: 0.75rem 0; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #cbd5e1;">
                                        <strong style="color: #ffffff;">"</strong>${req}<strong style="color: #ffffff;">"</strong>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        
                        <div class="content-card">
                            <h4>Most Asked Questions</h4>
                            <div style="max-height: 400px; overflow-y: auto; background: rgba(0,0,0,0.3); padding: 1rem; border-radius: 8px; margin-top: 1rem;">
                                ${(data.comment_analysis?.questions || ['No questions found']).map(q => `
                                    <div style="padding: 0.5rem 0; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #e2e8f0;">
                                        <span style="color: #ff1744;">Q:</span> ${q}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>

                    <div class="content-grid two-column" style="margin-top: 2rem;">
                        <div class="content-card" style="background: rgba(34, 197, 94, 0.1); border-color: rgba(34, 197, 94, 0.2);">
                            <h4 style="color: #22c55e;">Positive Feedback Examples</h4>
                            <div style="max-height: 300px; overflow-y: auto; padding: 1rem 0;">
                                ${(data.comment_analysis?.positive_feedback || ['No positive feedback available']).map(feedback => `
                                    <div style="padding: 0.75rem 0; border-bottom: 1px solid rgba(34, 197, 94, 0.2); font-size: 0.9rem; color: #86efac;">
                                        <span style="color: #22c55e;">💬</span> "${feedback}"
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        
                        <div class="content-card" style="background: rgba(239, 68, 68, 0.1); border-color: rgba(239, 68, 68, 0.2);">
                            <h4 style="color: #ef4444;">Critical Feedback Examples</h4>
                            <div style="max-height: 300px; overflow-y: auto; padding: 1rem 0;">
                                ${(data.comment_analysis?.negative_feedback || ['No negative feedback available']).map(feedback => `
                                    <div style="padding: 0.75rem 0; border-bottom: 1px solid rgba(239, 68, 68, 0.2); font-size: 0.9rem; color: #fca5a5;">
                                        <span style="color: #ef4444;">⚠️</span> "${feedback}"
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateVideoPerformanceTab(data) {
            return `
                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M18 3v2h-2V3H8v2H6V3H4v18h2v-2h2v2h8v-2h2v2h2V3h-2z"/>
                            </svg>
                        </div>
                        <div>
                            <div class="section-title">Video Performance Analysis</div>
                            <div class="section-subtitle">Top performing content breakdown and success patterns</div>
                        </div>
                    </div>
                    
                    <div class="content-card">
                        <h4>Top Performing Videos</h4>
                        <div style="overflow-x: auto; margin-top: 1rem;">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="border-bottom: 2px solid rgba(255,255,255,0.1);">
                                        <th style="text-align: left; padding: 1rem; color: #ffffff; font-weight: 600;">Video Title</th>
                                        <th style="text-align: right; padding: 1rem; color: #ffffff; font-weight: 600;">Views</th>
                                        <th style="text-align: right; padding: 1rem; color: #ffffff; font-weight: 600;">Likes</th>
                                        <th style="text-align: right; padding: 1rem; color: #ffffff; font-weight: 600;">Engagement</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${(data.video_performance?.top_performers || []).map(video => `
                                        <tr style="border-bottom: 1px solid rgba(255,255,255,0.05);">
                                            <td style="padding: 1rem; color: #e2e8f0; max-width: 400px;">${video.title}</td>
                                            <td style="padding: 1rem; color: #22c55e; text-align: right; font-weight: 600;">${video.views}</td>
                                            <td style="padding: 1rem; color: #3b82f6; text-align: right; font-weight: 600;">${video.likes}</td>
                                            <td style="padding: 1rem; color: #a855f7; text-align: right; font-weight: 600;">${video.engagement}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="content-grid two-column" style="margin-top: 2rem;">
                        <div class="content-card">
                            <h4>Performance Insights</h4>
                            <div style="padding: 1rem 0;">
                                ${(data.video_performance?.performance_insights || ['No insights available']).map(insight => `
                                    <div style="padding: 0.75rem 0; border-bottom: 1px solid rgba(255,255,255,0.1); color: #cbd5e1;">
                                        <span style="color: #22c55e;">📊</span> ${insight}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        
                        <div class="content-card">
                            <h4>Viral Content Patterns</h4>
                            <div style="padding: 1rem 0;">
                                ${(data.video_performance?.viral_patterns || ['No patterns identified']).map(pattern => `
                                    <div style="padding: 0.75rem 0; border-bottom: 1px solid rgba(255,255,255,0.1); color: #cbd5e1; font-size: 0.9rem;">
                                        <span style="color: #ff1744;">🎯</span> ${pattern}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateMarketOpportunitiesTab(data) {
            return `
                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <div>
                            <div class="section-title">Market Opportunities</div>
                            <div class="section-subtitle">Content gaps and blue ocean opportunities from audience analysis</div>
                        </div>
                    </div>
                    
                    <div class="content-grid three-column">
                        <div class="content-card" style="background: rgba(59, 130, 246, 0.15); border-color: rgba(59, 130, 246, 0.3);">
                            <h4 style="color: #3b82f6;">Tutorial Opportunities</h4>
                            <div style="margin-top: 1rem;">
                                ${(data.content_gaps?.tutorial_gaps || ['No tutorial requests found']).slice(0, 8).map(gap => `
                                    <div style="padding: 0.5rem 0; border-bottom: 1px solid rgba(59, 130, 246, 0.2); font-size: 0.9rem; color: #93c5fd;">
                                        • ${gap}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        
                        <div class="content-card" style="background: rgba(168, 85, 247, 0.15); border-color: rgba(168, 85, 247, 0.3);">
                            <h4 style="color: #a855f7;">Topic Requests</h4>
                            <div style="margin-top: 1rem;">
                                ${(data.content_gaps?.topic_gaps || ['No topic requests found']).slice(0, 8).map(gap => `
                                    <div style="padding: 0.5rem 0; border-bottom: 1px solid rgba(168, 85, 247, 0.2); font-size: 0.9rem; color: #c4b5fd;">
                                        • ${gap}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        
                        <div class="content-card" style="background: rgba(245, 158, 11, 0.15); border-color: rgba(245, 158, 11, 0.3);">
                            <h4 style="color: #f59e0b;">Audience Wishes</h4>
                            <div style="margin-top: 1rem;">
                                ${(data.content_gaps?.audience_requests || ['No specific wishes found']).slice(0, 8).map(wish => `
                                    <div style="padding: 0.5rem 0; border-bottom: 1px solid rgba(245, 158, 11, 0.2); font-size: 0.9rem; color: #fbbf24;">
                                        • ${wish}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>

                    <div class="content-card" style="margin-top: 2rem;">
                        <h4>All Identified Opportunities</h4>
                        <div style="margin-top: 1rem; max-height: 400px; overflow-y: auto;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 1rem;">
                                ${(data.content_gaps?.opportunities || ['No opportunities identified']).map((opp, index) => `
                                    <div style="padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 8px; border-left: 3px solid #ff1744;">
                                        <div style="font-weight: 600; color: #ffffff; margin-bottom: 0.5rem;">Opportunity #${index + 1}</div>
                                        <div style="color: #cbd5e1; font-size: 0.9rem;">${opp}</div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateAIProductionTab(data) {
            return `
                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                            </svg>
                        </div>
                        <div>
                            <div class="section-title">AI Production Feasibility</div>
                            <div class="section-subtitle">Comprehensive automation potential and implementation roadmap</div>
                        </div>
                    </div>
                    
                    <div class="content-grid two-column">
                        <div class="content-card" style="text-align: center;">
                            <h4>Overall AI Feasibility Score</h4>
                            <div style="margin: 2rem 0;">
                                <div style="font-size: 4rem; font-weight: 900; color: #22c55e; line-height: 1;">
                                    ${data.ai_feasibility?.score || '85'}
                                </div>
                                <div style="font-size: 1.5rem; color: #22c55e; margin-bottom: 0.5rem;">/100</div>
                                <div style="color: #94a3b8;">${data.ai_feasibility?.feasibility || 'High automation potential'}</div>
                            </div>
                            <div style="background: rgba(34, 197, 94, 0.1); border-radius: 12px; padding: 1rem;">
                                <div style="color: #22c55e; font-weight: 600;">ROI Rating: ${data.ai_feasibility?.roi_rating || 'High'}</div>
                                <div style="color: #86efac; margin-top: 0.5rem;">Time to Market: ${data.ai_feasibility?.time_to_market || '2-3wks'}</div>
                            </div>
                        </div>
                        
                        <div class="content-card">
                            <h4>Content Type Analysis</h4>
                            <div style="margin-top: 1rem;">
                                <div style="margin-bottom: 1rem; padding: 1rem; background: rgba(59, 130, 246, 0.1); border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <span style="color: #ffffff;">Tutorial Content</span>
                                        <span style="color: #3b82f6; font-weight: 600;">${data.ai_feasibility?.content_analysis?.tutorial_percentage || '0%'}</span>
                                    </div>
                                </div>
                                <div style="margin-bottom: 1rem; padding: 1rem; background: rgba(168, 85, 247, 0.1); border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <span style="color: #ffffff;">Educational Content</span>
                                        <span style="color: #a855f7; font-weight: 600;">${data.ai_feasibility?.content_analysis?.educational_percentage || '0%'}</span>
                                    </div>
                                </div>
                                <div style="margin-bottom: 1rem; padding: 1rem; background: rgba(34, 197, 94, 0.1); border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; align-items: center;">
                                        <span style="color: #ffffff;">AI-Friendly Content</span>
                                        <span style="color: #22c55e; font-weight: 600;">${data.ai_feasibility?.content_analysis?.ai_friendly_content || '0%'}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="content-card" style="margin-top: 2rem;">
                        <h4>Automation Breakdown by Production Stage</h4>
                        <div class="content-grid four-column" style="margin-top: 1rem;">
                            <div style="text-align: center; padding: 1.5rem; background: rgba(34, 197, 94, 0.1); border-radius: 12px;">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">📝</div>
                                <div style="font-weight: 600; color: #ffffff; margin-bottom: 0.5rem;">Script Generation</div>
                                <div style="font-size: 1.2rem; color: #22c55e; font-weight: 600;">${data.ai_feasibility?.automation_breakdown?.script_generation || 'Medium'}</div>
                            </div>
                            <div style="text-align: center; padding: 1.5rem; background: rgba(59, 130, 246, 0.1); border-radius: 12px;">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">🎤</div>
                                <div style="font-weight: 600; color: #ffffff; margin-bottom: 0.5rem;">Voice Synthesis</div>
                                <div style="font-size: 1.2rem; color: #3b82f6; font-weight: 600;">${data.ai_feasibility?.automation_breakdown?.voice_synthesis || 'Medium'}</div>
                            </div>
                            <div style="text-align: center; padding: 1.5rem; background: rgba(245, 158, 11, 0.1); border-radius: 12px;">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">✂️</div>
                                <div style="font-weight: 600; color: #ffffff; margin-bottom: 0.5rem;">Video Editing</div>
                                <div style="font-size: 1.2rem; color: #f59e0b; font-weight: 600;">${data.ai_feasibility?.automation_breakdown?.video_editing || 'Low'}</div>
                            </div>
                            <div style="text-align: center; padding: 1.5rem; background: rgba(168, 85, 247, 0.1); border-radius: 12px;">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">🖼️</div>
                                <div style="font-weight: 600; color: #ffffff; margin-bottom: 0.5rem;">Thumbnails</div>
                                <div style="font-size: 1.2rem; color: #a855f7; font-weight: 600;">${data.ai_feasibility?.automation_breakdown?.thumbnail_creation || 'High'}</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function generateLaunchStrategyTab(data) {
            return `
                <div class="analysis-section">
                    <div class="section-header">
                        <div class="section-icon">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div>
                            <div class="section-title">Launch Strategy Blueprint</div>
                            <div class="section-subtitle">Data-driven go-to-market plan for new channel creation</div>
                        </div>
                    </div>
                    
                    <div class="content-card">
                        <h4>Strategic Recommendations</h4>
                        <div class="content-grid two-column" style="margin-top: 1rem;">
                            ${(data.content_gaps?.launch_strategy || ['No strategy available']).map((point, index) => `
                                <div style="padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 12px; border-left: 4px solid #ff1744;">
                                    <div style="font-weight: 600; color: #ff1744; margin-bottom: 0.5rem;">Strategy #${index + 1}</div>
                                    <div style="color: #cbd5e1;">${point}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="content-grid three-column" style="margin-top: 2rem;">
                        <div class="content-card" style="background: rgba(34, 197, 94, 0.1); border-color: rgba(34, 197, 94, 0.3);">
                            <h4 style="color: #22c55e;">Phase 1: Foundation</h4>
                            <div style="margin-top: 1rem; color: #86efac;">
                                <div style="margin-bottom: 0.5rem;">• Channel setup and branding</div>
                                <div style="margin-bottom: 0.5rem;">• Content pillar definition</div>
                                <div style="margin-bottom: 0.5rem;">• AI tool setup and testing</div>
                                <div style="margin-bottom: 0.5rem;">• First 5 video scripts</div>
                            </div>
                        </div>
                        
                        <div class="content-card" style="background: rgba(59, 130, 246, 0.1); border-color: rgba(59, 130, 246, 0.3);">
                            <h4 style="color: #3b82f6;">Phase 2: Launch</h4>
                            <div style="margin-top: 1rem; color: #93c5fd;">
                                <div style="margin-bottom: 0.5rem;">• Content production workflow</div>
                                <div style="margin-bottom: 0.5rem;">• Upload schedule optimization</div>
                                <div style="margin-bottom: 0.5rem;">• SEO and thumbnail strategy</div>
                                <div style="margin-bottom: 0.5rem;">• Community engagement</div>
                            </div>
                        </div>
                        
                        <div class="content-card" style="background: rgba(168, 85, 247, 0.1); border-color: rgba(168, 85, 247, 0.3);">
                            <h4 style="color: #a855f7;">Phase 3: Scale</h4>
                            <div style="margin-top: 1rem; color: #c4b5fd;">
                                <div style="margin-bottom: 0.5rem;">• Performance monitoring</div>
                                <div style="margin-bottom: 0.5rem;">• Content optimization</div>
                                <div style="margin-bottom: 0.5rem;">• Monetization strategy</div>
                                <div style="margin-bottom: 0.5rem;">• Expansion planning</div>
                            </div>
                        </div>
                    </div>

                    <div class="content-card" style="margin-top: 2rem;">
                        <h4>Success Metrics & KPIs</h4>
                        <div class="content-grid four-column" style="margin-top: 1rem;">
                            <div style="text-align: center; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 8px;">
                                <div style="font-size: 1.8rem; font-weight: 700; color: #22c55e; margin-bottom: 0.5rem;">1K</div>
                                <div style="color: #94a3b8; font-size: 0.9rem;">Subscribers (Month 1)</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 8px;">
                                <div style="font-size: 1.8rem; font-weight: 700; color: #3b82f6; margin-bottom: 0.5rem;">10K</div>
                                <div style="color: #94a3b8; font-size: 0.9rem;">Avg Views (Month 2)</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 8px;">
                                <div style="font-size: 1.8rem; font-weight: 700; color: #a855f7; margin-bottom: 0.5rem;">5%</div>
                                <div style="color: #94a3b8; font-size: 0.9rem;">Engagement Rate</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: rgba(255, 255, 255, 0.05); border-radius: 8px;">
                                <div style="font-size: 1.8rem; font-weight: 700; color: #f59e0b; margin-bottom: 0.5rem;">${data.time_to_market || '2-4wks'}</div>
                                <div style="color: #94a3b8; font-size: 0.9rem;">Time to Market</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
"""

@app.post("/analyze")
async def analyze_channel(request: AnalysisRequest, background_tasks: BackgroundTasks):
    """Start channel analysis"""
    try:
        analysis_id = str(uuid.uuid4())
        
        # Store initial status
        analysis_store[analysis_id] = {
            'status': 'running',
            'data': None,
            'message': 'Analysis in progress...',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        # Start background analysis
        background_tasks.add_task(
            run_background_analysis, 
            analysis_id, 
            request.channel_id,
            request.max_videos,
            request.max_comments
        )
        
        return AnalysisResponse(
            analysis_id=analysis_id,
            status="started",
            message="Analysis started successfully"
        )
        
    except Exception as e:
        logger.error(f"Failed to start analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

async def run_background_analysis(analysis_id: str, channel_id: str, max_videos: int, max_comments: int):
    """Run analysis in background"""
    try:
        crew_ai = get_crewai()
        
        logger.info(f"Starting analysis for {channel_id}")
        result = crew_ai.run_analysis(channel_id, max_videos, max_comments)
        
        if 'error' in result:
            analysis_store[analysis_id] = {
                'status': 'failed',
                'data': None,
                'message': result['error'],
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        else:
            analysis_store[analysis_id] = {
                'status': 'completed',
                'data': result,
                'message': 'Analysis completed successfully',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        logger.info(f"Analysis {analysis_id} completed")
        
    except Exception as e:
        logger.error(f"Analysis {analysis_id} failed: {str(e)}")
        analysis_store[analysis_id] = {
            'status': 'failed',
            'data': None,
            'message': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

@app.post("/analyze-video")
async def analyze_video(request: VideoAnalysisRequest):
    """Analyze a single video with script"""
    try:
        video_ai = get_video_ai()
        
        logger.info(f"Starting video analysis for {request.video_url}")
        result = video_ai.analyze_video(request.video_url, request.script)
        
        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])
        
        logger.info(f"Video analysis completed successfully")
        return result
        
    except Exception as e:
        logger.error(f"Video analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/results/{analysis_id}")
async def get_results(analysis_id: str):
    """Get analysis results"""
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    return analysis_store[analysis_id]

@app.get("/health")
async def health():
    """Health check"""
    return {"status": "healthy", "timestamp": datetime.now(timezone.utc).isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)