<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Market Research Button Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ccc; }
        button { padding: 10px 20px; margin: 10px; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>Market Research Button Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Basic Button Click</h2>
        <input type="text" id="testQuery" placeholder="Enter test query" value="AI tutorials">
        <button id="testButton">Test Button</button>
        <div id="testResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: API Call Test</h2>
        <button id="apiTestButton">Test API Call</button>
        <div id="apiResult" class="result" style="display: none;"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Console Log Test</h2>
        <button id="consoleTestButton">Test Console</button>
        <p>Check browser console for output</p>
    </div>

    <script>
        // Test 1: Basic button functionality
        document.getElementById('testButton').addEventListener('click', function() {
            console.log('Test button clicked!');
            const query = document.getElementById('testQuery').value;
            const result = document.getElementById('testResult');
            result.style.display = 'block';
            result.innerHTML = `Button clicked! Query: "${query}"`;
        });
        
        // Test 2: API call test
        document.getElementById('apiTestButton').addEventListener('click', async function() {
            console.log('API test button clicked!');
            const result = document.getElementById('apiResult');
            result.style.display = 'block';
            result.innerHTML = 'Testing API call...';
            
            try {
                const response = await fetch('/api/market-research', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: 'test query',
                        time_range: 'month',
                        max_results: 5,
                        sort_by: 'relevance'
                    })
                });
                
                console.log('Response status:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    result.innerHTML = `API Success! Query: ${data.query || 'Unknown'}`;
                } else {
                    result.innerHTML = `API Error: ${response.status} - ${response.statusText}`;
                }
            } catch (error) {
                console.error('API Error:', error);
                result.innerHTML = `API Error: ${error.message}`;
            }
        });
        
        // Test 3: Console test
        document.getElementById('consoleTestButton').addEventListener('click', function() {
            console.log('Console test button clicked!');
            console.log('Current URL:', window.location.href);
            console.log('Available elements:', {
                testQuery: document.getElementById('testQuery'),
                testButton: document.getElementById('testButton'),
                apiTestButton: document.getElementById('apiTestButton')
            });
        });
        
        // Log when page loads
        console.log('Test page loaded successfully');
    </script>
</body>
</html>
