#!/usr/bin/env python3
"""
Debug script to test AI insights and identify parsing issues
"""

import requests
import json
import time

def test_ai_insights_debug():
    """Test AI insights generation and identify issues"""
    print("🔍 Debugging AI Insights Generation")
    print("=" * 50)
    
    base_url = "http://localhost:8003"
    
    # Simple test query
    test_data = {
        "query": "web development",
        "time_range": "week", 
        "max_results": 3,
        "sort_by": "relevance"
    }
    
    print(f"📝 Testing query: {test_data['query']}")
    print("-" * 30)
    
    try:
        response = requests.post(
            f"{base_url}/api/market-research",
            json=test_data,
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ API Response: SUCCESS")
            
            # Check AI insights structure
            ai_insights = data.get('ai_insights', {})
            if ai_insights:
                print(f"🧠 AI Insights Found: {len(ai_insights)} types")
                
                for insight_type, content in ai_insights.items():
                    print(f"\n📊 {insight_type}:")
                    if isinstance(content, str):
                        print(f"   Type: String ({len(content)} chars)")
                        print(f"   Preview: {content[:100]}...")
                    elif isinstance(content, list):
                        print(f"   Type: List ({len(content)} items)")
                        if content:
                            print(f"   First item: {content[0]}")
                    elif isinstance(content, dict):
                        print(f"   Type: Dict ({len(content)} keys)")
                        print(f"   Keys: {list(content.keys())}")
                    else:
                        print(f"   Type: {type(content)}")
                        print(f"   Value: {content}")
                
                # Check for None/null values that might cause UI errors
                print(f"\n🔍 Checking for problematic values:")
                for insight_type, content in ai_insights.items():
                    if content is None:
                        print(f"   ❌ {insight_type}: NULL")
                    elif isinstance(content, list):
                        for i, item in enumerate(content):
                            if item is None:
                                print(f"   ❌ {insight_type}[{i}]: NULL")
                            elif isinstance(item, dict):
                                for key, value in item.items():
                                    if value is None:
                                        print(f"   ❌ {insight_type}[{i}].{key}: NULL")
                
            else:
                print("❌ No AI insights found")
            
            # Check competition analysis structure
            comp_analysis = data.get('competition_analysis', {})
            if comp_analysis:
                top_performers = comp_analysis.get('top_performers', [])
                print(f"\n🏆 Top Performers: {len(top_performers)} found")
                
                if top_performers:
                    first_performer = top_performers[0]
                    print(f"   Sample performer keys: {list(first_performer.keys())}")
                    
                    # Check for None values in top performers
                    for key, value in first_performer.items():
                        if value is None:
                            print(f"   ❌ top_performers[0].{key}: NULL")
                        elif isinstance(value, (int, float)) and str(value) == 'nan':
                            print(f"   ❌ top_performers[0].{key}: NaN")
            
            # Check market overview
            market_overview = data.get('market_overview', {})
            if market_overview:
                print(f"\n📈 Market Overview: {len(market_overview)} fields")
                for key, value in market_overview.items():
                    if value is None:
                        print(f"   ❌ market_overview.{key}: NULL")
                    elif isinstance(value, (int, float)) and str(value) == 'nan':
                        print(f"   ❌ market_overview.{key}: NaN")
            
        else:
            print(f"❌ API Request Failed: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Test Failed: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 AI Insights Debug Complete")

if __name__ == "__main__":
    test_ai_insights_debug()
