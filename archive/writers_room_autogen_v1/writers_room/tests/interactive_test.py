#!/usr/bin/env python3
"""
Interactive testing interface for Writers' Room system
"""

from writers_room import WritersRoom, WritersRoomInput
from writers_room_enhanced import EnhancedWritersRoom
import json

def interactive_test():
    """Interactive command-line testing"""
    
    print("🎬 WRITERS' ROOM INTERACTIVE TESTER")
    print("="*50)
    
    # Get user input
    topic = input("\n📝 Enter your video topic: ")
    
    print("\n💎 Enter 3-5 golden nuggets (press Enter after each, empty line to finish):")
    golden_nuggets = []
    while True:
        nugget = input(f"   Golden nugget {len(golden_nuggets) + 1}: ")
        if not nugget.strip():
            break
        golden_nuggets.append(nugget)
    
    # Channel persona selection
    print("\n🎭 Select channel persona:")
    personas = {
        "1": {
            "channel": "Frontier",
            "voice": "Awestruck Strategist", 
            "focus": "Geography and logistics determining history"
        },
        "2": {
            "channel": "Tech Investigative",
            "voice": "Analytical Detective",
            "focus": "How technology promises can deceive experts"
        },
        "3": {
            "channel": "Human Stories",
            "voice": "Empathetic Narrator",
            "focus": "Personal stories behind big moments"
        },
        "4": {
            "channel": "Custom",
            "voice": "Custom",
            "focus": "Custom"
        }
    }
    
    for key, persona in personas.items():
        print(f"   {key}. {persona['channel']} - {persona['voice']}")
    
    choice = input("\nChoice (1-4): ")
    
    if choice == "4":
        channel = input("Channel name: ")
        voice = input("Voice style: ")
        focus = input("Content focus: ")
        selected_persona = {"channel": channel, "voice": voice, "focus": focus}
    else:
        selected_persona = personas.get(choice, personas["1"])
    
    # Quality level selection
    print("\n🎯 Select system:")
    print("   1. Basic Writers' Room")
    print("   2. Enhanced with Quality Validation")
    
    system_choice = input("Choice (1-2): ")
    
    # Create input
    test_input = WritersRoomInput(
        topic=topic,
        golden_nuggets=golden_nuggets,
        persona=selected_persona
    )
    
    print(f"\n🎬 GENERATING SCRIPT FOR: {topic}")
    print("="*60)
    
    # Run selected system
    if system_choice == "2":
        enhanced_room = EnhancedWritersRoom()
        result = enhanced_room.run_enhanced_writers_room({
            "topic": topic,
            "golden_nuggets": golden_nuggets,
            "persona": selected_persona
        })
        
        print(f"\n📊 QUALITY REPORT:")
        print(f"   Score: {result['final_score']}/100")
        print(f"   Revisions: {result['revisions_made']}")
        print(f"   Time: {result['execution_time']}")
        
        script = result['script']
        filename = f"interactive_enhanced_{topic.replace(' ', '_')[:20]}.json"
        
        with open(filename, 'w') as f:
            json.dump(result, f, indent=2)
            
    else:
        writers_room = WritersRoom()
        result = writers_room.run_writers_room(test_input)
        
        script = result.final_script
        filename = f"interactive_basic_{topic.replace(' ', '_')[:20]}.json"
        
        output_dict = {
            "final_script": result.final_script,
            "structure": result.structure,
            "hook_variations": result.hook_variations,
            "vocal_annotations": result.vocal_annotations,
            "estimated_duration": result.estimated_duration
        }
        
        with open(filename, 'w') as f:
            json.dump(output_dict, f, indent=2)
    
    # Show preview
    print(f"\n🎣 HOOK PREVIEW:")
    if "PRIMARY HOOK" in script:
        hook_start = script.find("PRIMARY HOOK")
        hook_end = script.find("ALTERNATIVE HOOK")
        if hook_end == -1:
            hook_end = hook_start + 200
        primary_hook = script[hook_start:hook_end].strip()
        print(primary_hook[:300] + "..." if len(primary_hook) > 300 else primary_hook)
    
    print(f"\n💾 Full script saved to: {filename}")
    
    # Ask for another test
    another = input("\n🔄 Test another topic? (y/n): ")
    if another.lower() == 'y':
        interactive_test()

if __name__ == "__main__":
    interactive_test()