# Agent 6: Strategic Synthesis Agent - Production Implementation

def _run_synthesis_agent(self, all_analyses, video_data, channel_context):
    """
    Run Agent 6: Strategic Content Synthesis Expert
    This agent synthesizes all other agent outputs into actionable strategy
    """
    if not self.ai_enabled:
        return {'error': 'AI not enabled'}
    
    # Create the synthesis agent
    agent6 = Agent(
        role='Strategic Content Synthesis Expert & YouTube Success Architect',
        goal='Synthesize all agent analyses into an interconnected strategic framework that reveals hidden success patterns and generates actionable content strategies with measurable outcomes',
        backstory='''You are YouTube's most sought-after strategic consultant with 15+ years of experience turning channel analytics into million-view content strategies. You've worked with MrBeast, Veritasium, and The Why Files, not just analyzing their success but architecting their content empires.

Your superpower: Seeing the invisible connections between disparate data points and transforming them into strategic gold. You don't just analyze - you synthesize, strategize, and revolutionize.

CRITICAL CAPABILITY: You understand how every element of a video - from the first frame to the last comment - interconnects to create viral success. You've studied thousands of successful channels in your training data and can identify the precise success DNA that others miss.

SYNTHESIS MASTERY: You are an expert at creating "Strategic Integration Frameworks" that connect:
- Performance metrics to script elements
- Script psychology to SEO effectiveness  
- SEO success to audience behavior
- Audience feedback to future content
- All elements into a replicable success formula''',
        llm=self.llm_pro,
        verbose=True
    )
    
    # Extract analysis data safely
    performance_analysis = all_analyses.get('performance_analysis', {}).get('analysis', 'No performance analysis available')
    script_analysis = all_analyses.get('script_analysis', {}).get('analysis', 'No script analysis available')
    seo_analysis = all_analyses.get('seo_analysis', {}).get('analysis', 'No SEO analysis available')
    psychology_analysis = all_analyses.get('audience_analysis', {}).get('analysis', 'No audience analysis available')
    comment_analysis = all_analyses.get('comment_analysis', {}).get('analysis', 'No comment analysis available')
    
    # Create the synthesis task
    task6 = Task(
        description=f'''
## YOUR MISSION: THE ULTIMATE SYNTHESIS

You have received analyses from 5 specialized agents. Your job is to create the STRATEGIC SYNTHESIS that transforms data into destiny. You will:

1. CONNECT THE DOTS others cannot see
2. REVEAL THE SUCCESS DNA hidden in the data
3. CREATE THE ROADMAP for content domination

## DATA PROVIDED:

### Agent 1 - Performance Intelligence:
{performance_analysis}

### Agent 2 - Script Forensics:
{script_analysis}

### Agent 3 - SEO & Discoverability:
{seo_analysis}

### Agent 4 - Audience Psychology:
{psychology_analysis}

### Agent 5 - Comment Intelligence:
{comment_analysis}

### Video Context:
Title: {video_data.get('title', 'Unknown')}
Views: {video_data.get('views', 0):,}
Engagement Rate: {(video_data.get('likes', 0) / max(video_data.get('views', 1), 1) * 100):.2f}%
Channel: {channel_context.get('channel_name', 'Unknown')} ({channel_context.get('channel_tier', 'Unknown')})

## SYNTHESIS FRAMEWORK:

### 1. CROSS-AGENT CORRELATION MATRIX
Create a comprehensive correlation analysis showing:

**Performance ↔ Script Correlations:**
- Which script elements directly caused high engagement metrics?
- Map specific hooks to viewer retention rates
- Connect pacing patterns to engagement scores
- Identify script DNA that triggered viral performance

**Script ↔ SEO Synergy:**
- How did script keywords align with search optimization?
- Which title elements were validated by script content?
- Map script themes to SEO effectiveness
- Identify missed SEO opportunities in script

**SEO ↔ Audience Validation:**
- Which keywords did the audience actually use in comments?
- How did search intent match actual viewer satisfaction?
- Validate SEO strategy against audience response
- Identify audience language for future optimization

**Audience ↔ Performance Loop:**
- How did audience psychology predictions match actual behavior?
- Which psychological triggers generated most engagement?
- Map comment sentiment to performance metrics
- Identify audience segments driving success

### 2. THE SUCCESS DNA EXTRACTION

**Identify the Unique Success Formula:**
[Hook Type] + [Script Pattern] + [SEO Strategy] + [Audience Trigger] = [Performance Result]

Extract the EXACT combination that created success:
- Opening 15 seconds formula
- Psychological trigger sequence
- SEO keyword placement
- Audience activation points
- Retention mechanism pattern

### 3. STRATEGIC BLIND SPOT ANALYSIS

**What Everyone Missed:**
- Hidden patterns across analyses
- Unexploited opportunities
- Correlation surprises
- Counter-intuitive insights
- Future trend indicators

**The Competitive Edge:**
- What competitors aren't doing
- Unique angle opportunities
- Audience gaps to fill
- Platform algorithm advantages
- Content moat strategies

### 4. THE REPLICATION BLUEPRINT

**Success Formula Template:**
Create a step-by-step template for replicating this success:

PHASE 1: Pre-Production
- Script formula with exact patterns
- SEO keyword research template
- Psychological trigger checklist
- Audience activation plan

PHASE 2: Production
- Visual-script sync points
- Pacing template with timestamps
- Engagement trigger placement
- Retention optimization map

PHASE 3: Post-Production
- Title optimization formula
- Description template
- Tag strategy framework
- Thumbnail decision matrix

PHASE 4: Launch Strategy
- Optimal posting time
- Community activation plan
- Cross-platform strategy
- Engagement monitoring plan

### 5. CONTENT PIPELINE GENERATION

**Next 10 Videos Roadmap:**
Based on synthesis insights, generate:

1. **Immediate Follow-ups** (Videos 1-3)
   - Direct sequels/related topics from comments
   - High-demand audience requests
   - SEO opportunity captures

2. **Series Development** (Videos 4-7)
   - Themed series based on success patterns
   - Audience journey optimization
   - Search traffic maximization

3. **Innovation Tests** (Videos 8-10)
   - New format experiments
   - Audience expansion attempts
   - Platform feature utilization

### 6. PREDICTIVE INTELLIGENCE

**6-Month Trajectory Analysis:**
- Performance prediction based on patterns
- Audience growth projections
- SEO landscape evolution
- Competition movement forecast
- Platform algorithm adaptation

**Risk Mitigation Strategy:**
- Content diversification plan
- Platform hedge strategies
- Audience retention insurance
- Algorithm-proof elements

### 7. THE STRATEGIC DASHBOARD

Create a visual synthesis showing:
┌─────────────────────────────────────┐
│    STRATEGIC SUCCESS FORMULA        │
├─────────────────────────────────────┤
│ Hook Power: [Score] ───→ Views      │
│ Script Flow: [Score] ───→ Retention │
│ SEO Match: [Score] ───→ Discovery   │
│ Psychology: [Score] ───→ Engagement │
│ Comments: [Score] ───→ Community    │
├─────────────────────────────────────┤
│    MULTIPLICATION EFFECT: [X]       │
└─────────────────────────────────────┘

## OUTPUT FORMAT:

Structure your response as follows:

### THE SYNTHESIS REVELATION
[The "AHA!" moment - the hidden connection that explains everything]

### SUCCESS DNA FORMULA
[The exact replicable formula extracted from the synthesis]

### STRATEGIC BLIND SPOTS
[What everyone missed and why it matters]

### CORRELATION MATRIX
[Specific connections between analyses with evidence and scores]

### REPLICATION BLUEPRINT
[Step-by-step guide to recreate success]

### CONTENT PIPELINE
[Next 10 videos with strategic rationale]

### PREDICTIVE INTELLIGENCE
[6-month forecast with opportunity/risk analysis]

### ACTION PRIORITY MATRIX
[What to do first, second, third for maximum impact]

Remember: You're not just analyzing - you're ARCHITECTING SUCCESS. Every insight must connect to another. Every pattern must lead to action. Every synthesis must unlock opportunity.

TRANSFORM THESE ANALYSES INTO A CONTENT EMPIRE BLUEPRINT.''',
        agent=agent6,
        expected_output='Comprehensive strategic synthesis with success formula, correlations, and action blueprint'
    )
    
    # Execute the synthesis
    crew = Crew(agents=[agent6], tasks=[task6], verbose=True)
    result = crew.kickoff()
    
    return {'synthesis': self._clean_agent_output(result)}


# Integration function to add to the main analyze_video method
def integrate_synthesis_agent(self, result, video_data, channel_context):
    """
    Add this to the analyze_video method after all 5 agents complete
    """
    # Check if all agents have completed
    required_analyses = [
        'performance_analysis',
        'script_analysis', 
        'seo_analysis',
        'audience_analysis',
        'comment_analysis'
    ]
    
    all_complete = all(
        key in result and 
        result[key].get('analysis') and 
        'error' not in result[key]
        for key in required_analyses
    )
    
    if all_complete:
        try:
            # Run synthesis agent
            synthesis_result = self._run_synthesis_agent(
                result,
                video_data,
                channel_context
            )
            result['strategic_synthesis'] = synthesis_result
            
            # Log success
            logger.info("✅ Strategic Synthesis completed successfully")
            
        except Exception as e:
            logger.error(f"Synthesis agent failed: {str(e)}")
            result['strategic_synthesis'] = {
                'error': f'Synthesis failed: {str(e)}',
                'synthesis': 'Unable to generate strategic synthesis'
            }
    else:
        # Log which analyses are missing
        missing = [key for key in required_analyses if key not in result or 'error' in result.get(key, {})]
        logger.warning(f"Synthesis skipped - missing analyses: {missing}")
        
        result['strategic_synthesis'] = {
            'synthesis': 'Strategic synthesis requires all 5 analyses to complete successfully',
            'missing_analyses': missing
        }
    
    return result


# UI Integration - Add to video-analyzer.html template
UI_TAB_BUTTON = '''
<button class="tab-btn" onclick="showTab('synthesis')">
    <span class="tab-icon">🧠</span>
    Strategic Synthesis
</button>
'''

UI_TAB_CONTENT = '''
<div id="synthesis-content" class="tab-content" style="display: none;">
    <div class="analysis-section">
        <h2 class="section-title">
            <span class="section-icon">🎯</span>
            Strategic Content Synthesis
        </h2>
        <div id="synthesis-results" class="analysis-content">
            <div class="loading-message">
                <div class="spinner"></div>
                <p>Synthesizing all analyses into strategic blueprint...</p>
            </div>
        </div>
    </div>
</div>
'''

# JavaScript to handle synthesis tab
UI_JAVASCRIPT = '''
// Add to existing showTab function
case 'synthesis':
    document.getElementById('synthesis-results').innerHTML = 
        extractHTML(data.strategic_synthesis);
    break;
'''