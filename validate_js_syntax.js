#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

function validateJavaScriptSyntax() {
    console.log('🔍 JavaScript Syntax Validation');
    console.log('=' * 50);
    
    try {
        // Read the HTML file
        const htmlContent = fs.readFileSync('templates/market-research-hub.html', 'utf8');
        
        // Extract JavaScript content between <script> tags
        const scriptMatches = htmlContent.match(/<script[^>]*>([\s\S]*?)<\/script>/gi);
        
        if (!scriptMatches) {
            console.log('❌ No JavaScript found in the file');
            return false;
        }
        
        console.log(`📄 Found ${scriptMatches.length} script block(s)`);
        
        let allValid = true;
        
        scriptMatches.forEach((scriptBlock, index) => {
            // Extract just the JavaScript code (remove <script> tags)
            const jsCode = scriptBlock.replace(/<script[^>]*>|<\/script>/gi, '').trim();
            
            if (jsCode.length === 0) {
                console.log(`⚠️  Script block ${index + 1}: Empty`);
                return;
            }
            
            try {
                // Try to parse the JavaScript code
                new Function(jsCode);
                console.log(`✅ Script block ${index + 1}: Valid syntax (${jsCode.length} characters)`);
                
                // Check for specific patterns that were problematic
                const hasTryCatch = jsCode.includes('try') && jsCode.includes('catch');
                const hasPopulateContentOpportunities = jsCode.includes('populateContentOpportunities');
                const hasFormatNumber = jsCode.includes('formatNumber');
                
                if (hasPopulateContentOpportunities) {
                    console.log('   📋 Contains populateContentOpportunities function');
                    
                    if (hasTryCatch) {
                        console.log('   🛡️  Has try-catch error handling');
                        
                        // Check for proper try-catch structure
                        const tryCount = (jsCode.match(/try\s*{/g) || []).length;
                        const catchCount = (jsCode.match(/}\s*catch\s*\(/g) || []).length;
                        
                        if (tryCount === catchCount) {
                            console.log(`   ✅ Try-catch blocks balanced (${tryCount} try, ${catchCount} catch)`);
                        } else {
                            console.log(`   ❌ Try-catch blocks unbalanced (${tryCount} try, ${catchCount} catch)`);
                            allValid = false;
                        }
                    }
                }
                
                if (hasFormatNumber) {
                    console.log('   🔢 Contains formatNumber function');
                }
                
            } catch (syntaxError) {
                console.log(`❌ Script block ${index + 1}: Syntax Error`);
                console.log(`   Error: ${syntaxError.message}`);
                
                // Try to identify the problematic line
                const lines = jsCode.split('\n');
                const errorLine = syntaxError.message.match(/line (\d+)/);
                if (errorLine) {
                    const lineNum = parseInt(errorLine[1]);
                    if (lineNum <= lines.length) {
                        console.log(`   Problematic line ${lineNum}: ${lines[lineNum - 1].trim()}`);
                    }
                }
                
                allValid = false;
            }
        });
        
        if (allValid) {
            console.log('\n🎉 All JavaScript syntax is valid!');
            console.log('✅ The market research page should load without syntax errors');
        } else {
            console.log('\n❌ JavaScript syntax errors detected');
            console.log('🔧 Please fix the syntax errors before testing');
        }
        
        return allValid;
        
    } catch (error) {
        console.log(`❌ Failed to validate syntax: ${error.message}`);
        return false;
    }
}

// Run validation
validateJavaScriptSyntax();
