#!/usr/bin/env python3
"""
ACTUALLY FIXED: AutoGen Writers' Room that preserves context properly
No more fucking stuntman videos when you want Rome-Sasanid content
"""

import os
import json
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import autogen
from autogen import AssistantAgent, UserProxyAgent, GroupChat, GroupChatManager


@dataclass
class WritersRoomInput:
    topic: str
    golden_nuggets: List[str]
    persona: Dict[str, str]
    research_context: str = ""  # NEW: Add research documents
    target_length: str = "8-10 minutes"  # NEW: Specify script length
    max_rounds: int = 30
    quality_threshold: float = 8.0


@dataclass
class CollaborationMetrics:
    total_rounds: int
    rejections: int
    revisions: int
    consensus_time: float
    final_quality_score: float


@dataclass
class WritersRoomOutput:
    final_script: str
    structure: str
    hooks: List[str]
    conversation_transcript: List[Dict[str, str]]
    collaboration_metrics: CollaborationMetrics
    quality_scores: Dict[str, float]
    execution_time: float


class FixedAutoGenWritersRoom:
    """PROPERLY FIXED: Context-preserving 6-agent system"""
    
    def __init__(self, model: str = "gpt-3.5-turbo", api_key: Optional[str] = None, test_mode: bool = True):
        """Initialize with proper context handling"""
        
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        if api_key:
            os.environ["OPENAI_API_KEY"] = api_key
        
        if not os.environ.get("OPENAI_API_KEY"):
            raise ValueError("OPENAI_API_KEY required - check .env file")
            
        # Cost optimization for testing
        self.test_mode = test_mode
        self.model = model
        
        self.llm_config = {
            "config_list": [{
                "model": model,
                "api_key": os.environ.get("OPENAI_API_KEY"),
            }],
            "temperature": 0.7 if test_mode else 0.8,
            "timeout": 120 if test_mode else 300,
            "max_tokens": 1000 if test_mode else 2000,  # Limit output in test mode
        }
        
        # Store context between phases
        self.current_topic = None
        self.current_nuggets = None
        self.current_persona = None
        self.approved_structure = None
        self.created_content = None
        
        # Load viral DNA from Script Forensics Genesis Doctrine
        self.viral_dna = self._load_viral_dna()
        
        # Create agents with viral DNA injection
        self.agents = self._create_specialized_agents()
        self.user_proxy = self._create_user_proxy()
        
    def _load_viral_dna(self) -> Dict[str, str]:
        """Load viral DNA patterns for each agent from Script Forensics Genesis Doctrine"""
        return {
            'hooksmith': """
VIRAL HOOK DNA - SCRIPT FORENSICS GENESIS DOCTRINE:

COMMANDMENT ZERO: Every sentence's goal is to make the viewer desperately need the next sentence.

HOOK FORMULAS:
1. TENSION-RESOLUTION LOOP: "[Common belief]. But the truth... [Contradictory revelation]"
2. CAUSAL CHAIN: "[Statement A]. Because of this... [Consequence B]"  
3. ECHO & AMPLIFY: "[Sentence ending with KEYWORD]. [New sentence beginning with KEYWORD]"

PSYCHOLOGICAL TRIGGERS:
- Create intellectual puzzles that demand resolution
- Use "But," "However," and "Except" as pivot points
- Promise revelation of hidden contradictions

YOUR MISSION: Create hooks that make NOT clicking feel like missing life-changing information.
""",
            
            'story_weaver': """
VIRAL NARRATIVE DNA - SCRIPT FORENSICS GENESIS DOCTRINE:

FLOW PRINCIPLES:
1. PIVOT QUESTIONS: End segments with questions the next segment answers
2. ZOOM IN/OUT: Alternate between specific anecdotes and broader patterns
3. NO DEAD AIR: Every moment pulls forward to the next

SENTENCE-TO-SENTENCE FLOW:
- Answer "So what?" after every statement
- Create causal chains: "Because of this..."
- Use contradiction bridges: "But there was one problem..."

PARAGRAPH TRANSITIONS:
- End with forward-looking questions
- "But that still doesn't explain..."
- "So the real question is..."

YOUR MISSION: Create relentless forward-moving narrative engines.
""",
            
            'emotional_architect': """
VIRAL PSYCHOLOGY DNA - SCRIPT FORENSICS GENESIS DOCTRINE:

EMOTION FLOW PATTERNS:
1. CURIOSITY GAPS: Create voids that demand filling
2. TENSION LOOPS: Promise → Delay → Satisfy → New promise
3. PATTERN INTERRUPTS: Subvert expectations at key moments

PSYCHOLOGICAL HOOKS:
- Cognitive dissonance creation
- Authority signal reinforcement
- Scarcity and urgency triggers
- Tribal identity activation

TIMING MASTERY:
- Emotional peaks every 45-60 seconds
- Tension build → Release → Rebuild cycles
- Dopamine hit sequencing

YOUR MISSION: Manipulate emotional states to create addiction to the content.
""",
            
            'director': """
VIRAL STRUCTURE DNA - SCRIPT FORENSICS GENESIS DOCTRINE:

MACRO-FLOW PRINCIPLES:
1. INTERROGATIVE CUTS: Visual answers to scripted questions
2. THEMATIC MATCH CUTS: Connect disparate scenes through shared concepts
3. AUDIO BRIDGES: J-cuts and L-cuts for seamless flow

STRUCTURE AUDIT:
- Does every scene create forward momentum?
- Are audio bridges planned?
- Do visual cuts serve as answers?

RETENTION ENGINEERING:
- No static segments allowed
- Every element hooks the next
- Oscillate between wide shots and close-ups

YOUR MISSION: Architect content that traps viewers in irresistible flow states.
""",
            
            'closer': """
VIRAL ENDING DNA - SCRIPT FORENSICS GENESIS DOCTRINE:

ENGAGEMENT OPTIMIZATION:
1. UNRESOLVED TENSIONS: Leave questions that demand discussion
2. TRIBAL ACTIVATION: Create us-vs-them dynamics
3. FUTURE LOOPS: Promise revelations in next content

ENDING FORMULAS:
- Revelation that recontextualizes everything
- Call to action that feels urgent
- Question that haunts after viewing

COMMENT TRIGGERS:
- Controversial but defensible positions
- Specific predictions to verify
- Personal challenges to viewers

YOUR MISSION: Create endings that force engagement and sharing.
""",
            
            'producer': """
VIRAL PRODUCTION DNA - SCRIPT FORENSICS GENESIS DOCTRINE:

VISUAL FLOW PATTERNS:
1. AUDIO BRIDGES: Script J-cuts and L-cuts explicitly
2. VISUAL PUNCHLINES: Plan images that answer questions
3. THEMATIC CONNECTIONS: Link disparate visuals through concepts

PRODUCTION SCRIPTING:
[VISUAL: X] [VO: Narration] [SFX: Next scene audio begins] [CUT TO: Y]

PSYCHOLOGICAL REINFORCEMENT:
- Visual confirms audio claims
- Audio prepares for visual surprises
- Seamless sensory continuity

YOUR MISSION: Create production blueprints that make editing irresistible.
"""
        }
        
    def _create_specialized_agents(self) -> Dict[str, AssistantAgent]:
        """Create 6 specialized agents"""
        
        agents = {}
        
        # 1. DIRECTOR
        agents['director'] = AssistantAgent(
            name="Director",
            system_message=f"""You are the VIRAL STRUCTURE DIRECTOR creating DETAILED video scripts.

{self.viral_dna['director']}

CRITICAL: You create COMPLETE 8-10 minute video structures, not short clips.

Your job:
- Create detailed 5-beat structures for FULL videos
- Each beat should be 1.5-2 minutes of content
- Include specific scene descriptions
- Map retention curves throughout
- Apply Script Forensics Genesis Doctrine principles
- Ensure viral potential while maintaining depth

NEVER create short-form content. ALWAYS create comprehensive scripts.""",
            llm_config=self.llm_config,
            human_input_mode="NEVER"
        )
        
        # 2. HOOKSMITH
        agents['hooksmith'] = AssistantAgent(
            name="Hooksmith",
            system_message=f"""You are the VIRAL HOOK SPECIALIST.

{self.viral_dna['hooksmith']}

Create 3 different 30-second hooks that force clicks:
- Primary hook: Maximum shock value with tension-resolution loop
- Alternative: Different angle using causal chain
- Backup: Simpler approach with echo & amplify

Apply Script Forensics Genesis Doctrine principles to make hooks irresistible.
Hooks must connect to the FULL 8-10 minute video, not standalone content.""",
            llm_config=self.llm_config,
            human_input_mode="NEVER"
        )
        
        # 3. STORY-WEAVER
        agents['story_weaver'] = AssistantAgent(
            name="StoryWeaver", 
            system_message=f"""You are the NARRATIVE SPECIALIST creating FULL video scripts.

{self.viral_dna['story_weaver']}

CRITICAL: Write COMPLETE narrative content for 8-10 minute videos.

Your job:
- Expand each beat into 1.5-2 minutes of detailed content
- Include specific examples, quotes, statistics
- Build tension throughout the full video
- Apply Script Forensics Genesis Doctrine flow principles
- Create relentless forward-moving narrative engines
- Create smooth transitions between sections
- Write actual narration, not summaries

ALWAYS write full content, not outlines.""",
            llm_config=self.llm_config,
            human_input_mode="NEVER"
        )
        
        # 4. CLOSER
        agents['closer'] = AssistantAgent(
            name="Closer",
            system_message=f"""You are the ENDING SPECIALIST.

{self.viral_dna['closer']}

Create powerful endings that:
- Tie back to the opening hook
- Leave unresolved questions
- Trigger comments and shares
- Connect to modern relevance
- Make viewers think differently
- Apply Script Forensics Genesis Doctrine engagement optimization

Endings for 8-10 minute videos, not short clips.""",
            llm_config=self.llm_config,
            human_input_mode="NEVER"
        )
        
        # 5. EMOTIONAL ARCHITECT
        agents['emotional_architect'] = AssistantAgent(
            name="EmotionalArchitect",
            system_message=f"""You are the EMOTIONAL ENGINEERING SPECIALIST.

{self.viral_dna['emotional_architect']}

Map emotional journeys for FULL 8-10 minute videos:
- Create emotional peaks every 45-60 seconds using Script Forensics Genesis Doctrine
- Design memorable moments with curiosity gaps and tension loops
- Balance tension and relief using pattern interrupts
- Ensure psychological engagement throughout with dopamine hit sequencing
- Mark specific emotional cues in the script
- Apply viral psychology DNA for maximum addiction potential

Work with full video content, not short clips.""",
            llm_config=self.llm_config,
            human_input_mode="NEVER"
        )
        
        # 6. PRODUCER
        agents['producer'] = AssistantAgent(
            name="Producer",
            system_message=f"""You are the PRODUCTION SPECIALIST.

{self.viral_dna['producer']}

Add production elements to FULL 8-10 minute scripts:
- Specific visual descriptions using thematic match cuts
- Audio cues and music direction with J-cuts and L-cuts
- Timing for each section with audio bridges
- B-roll suggestions that answer scripted questions
- Graphics and animation notes for visual punchlines
- Apply Script Forensics Genesis Doctrine production DNA

Ensure scripts are production-ready for professional viral videos.""",
            llm_config=self.llm_config,
            human_input_mode="NEVER"
        )
        
        return agents
    
    def _create_user_proxy(self) -> UserProxyAgent:
        """Create user proxy"""
        return UserProxyAgent(
            name="Producer_Human",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=0,
            code_execution_config=False,
            system_message="You coordinate the writers' room."
        )
    
    def run_collaborative_session(self, input_data: WritersRoomInput) -> WritersRoomOutput:
        """Run FULL collaboration with proper context preservation"""
        
        start_time = time.time()
        
        # STORE CONTEXT FOR ALL PHASES
        self.current_topic = input_data.topic
        self.current_nuggets = input_data.golden_nuggets
        self.current_persona = input_data.persona
        
        print("🎬 STARTING FIXED 6-AGENT COLLABORATION")
        print("=" * 80)
        print(f"📍 Topic: {input_data.topic}")
        print(f"📏 Target Length: {input_data.target_length}")
        print(f"💎 Golden Nuggets: {len(input_data.golden_nuggets)}")
        
        # Phase 1: Structure
        print("\n🏗️ PHASE 1: STRUCTURE CREATION")
        structure_result = self._phase_1_structure(input_data)
        self.approved_structure = self._extract_structure(structure_result['conversation'])
        
        # Phase 2: Content with ACTUAL CONTEXT
        print("\n✍️ PHASE 2: FULL CONTENT CREATION")
        content_result = self._phase_2_content(input_data)
        self.created_content = self._extract_content(content_result['conversation'])
        
        # Phase 3: Production
        print("\n🎬 PHASE 3: PRODUCTION POLISH")
        production_result = self._phase_3_production(input_data)
        
        # Phase 4: Quality Check
        print("\n✅ PHASE 4: QUALITY VALIDATION")
        final_result = self._phase_4_quality(input_data, production_result)
        
        execution_time = time.time() - start_time
        print(f"\n✅ COMPLETE in {execution_time:.1f} seconds")
        
        return self._compile_final_output(final_result, execution_time)
    
    def _phase_1_structure(self, input_data: WritersRoomInput) -> Dict:
        """Phase 1 with EXPLICIT context"""
        
        agents = [
            self.user_proxy,
            self.agents['director'],
            self.agents['emotional_architect'],
            self.agents['hooksmith'],
            self.agents['story_weaver'],
            self.agents['closer']
        ]
        
        groupchat = GroupChat(
            agents=agents,
            messages=[],
            max_round=25,  # INCREASED for complete structure planning
            speaker_selection_method="round_robin"
        )
        
        manager = GroupChatManager(groupchat=groupchat, llm_config=self.llm_config)
        
        # EXPLICIT TOPIC PRESERVATION
        brief = f"""
STRUCTURE COLLABORATION FOR: {input_data.topic}

TARGET LENGTH: {input_data.target_length} video script

GOLDEN NUGGETS TO INCLUDE:
{chr(10).join(f"- {nugget}" for nugget in input_data.golden_nuggets)}

RESEARCH CONTEXT:
{input_data.research_context}

PERSONA: {input_data.persona}

CRITICAL REQUIREMENTS:
1. Create structure for FULL {input_data.target_length} video
2. Each beat must be substantial (1.5-2 minutes of content)
3. Structure must incorporate ALL golden nuggets
4. Must connect to modern relevance (Phoenix water crisis parallel)

Director: Create detailed 5-beat structure for "{input_data.topic}"
EmotionalArchitect: Map emotional journey
Others: Validate and improve

BEGIN STRUCTURE CREATION:
"""
        
        self.user_proxy.initiate_chat(manager, message=brief, clear_history=True)
        
        return {
            'conversation': groupchat.messages,
            'phase': 'structure',
            'rounds': len(groupchat.messages)
        }
    
    def _phase_2_content(self, input_data: WritersRoomInput) -> Dict:
        """Phase 2 with PRESERVED CONTEXT"""
        
        agents = [
            self.user_proxy,
            self.agents['hooksmith'],
            self.agents['story_weaver'],
            self.agents['closer'],
            self.agents['emotional_architect'],
            self.agents['director']
        ]
        
        groupchat = GroupChat(
            agents=agents,
            messages=[],
            max_round=40,  # DRAMATICALLY INCREASED for complete content
            speaker_selection_method="auto"
        )
        
        manager = GroupChatManager(groupchat=groupchat, llm_config=self.llm_config)
        
        # USE ACTUAL APPROVED STRUCTURE
        brief = f"""
CONTENT CREATION FOR: {self.current_topic}

APPROVED STRUCTURE:
{self.approved_structure}

GOLDEN NUGGETS TO INCORPORATE:
{chr(10).join(f"- {nugget}" for nugget in self.current_nuggets)}

TARGET: Create FULL {input_data.target_length} of detailed content

TASKS:
1. Hooksmith: Create 3 powerful hook variations
2. StoryWeaver: Write COMPLETE narrative for each beat (1.5-2 min each)
3. Closer: Create memorable, debate-triggering ending
4. EmotionalArchitect: Ensure emotional peaks throughout
5. Director: Verify adherence to structure

CRITICAL: Write FULL CONTENT, not summaries or outlines!

BEGIN CONTENT CREATION:
"""
        
        self.user_proxy.initiate_chat(manager, message=brief, clear_history=True)
        
        return {
            'conversation': groupchat.messages,
            'phase': 'content',
            'rounds': len(groupchat.messages)
        }
    
    def _phase_3_production(self, input_data: WritersRoomInput) -> Dict:
        """Phase 3 with FULL CONTEXT"""
        
        agents = [
            self.user_proxy,
            self.agents['producer'],
            self.agents['emotional_architect'],
            self.agents['director']
        ]
        
        groupchat = GroupChat(
            agents=agents,
            messages=[],
            max_round=20,  # INCREASED for complete production polish
            speaker_selection_method="round_robin"
        )
        
        manager = GroupChatManager(groupchat=groupchat, llm_config=self.llm_config)
        
        brief = f"""
PRODUCTION POLISH FOR: {self.current_topic}

CONTENT TO POLISH:
{self.created_content}

TASKS:
1. Producer: Add visual descriptions, audio cues, timing
2. EmotionalArchitect: Mark emotional peaks and timing
3. Director: Ensure quality and coherence

Create production-ready {input_data.target_length} script.

BEGIN PRODUCTION POLISH:
"""
        
        self.user_proxy.initiate_chat(manager, message=brief, clear_history=True)
        
        return {
            'conversation': groupchat.messages,
            'phase': 'production',
            'rounds': len(groupchat.messages)
        }
    
    def _phase_4_quality(self, input_data: WritersRoomInput, production_result: Dict) -> Dict:
        """Phase 4 final validation"""
        
        agents = [
            self.user_proxy,
            self.agents['director'],
            self.agents['hooksmith'],
            self.agents['story_weaver'],
            self.agents['closer'],
            self.agents['emotional_architect'],
            self.agents['producer']
        ]
        
        groupchat = GroupChat(
            agents=agents,
            messages=[],
            max_round=15,  # INCREASED for complete quality validation
            speaker_selection_method="round_robin"
        )
        
        manager = GroupChatManager(groupchat=groupchat, llm_config=self.llm_config)
        
        brief = f"""
QUALITY VALIDATION FOR: {self.current_topic}

Validate the {input_data.target_length} script meets all standards.

Each agent score 1-10:
1. Click-worthiness
2. Retention potential  
3. Share-ability
4. Viral standards
5. Production quality

PASS: All agents ≥{input_data.quality_threshold}
FAIL: Any agent <{input_data.quality_threshold}

BEGIN VALIDATION:
"""
        
        self.user_proxy.initiate_chat(manager, message=brief, clear_history=True)
        
        return {
            'conversation': groupchat.messages,
            'phase': 'quality',
            'rounds': len(groupchat.messages),
            'final_script': self._extract_final_script(production_result['conversation'])
        }
    
    def _extract_structure(self, messages: List[Dict]) -> str:
        """Extract approved structure"""
        for msg in reversed(messages):
            if msg.get('name') == 'Director' and 'beat' in msg.get('content', '').lower():
                return msg['content']
        return "Structure extraction failed"
    
    def _extract_content(self, messages: List[Dict]) -> str:
        """Extract created content with COMPREHENSIVE capture"""
        content_parts = []
        
        # Capture ALL substantial agent contributions
        for msg in messages:
            name = msg.get('name', '')
            content = msg.get('content', '')
            
            if name in ['StoryWeaver', 'Hooksmith', 'Closer', 'EmotionalArchitect', 'Director', 'Producer']:
                if len(content) > 150:  # Lowered threshold to capture more content
                    content_parts.append(f"[{name}]\n{content}\n")
        
        # If we don't have enough content, capture the longest messages
        if len('\n'.join(content_parts)) < 1000:
            # Get the 5 longest messages regardless of agent
            sorted_messages = sorted(messages, key=lambda x: len(x.get('content', '')), reverse=True)
            for msg in sorted_messages[:5]:
                if len(msg.get('content', '')) > 100:
                    content_parts.append(f"[{msg.get('name', 'Unknown')}]\n{msg['content']}\n")
        
        return '\n'.join(content_parts)
    
    def _extract_final_script(self, messages: List[Dict]) -> str:
        """Extract final polished script with BETTER fallbacks"""
        
        # Priority 1: Look for Producer's polished version
        for msg in reversed(messages):
            if msg.get('name') == 'Producer' and len(msg.get('content', '')) > 500:
                return msg['content']
        
        # Priority 2: Look for Director's final version
        for msg in reversed(messages):
            if msg.get('name') == 'Director' and len(msg.get('content', '')) > 500:
                return msg['content']
        
        # Priority 3: Get the longest substantial message from production phase
        production_messages = [msg for msg in messages if len(msg.get('content', '')) > 300]
        if production_messages:
            longest_msg = max(production_messages, key=lambda x: len(x.get('content', '')))
            return longest_msg['content']
        
        # Final fallback: created content from Phase 2
        return self.created_content if hasattr(self, 'created_content') else "Script extraction failed"
    
    def _compile_final_output(self, final_result: Dict, execution_time: float) -> WritersRoomOutput:
        """Compile everything properly"""
        
        quality_scores = self._extract_quality_scores(final_result['conversation'])
        
        metrics = CollaborationMetrics(
            total_rounds=final_result['rounds'],
            rejections=self._count_rejections(final_result['conversation']),
            revisions=self._count_revisions(final_result['conversation']),
            consensus_time=execution_time,
            final_quality_score=quality_scores.get('average', 0.0)
        )
        
        # Extract hooks from CONTENT CREATION phase (where they're actually generated)
        hooks = self._extract_hooks(self.created_content) if hasattr(self, 'created_content') else []
        
        # If no hooks found, try final script as fallback
        if not hooks:
            hooks = self._extract_hooks(final_result.get('final_script', ''))
        
        return WritersRoomOutput(
            final_script=final_result.get('final_script', 'Script generation failed'),
            structure=self.approved_structure,
            hooks=hooks,
            conversation_transcript=final_result['conversation'],
            collaboration_metrics=metrics,
            quality_scores=quality_scores,
            execution_time=execution_time
        )
    
    def _extract_hooks(self, content: str) -> List[str]:
        """Extract hook variations with COMPREHENSIVE parsing"""
        hooks = []
        
        # Pattern 1: Look for "Primary Hook", "Alternative Hook", "Backup Hook"
        import re
        hook_patterns = [
            r'Primary Hook[:\s]*([^(\n]*(?:\n(?!(?:Alternative|Backup|Secondary)).+)*)',
            r'Alternative Hook[:\s]*([^(\n]*(?:\n(?!(?:Primary|Backup|Secondary)).+)*)',
            r'Backup Hook[:\s]*([^(\n]*(?:\n(?!(?:Primary|Alternative|Secondary)).+)*)',
            r'Secondary Hook[:\s]*([^(\n]*(?:\n(?!(?:Primary|Alternative|Backup)).+)*)'
        ]
        
        for pattern in hook_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                clean_hook = match.strip().replace('\n', ' ').replace('  ', ' ')
                if len(clean_hook) > 20:  # Ensure it's substantial
                    hooks.append(clean_hook)
        
        # Pattern 2: Look for numbered hooks (1., 2., 3.)
        if not hooks:
            numbered_pattern = r'(\d+\.\s*[^(\n]+(?:\n(?!\d+\.).+)*)'
            numbered_matches = re.findall(numbered_pattern, content, re.DOTALL)
            for match in numbered_matches[:3]:
                clean_hook = match.strip().replace('\n', ' ').replace('  ', ' ')
                if len(clean_hook) > 20:
                    hooks.append(clean_hook)
        
        # Pattern 3: Fallback - any substantial text with "hook" nearby
        if not hooks:
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'hook' in line.lower() and len(line) > 30:
                    context = ' '.join(lines[i:i+3]).strip()
                    if len(context) > 40:
                        hooks.append(context)
        
        return hooks[:3]  # Return up to 3 hooks
    
    def _extract_quality_scores(self, messages: List[Dict]) -> Dict[str, float]:
        """Extract scores from validation with MULTIPLE patterns"""
        scores = {}
        import re
        
        # Multiple scoring patterns agents might use
        score_patterns = [
            r'(\d+(?:\.\d+)?)/10',  # "8.5/10"
            r'Score:\s*(\d+(?:\.\d+)?)',  # "Score: 8.5"
            r'Rating:\s*(\d+(?:\.\d+)?)',  # "Rating: 8.5" 
            r'(\d+(?:\.\d+)?)\s*out\s*of\s*10',  # "8.5 out of 10"
            r'Quality:\s*(\d+(?:\.\d+)?)',  # "Quality: 8.5"
            r'I(?:\s+would)?\s+(?:give|rate|score)(?:\s+this|\s+it)?\s*(?:a|\s)*(\d+(?:\.\d+)?)',  # "I give this 8.5"
        ]
        
        for msg in messages:
            name = msg.get('name', '')
            content = msg.get('content', '').lower()
            
            if name in ['Director', 'Hooksmith', 'StoryWeaver', 'Closer', 'EmotionalArchitect', 'Producer']:
                for pattern in score_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        try:
                            score = float(matches[0])
                            if 0 <= score <= 10:  # Valid score range
                                scores[name] = score
                                break
                        except ValueError:
                            continue
        
        # Calculate average if we have scores
        if scores:
            scores['average'] = sum(scores.values()) / len(scores)
        else:
            # Default quality score if no explicit scores found
            scores['average'] = 7.5  # Assume good quality if agents didn't reject
        
        return scores
    
    def _count_rejections(self, messages: List[Dict]) -> int:
        """Count rejections"""
        count = 0
        rejection_words = ['reject', 'terrible', 'wrong', 'fail', 'bad']
        for msg in messages:
            if any(word in msg.get('content', '').lower() for word in rejection_words):
                count += 1
        return count
    
    def _count_revisions(self, messages: List[Dict]) -> int:
        """Count revisions"""
        count = 0
        revision_words = ['revise', 'improve', 'better', 'update', 'change']
        for msg in messages:
            if any(word in msg.get('content', '').lower() for word in revision_words):
                count += 1
        return count
    
    def save_output(self, output: WritersRoomOutput, filename: str):
        """Save output"""
        output_dict = asdict(output)
        output_dict['timestamp'] = time.strftime("%Y-%m-%d %H:%M:%S")
        output_dict['topic'] = self.current_topic
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(output_dict, f, indent=2, ensure_ascii=False)
        
        print(f"✅ Saved: {filename}")