<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Research v2 - Channel Analyzer</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/static/favicon.svg">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- YouTube Research v2 Design System -->
    <link rel="stylesheet" href="/static/styles/youtube-research-v2-design-system.css?v=2025073002">
</head>
<body>
    <div class="app-layout">
        <!-- Premium Sidebar -->
        <aside class="app-sidebar" id="sidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <a href="/" class="sidebar-logo">
                    <div class="sidebar-logo-icon">YR</div>
                    <span class="sidebar-logo-text">YouTube Research</span>
                </a>
                <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                    <i data-lucide="sun" class="theme-icon-light"></i>
                    <i data-lucide="moon" class="theme-icon-dark" style="display: none;"></i>
                </button>
            </div>

            <!-- Navigation Menu -->
            <nav class="sidebar-nav">
                <!-- Overview Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Overview</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/" class="nav-item" data-page="dashboard">
                                <i data-lucide="layout-dashboard" class="nav-icon"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <a href="/analytics" class="nav-item" data-page="analytics">
                                <i data-lucide="bar-chart-3" class="nav-icon"></i>
                                <span>Analytics</span>
                            </a>
                        </li>
                        <li>
                            <a href="/reports" class="nav-item" data-page="reports">
                                <i data-lucide="folder" class="nav-icon"></i>
                                <span>My Reports</span>
                                <span class="nav-badge" id="reportsCount">0</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Analysis Tools Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">AI Analysis Tools</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/video-analyzer" class="nav-item" data-page="video-analyzer">
                                <i data-lucide="video" class="nav-icon"></i>
                                <span>Video Analyzer</span>
                                <span class="nav-badge nav-badge-purple">6 AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/channel-analyzer" class="nav-item active" data-page="channel-analyzer">
                                <i data-lucide="users" class="nav-icon"></i>
                                <span>Channel DNA</span>
                                <span class="nav-badge nav-badge-blue">5 AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/market-research" class="nav-item" data-page="market-research">
                                <i data-lucide="trending-up" class="nav-icon"></i>
                                <span>Market Research</span>
                                <span class="nav-badge nav-badge-green">AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/agent/comment" class="nav-item" data-page="comment-intelligence">
                                <i data-lucide="message-circle" class="nav-icon"></i>
                                <span>Comment Intel</span>
                                <span class="nav-badge nav-badge-orange">AI</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Account Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Account</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/settings" class="nav-item" data-page="settings">
                                <i data-lucide="settings" class="nav-icon"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li>
                            <a href="/help" class="nav-item" data-page="help">
                                <i data-lucide="help-circle" class="nav-icon"></i>
                                <span>Help & Support</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="sidebar-user">
                    <div class="sidebar-user-avatar">A</div>
                    <div class="sidebar-user-info">
                        <div class="sidebar-user-name">Ardi</div>
                        <div class="sidebar-user-plan">Premium Plan</div>
                    </div>
                </div>
                <div class="version-info">
                    <span class="version-label">YouTube Research v2</span>
                    <span class="version-number">2.0.0</span>
                </div>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="app-main">
            <!-- Header -->
            <header class="app-header">
                <div class="header-left">
                    <button class="btn btn-ghost btn-sm" id="sidebar-toggle">
                        <i data-lucide="menu" class="sidebar-nav-icon"></i>
                    </button>
                    <nav class="breadcrumb">
                        <span class="breadcrumb-item">Analysis Tools</span>
                        <i data-lucide="chevron-right" class="breadcrumb-separator"></i>
                        <span class="breadcrumb-item">Channel Analyzer</span>
                    </nav>
                </div>
                <div class="header-right">
                    <button class="btn btn-ghost btn-sm">
                        <i data-lucide="history" class="sidebar-nav-icon"></i>
                    </button>
                    <button class="btn btn-ghost btn-sm">
                        <i data-lucide="download" class="sidebar-nav-icon"></i>
                    </button>
                </div>
            </header>
            
            <!-- Content -->
            <div class="premium-content">
                <!-- Channel Analyzer Welcome Section -->
                <div class="welcome-section">
                    <div>
                        <div class="form-header">
                            <div class="market-icon">
                                <i data-lucide="users"></i>
                            </div>
                            <h1>Channel DNA Analyzer</h1>
                            <p>AI-powered channel intelligence. Discover content patterns, analyze performance metrics, and unlock optimization opportunities with comprehensive data analysis.</p>
                        </div>
                    </div>
                    <div class="welcome-stats">
                        <div class="premium-card glass-card">
                            <div class="stat-item">
                                <div class="stat-value">5 AI</div>
                                <div class="stat-label">Agents</div>
                            </div>
                        </div>
                        <div class="premium-card glass-card">
                            <div class="stat-item">
                                <div class="stat-value">∞</div>
                                <div class="stat-label">Channels</div>
                            </div>
                        </div>
                        <div class="premium-card glass-card">
                            <div class="stat-item">
                                <div class="stat-value">200</div>
                                <div class="stat-label">Max Videos</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Analysis Form -->
                <div class="analyzer-form-section">
                    <div class="premium-card">
                        <div class="premium-card-header">
                            <div>
                                <h2 class="premium-card-title">
                                    <i data-lucide="search" style="margin-right: var(--space-sm);"></i>
                                    Channel Analysis Query
                                </h2>
                                <p class="premium-card-subtitle">Enter a YouTube channel to begin comprehensive DNA analysis</p>
                            </div>
                            <div class="agent-badge-large">
                                <i data-lucide="users" class="agent-icon-large"></i>
                                <span class="agent-label">DNA Analysis</span>
                            </div>
                        </div>
                        <div class="premium-card-content">
                            <form class="analyzer-form flex-layout" id="analysisForm">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i data-lucide="at-sign" class="form-label-icon"></i>
                                            Channel ID or Handle
                                        </label>
                                        <input type="text" class="form-input" id="channelId" placeholder="Enter @MrBeast or UCX6OQ3DkcsbYNE6H8uQQuVA" required>
                                    </div>
                                    <div class="form-group">
                                        <label class="form-label">
                                            <i data-lucide="video" class="form-label-icon"></i>
                                            Max Videos
                                        </label>
                                        <select class="form-input form-select" id="maxVideos">
                                            <option value="25">25 Videos</option>
                                            <option value="50" selected>50 Videos</option>
                                            <option value="100">100 Videos</option>
                                            <option value="200">200 Videos</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary btn-lg" id="analyzeBtn">
                                        <i data-lucide="zap" class="btn-icon"></i>
                                        <span class="btn-text">Analyze Channel DNA</span>
                                        <div class="btn-spinner" style="display: none;">
                                            <div class="spinner"></div>
                                            <span>Analyzing...</span>
                                        </div>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Analysis Results -->
                <div class="analyzer-results" id="resultsContainer" style="display: none;">
                    <!-- Channel Overview -->
                    <div class="channel-overview" id="channelOverview">
                        <div class="premium-card">
                            <div class="premium-card-header">
                                <h2 class="premium-card-title">Channel Overview</h2>
                                <div class="channel-actions">
                                    <div class="export-dropdown">
                                        <button class="btn btn-secondary btn-sm" id="exportDropdownBtn">
                                            <i data-lucide="download"></i>
                                            Export Data
                                            <i data-lucide="chevron-down" class="dropdown-arrow"></i>
                                        </button>
                                        <div class="export-dropdown-menu" id="exportDropdownMenu">
                                            <button class="export-option" data-format="json">
                                                <i data-lucide="file-text"></i>
                                                <div class="export-option-content">
                                                    <span class="export-title">JSON Export</span>
                                                    <span class="export-desc">Complete data with metadata</span>
                                                </div>
                                            </button>
                                            <button class="export-option" data-format="csv">
                                                <i data-lucide="table"></i>
                                                <div class="export-option-content">
                                                    <span class="export-title">CSV Export</span>
                                                    <span class="export-desc">Spreadsheet-friendly format</span>
                                                </div>
                                            </button>
                                            <button class="export-option" data-format="pdf">
                                                <i data-lucide="file-text"></i>
                                                <div class="export-option-content">
                                                    <span class="export-title">PDF Report</span>
                                                    <span class="export-desc">Professional summary report</span>
                                                </div>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="premium-card-content">
                                <div class="channel-header" id="channelHeader">
                                    <!-- Channel info will be populated here -->
                                </div>
                                <div class="channel-stats" id="channelStats">
                                    <!-- Channel statistics will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Videos Library -->
                    <div class="videos-section">
                        <div class="premium-card">
                            <div class="premium-card-header">
                                <h2 class="premium-card-title">Video Library</h2>
                                <div class="videos-controls">
                                    <select class="form-input" id="sortBy" style="width: auto;">
                                        <option value="published">Sort by Published Date</option>
                                        <option value="views">Sort by Views</option>
                                        <option value="likes">Sort by Likes</option>
                                        <option value="comments">Sort by Comments</option>
                                    </select>
                                    <select class="form-input" id="contentFilter" style="width: auto;">
                                        <option value="all">All Content</option>
                                        <option value="longform">Longform Only</option>
                                        <option value="shorts">Shorts Only</option>
                                    </select>
                                    <button class="btn btn-ghost btn-sm" id="toggleView">
                                        <i data-lucide="grid"></i>
                                        Grid View
                                    </button>
                                    <button class="btn btn-primary btn-sm" id="scheduleAnalysisBtn">
                                        <i data-lucide="clock"></i>
                                        Schedule Intelligence
                                    </button>
                                    <button class="btn btn-secondary btn-sm" id="titleAnalysisBtn">
                                        <i data-lucide="type"></i>
                                        Title Analyzer
                                    </button>
                                </div>
                            </div>
                            <div class="premium-card-content">
                                <div class="videos-grid" id="videosGrid">
                                    <!-- Videos will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Upload Schedule Intelligence -->
                    <div class="schedule-analysis-section" id="scheduleAnalysisSection" style="display: none;">
                        <div class="premium-card">
                            <div class="premium-card-header">
                                <h2 class="premium-card-title">
                                    <i data-lucide="clock"></i>
                                    Upload Schedule Intelligence
                                </h2>
                                <div class="schedule-actions">
                                    <button class="btn btn-ghost btn-sm" id="closeScheduleBtn">
                                        <i data-lucide="x"></i>
                                        Close
                                    </button>
                                </div>
                            </div>
                            <div class="premium-card-content">
                                <div class="schedule-analysis-content" id="scheduleAnalysisContent">
                                    <div class="schedule-loading">
                                        <div class="spinner"></div>
                                        <p>Analyzing upload patterns...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Title Intelligence Analyzer -->
                    <div class="title-analysis-section" id="titleAnalysisSection" style="display: none;">
                        <div class="premium-card">
                            <div class="premium-card-header">
                                <h2 class="premium-card-title">
                                    <i data-lucide="type"></i>
                                    Title Intelligence Analyzer
                                </h2>
                                <div class="title-actions">
                                    <button class="btn btn-ghost btn-sm" id="closeTitleBtn">
                                        <i data-lucide="x"></i>
                                        Close
                                    </button>
                                </div>
                            </div>
                            <div class="premium-card-content">
                                <div class="title-analysis-content" id="titleAnalysisContent">
                                    <div class="title-loading">
                                        <div class="spinner"></div>
                                        <p>Analyzing title patterns and optimization opportunities...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Navigation Manager
        class NavigationManager {
            constructor() {
                this.currentTheme = localStorage.getItem('theme') || 'dark';
                this.init();
            }

            init() {
                this.setupThemeToggle();
                this.setupNavigation();
                this.updateActiveNavItem();
            }

            setupThemeToggle() {
                const themeToggle = document.getElementById('themeToggle');
                const lightIcon = themeToggle.querySelector('.theme-icon-light');
                const darkIcon = themeToggle.querySelector('.theme-icon-dark');

                // Apply saved theme
                document.documentElement.setAttribute('data-theme', this.currentTheme);
                this.updateThemeIcons(lightIcon, darkIcon);

                themeToggle.addEventListener('click', () => {
                    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
                    document.documentElement.setAttribute('data-theme', this.currentTheme);
                    localStorage.setItem('theme', this.currentTheme);
                    this.updateThemeIcons(lightIcon, darkIcon);

                    // Add theme transition effect
                    document.body.classList.add('theme-transitioning');
                    setTimeout(() => {
                        document.body.classList.remove('theme-transitioning');
                    }, 300);
                });
            }

            updateThemeIcons(lightIcon, darkIcon) {
                if (this.currentTheme === 'dark') {
                    lightIcon.style.display = 'none';
                    darkIcon.style.display = 'block';
                } else {
                    lightIcon.style.display = 'block';
                    darkIcon.style.display = 'none';
                }
            }

            setupNavigation() {
                // Add click handlers for navigation items
                const navItems = document.querySelectorAll('.nav-item');
                navItems.forEach(item => {
                    item.addEventListener('click', (e) => {
                        // Update active state
                        navItems.forEach(nav => nav.classList.remove('active'));
                        item.classList.add('active');

                        // Store active page
                        const page = item.getAttribute('data-page');
                        if (page) {
                            localStorage.setItem('activePage', page);
                        }
                    });
                });
            }

            updateActiveNavItem() {
                const activePage = localStorage.getItem('activePage') || 'channel-analyzer';
                const currentPath = window.location.pathname;

                // Remove all active states
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });

                // Set active based on current path or stored page
                let activeItem = document.querySelector(`[data-page="${activePage}"]`);

                if (!activeItem) {
                    // Fallback to path matching
                    activeItem = document.querySelector(`[href="${currentPath}"]`);
                }

                if (activeItem) {
                    activeItem.classList.add('active');
                }
            }
        }

        // Initialize navigation when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.navigationManager = new NavigationManager();
        });

        // Sidebar toggle functionality
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('collapsed');
        });
        
        // Form submission
        document.getElementById('analysisForm').addEventListener('submit', function(e) {
            e.preventDefault();
            startAnalysis();
        });
        
        // Global variables
        let channelData = null;
        let videosData = null;
        let currentView = 'list';
        
        // Analysis functions
        function startAnalysis() {
            const channelId = document.getElementById('channelId').value;
            const maxVideos = parseInt(document.getElementById('maxVideos').value);
            
            if (!channelId.trim()) {
                alert('Please enter a channel ID or handle');
                return;
            }
            
            showLoadingState();
            analyzeChannel(channelId, maxVideos);
        }
        
        function showLoadingState() {
            const analyzeBtn = document.getElementById('analyzeBtn');
            const btnText = analyzeBtn.querySelector('.btn-text');
            const btnSpinner = analyzeBtn.querySelector('.btn-spinner');
            
            btnText.style.display = 'none';
            btnSpinner.style.display = 'flex';
            analyzeBtn.disabled = true;
            
            document.getElementById('resultsContainer').style.display = 'block';
        }
        
        function hideLoadingState() {
            const analyzeBtn = document.getElementById('analyzeBtn');
            const btnText = analyzeBtn.querySelector('.btn-text');
            const btnSpinner = analyzeBtn.querySelector('.btn-spinner');
            
            btnText.style.display = 'inline';
            btnSpinner.style.display = 'none';
            analyzeBtn.disabled = false;
            btnText.textContent = 'Analyze Another Channel';
        }
        
        function analyzeChannel(channelId, maxVideos) {
            fetch('/api/tools/channel_profile', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    channel_id: channelId,
                    max_videos: maxVideos
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                
                channelData = data.channel_profile;
                videosData = data.video_library;
                
                populateChannelData();
                populateVideosData();
                hideLoadingState();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Analysis failed: ' + error.message);
                hideLoadingState();
            });
        }
        
        function populateChannelData() {
            const channelHeader = document.getElementById('channelHeader');
            const channelStats = document.getElementById('channelStats');
            
            // Channel header
            channelHeader.innerHTML = `
                <div class="channel-info">
                    <div class="channel-avatar">
                        <img src="${channelData.thumbnails?.high?.url || channelData.thumbnails?.medium?.url || ''}" alt="${channelData.title}" onerror="this.style.display='none'">
                        <div class="channel-avatar-fallback">
                            <i data-lucide="user"></i>
                        </div>
                    </div>
                    <div class="channel-details">
                        <h3 class="channel-title">${channelData.title || 'Unknown Channel'}</h3>
                        <div class="channel-description-container">
                            <p class="channel-description" id="channelDescription">${channelData.description || 'No description available'}</p>
                        </div>
                        <div class="channel-meta">
                            <span class="meta-item">
                                <i data-lucide="calendar"></i>
                                Created: ${channelData.publishedAt ? new Date(channelData.publishedAt).toLocaleDateString() : 'Unknown'}
                            </span>
                            <span class="meta-item">
                                <i data-lucide="globe"></i>
                                ${channelData.country || 'Unknown Location'}
                            </span>
                        </div>
                    </div>
                </div>
            `;
            
            // Channel statistics
            const stats = channelData.statistics || {};
            const subscriberCount = parseInt(stats.subscriberCount || 0);
            const videoCount = parseInt(stats.videoCount || 0);
            const viewCount = parseInt(stats.viewCount || 0);
            const avgViews = videoCount > 0 ? Math.round(viewCount / videoCount) : 0;
            
            channelStats.innerHTML = `
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i data-lucide="users"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">${formatNumber(subscriberCount)}</div>
                            <div class="stat-label">Subscribers</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i data-lucide="video"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">${formatNumber(videoCount)}</div>
                            <div class="stat-label">Total Videos</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i data-lucide="eye"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">${formatNumber(viewCount)}</div>
                            <div class="stat-label">Total Views</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i data-lucide="trending-up"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">${formatNumber(avgViews)}</div>
                            <div class="stat-label">Avg Views/Video</div>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i data-lucide="clock"></i>
                        </div>
                        <div class="stat-content">
                            <div class="stat-value">${calculateAverageDuration(videosData)}</div>
                            <div class="stat-label">Avg Duration</div>
                        </div>
                    </div>
                </div>
            `;
            
            // Re-initialize icons
            lucide.createIcons();
        }
        
        function populateVideosData() {
            if (!videosData || videosData.length === 0) {
                document.getElementById('videosGrid').innerHTML = '<div class="no-videos">No videos found for this channel.</div>';
                return;
            }
            
            // Use the new filtering system
            updateVideoDisplay();
        }
        
        // Content type detection and filtering functions
        function isVideoShort(duration) {
            if (!duration) return false;
            
            // Parse duration (format: PT1M30S or PT30S)
            const match = duration.match(/PT(?:(\d+)M)?(?:(\d+)S)?/);
            if (!match) return false;
            
            const minutes = parseInt(match[1] || 0);
            const seconds = parseInt(match[2] || 0);
            const totalSeconds = minutes * 60 + seconds;
            
            // YouTube Shorts are 60 seconds or less
            return totalSeconds <= 60;
        }
        
        function filterVideosByContentType(videos, filterType) {
            if (filterType === 'all') return videos;
            
            return videos.filter(video => {
                const duration = video.duration || '';
                const isShort = isVideoShort(duration);
                
                if (filterType === 'shorts') return isShort;
                if (filterType === 'longform') return !isShort;
                return true;
            });
        }
        
        function updateVideoDisplay() {
            const filterType = document.getElementById('contentFilter').value;
            const sortBy = document.getElementById('sortBy').value;
            
            if (!videosData) return;
            
            // Filter by content type
            let filteredVideos = filterVideosByContentType([...videosData], filterType);
            
            // Sort the filtered videos
            filteredVideos.sort((a, b) => {
                switch(sortBy) {
                    case 'views':
                        return (parseInt(b.statistics?.viewCount || 0)) - (parseInt(a.statistics?.viewCount || 0));
                    case 'likes':
                        return (parseInt(b.statistics?.likeCount || 0)) - (parseInt(a.statistics?.likeCount || 0));
                    case 'comments':
                        return (parseInt(b.statistics?.commentCount || 0)) - (parseInt(a.statistics?.commentCount || 0));
                    case 'published':
                    default:
                        return new Date(b.publishedAt || 0) - new Date(a.publishedAt || 0);
                }
            });
            
            // Update the display
            displayFilteredVideos(filteredVideos, filterType);
        }
        
        function displayFilteredVideos(videos, filterType) {
            const videosGrid = document.getElementById('videosGrid');
            
            if (videos.length === 0) {
                const filterText = filterType === 'all' ? '' : ` (${filterType})`;
                videosGrid.innerHTML = `<div class="no-videos">No videos found${filterText} for this channel.</div>`;
                return;
            }
            
            const videosHtml = videos.map(video => {
                const duration = video.duration || '';
                const isShort = isVideoShort(duration);
                const contentType = isShort ? 'shorts' : 'longform';
                
                return `
                <div class="video-card" data-content-type="${contentType}">
                    <div class="video-thumbnail">
                        <img src="${video.thumbnails?.medium?.url || video.thumbnails?.high?.url || ''}" alt="${video.title}" onerror="this.style.display='none'">
                        <div class="video-duration ${isShort ? 'shorts-duration' : ''}">${duration || ''}</div>
                        ${isShort ? '<div class="shorts-indicator">Shorts</div>' : ''}
                    </div>
                    <div class="video-content">
                        <h4 class="video-title">${video.title || 'Untitled Video'}</h4>
                        <div class="video-stats">
                            <span class="stat-item">
                                <i data-lucide="eye"></i>
                                ${formatNumber(video.statistics?.viewCount || 0)}
                            </span>
                            <span class="stat-item">
                                <i data-lucide="thumbs-up"></i>
                                ${formatNumber(video.statistics?.likeCount || 0)}
                            </span>
                            <span class="stat-item">
                                <i data-lucide="message-circle"></i>
                                ${formatNumber(video.statistics?.commentCount || 0)}
                            </span>
                            <span class="stat-item duration-stat">
                                <i data-lucide="clock"></i>
                                ${getDetailedDuration(duration).formatted}
                            </span>
                        </div>
                        <div class="video-meta">
                            <span class="publish-date">${video.publishedAt ? new Date(video.publishedAt).toLocaleDateString() : 'Unknown date'}</span>
                            <div class="video-actions">
                                <a href="/video-analyzer?url=${encodeURIComponent('https://www.youtube.com/watch?v=' + video.id)}" class="video-link analyze-link">
                                    <i data-lucide="zap"></i>
                                    Analyze
                                </a>
                                <a href="https://youtube.com/watch?v=${video.id}" target="_blank" class="video-link">
                                    <i data-lucide="external-link"></i>
                                    Watch
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                `;
            }).join('');
            
            videosGrid.innerHTML = videosHtml;
            
            // Show content type stats
            updateContentTypeStats(videos, filterType);
            
            // Re-initialize icons
            lucide.createIcons();
        }
        
        function updateContentTypeStats(videos, filterType) {
            const totalVideos = videosData.length;
            const shortsCount = videosData.filter(v => isVideoShort(v.duration)).length;
            const longformCount = totalVideos - shortsCount;
            
            // Update the video library title with stats
            const titleElement = document.querySelector('.videos-section .premium-card-title');
            if (titleElement) {
                let titleText = 'Video Library';
                if (filterType === 'all') {
                    titleText += ` (${totalVideos} total: ${longformCount} longform, ${shortsCount} shorts)`;
                } else if (filterType === 'longform') {
                    titleText += ` (${longformCount} longform videos)`;
                } else if (filterType === 'shorts') {
                    titleText += ` (${shortsCount} shorts)`;
                }
                titleElement.textContent = titleText;
            }
        }
        
        // Duration utility functions
        function parseDurationToSeconds(duration) {
            if (!duration) return 0;
            
            // Parse duration (format: PT1M30S or PT30S or PT1H5M30S)
            const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
            if (!match) return 0;
            
            const hours = parseInt(match[1] || 0);
            const minutes = parseInt(match[2] || 0);
            const seconds = parseInt(match[3] || 0);
            
            return hours * 3600 + minutes * 60 + seconds;
        }
        
        function formatDuration(totalSeconds) {
            if (totalSeconds === 0) return '0s';
            
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;
            
            if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else if (minutes > 0) {
                return `${minutes}m ${seconds}s`;
            } else {
                return `${seconds}s`;
            }
        }
        
        function calculateAverageDuration(videos) {
            if (!videos || videos.length === 0) return '0s';
            
            const totalSeconds = videos.reduce((sum, video) => {
                return sum + parseDurationToSeconds(video.duration);
            }, 0);
            
            const avgSeconds = Math.round(totalSeconds / videos.length);
            return formatDuration(avgSeconds);
        }
        
        function getDetailedDuration(duration) {
            const seconds = parseDurationToSeconds(duration);
            return {
                formatted: formatDuration(seconds),
                seconds: seconds,
                original: duration
            };
        }
        
        // Utility functions
        function formatNumber(num) {
            if (num >= 1000000000) {
                return (num / 1000000000).toFixed(1) + 'B';
            } else if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }
        
        // Enhanced Export functionality with multiple formats
        document.getElementById('exportDropdownBtn').addEventListener('click', function() {
            const dropdown = document.getElementById('exportDropdownMenu');
            dropdown.classList.toggle('show');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('exportDropdownMenu');
            const button = document.getElementById('exportDropdownBtn');
            if (!button.contains(event.target) && !dropdown.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });
        
        // Handle export option selection
        document.querySelectorAll('.export-option').forEach(button => {
            button.addEventListener('click', function() {
                const format = this.getAttribute('data-format');
                exportChannelData(format);
                document.getElementById('exportDropdownMenu').classList.remove('show');
            });
        });
        
        function exportChannelData(format) {
            if (!channelData) {
                alert('No channel data to export');
                return;
            }
            
            // Show loading state
            const exportBtn = document.getElementById('exportDropdownBtn');
            const originalText = exportBtn.innerHTML;
            exportBtn.innerHTML = `
                <div class="spinner"></div>
                Exporting...
            `;
            exportBtn.disabled = true;
            
            // Prepare export data
            const exportData = {
                channel_profile: channelData,
                video_library: videosData
            };
            
            // Generate safe filename
            const channelName = channelData.title || 'channel';
            const safeChannelName = channelName.replace(/[^a-zA-Z0-9\s\-_]/g, '').trim();
            const timestamp = new Date().toISOString().split('T')[0];
            const filename = `${safeChannelName}_${timestamp}`;
            
            // Call enhanced export API
            fetch('/api/export/channel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    data: exportData,
                    format_type: format,
                    filename: filename
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Export failed: ${response.statusText}`);
                }
                
                // Get filename from headers or generate one
                const contentDisposition = response.headers.get('Content-Disposition');
                let downloadFilename = filename;
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename=(.+)/);
                    if (filenameMatch) {
                        downloadFilename = filenameMatch[1].replace(/"/g, '');
                    }
                }
                
                // Download the file
                return response.blob().then(blob => ({
                    blob: blob,
                    filename: downloadFilename
                }));
            })
            .then(({ blob, filename }) => {
                // Create download link
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                // Show success message
                showExportSuccess(format);
            })
            .catch(error => {
                console.error('Export error:', error);
                alert(`Export failed: ${error.message}`);
            })
            .finally(() => {
                // Restore button state
                exportBtn.innerHTML = originalText;
                exportBtn.disabled = false;
                lucide.createIcons();
            });
        }
        
        function showExportSuccess(format) {
            // Create temporary success message
            const successMsg = document.createElement('div');
            successMsg.className = 'export-success-message';
            successMsg.innerHTML = `
                <i data-lucide="check-circle"></i>
                ${format.toUpperCase()} export completed successfully!
            `;
            
            document.body.appendChild(successMsg);
            lucide.createIcons();
            
            // Remove after 3 seconds
            setTimeout(() => {
                successMsg.remove();
            }, 3000);
        }
        
        // Sort and filter functionality
        document.getElementById('sortBy').addEventListener('change', function() {
            updateVideoDisplay();
        });
        
        document.getElementById('contentFilter').addEventListener('change', function() {
            updateVideoDisplay();
        });
        
        // View toggle functionality
        document.getElementById('toggleView').addEventListener('click', function() {
            const videosGrid = document.getElementById('videosGrid');
            const icon = this.querySelector('i');
            const text = this.querySelector('span') || this;
            
            if (currentView === 'list') {
                videosGrid.classList.add('grid-view');
                icon.setAttribute('data-lucide', 'list');
                if (text.textContent) text.textContent = 'List View';
                currentView = 'grid';
            } else {
                videosGrid.classList.remove('grid-view');
                icon.setAttribute('data-lucide', 'grid');
                if (text.textContent) text.textContent = 'Grid View';
                currentView = 'list';
            }
            
            lucide.createIcons();
        });
        
        // Upload Schedule Intelligence functionality
        document.getElementById('scheduleAnalysisBtn').addEventListener('click', function() {
            if (!channelData) {
                alert('Please analyze a channel first');
                return;
            }
            
            showScheduleAnalysis();
        });
        
        document.getElementById('closeScheduleBtn').addEventListener('click', function() {
            document.getElementById('scheduleAnalysisSection').style.display = 'none';
        });
        
        // Title Intelligence functionality
        document.getElementById('titleAnalysisBtn').addEventListener('click', function() {
            if (!channelData) {
                alert('Please analyze a channel first');
                return;
            }
            
            showTitleAnalysis();
        });
        
        document.getElementById('closeTitleBtn').addEventListener('click', function() {
            document.getElementById('titleAnalysisSection').style.display = 'none';
        });
        
        function showScheduleAnalysis() {
            const section = document.getElementById('scheduleAnalysisSection');
            const content = document.getElementById('scheduleAnalysisContent');
            
            // Show section with loading state
            section.style.display = 'block';
            content.innerHTML = `
                <div class="schedule-loading">
                    <div class="spinner"></div>
                    <p>Analyzing upload patterns and optimization opportunities...</p>
                </div>
            `;
            
            // Scroll to schedule analysis
            section.scrollIntoView({ behavior: 'smooth' });
            
            // Get channel ID for analysis
            const channelId = document.getElementById('channelId').value;
            const maxVideos = parseInt(document.getElementById('maxVideos').value);
            
            // Call upload schedule analysis API
            fetch('/api/analyze/upload-schedule', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    channel_id: channelId,
                    max_videos: maxVideos
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                
                displayScheduleAnalysis(data);
            })
            .catch(error => {
                console.error('Schedule analysis error:', error);
                content.innerHTML = `
                    <div class="schedule-error">
                        <i data-lucide="alert-circle"></i>
                        <h3>Analysis Failed</h3>
                        <p>${error.message}</p>
                        <button class="btn btn-primary" onclick="showScheduleAnalysis()">Try Again</button>
                    </div>
                `;
                lucide.createIcons();
            });
        }
        
        function displayScheduleAnalysis(data) {
            const content = document.getElementById('scheduleAnalysisContent');
            const analysis = data.schedule_intelligence;
            const channelInfo = data.channel_info;
            
            // Build comprehensive analysis display
            const html = `
                <div class="schedule-analysis-results">
                    <!-- Overview Section -->
                    <div class="schedule-overview">
                        <h3>📊 Upload Pattern Overview</h3>
                        <div class="schedule-stats">
                            <div class="schedule-stat">
                                <span class="stat-label">Upload Frequency</span>
                                <span class="stat-value">${analysis.upload_frequency?.frequency_category || 'Unknown'}</span>
                            </div>
                            <div class="schedule-stat">
                                <span class="stat-label">Consistency Score</span>
                                <span class="stat-value">${analysis.upload_frequency?.consistency_score || 0}/100</span>
                            </div>
                            <div class="schedule-stat">
                                <span class="stat-label">Preferred Day</span>
                                <span class="stat-value">${analysis.day_of_week_patterns?.preferred_day || 'Unknown'}</span>
                            </div>
                            <div class="schedule-stat">
                                <span class="stat-label">Best Time Slot</span>
                                <span class="stat-value">${analysis.time_of_day_patterns?.preferred_time_slot || 'Unknown'}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Performance Correlation -->
                    <div class="schedule-performance">
                        <h3>🚀 Performance Insights</h3>
                        <div class="performance-insights">
                            <div class="insight-card">
                                <h4>Best Performing Day</h4>
                                <p><strong>${analysis.performance_correlation?.best_performing_day || 'Unknown'}</strong></p>
                                <span class="insight-metric">${formatNumber(analysis.performance_correlation?.best_day_avg_views || 0)} avg views</span>
                            </div>
                            <div class="insight-card">
                                <h4>Best Time Slot</h4>
                                <p><strong>${analysis.performance_correlation?.best_performing_time_slot || 'Unknown'}</strong></p>
                                <span class="insight-metric">${formatNumber(analysis.performance_correlation?.best_slot_avg_views || 0)} avg views</span>
                            </div>
                            <div class="insight-card">
                                <h4>Upload Consistency</h4>
                                <p><strong>${analysis.consistency_metrics?.reliability || 'Unknown'}</strong></p>
                                <span class="insight-metric">${analysis.consistency_metrics?.consistency_score || 0}% consistent</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Optimization Recommendations -->
                    <div class="schedule-recommendations">
                        <h3>💡 Optimization Recommendations</h3>
                        <div class="recommendations-grid">
                            <div class="recommendation-section">
                                <h4>📅 Optimal Schedule</h4>
                                <ul class="recommendation-list">
                                    <li><strong>Day:</strong> ${analysis.optimization_recommendations?.optimal_schedule?.recommended_day || 'Unknown'}</li>
                                    <li><strong>Time:</strong> ${analysis.optimization_recommendations?.optimal_schedule?.recommended_time_slot || 'Unknown'}</li>
                                    <li><strong>Frequency:</strong> ${analysis.optimization_recommendations?.optimal_schedule?.recommended_frequency || 'Unknown'}</li>
                                    <li><strong>Confidence:</strong> ${analysis.optimization_recommendations?.optimal_schedule?.confidence_level || 'Unknown'}</li>
                                </ul>
                            </div>
                            
                            <div class="recommendation-section">
                                <h4>🎯 Improvement Opportunities</h4>
                                <ul class="recommendation-list">
                                    ${(analysis.optimization_recommendations?.improvement_opportunities || []).map(opp => 
                                        `<li>${opp}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                            
                            <div class="recommendation-section">
                                <h4>📈 Next Actions</h4>
                                <ul class="recommendation-list">
                                    ${(analysis.optimization_recommendations?.next_actions || []).map(action => 
                                        `<li>${action}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Day of Week Analysis -->
                    <div class="schedule-patterns">
                        <h3>📅 Weekly Upload Patterns</h3>
                        <div class="day-analysis">
                            ${Object.entries(analysis.day_of_week_patterns?.day_percentages || {}).map(([day, percentage]) => `
                                <div class="day-bar">
                                    <span class="day-name">${day}</span>
                                    <div class="day-bar-container">
                                        <div class="day-bar-fill" style="width: ${percentage}%"></div>
                                    </div>
                                    <span class="day-percentage">${percentage.toFixed(1)}%</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
            
            content.innerHTML = html;
            lucide.createIcons();
        }
        
        function showTitleAnalysis() {
            const section = document.getElementById('titleAnalysisSection');
            const content = document.getElementById('titleAnalysisContent');
            
            // Show section with loading state
            section.style.display = 'block';
            content.innerHTML = `
                <div class="title-loading">
                    <div class="spinner"></div>
                    <p>Analyzing title patterns, psychology triggers, and optimization opportunities...</p>
                </div>
            `;
            
            // Scroll to title analysis
            section.scrollIntoView({ behavior: 'smooth' });
            
            // Get channel ID for analysis
            const channelId = document.getElementById('channelId').value;
            const maxVideos = parseInt(document.getElementById('maxVideos').value);
            
            // Call title analysis API
            fetch('/api/analyze/titles', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    channel_id: channelId,
                    max_videos: maxVideos
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                
                displayTitleAnalysis(data);
            })
            .catch(error => {
                console.error('Title analysis error:', error);
                content.innerHTML = `
                    <div class="title-error">
                        <i data-lucide="alert-circle"></i>
                        <h3>Analysis Failed</h3>
                        <p>${error.message}</p>
                        <button class="btn btn-primary" onclick="showTitleAnalysis()">Try Again</button>
                    </div>
                `;
                lucide.createIcons();
            });
        }
        
        function displayTitleAnalysis(data) {
            const content = document.getElementById('titleAnalysisContent');
            const analysis = data.title_intelligence;
            const channelInfo = data.channel_info;
            
            // Build comprehensive title analysis display
            const html = `
                <div class="title-analysis-results">
                    <!-- Performance Overview -->
                    <div class="title-overview">
                        <h3>📊 Title Performance Overview</h3>
                        <div class="title-stats">
                            <div class="title-stat">
                                <span class="stat-label">Titles Analyzed</span>
                                <span class="stat-value">${analysis.title_performance_analysis?.total_titles_analyzed || 0}</span>
                            </div>
                            <div class="title-stat">
                                <span class="stat-label">Avg Views per Video</span>
                                <span class="stat-value">${formatNumber(analysis.title_performance_analysis?.average_views || 0)}</span>
                            </div>
                            <div class="title-stat">
                                <span class="stat-label">Avg Title Length</span>
                                <span class="stat-value">${analysis.length_optimization?.average_title_length || 0} chars</span>
                            </div>
                            <div class="title-stat">
                                <span class="stat-label">Psychology Score</span>
                                <span class="stat-value">${analysis.psychology_triggers?.psychology_score || 0}/100</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Top Performing Titles -->
                    <div class="title-performance">
                        <h3>🏆 Top Performing Titles</h3>
                        <div class="top-titles">
                            ${(analysis.title_performance_analysis?.top_performing_titles || []).slice(0, 5).map(title => `
                                <div class="title-card">
                                    <h4 class="title-text">"${title.title}"</h4>
                                    <div class="title-metrics">
                                        <span class="metric">
                                            <i data-lucide="eye"></i>
                                            ${formatNumber(title.views)} views
                                        </span>
                                        <span class="metric">
                                            <i data-lucide="heart"></i>
                                            ${title.engagement_rate}% engagement
                                        </span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    <!-- Length Optimization -->
                    <div class="length-analysis">
                        <h3>📏 Length Optimization</h3>
                        <div class="length-insights">
                            <div class="insight-card">
                                <h4>Optimal Length</h4>
                                <p><strong>${analysis.length_optimization?.optimal_length_range || 'Unknown'}</strong></p>
                                <span class="insight-metric">Best performing range</span>
                            </div>
                            <div class="insight-card">
                                <h4>Current Average</h4>
                                <p><strong>${analysis.length_optimization?.average_title_length || 0} characters</strong></p>
                                <span class="insight-metric">Channel average</span>
                            </div>
                            <div class="insight-card">
                                <h4>Best Group</h4>
                                <p><strong>${analysis.length_optimization?.best_performing_length_group || 'Unknown'}</strong></p>
                                <span class="insight-metric">Highest avg views</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Psychology Triggers -->
                    <div class="psychology-analysis">
                        <h3>🧠 Psychology Triggers</h3>
                        <div class="trigger-grid">
                            ${Object.entries(analysis.psychology_triggers?.trigger_analysis || {}).map(([trigger, data]) => `
                                <div class="trigger-card">
                                    <h4>${trigger.replace('_', ' ').toUpperCase()}</h4>
                                    <div class="trigger-stats">
                                        <span class="trigger-usage">${data.usage_percentage}% usage</span>
                                        <span class="trigger-performance">${formatNumber(data.average_views)} avg views</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                        <div class="psychology-insights">
                            <p><strong>Most Effective:</strong> ${analysis.psychology_triggers?.most_effective_trigger || 'None'}</p>
                            <p><strong>Most Used:</strong> ${analysis.psychology_triggers?.most_used_trigger || 'None'}</p>
                        </div>
                    </div>
                    
                    <!-- Keywords Intelligence -->
                    <div class="keyword-analysis">
                        <h3>🔑 Keyword Intelligence</h3>
                        <div class="keyword-sections">
                            <div class="keyword-section">
                                <h4>Top Performing Keywords</h4>
                                <div class="keyword-list">
                                    ${(analysis.keyword_intelligence?.top_performing_keywords || []).slice(0, 8).map(keyword => `
                                        <span class="keyword-tag high-performance">
                                            ${keyword.keyword}
                                            <small>${formatNumber(keyword.average_views)} avg</small>
                                        </span>
                                    `).join('')}
                                </div>
                            </div>
                            <div class="keyword-section">
                                <h4>Most Used Keywords</h4>
                                <div class="keyword-list">
                                    ${(analysis.keyword_intelligence?.most_used_keywords || []).slice(0, 8).map(keyword => `
                                        <span class="keyword-tag frequent-use">
                                            ${keyword.keyword}
                                            <small>${keyword.usage_count} uses</small>
                                        </span>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Optimization Recommendations -->
                    <div class="title-recommendations">
                        <h3>💡 Optimization Recommendations</h3>
                        <div class="recommendations-grid">
                            <div class="recommendation-section">
                                <h4>🎯 Immediate Actions</h4>
                                <ul class="recommendation-list">
                                    ${(analysis.optimization_recommendations?.immediate_actions || []).map(action => 
                                        `<li>${action}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                            
                            <div class="recommendation-section">
                                <h4>🚀 Optimization Opportunities</h4>
                                <ul class="recommendation-list">
                                    ${(analysis.optimization_recommendations?.optimization_opportunities || []).map(opp => 
                                        `<li>${opp}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                            
                            <div class="recommendation-section">
                                <h4>🧠 Psychology Enhancements</h4>
                                <ul class="recommendation-list">
                                    ${(analysis.optimization_recommendations?.psychology_enhancements || []).map(enhancement => 
                                        `<li>${enhancement}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Title Templates -->
                    <div class="title-templates">
                        <h3>📝 Proven Title Templates</h3>
                        <div class="template-grid">
                            ${(analysis.optimization_recommendations?.title_templates || []).map(template => `
                                <div class="template-card">
                                    <h4>Template</h4>
                                    <p class="template-text">${template.template}</p>
                                    <p class="template-example"><strong>Example:</strong> ${template.example}</p>
                                    <span class="template-psychology">${template.psychology}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
            
            content.innerHTML = html;
            lucide.createIcons();
        }
    </script>
    
    <!-- Channel Analyzer styles now in youtube-research-v2-design-system.css -->


</body>
</html>