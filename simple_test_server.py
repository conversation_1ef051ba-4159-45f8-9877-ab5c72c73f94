#!/usr/bin/env python3
"""
Simple test server for Dashboard styling validation
"""
import http.server
import socketserver
import os
from urllib.parse import urlparse

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        # Parse the URL
        parsed_path = urlparse(self.path)
        path = parsed_path.path
        
        # Route handling
        if path == '/' or path == '/dashboard':
            # Serve dashboard.html
            self.serve_file('templates/dashboard.html', 'text/html')
        elif path == '/component-showcase':
            # Serve component-showcase.html
            self.serve_file('templates/component-showcase.html', 'text/html')
        elif path == '/video-analyzer':
            # Serve Video Analyzer
            self.serve_file('templates/premium-video-analyzer.html', 'text/html')
        elif path == '/channel-analyzer':
            # Serve Channel Analyzer
            self.serve_file('templates/premium-channel-analyzer.html', 'text/html')
        elif path == '/market-research':
            # Serve Market Research Hub
            self.serve_file('templates/market-research-hub.html', 'text/html')
        elif path == '/comment-intelligence':
            # Serve Comment Intelligence
            self.serve_file('templates/premium-comment-intelligence.html', 'text/html')
        elif path == '/analytics':
            # Serve Analytics (redirect to dashboard for now)
            self.serve_file('templates/dashboard.html', 'text/html')
        elif path == '/reports':
            # Serve Reports (redirect to dashboard for now)
            self.serve_file('templates/dashboard.html', 'text/html')
        elif path == '/settings':
            # Serve Settings (redirect to dashboard for now)
            self.serve_file('templates/dashboard.html', 'text/html')
        elif path == '/help':
            # Serve Help (redirect to dashboard for now)
            self.serve_file('templates/dashboard.html', 'text/html')
        elif path.startswith('/static/'):
            # Serve static files
            file_path = path[1:]  # Remove leading slash
            if os.path.exists(file_path):
                self.serve_static_file(file_path)
            else:
                self.send_error(404, f"File not found: {file_path}")
        elif path.startswith('/templates/components/'):
            # Serve component templates
            file_path = path[1:]  # Remove leading slash
            if os.path.exists(file_path):
                self.serve_file(file_path, 'text/html')
            else:
                self.send_error(404, f"File not found: {file_path}")
        else:
            self.send_error(404, f"Path not found: {path}")
    
    def serve_file(self, file_path, content_type):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.send_header('Content-length', len(content.encode('utf-8')))
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, f"File not found: {file_path}")
        except Exception as e:
            self.send_error(500, f"Server error: {str(e)}")
    
    def serve_static_file(self, file_path):
        try:
            # Determine content type
            if file_path.endswith('.css'):
                content_type = 'text/css'
            elif file_path.endswith('.js'):
                content_type = 'application/javascript'
            elif file_path.endswith('.png'):
                content_type = 'image/png'
            elif file_path.endswith('.jpg') or file_path.endswith('.jpeg'):
                content_type = 'image/jpeg'
            elif file_path.endswith('.svg'):
                content_type = 'image/svg+xml'
            else:
                content_type = 'application/octet-stream'
            
            # Read file
            if content_type.startswith('text/') or content_type == 'application/javascript':
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                content_bytes = content.encode('utf-8')
            else:
                with open(file_path, 'rb') as f:
                    content_bytes = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', content_type)
            self.send_header('Content-length', len(content_bytes))
            self.end_headers()
            self.wfile.write(content_bytes)
        except Exception as e:
            self.send_error(500, f"Error serving static file: {str(e)}")

def run_server(port=8003):
    # Change to the correct directory
    os.chdir('/Users/<USER>/Documents/CrewAI/youtube-research-v2')
    
    with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
        print(f"YouTube Research v2 Server running at http://localhost:{port}/")
        print("Available routes:")
        print("  / - Dashboard")
        print("  /video-analyzer - Video Analyzer")
        print("  /channel-analyzer - Channel Analyzer")
        print("  /market-research - Market Research Hub")
        print("  /comment-intelligence - Comment Intelligence")
        print("  /component-showcase - Component Showcase")
        print("  /static/* - Static files")
        print("  /templates/components/* - Component templates")
        print("Press Ctrl+C to stop the server")
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    run_server()
