from crewai import Agent, Crew, Task
from crewai.project import Crew<PERSON><PERSON>, agent, crew, task
import yaml
from pathlib import Path

@CrewBase
class VideoAnalyzerCrew():
    """Video Analyzer Crew for deep video content analysis"""
    
    agents_config = 'config/video_analyzer/agents.yaml'
    tasks_config = 'config/video_analyzer/tasks.yaml'
    
    @agent
    def video_content_analyst(self) -> Agent:
        return Agent(
            config=self.agents_config['video_content_analyst'],
            tools=[],  # Add video analysis tools here
            verbose=True
        )
    
    @agent
    def engagement_optimizer(self) -> Agent:
        return Agent(
            config=self.agents_config['engagement_optimizer'],
            tools=[],  # Add engagement analysis tools here
            verbose=True
        )
    
    @task
    def analyze_video_content_task(self) -> Task:
        return Task(
            config=self.tasks_config['analyze_video_content'],
            agent=self.video_content_analyst()
        )
    
    @task
    def optimize_engagement_elements_task(self) -> Task:
        return Task(
            config=self.tasks_config['optimize_engagement_elements'],
            agent=self.engagement_optimizer()
        )
    
    @crew
    def crew(self) -> Crew:
        """Creates the Video Analyzer crew"""
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True
        )