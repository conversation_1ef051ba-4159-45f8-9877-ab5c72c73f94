#!/usr/bin/env python3
"""
Test script to check the data structure returned by analyze_video
"""

import os
import sys
import json
from working_crewai_app_tabbed import VideoAnalysisCrewAI

def test_data_structure():
    """Test the data structure without running expensive analysis"""
    
    print("🧪 Testing Data Structure")
    print("=" * 50)
    
    # Initialize the VideoAnalysisCrewAI
    video_ai = VideoAnalysisCrewAI()
    
    # Test with AI disabled to see the structure
    original_ai_enabled = video_ai.ai_enabled
    video_ai.ai_enabled = False
    
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    print(f"🎬 Testing with video: {test_url}")
    print("🔧 AI disabled to check structure only")
    
    try:
        result = video_ai.analyze_video(test_url, "")
        
        print("✅ Analysis structure check completed")
        print(f"   Result keys: {list(result.keys())}")
        
        # Check each analysis section
        analysis_sections = [
            'performance_analysis',
            'script_analysis', 
            'seo_analysis',
            'psychology_analysis',
            'comment_analysis',
            'thumbnail_analysis',
            'strategic_synthesis'
        ]
        
        for section in analysis_sections:
            if section in result:
                section_data = result[section]
                print(f"\n📊 {section}:")
                print(f"   Type: {type(section_data)}")
                if isinstance(section_data, dict):
                    print(f"   Keys: {list(section_data.keys())}")
                    if 'error' in section_data:
                        print(f"   ❌ Error: {section_data['error']}")
                else:
                    print(f"   Value: {section_data}")
            else:
                print(f"\n❌ Missing: {section}")
        
        # Now test with AI enabled but mock the thumbnail analysis
        print(f"\n" + "="*50)
        print("🤖 Testing with AI enabled (thumbnail only)")
        video_ai.ai_enabled = original_ai_enabled
        
        if video_ai.ai_enabled:
            # Get video data first
            video_data = video_ai._get_video_data_from_url(test_url)
            if 'error' not in video_data:
                thumbnail_url = video_data.get('thumbnail_url', '')
                if thumbnail_url:
                    print(f"🖼️  Testing thumbnail analysis only...")
                    thumbnail_result = video_ai._run_thumbnail_intelligence_agent(video_data, thumbnail_url)
                    print(f"   Thumbnail result type: {type(thumbnail_result)}")
                    print(f"   Thumbnail result keys: {list(thumbnail_result.keys()) if isinstance(thumbnail_result, dict) else 'Not a dict'}")
                    
                    if isinstance(thumbnail_result, dict) and 'analysis' in thumbnail_result:
                        print(f"   ✅ Thumbnail analysis generated: {len(thumbnail_result['analysis'])} chars")
                    else:
                        print(f"   ❌ No analysis in thumbnail result")
                        
    except Exception as e:
        print(f"❌ Exception: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_data_structure()
