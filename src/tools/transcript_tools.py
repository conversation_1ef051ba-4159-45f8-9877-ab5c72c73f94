"""
Smart Transcript Processing Tools
Handles both timestamped and plain text transcripts with graceful fallbacks
"""

import os
import json
import logging
from typing import Dict, Any, Optional, List, Union
from youtube_transcript_api import YouTubeTranscriptApi
from youtube_transcript_api._errors import TranscriptsDisabled, NoTranscriptFound

logger = logging.getLogger(__name__)


class SmartTranscriptProcessor:
    """
    Smart transcript processing that handles multiple scenarios:
    1. Auto-fetch timestamped transcripts from YouTube
    2. Process manually provided transcripts (timestamped or plain)
    3. Graceful fallbacks when transcripts aren't available
    4. Timing analysis when timestamps are present
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_transcript_data(self, video_id: str, manual_transcript: str = None) -> Dict[str, Any]:
        """
        Get transcript data with smart fallback logic
        
        Args:
            video_id: YouTube video ID  
            manual_transcript: Optional manual transcript provided by user
            
        Returns:
            Dictionary with transcript data and metadata
        """
        result = {
            'transcript_text': '',
            'transcript_segments': [],
            'has_timestamps': False,
            'source': 'none',
            'timing_analysis': None,
            'error': None
        }
        
        # Priority 1: Try auto-fetch timestamped transcript
        if video_id:
            auto_result = self._fetch_auto_transcript(video_id)
            if auto_result['success']:
                result.update(auto_result['data'])
                result['source'] = 'auto_timestamped'
                return result
            else:
                result['error'] = auto_result['error']
        
        # Priority 2: Use manual transcript if provided
        if manual_transcript and manual_transcript.strip():
            manual_result = self._process_manual_transcript(manual_transcript)
            result.update(manual_result)
            return result
            
        # Priority 3: No transcript available
        result['error'] = "No transcript available - auto-fetch failed and no manual transcript provided"
        return result
    
    def _fetch_auto_transcript(self, video_id: str) -> Dict[str, Any]:
        """Fetch transcript automatically from YouTube"""
        try:
            # Get transcript with timestamps
            transcript_list = YouTubeTranscriptApi.get_transcript(video_id)
            
            # Process timestamped segments
            segments = []
            full_text_parts = []
            
            for entry in transcript_list:
                segment = {
                    'text': entry['text'].strip(),
                    'start': entry['start'],
                    'duration': entry['duration']
                }
                segments.append(segment)
                full_text_parts.append(entry['text'].strip())
            
            # Create plain text version
            full_text = ' '.join(full_text_parts)
            
            # Generate timing analysis
            timing_analysis = self._analyze_timing(segments) if segments else None
            
            return {
                'success': True,
                'data': {
                    'transcript_text': full_text,
                    'transcript_segments': segments,
                    'has_timestamps': True,
                    'timing_analysis': timing_analysis
                }
            }
            
        except TranscriptsDisabled:
            return {'success': False, 'error': 'Transcripts are disabled for this video'}
        except NoTranscriptFound:
            return {'success': False, 'error': 'No transcript found for this video'}
        except Exception as e:
            return {'success': False, 'error': f'Error fetching transcript: {str(e)}'}
    
    def _process_manual_transcript(self, manual_transcript: str) -> Dict[str, Any]:
        """Process manually provided transcript (timestamped or plain)"""
        
        # Check if manual transcript has timestamps
        has_timestamps = self._detect_timestamps(manual_transcript)
        
        if has_timestamps:
            # Parse timestamped format
            segments = self._parse_timestamped_transcript(manual_transcript)
            full_text = ' '.join([seg['text'] for seg in segments])
            timing_analysis = self._analyze_timing(segments)
            
            return {
                'transcript_text': full_text,
                'transcript_segments': segments,
                'has_timestamps': True,
                'source': 'manual_timestamped',
                'timing_analysis': timing_analysis
            }
        else:
            # Plain text transcript
            return {
                'transcript_text': manual_transcript.strip(),
                'transcript_segments': [],
                'has_timestamps': False,
                'source': 'manual_plain',
                'timing_analysis': None
            }
    
    def _detect_timestamps(self, transcript: str) -> bool:
        """Detect if transcript contains timestamps"""
        import re
        
        # Common timestamp patterns
        patterns = [
            r'\[\d{1,2}:\d{2}\]',  # [00:00], [5:30]
            r'\(\d{1,2}:\d{2}\)',  # (00:00), (5:30)
            r'\d{1,2}:\d{2}\s',    # 00:00 , 5:30 
            r'^\d{1,2}:\d{2}',     # Starting with timestamp
        ]
        
        for pattern in patterns:
            if re.search(pattern, transcript):
                return True
        return False
    
    def _parse_timestamped_transcript(self, transcript: str) -> List[Dict[str, Any]]:
        """Parse timestamped transcript into segments"""
        import re
        
        segments = []
        
        # Split by common timestamp patterns
        # Handle [MM:SS] format
        if re.search(r'\[\d{1,2}:\d{2}\]', transcript):
            parts = re.split(r'\[(\d{1,2}:\d{2})\]', transcript)
            for i in range(1, len(parts), 2):
                if i + 1 < len(parts):
                    timestamp = parts[i]
                    text = parts[i + 1].strip()
                    if text:
                        start_seconds = self._timestamp_to_seconds(timestamp)
                        segments.append({
                            'text': text,
                            'start': start_seconds,
                            'duration': 5.0  # Default duration
                        })
        
        # Handle (MM:SS) format  
        elif re.search(r'\(\d{1,2}:\d{2}\)', transcript):
            parts = re.split(r'\((\d{1,2}:\d{2})\)', transcript)
            for i in range(1, len(parts), 2):
                if i + 1 < len(parts):
                    timestamp = parts[i]
                    text = parts[i + 1].strip()
                    if text:
                        start_seconds = self._timestamp_to_seconds(timestamp)
                        segments.append({
                            'text': text,
                            'start': start_seconds,
                            'duration': 5.0
                        })
        
        # Handle line-by-line MM:SS format
        else:
            lines = transcript.split('\n')
            for line in lines:
                match = re.match(r'^(\d{1,2}:\d{2})\s+(.+)', line.strip())
                if match:
                    timestamp = match.group(1)
                    text = match.group(2).strip()
                    if text:
                        start_seconds = self._timestamp_to_seconds(timestamp)
                        segments.append({
                            'text': text,
                            'start': start_seconds,
                            'duration': 5.0
                        })
        
        return segments
    
    def _timestamp_to_seconds(self, timestamp: str) -> float:
        """Convert MM:SS or HH:MM:SS to seconds"""
        parts = timestamp.split(':')
        if len(parts) == 2:  # MM:SS
            return int(parts[0]) * 60 + int(parts[1])
        elif len(parts) == 3:  # HH:MM:SS
            return int(parts[0]) * 3600 + int(parts[1]) * 60 + int(parts[2])
        return 0.0
    
    def _analyze_timing(self, segments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze timing patterns in transcript segments"""
        if not segments:
            return None
        
        # Calculate timing statistics
        durations = [seg['duration'] for seg in segments]
        total_duration = max([seg['start'] + seg['duration'] for seg in segments])
        
        # Analyze pacing
        words_per_segment = []
        for seg in segments:
            word_count = len(seg['text'].split())
            words_per_segment.append(word_count)
        
        avg_words_per_segment = sum(words_per_segment) / len(words_per_segment) if words_per_segment else 0
        avg_duration = sum(durations) / len(durations) if durations else 0
        
        # Calculate speaking rate (words per minute)
        total_words = sum(words_per_segment)
        speaking_rate = (total_words / total_duration) * 60 if total_duration > 0 else 0
        
        return {
            'total_segments': len(segments),
            'total_duration_seconds': total_duration,
            'total_words': total_words,
            'average_segment_duration': avg_duration,
            'average_words_per_segment': avg_words_per_segment,
            'speaking_rate_wpm': speaking_rate,
            'pacing_analysis': self._analyze_pacing_patterns(segments)
        }
    
    def _analyze_pacing_patterns(self, segments: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze pacing patterns for timing insights"""
        if not segments:
            return {}
        
        # Identify fast/slow sections
        word_densities = []
        for seg in segments:
            words = len(seg['text'].split())
            density = words / seg['duration'] if seg['duration'] > 0 else 0
            word_densities.append(density)
        
        avg_density = sum(word_densities) / len(word_densities) if word_densities else 0
        
        fast_sections = []
        slow_sections = []
        
        for i, density in enumerate(word_densities):
            if density > avg_density * 1.3:  # 30% above average
                fast_sections.append({
                    'start': segments[i]['start'],
                    'text_preview': segments[i]['text'][:50] + '...',
                    'density': density
                })
            elif density < avg_density * 0.7:  # 30% below average
                slow_sections.append({
                    'start': segments[i]['start'],
                    'text_preview': segments[i]['text'][:50] + '...',
                    'density': density
                })
        
        return {
            'average_word_density': avg_density,
            'fast_sections': fast_sections[:5],  # Top 5
            'slow_sections': slow_sections[:5],   # Top 5
            'pacing_variation': max(word_densities) - min(word_densities) if word_densities else 0
        }
    
    def format_transcript_for_agents(self, transcript_data: Dict[str, Any], include_timing: bool = True) -> str:
        """
        Format transcript data for agent analysis
        
        Args:
            transcript_data: Result from get_transcript_data()
            include_timing: Whether to include timing analysis in output
            
        Returns:
            Formatted string for agent consumption
        """
        
        if transcript_data.get('error'):
            return f"TRANSCRIPT ERROR: {transcript_data['error']}"
        
        output_parts = []
        
        # Add transcript source info
        source_info = f"TRANSCRIPT SOURCE: {transcript_data['source']}"
        if transcript_data['has_timestamps']:
            source_info += " (with timestamps)"
        output_parts.append(source_info)
        
        # Add main transcript text
        output_parts.append(f"TRANSCRIPT TEXT:\n{transcript_data['transcript_text']}")
        
        # Add timing analysis if available and requested
        if include_timing and transcript_data['has_timestamps'] and transcript_data['timing_analysis']:
            timing = transcript_data['timing_analysis']
            
            timing_info = f"""
TIMING ANALYSIS:
- Total Duration: {timing['total_duration_seconds']:.1f} seconds
- Total Words: {timing['total_words']}
- Speaking Rate: {timing['speaking_rate_wpm']:.1f} words per minute
- Average Segment Length: {timing['average_segment_duration']:.1f} seconds
- Pacing Variation: {timing['pacing_analysis']['pacing_variation']:.2f}"""

            if timing['pacing_analysis']['fast_sections']:
                timing_info += "\n\nFAST PACED SECTIONS:"
                for section in timing['pacing_analysis']['fast_sections']:
                    timing_info += f"\n- {section['start']:.1f}s: {section['text_preview']}"
            
            if timing['pacing_analysis']['slow_sections']:
                timing_info += "\n\nSLOW PACED SECTIONS:"
                for section in timing['pacing_analysis']['slow_sections']:
                    timing_info += f"\n- {section['start']:.1f}s: {section['text_preview']}"
                    
            output_parts.append(timing_info)
        
        return '\n\n'.join(output_parts)


# Convenience function for easy integration
def get_smart_transcript(video_id: str = None, manual_transcript: str = None) -> Dict[str, Any]:
    """
    Simple function to get transcript data with smart fallbacks
    
    Args:
        video_id: YouTube video ID for auto-fetching
        manual_transcript: Manual transcript text (optional)
        
    Returns:
        Processed transcript data
    """
    processor = SmartTranscriptProcessor()
    return processor.get_transcript_data(video_id, manual_transcript)