/**
 * YouTube Research v2 - Analytics & Metrics Tracking System
 * Implements Pirate Metrics (AARRR) funnel tracking with Mixpanel integration
 * 
 * Key Metrics Tracked:
 * - Activation: >60% within 7 days
 * - 3-month logo churn: <5%
 * - Net revenue retention: >120%
 */

class AnalyticsTracker {
    constructor() {
        this.config = {
            mixpanelToken: this.getMixpanelToken(),
            environment: this.getEnvironment(),
            userId: this.getUserId(),
            sessionId: this.generateSessionId(),
            startTime: Date.now(),
            debugMode: this.isDebugMode()
        };

        this.activationEvents = [
            'video_analyzed',
            'channel_analyzed', 
            'first_report_generated',
            'dashboard_viewed',
            'tool_used'
        ];

        this.retentionEvents = [
            'daily_login',
            'weekly_active',
            'monthly_active',
            'feature_used',
            'report_exported'
        ];

        this.revenueEvents = [
            'subscription_started',
            'subscription_upgraded',
            'subscription_renewed',
            'payment_completed',
            'trial_converted'
        ];

        this.init();
    }

    init() {
        this.initializeMixpanel();
        this.setupPageTracking();
        this.setupUserTracking();
        this.setupErrorTracking();
        this.trackPageLoad();
        this.startSessionTracking();
        
        if (this.config.debugMode) {
            console.log('🔍 Analytics Tracker initialized:', this.config);
        }
    }

    // Mixpanel Integration
    initializeMixpanel() {
        if (!this.config.mixpanelToken) {
            console.warn('Mixpanel token not found. Analytics will be logged locally.');
            return;
        }

        try {
            // Load Mixpanel SDK
            if (typeof mixpanel === 'undefined') {
                this.loadMixpanelSDK();
            } else {
                this.configureMixpanel();
            }
        } catch (error) {
            console.error('Failed to initialize Mixpanel:', error);
        }
    }

    loadMixpanelSDK() {
        const script = document.createElement('script');
        script.src = 'https://cdn.mxpnl.com/libs/mixpanel-2-latest.min.js';
        script.onload = () => this.configureMixpanel();
        document.head.appendChild(script);
    }

    configureMixpanel() {
        if (typeof mixpanel !== 'undefined') {
            mixpanel.init(this.config.mixpanelToken, {
                debug: this.config.debugMode,
                track_pageview: true,
                persistence: 'localStorage',
                batch_requests: true,
                cross_subdomain_cookie: true,
                secure_cookie: true
            });

            // Identify user
            if (this.config.userId) {
                mixpanel.identify(this.config.userId);
                this.setUserProperties();
            }
        }
    }

    // AARRR Funnel Tracking
    
    // Acquisition Tracking
    trackAcquisition(source, medium = '', campaign = '') {
        const acquisitionData = {
            source: source,
            medium: medium,
            campaign: campaign,
            landing_page: window.location.pathname,
            referrer: document.referrer,
            timestamp: new Date().toISOString()
        };

        this.trackEvent('user_acquired', acquisitionData);
        this.storeUserProperty('acquisition_source', source);
        this.storeUserProperty('acquisition_date', new Date().toISOString());
    }

    // Activation Tracking (60% target within 7 days)
    trackActivation(eventType, metadata = {}) {
        if (!this.activationEvents.includes(eventType)) {
            console.warn(`Unknown activation event: ${eventType}`);
            return;
        }

        const activationData = {
            event_type: eventType,
            user_id: this.config.userId,
            session_id: this.config.sessionId,
            days_since_signup: this.getDaysSinceSignup(),
            is_trial_user: this.isTrialUser(),
            plan_type: this.getUserProperty('planType', 'Free'),
            ...metadata
        };

        this.trackEvent('activation_event', activationData);
        this.updateActivationProgress(eventType);

        // Check if user has achieved activation threshold
        if (this.checkActivationThreshold()) {
            this.trackEvent('user_activated', {
                activation_time: Date.now() - this.getSignupTime(),
                activation_events_completed: this.getCompletedActivationEvents().length
            });
        }
    }

    // Revenue Tracking (120% NRR target)
    trackRevenue(eventType, amount, currency = 'USD', metadata = {}) {
        const revenueData = {
            event_type: eventType,
            amount: amount,
            currency: currency,
            user_id: this.config.userId,
            plan_type: metadata.planType || this.getUserProperty('planType'),
            billing_cycle: metadata.billingCycle,
            mrr_impact: this.calculateMRRImpact(eventType, amount, metadata.billingCycle),
            timestamp: new Date().toISOString(),
            ...metadata
        };

        this.trackEvent('revenue_event', revenueData);
        this.updateRevenueMetrics(revenueData);

        // Track with Mixpanel revenue tracking
        if (typeof mixpanel !== 'undefined') {
            mixpanel.track('$transaction', {
                '$amount': amount,
                '$currency': currency,
                'event_type': eventType
            });
        }
    }

    // Feature Usage Tracking
    trackFeatureUsage(featureName, action, metadata = {}) {
        const featureData = {
            feature_name: featureName,
            action: action,
            user_id: this.config.userId,
            session_id: this.config.sessionId,
            timestamp: new Date().toISOString(),
            page_url: window.location.pathname,
            ...metadata
        };

        this.trackEvent('feature_used', featureData);
        
        // Check if this is an activation event
        if (this.activationEvents.includes(action)) {
            this.trackActivation(action, { feature_name: featureName });
        }
    }

    // Core Tracking Methods
    trackEvent(eventName, properties = {}) {
        const eventData = {
            ...properties,
            session_id: this.config.sessionId,
            user_id: this.config.userId,
            timestamp: new Date().toISOString(),
            environment: this.config.environment,
            page_url: window.location.pathname,
            referrer: document.referrer
        };

        // Send to Mixpanel
        if (typeof mixpanel !== 'undefined') {
            mixpanel.track(eventName, eventData);
        }

        // Local logging for development
        if (this.config.debugMode) {
            console.log(`📈 Analytics Event: ${eventName}`, eventData);
        }

        // Store locally as backup
        this.storeEventLocally(eventName, eventData);

        // Send to server if endpoint available
        this.sendToServer(eventName, eventData);
    }

    // Helper Methods
    getMixpanelToken() {
        return window.MIXPANEL_TOKEN || document.querySelector('meta[name="mixpanel-token"]')?.content || null;
    }

    getEnvironment() {
        return window.ENVIRONMENT || (window.location.hostname.includes('localhost') ? 'development' : 'production');
    }

    getUserId() {
        return window.USER_ID || localStorage.getItem('user_id') || this.generateUserId();
    }

    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    generateUserId() {
        const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        localStorage.setItem('user_id', userId);
        return userId;
    }

    isDebugMode() {
        return window.DEBUG_ANALYTICS || localStorage.getItem('debug_analytics') === 'true';
    }

    getUserProperty(key, defaultValue = null) {
        return window.USER_PROPERTIES?.[key] || localStorage.getItem(`user_${key}`) || defaultValue;
    }

    storeUserProperty(key, value) {
        localStorage.setItem(`user_${key}`, value);
    }

    getDaysSinceSignup() {
        const signupDate = this.getUserProperty('signupDate');
        if (!signupDate) return 0;
        return Math.floor((Date.now() - new Date(signupDate).getTime()) / (1000 * 60 * 60 * 24));
    }

    isTrialUser() {
        return this.getUserProperty('planType') === 'Trial';
    }

    getSignupTime() {
        const signupDate = this.getUserProperty('signupDate');
        return signupDate ? new Date(signupDate).getTime() : Date.now();
    }

    // Activation Logic
    checkActivationThreshold() {
        const completedEvents = this.getCompletedActivationEvents();
        return completedEvents.length >= 3; // Activation requires 3+ key actions
    }

    getCompletedActivationEvents() {
        const completed = localStorage.getItem('completed_activation_events');
        return completed ? JSON.parse(completed) : [];
    }

    updateActivationProgress(eventType) {
        const completed = this.getCompletedActivationEvents();
        if (!completed.includes(eventType)) {
            completed.push(eventType);
            localStorage.setItem('completed_activation_events', JSON.stringify(completed));
        }
    }

    // Revenue Calculations
    calculateMRRImpact(eventType, amount, billingCycle) {
        if (eventType === 'subscription_started' || eventType === 'subscription_upgraded') {
            switch (billingCycle) {
                case 'monthly': return amount;
                case 'annual': return amount / 12;
                case 'quarterly': return amount / 3;
                default: return 0;
            }
        }
        return 0;
    }

    updateRevenueMetrics(revenueData) {
        // Store revenue data locally for NRR calculations
        const revenueHistory = JSON.parse(localStorage.getItem('revenue_history') || '[]');
        revenueHistory.push(revenueData);
        localStorage.setItem('revenue_history', JSON.stringify(revenueHistory));
    }

    // Data Storage & Transmission
    storeEventLocally(eventName, eventData) {
        const events = JSON.parse(localStorage.getItem('analytics_events') || '[]');
        events.push({ event: eventName, data: eventData, timestamp: Date.now() });
        
        // Keep only last 1000 events locally
        if (events.length > 1000) {
            events.splice(0, events.length - 1000);
        }
        
        localStorage.setItem('analytics_events', JSON.stringify(events));
    }

    sendToServer(eventName, eventData) {
        // Send to server analytics endpoint if available
        const endpoint = '/api/analytics/track';
        
        fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ event: eventName, data: eventData })
        }).catch(error => {
            // Silently fail if server endpoint not available
            if (this.config.debugMode) {
                console.warn('Failed to send analytics to server:', error);
            }
        });
    }

    // Page Tracking Setup
    setupPageTracking() {
        // Track page views
        this.trackEvent('page_viewed', {
            page_url: window.location.pathname,
            page_title: document.title,
            referrer: document.referrer
        });
    }

    setupUserTracking() {
        // Track user interactions with data-track attributes
        document.addEventListener('click', (e) => {
            const element = e.target.closest('[data-track]');
            if (element) {
                const trackingData = element.dataset.track;
                try {
                    const data = JSON.parse(trackingData);
                    this.trackEvent('element_clicked', {
                        element_type: element.tagName.toLowerCase(),
                        element_text: element.textContent?.trim().substring(0, 100),
                        ...data
                    });
                } catch (error) {
                    this.trackEvent('element_clicked', {
                        element_type: element.tagName.toLowerCase(),
                        element_text: element.textContent?.trim().substring(0, 100),
                        track_data: trackingData
                    });
                }
            }
        });
    }

    setupErrorTracking() {
        window.addEventListener('error', (e) => {
            this.trackEvent('error_occurred', {
                error_message: e.message,
                source: e.filename,
                line: e.lineno,
                column: e.colno
            });
        });
    }

    trackPageLoad() {
        this.trackEvent('page_loaded', {
            load_time: Date.now() - this.config.startTime,
            user_agent: navigator.userAgent,
            screen_resolution: `${screen.width}x${screen.height}`,
            viewport_size: `${window.innerWidth}x${window.innerHeight}`
        });
    }

    startSessionTracking() {
        // Track session activity every 30 seconds
        setInterval(() => {
            this.trackEvent('session_active', {
                session_duration: Date.now() - this.config.startTime
            });
        }, 30000);
    }

    // Public API Methods
    static getInstance() {
        if (!window.analyticsTracker) {
            window.analyticsTracker = new AnalyticsTracker();
        }
        return window.analyticsTracker;
    }

    // Convenience methods for external use
    static track(eventName, properties = {}) {
        return AnalyticsTracker.getInstance().trackEvent(eventName, properties);
    }

    static trackFeature(featureName, action, metadata = {}) {
        return AnalyticsTracker.getInstance().trackFeatureUsage(featureName, action, metadata);
    }

    static trackActivation(eventType, metadata = {}) {
        return AnalyticsTracker.getInstance().trackActivation(eventType, metadata);
    }

    static trackRevenue(eventType, amount, currency = 'USD', metadata = {}) {
        return AnalyticsTracker.getInstance().trackRevenue(eventType, amount, currency, metadata);
    }
}

// Initialize analytics when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        AnalyticsTracker.getInstance();
    });
} else {
    AnalyticsTracker.getInstance();
}

// Export for external use
window.AnalyticsTracker = AnalyticsTracker;
window.analytics = AnalyticsTracker;

// Console tip for developers
console.log('📊 Analytics Tracker loaded. Use analytics.track("event_name", {data}) to track custom events.');
