# YouTube Research v2 - Design System Documentation

## 🎨 Modern Design System Overview

Professional SaaS-grade design system with glass morphism effects, purple-pink gradients, and modern typography.

---

## Color Palette

### Primary Colors
```css
--primary-gradient: linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%);
--primary-purple: #8B5CF6;
--primary-pink: #EC4899;
```

### Secondary Colors
```css
--secondary-orange: #F97316;
--success-green: #22C55E;
--warning-yellow: #F59E0B;
--error-red: #EF4444;
```

### Neutral Colors
```css
--background: #0A0A0B;      /* Deep charcoal */
--surface: #1F1F23;         /* Elevated dark */
--surface-hover: #2A2A2F;   /* Hover state */
--text-primary: #FAFAFA;    /* High contrast white */
--text-secondary: #9CA3AF;  /* Muted gray */
--border: rgba(255, 255, 255, 0.05);
```

---

## Typography

### Font Stack
```css
--font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
--font-mono: 'JetBrains Mono', 'SF Mono', Consolas, monospace;
```

### Type Scale
```css
--heading-display: clamp(2.5rem, 5vw, 4rem);   /* 40-64px responsive */
--heading-1: clamp(2rem, 4vw, 3rem);           /* 32-48px responsive */
--heading-2: clamp(1.5rem, 3vw, 2rem);         /* 24-32px responsive */
--heading-3: 1.25rem;                           /* 20px */
--text-body: 1rem;                              /* 16px */
--text-small: 0.875rem;                         /* 14px */
```

### Font Weights
```css
--font-normal: 400;
--font-medium: 500;
--font-semibold: 600;
--font-bold: 700;
--font-extrabold: 800;
```

---

## Spacing System

```css
--space-xs: 0.5rem;    /* 8px */
--space-sm: 1rem;      /* 16px */
--space-md: 1.5rem;    /* 24px */
--space-lg: 2rem;      /* 32px */
--space-xl: 3rem;      /* 48px */
--space-2xl: 4rem;     /* 64px */
```

---

## Border Radius

```css
--radius-sm: 0.75rem;   /* 12px */
--radius-md: 1rem;      /* 16px */
--radius-lg: 1.5rem;    /* 24px */
--radius-xl: 2rem;      /* 32px */
```

---

## Core Components

### Glass Card
```css
.card {
    background: rgba(31, 31, 35, 0.6);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.card-gradient-border:hover {
    background: linear-gradient(var(--surface), var(--surface)) padding-box,
                var(--primary-gradient) border-box;
    border: 2px solid transparent;
}
```

### Primary Button
```css
.btn-primary {
    background: var(--primary-gradient);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-md);
    font-weight: var(--font-semibold);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(139, 92, 246, 0.4);
}
```

### Feature Card
```css
.feature-card {
    position: relative;
    padding: var(--space-xl);
    transition: all 300ms ease;
}

.feature-badge {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    padding: 0.375rem 0.75rem;
    background: rgba(139, 92, 246, 0.1);
    border: 1px solid rgba(139, 92, 246, 0.3);
    border-radius: var(--radius-sm);
    font-size: var(--text-small);
    color: #A78BFA;
}
```

### Stat Card
```css
.stat-card {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
}

.stat-icon {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-gradient);
    border-radius: var(--radius-md);
    font-size: 1.5rem;
}

.stat-trend {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: rgba(34, 197, 94, 0.1);
    border-radius: var(--radius-sm);
    color: #22C55E;
}
```

---

## Animation System

### Timing Functions
```css
--ease-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
--ease-base: 300ms cubic-bezier(0.4, 0, 0.2, 1);
--ease-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
```

### Common Animations
```css
/* Fade In */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Pulse */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Shimmer */
@keyframes shimmer {
    0% { background-position: -200% center; }
    100% { background-position: 200% center; }
}

/* Float */
@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}
```

### Stagger Children
```css
.stagger-children > * {
    animation: fadeIn var(--ease-base) backwards;
}

.stagger-children > *:nth-child(1) { animation-delay: 50ms; }
.stagger-children > *:nth-child(2) { animation-delay: 100ms; }
.stagger-children > *:nth-child(3) { animation-delay: 150ms; }
.stagger-children > *:nth-child(4) { animation-delay: 200ms; }
```

---

## Responsive Design

### Breakpoints
```css
/* Mobile First Approach */
--breakpoint-sm: 640px;
--breakpoint-md: 768px;
--breakpoint-lg: 1024px;
--breakpoint-xl: 1280px;
--breakpoint-2xl: 1536px;
```

### Grid System
```css
.grid {
    display: grid;
    gap: var(--space-md);
}

/* Responsive columns */
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }

@media (min-width: 768px) {
    .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}
```

### Container
```css
.container {
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--space-md);
}

@media (min-width: 768px) {
    .container { padding: 0 var(--space-lg); }
}
```

---

## Performance Patterns

### Glass Morphism
```css
.glass {
    background: rgba(31, 31, 35, 0.6);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}
```

### GPU Acceleration
```css
.gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
}
```

### Reduced Motion
```css
@media (prefers-reduced-motion: reduce) {
    *, *::before, *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
```

---

## Navigation System

### Dynamic Height System
```css
:root {
    --nav-height: 80px; /* Default fallback */
    --nav-offset: calc(var(--nav-height) + var(--space-md));
}

.nav-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    height: auto; /* Let content determine height */
}

.dashboard-layout,
.page-layout {
    padding-top: max(var(--nav-offset), 80px);
}
```

---

## Video Analyzer Components

### Grade Circle
```css
.grade-circle {
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: conic-gradient(from 180deg, #8B5CF6, #EC4899, #8B5CF6);
    border-radius: 50%;
    position: relative;
    animation: gradePulse 3s ease-in-out infinite;
}

.grade-circle::before {
    content: '';
    position: absolute;
    inset: 4px;
    background: var(--surface);
    border-radius: 50%;
}

.grade-text {
    position: relative;
    font-size: 2.5rem;
    font-weight: var(--font-extrabold);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}
```

### Progress Bar
```css
.progress-bar {
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 999px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: 999px;
    position: relative;
    animation: progressFill 1s ease-out forwards;
}

.progress-fill::after {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: shimmer 2s linear infinite;
}
```

---

## Performance Classifications

### Viral Performance
```css
.viral-performance {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
    border: 1px solid rgba(34, 197, 94, 0.3);
    color: #22C55E;
}
```

### Strong Performance
```css
.strong-performance {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%);
    border: 1px solid rgba(59, 130, 246, 0.3);
    color: #3B82F6;
}
```

### Weak Performance
```css
.weak-performance {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
    border: 1px solid rgba(239, 68, 68, 0.3);
    color: #EF4444;
}
```

---

## Usage Guidelines

1. **Always use CSS variables** for colors, spacing, and typography
2. **Prefer utility classes** over inline styles
3. **Use semantic HTML** with appropriate ARIA labels
4. **Test responsive behavior** at all breakpoints
5. **Ensure keyboard navigation** works properly
6. **Maintain consistent spacing** using the spacing scale
7. **Apply animations sparingly** for better performance
8. **Use GPU acceleration** for smooth animations

---

*This design system ensures consistent, professional UI across the entire application.*