#!/usr/bin/env python3
"""
Debug test specifically for recommendations parsing
"""

import requests
import json

def test_debug_recommendations():
    """Test to trigger detailed recommendations debugging"""
    print("🔍 Debug Recommendations Test")
    print("=" * 40)
    
    try:
        response = requests.post(
            "http://localhost:8003/api/market-research",
            json={
                "query": "cooking tips",
                "time_range": "week",
                "max_results": 1,
                "sort_by": "relevance"
            },
            timeout=70
        )
        
        if response.status_code == 200:
            data = response.json()
            ai_insights = data.get('ai_insights', {})
            recommendations = ai_insights.get('recommendations', [])
            
            print(f"✅ API Response: SUCCESS")
            print(f"📋 Recommendations: {len(recommendations)} items")
            
            if recommendations:
                for i, rec in enumerate(recommendations):
                    print(f"   {i+1}. {rec.get('title', 'No title')}")
                    print(f"      {rec.get('description', 'No description')[:100]}...")
                    print(f"      Priority: {rec.get('priority', 'No priority')}")
            else:
                print("   ❌ No recommendations found")
                
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Test Failed: {e}")

if __name__ == "__main__":
    test_debug_recommendations()
