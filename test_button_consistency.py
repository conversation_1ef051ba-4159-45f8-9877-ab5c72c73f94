#!/usr/bin/env python3
"""
Test Button Styling Consistency Across All Pages
"""

import requests
import time
import subprocess
import sys
import os
from datetime import datetime

class ButtonConsistencyTester:
    def __init__(self):
        self.base_url = "http://localhost:8003"
        self.server_process = None
        self.test_pages = [
            ("/", "Dashboard"),
            ("/video-analyzer", "Video Analyzer"),
            ("/channel-analyzer", "Channel Analyzer"),
            ("/market-research", "Market Research Hub"),
            ("/agent/comment", "Comment Intelligence")
        ]
    
    def start_server(self, timeout=30):
        """Start the main application server"""
        print("🚀 Starting server...")
        try:
            self.server_process = subprocess.Popen(
                [sys.executable, "working_crewai_app_tabbed.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=5)
                    if response.status_code == 200:
                        print("✅ Server started successfully")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)
                    continue
            
            print("❌ Server failed to start within timeout")
            return False
            
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return False
    
    def stop_server(self):
        """Stop the server"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                print("🛑 Server stopped")
            except subprocess.TimeoutExpired:
                self.server_process.kill()
                print("🛑 Server force killed")
            except Exception as e:
                print(f"⚠️ Error stopping server: {e}")
    
    def test_button_classes_in_pages(self):
        """Test if all pages use consistent button classes"""
        print("\n📄 Testing Button Classes Across Pages...")
        
        all_pages_pass = True
        
        for url, name in self.test_pages:
            try:
                response = requests.get(f"{self.base_url}{url}", timeout=10)
                if response.status_code == 200:
                    html_content = response.text
                    
                    # Check for button classes
                    button_classes = [
                        'btn',
                        'btn-primary',
                        'btn-secondary',
                        'btn-ghost'
                    ]
                    
                    found_buttons = []
                    for btn_class in button_classes:
                        if btn_class in html_content:
                            found_buttons.append(btn_class)
                    
                    if found_buttons:
                        print(f"   ✅ {name}: Found button classes: {', '.join(found_buttons)}")
                    else:
                        print(f"   ⚠️ {name}: No standard button classes found")
                        
                else:
                    print(f"   ❌ {name}: Failed to load (status: {response.status_code})")
                    all_pages_pass = False
                    
            except Exception as e:
                print(f"   ❌ {name}: Error loading page: {e}")
                all_pages_pass = False
        
        return all_pages_pass
    
    def test_refined_button_css(self):
        """Test if CSS contains refined button styles"""
        print("\n🎨 Testing Refined Button CSS...")
        try:
            response = requests.get(f"{self.base_url}/static/styles/youtube-research-v2-design-system.css", timeout=10)
            if response.status_code == 200:
                css_content = response.text
                
                # Check for refined button styles
                required_styles = [
                    'padding: var(--space-sm) var(--space-md)',  # Refined padding
                    'font-size: var(--text-sm)',  # Smaller font size
                    'line-height: 1.4',  # Better line height
                    '.btn-xs',  # Extra small variant
                    'transform: translateY(-0.5px)',  # Subtle hover
                    'filter: brightness(1.05)',  # Elegant brightness
                    'gap: var(--space-sm)'  # Consistent gap
                ]
                
                missing_styles = []
                for style in required_styles:
                    if style not in css_content:
                        missing_styles.append(style)
                
                if missing_styles:
                    print(f"❌ Missing refined button styles: {missing_styles}")
                    return False
                else:
                    print("✅ All refined button styles found")
                    
                    # Check button variants
                    button_variants = ['.btn', '.btn-primary', '.btn-secondary', '.btn-ghost', '.btn-sm', '.btn-lg', '.btn-xs']
                    found_variants = []
                    for variant in button_variants:
                        if variant in css_content:
                            found_variants.append(variant)
                    
                    print(f"✅ Button variants available: {', '.join(found_variants)}")
                    return True
            else:
                print(f"❌ CSS file not accessible: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error accessing CSS file: {e}")
            return False
    
    def test_design_system_consistency(self):
        """Test that all pages reference the same design system CSS"""
        print("\n🔗 Testing Design System Consistency...")
        
        all_consistent = True
        
        for url, name in self.test_pages:
            try:
                response = requests.get(f"{self.base_url}{url}", timeout=10)
                if response.status_code == 200:
                    html_content = response.text
                    
                    # Check for design system CSS reference
                    if 'youtube-research-v2-design-system.css' in html_content:
                        print(f"   ✅ {name}: Uses unified design system CSS")
                    else:
                        print(f"   ❌ {name}: Missing design system CSS reference")
                        all_consistent = False
                        
                    # Check for inline styles (should be minimal)
                    inline_style_count = html_content.count('style=')
                    if inline_style_count < 10:  # Allow some inline styles for dynamic content
                        print(f"   ✅ {name}: Minimal inline styles ({inline_style_count})")
                    else:
                        print(f"   ⚠️ {name}: Many inline styles ({inline_style_count}) - may affect consistency")
                        
                else:
                    print(f"   ❌ {name}: Failed to load (status: {response.status_code})")
                    all_consistent = False
                    
            except Exception as e:
                print(f"   ❌ {name}: Error loading page: {e}")
                all_consistent = False
        
        return all_consistent
    
    def run_tests(self):
        """Run all tests"""
        print("🔘 Button Styling Consistency Test")
        print("=" * 50)
        
        # Start server
        if not self.start_server():
            return False
        
        try:
            # Test button classes in pages
            classes_test = self.test_button_classes_in_pages()
            
            # Test refined button CSS
            css_test = self.test_refined_button_css()
            
            # Test design system consistency
            consistency_test = self.test_design_system_consistency()
            
            # Summary
            print(f"\n📊 Test Results:")
            print(f"   Button Classes: {'✅ PASS' if classes_test else '❌ FAIL'}")
            print(f"   Refined CSS: {'✅ PASS' if css_test else '❌ FAIL'}")
            print(f"   Design System: {'✅ PASS' if consistency_test else '❌ FAIL'}")
            
            if classes_test and css_test and consistency_test:
                print(f"\n🎉 All tests passed! Button styling is consistent across all pages.")
                print(f"   ✨ All pages use standard button classes (.btn, .btn-primary, etc.)")
                print(f"   ✨ Refined button styling with elegant padding and font sizes")
                print(f"   ✨ Consistent design system CSS across all pages")
                print(f"   ✨ Minimal inline styles for better maintainability")
                print(f"   ✨ Multiple button variants available (.btn-xs, .btn-sm, .btn-lg)")
                return True
            else:
                print(f"\n⚠️ Some tests failed. Button styling consistency needs attention.")
                return False
                
        finally:
            self.stop_server()

if __name__ == "__main__":
    tester = ButtonConsistencyTester()
    success = tester.run_tests()
    sys.exit(0 if success else 1)
