<!DOCTYPE html>
<html>
<head>
    <title>Debug JS Issue</title>
</head>
<body>
    <h1>Debug Channel Context Display</h1>
    <button onclick="testAnalysis()">Test Analysis</button>
    <div id="output"></div>
    
    <script>
        async function testAnalysis() {
            const output = document.getElementById('output');
            output.innerHTML = 'Starting analysis...';
            
            try {
                const response = await fetch('http://localhost:8003/analyze-video', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        video_url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                        script: ''
                    })
                });
                
                const result = await response.json();
                
                console.log('Full API Response:', result);
                
                output.innerHTML = `
                    <h2>API Response Analysis:</h2>
                    <p>Response keys: ${Object.keys(result).join(', ')}</p>
                    <p>Has video_data: ${result.video_data ? 'YES' : 'NO'}</p>
                    ${result.video_data ? `
                        <p>video_data keys: ${Object.keys(result.video_data).join(', ')}</p>
                        <p>Has channel_context: ${result.video_data.channel_context ? 'YES' : 'NO'}</p>
                        ${result.video_data.channel_context ? `
                            <p>Channel Name: ${result.video_data.channel_context.channel_name}</p>
                            <p>Channel Tier: ${result.video_data.channel_context.channel_tier}</p>
                        ` : ''}
                    ` : ''}
                    <h3>Raw JSON:</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
                
            } catch (error) {
                output.innerHTML = `Error: ${error.message}`;
                console.error('Error:', error);
            }
        }
    </script>
</body>
</html>