# Standalone Agents Usage Guide

## Overview

The YouTube Research v2 system has been modularized into **6 independent agents** that can be used standalone or together. Each agent specializes in a specific type of analysis and can operate independently.

---

## Available Agents

### 1. **PerformanceIntelligenceAgent**
- **Purpose**: Channel-relative metrics & performance benchmarks
- **Input**: Video data with metrics
- **Output**: Performance analysis with overperformance ratios and strategic recommendations

### 2. **ScriptForensicsAgent** 
- **Purpose**: Script structure & psychological triggers analysis
- **Input**: Video data + script text
- **Output**: Script DNA analysis with psychological triggers and replication blueprints

### 3. **SEODiscoverabilityAgent**
- **Purpose**: Keyword optimization & search intent analysis
- **Input**: Video data + optional script analysis
- **Output**: SEO strategy analysis with keyword recommendations

### 4. **AudiencePsychologyAgent**
- **Purpose**: Emotional triggers & viewer motivations
- **Input**: Video data + optional previous analyses
- **Output**: Audience psychology analysis with behavioral insights

### 5. **CommentIntelligenceAgent**
- **Purpose**: Real comment sentiment analysis
- **Input**: Video data with comments + optional previous analyses
- **Output**: Comment analysis with sentiment insights and community intelligence

### 6. **StrategicSynthesisAgent**
- **Purpose**: Cross-agent correlation & comprehensive strategy
- **Input**: All previous analyses + video data
- **Output**: Strategic synthesis with integrated success blueprints

---

## Quick Start

### Basic Usage

```python
from standalone_agents import StandaloneAgentInterface

# Initialize the interface
agents = StandaloneAgentInterface(use_sequential_thinking=True)

# Prepare your video data
video_data = {
    'title': 'Your Video Title',
    'views': 1000000,
    'likes': 50000,
    'comments': 5000,
    'duration_minutes': 10,
    'duration_seconds': 30,
    'published_date': '2025-01-20',
    'days_since_published': 5,
    # Optional: Channel context for better analysis
    'channel_context': {
        'channel_name': 'Your Channel',
        'channel_tier': 'Medium (100K-1M)',
        'channel_subscribers': 500000,
        'avg_views_per_video': 200000
    }
}

# Run a single agent
performance_analysis = agents.run_single_agent('performance', video_data=video_data)
print(performance_analysis['analysis'])
```

### Running All Agents (Original Flow)

```python
# Run all agents in sequence
script_text = "Your video script here..."
all_results = agents.run_all_agents(video_data, script_text)

# Access individual results
performance = all_results['performance']['analysis']
script_analysis = all_results['script']['analysis']
seo_analysis = all_results['seo']['analysis']
psychology = all_results['psychology']['analysis']
comments = all_results['comment']['analysis']
synthesis = all_results['synthesis']['analysis']
```

---

## Data Requirements

### Required Video Data Fields
```python
video_data = {
    'title': str,              # Video title
    'views': int,              # View count
    'likes': int,              # Like count  
    'comments': int,           # Comment count
    'duration_minutes': int,   # Duration minutes
    'duration_seconds': int,   # Duration seconds
    'published_date': str,     # Publication date
    'days_since_published': int # Days since publication
}
```

### Optional Enhanced Data
```python
# Channel context (highly recommended)
'channel_context': {
    'channel_name': str,
    'channel_tier': str,       # Nano, Micro, Small, Medium, Large, Mega
    'channel_subscribers': int,
    'avg_views_per_video': int,
    'channel_country': str
}

# Performance metrics (auto-calculated if channel_context provided)
'performance_metrics': {
    'overperformance_ratio': float,
    'subscriber_reach_rate': float,
    'performance_note': str
}

# Comments data (for Comment Intelligence Agent)
'comments_data': [str, str, ...]  # List of actual comments
```

---

## Individual Agent Usage

### Performance Intelligence Agent
```python
# Analyzes video performance relative to channel
result = agents.run_single_agent('performance', video_data=video_data)
```
**Best for**: Understanding how well a video performed relative to the channel's typical performance.

### Script Forensics Agent  
```python
# Analyzes script structure and psychological triggers
script = "Your video script text here..."
result = agents.run_single_agent('script', 
    video_data=video_data, 
    script=script, 
    performance_data=performance_result  # Optional
)
```
**Best for**: Understanding what makes a script psychologically effective.

### SEO & Discoverability Agent
```python
# Analyzes search optimization and discoverability
result = agents.run_single_agent('seo',
    video_data=video_data,
    script_analysis=script_result,      # Optional
    performance_data=performance_result # Optional
)
```
**Best for**: Understanding how content gets discovered and optimizing for search.

### Audience Psychology Agent
```python
# Analyzes viewer motivations and engagement psychology
result = agents.run_single_agent('psychology',
    video_data=video_data,
    script_analysis=script_result,      # Optional
    performance_data=performance_result # Optional
)
```
**Best for**: Understanding why audiences engage with content.

### Comment Intelligence Agent
```python
# Analyzes real audience feedback patterns
video_data['comments_data'] = ['comment1', 'comment2', ...]  # Add actual comments
result = agents.run_single_agent('comment',
    video_data=video_data,
    script_analysis=script_result,      # Optional
    performance_data=performance_result, # Optional
    seo_data=seo_result,               # Optional
    psychology_data=psychology_result   # Optional
)
```
**Best for**: Understanding actual audience sentiment and validating other analyses.

### Strategic Synthesis Agent
```python
# Synthesizes all analyses into comprehensive strategy
all_analyses = {
    'performance': performance_result,
    'script': script_result,
    'seo': seo_result,
    'psychology': psychology_result,
    'comment': comment_result
}
result = agents.run_single_agent('synthesis',
    all_analyses=all_analyses,
    video_data=video_data,
    channel_context=video_data.get('channel_context')
)
```
**Best for**: Getting comprehensive strategic recommendations based on all factors.

---

## Configuration Options

### Sequential Thinking
```python
# Enable deeper analysis (slower but higher quality)
agents = StandaloneAgentInterface(use_sequential_thinking=True)

# Faster analysis (good for testing)
agents = StandaloneAgentInterface(use_sequential_thinking=False)
```

### Individual Agent Initialization
```python
# Initialize specific agents directly
from standalone_agents import PerformanceIntelligenceAgent, ScriptForensicsAgent

performance_agent = PerformanceIntelligenceAgent(use_sequential_thinking=True)
script_agent = ScriptForensicsAgent(use_sequential_thinking=False)

# Use directly
result = performance_agent.analyze(video_data)
```

---

## Use Cases

### 1. **Quick Performance Check**
```python
# Just want to know how a video performed
agents = StandaloneAgentInterface(use_sequential_thinking=False)
result = agents.run_single_agent('performance', video_data=video_data)
```

### 2. **Script Analysis Only**
```python
# Analyzing script effectiveness
agents = StandaloneAgentInterface()
result = agents.run_single_agent('script', video_data=video_data, script=script_text)
```

### 3. **SEO Audit**
```python
# Understanding discoverability
result = agents.run_single_agent('seo', video_data=video_data)
```

### 4. **Community Analysis**
```python
# Understanding audience feedback
video_data['comments_data'] = get_youtube_comments(video_id)  # Your function
result = agents.run_single_agent('comment', video_data=video_data)
```

### 5. **Complete Analysis**
```python
# Full analysis like the original system
all_results = agents.run_all_agents(video_data, script_text)
```

---

## Error Handling

```python
try:
    result = agents.run_single_agent('performance', video_data=video_data)
    analysis = result['analysis']
except ValueError as e:
    print(f"Invalid agent name: {e}")
except Exception as e:
    print(f"Analysis failed: {e}")
```

---

## Testing

Run the test suite to verify everything works:

```bash
python test_standalone_agents.py
```

---

## Performance Notes

- **Sequential Thinking**: Adds ~5-10 seconds per agent but significantly improves analysis quality
- **Single Agent**: ~30-60 seconds depending on complexity
- **All Agents**: ~2-5 minutes total (same as original system)
- **Cost**: ~$0.10-0.50 per agent with optimization enabled

---

## Integration with YouTube API

```python
# Example: Fetch data and run analysis
from src.tools.youtube_tools import YouTubeChannelAnalysisTool

# Get video data from YouTube
youtube_tool = YouTubeChannelAnalysisTool()
video_data = youtube_tool.get_video_data(video_url)

# Run analysis
agents = StandaloneAgentInterface()
result = agents.run_single_agent('performance', video_data=video_data)
```

---

This system gives you complete flexibility to use any part of the YouTube intelligence system independently while maintaining the option to run the full analysis pipeline when needed.