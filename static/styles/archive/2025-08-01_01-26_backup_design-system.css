/*
YouTube Research v2 - Unified Design System
Professional Enterprise Analytics Platform
Based on Comment Intelligence Template with Monochromatic + Purple Accents
*/

/* ====== DESIGN TOKENS & FOUNDATION ====== */
:root {
  /* Monochromatic Base Palette */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8F9FA;
  --bg-tertiary: #F1F3F4;
  --bg-elevated: #FFFFFF;
  --bg-glass: rgba(255, 255, 255, 0.95);
  --bg-overlay: rgba(0, 0, 0, 0.05);
  
  --text-primary: #1A1A1A;
  --text-secondary: #4A4A4A;
  --text-tertiary: #6B7280;
  --text-muted: #9CA3AF;
  
  --border-light: #E5E7EB;
  --border-medium: #D1D5DB;
  --border-strong: #9CA3AF;
  
  /* Purple Accent System (Existing Brand) */
  --purple-primary: #8B5CF6;
  --purple-secondary: #A78BFA;
  --purple-tertiary: #C4B5FD;
  --purple-light: #EDE9FE;
  --purple-gradient: linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%);
  
  /* AI Agent Personality Colors */
  --agent-1: #8B5CF6;  /* Performance - Purple */
  --agent-2: #3B82F6;  /* Script - Blue */
  --agent-3: #10B981;  /* SEO - Green */
  --agent-4: #F59E0B;  /* Psychology - Orange */
  --agent-5: #EF4444;  /* Comments - Red */
  --agent-6: #6366F1;  /* Synthesis - Indigo */
  
  /* Status & Feedback Colors */
  --success: #10B981;
  --success-light: #D1FAE5;
  --warning: #F59E0B;
  --warning-light: #FEF3C7;
  --error: #EF4444;
  --error-light: #FEE2E2;
  --info: #3B82F6;
  --info-light: #DBEAFE;
  
  /* Typography System */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  --font-display: 'Inter', sans-serif;
  
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  /* Spacing System */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */
  --space-3xl: 4rem;     /* 64px */
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Rich Professional Shadows */
  --shadow-sm: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  --shadow-md: 0 8px 16px -4px rgba(0, 0, 0, 0.25), 0 4px 8px -2px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 16px 32px -8px rgba(0, 0, 0, 0.35), 0 8px 16px -4px rgba(0, 0, 0, 0.25);
  --shadow-xl: 0 32px 64px -16px rgba(0, 0, 0, 0.45), 0 16px 32px -8px rgba(0, 0, 0, 0.35);
  --shadow-purple: 0 16px 32px -8px rgba(139, 92, 246, 0.3), 0 8px 16px -4px rgba(139, 92, 246, 0.2);
  --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Layout */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 64px;
  --header-height: 64px;
  --content-max-width: 1400px;
}

/* Rich Dark Mode Variables - Premium Analytics Theme */
[data-theme="dark"] {
  /* Rich Dark Backgrounds with Gradients */
  --bg-primary: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
  --bg-secondary: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e1b4b 100%);
  --bg-tertiary: #1e293b;
  --bg-elevated: rgba(30, 41, 59, 0.95);
  --bg-glass: rgba(255, 255, 255, 0.08);
  --bg-overlay: rgba(255, 255, 255, 0.05);

  /* Surface Cards with Glass Morphism */
  --surface-primary: rgba(30, 41, 59, 0.8);
  --surface-secondary: rgba(51, 65, 85, 0.6);
  --surface-glass: rgba(255, 255, 255, 0.08);
  --surface-card: rgba(30, 41, 59, 0.8);

  /* Rich Text Colors */
  --text-primary: #FAFAFA;
  --text-secondary: #E2E8F0;
  --text-tertiary: #94A3B8;
  --text-muted: #64748B;

  /* Enhanced Borders */
  --border-light: rgba(255, 255, 255, 0.1);
  --border-medium: rgba(255, 255, 255, 0.15);
  --border-strong: rgba(255, 255, 255, 0.2);

  /* Rich Status Colors */
  --success-light: rgba(34, 197, 94, 0.15);
  --warning-light: rgba(251, 191, 36, 0.15);
  --error-light: rgba(239, 68, 68, 0.15);
  --info-light: rgba(59, 130, 246, 0.15);

  /* Enhanced Purple Gradient */
  --primary-gradient: linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%);
  --purple-gradient: linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%);
}

/* ====== RESET & BASE STYLES ====== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-primary);
  overflow-x: hidden;
  transition: background var(--transition-normal), color var(--transition-normal);
}

/* ====== LAYOUT SYSTEM ====== */
.app-layout {
  display: flex;
  min-height: 100vh;
  background: var(--bg-primary);
}

.app-sidebar {
  width: var(--sidebar-width);
  background: var(--bg-elevated);
  border-right: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-lg);
}

.app-main {
  flex: 1;
  margin-left: var(--sidebar-width);
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  transition: margin-left var(--transition-normal);
}

.app-header {
  height: var(--header-height);
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-xl);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(20px);
}

.app-content {
  flex: 1;
  padding: var(--space-xl);
  background: var(--bg-secondary);
  max-width: var(--content-max-width);
  margin: 0 auto;
  width: 100%;
}

/* ====== SIDEBAR COMPONENTS ====== */
.sidebar-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  text-decoration: none;
  color: var(--text-primary);
  font-weight: var(--font-weight-bold);
  font-size: var(--text-lg);
}

.sidebar-logo-icon {
  width: 40px;
  height: 40px;
  background: var(--purple-gradient);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-bold);
  font-size: var(--text-lg);
}

.theme-toggle {
  width: 36px;
  height: 36px;
  border: none;
  background: var(--bg-overlay);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-secondary);
}

.theme-toggle:hover {
  background: var(--purple-light);
  color: var(--purple-primary);
  transform: scale(1.05);
}

.theme-toggle:active {
  transform: scale(0.95);
}

/* User Section */
.sidebar-user-section {
  padding: var(--space-md);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-elevated);
}

.user-profile-card {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm);
  background: var(--bg-overlay);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-md);
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: var(--purple-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-semibold);
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--text-sm);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0;
}

.user-tier {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin: 0;
}

/* Usage Indicator */
.usage-indicator {
  background: var(--bg-overlay);
  border-radius: var(--radius-md);
  padding: var(--space-sm);
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xs);
}

.usage-label {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.usage-count {
  font-size: var(--text-xs);
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.usage-bar {
  height: 4px;
  background: var(--bg-muted);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-bottom: var(--space-sm);
}

.usage-fill {
  height: 100%;
  background: var(--purple-gradient);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.upgrade-btn {
  width: 100%;
  justify-content: center;
  font-size: var(--text-xs);
  padding: var(--space-xs) var(--space-sm);
}

/* ====== NAVIGATION COMPONENTS ====== */
.sidebar-nav {
  flex: 1;
  padding: var(--space-lg);
  overflow-y: auto;
}

.nav-section {
  margin-bottom: var(--space-xl);
}

.nav-section-title {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-md);
}

.nav-items {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
  position: relative;
  margin-bottom: var(--space-xs);
}

.nav-item:hover {
  background: var(--bg-overlay);
  color: var(--text-primary);
  transform: translateX(2px);
}

.nav-item.active {
  background: var(--purple-gradient);
  color: white;
  font-weight: var(--font-weight-semibold);
  box-shadow: var(--shadow-sm);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: -var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--purple-primary);
  border-radius: 0 2px 2px 0;
}

.nav-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.nav-badge {
  margin-left: auto;
  padding: 2px 6px;
  background: var(--bg-muted);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
}

.nav-badge-purple {
  background: var(--purple-primary);
  color: white;
}

.nav-badge-blue {
  background: #3b82f6;
  color: white;
}

.nav-badge-green {
  background: #10b981;
  color: white;
}

.nav-badge-orange {
  background: #f59e0b;
  color: white;
}

.nav-item.active .nav-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Agent Indicators */
.agent-indicator {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  margin-left: auto;
  background: var(--bg-muted);
  transition: var(--transition-fast);
}

.agent-1 { background: #ef4444; }
.agent-2 { background: #f97316; }
.agent-3 { background: #eab308; }
.agent-4 { background: #22c55e; }
.agent-5 { background: #3b82f6; }
.agent-6 { background: var(--purple-primary); }

.nav-item:hover .agent-indicator {
  transform: scale(1.2);
  box-shadow: 0 0 8px currentColor;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: var(--space-md);
  border-top: 1px solid var(--border-light);
  background: var(--bg-elevated);
}

.version-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  margin-bottom: var(--space-sm);
}

.version-label {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.version-number {
  font-size: var(--text-xs);
  color: var(--purple-primary);
  font-weight: var(--font-weight-semibold);
}

.easter-egg-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  padding: var(--space-xs);
  background: var(--bg-overlay);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: var(--text-xs);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Konami Code Effects */
.konami-activated .app-sidebar {
  animation: rainbow-border 2s infinite;
}

@keyframes rainbow-border {
  0% { border-right-color: #ef4444; }
  16% { border-right-color: #f97316; }
  33% { border-right-color: #eab308; }
  50% { border-right-color: #22c55e; }
  66% { border-right-color: #3b82f6; }
  83% { border-right-color: var(--purple-primary); }
  100% { border-right-color: #ef4444; }
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-badge {
  margin-left: auto;
  background: var(--purple-primary);
  color: white;
  font-size: var(--text-xs);
  font-weight: var(--font-weight-semibold);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
}

/* ====== CARD COMPONENTS ====== */
.card {
  background: var(--surface-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-glass);
  backdrop-filter: blur(12px);
  transition: all var(--transition-fast);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-medium);
}

.card-gradient {
  background: var(--primary-gradient);
  border: 1px solid var(--purple-tertiary);
  box-shadow: var(--shadow-purple);
  backdrop-filter: blur(12px);
}

.card-header {
  padding: var(--space-xl);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

.card-subtitle {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: var(--space-xs) 0 0 0;
}

.card-content {
  padding: var(--space-xl);
}

.card-footer {
  padding: var(--space-xl);
  border-top: 1px solid var(--border-light);
  background: var(--bg-overlay);
}

/* ====== BUTTON COMPONENTS ====== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-lg);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--purple-gradient);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--bg-elevated);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-overlay);
  border-color: var(--purple-primary);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--bg-overlay);
  color: var(--text-primary);
}

.btn-sm {
  padding: var(--space-sm) var(--space-md);
  font-size: var(--text-sm);
}

.btn-lg {
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.btn-spinner {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: inherit;
}

/* ====== COMPONENT LIBRARY STYLES ====== */

/* Analysis Cards */
.analysis-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.analysis-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.analysis-card-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-elevated);
}

.analysis-card-icon {
  width: 40px;
  height: 40px;
  background: var(--purple-gradient);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.card-icon {
  width: 20px;
  height: 20px;
}

.analysis-card-title-section {
  flex: 1;
  min-width: 0;
}

.analysis-card-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.analysis-card-subtitle {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.analysis-card-actions {
  display: flex;
  gap: var(--space-xs);
}

.card-action-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.analysis-card-content {
  padding: var(--space-lg);
}

.analysis-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  background: var(--bg-overlay);
  border-top: 1px solid var(--border-light);
}

.analysis-card-meta {
  display: flex;
  gap: var(--space-md);
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.analysis-timestamp {
  font-weight: var(--font-weight-medium);
}

.analysis-confidence {
  color: var(--purple-primary);
  font-weight: var(--font-weight-semibold);
}

.analysis-card-tags {
  display: flex;
  gap: var(--space-xs);
}

/* Agent Status Cards */
.agent-status-card {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.agent-status-card:hover {
  background: var(--bg-overlay);
  border-color: var(--purple-primary);
}

.agent-avatar-section {
  position: relative;
  flex-shrink: 0;
}

.agent-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-semibold);
}

.agent-performance { background: #ef4444; }
.agent-script { background: #f97316; }
.agent-seo { background: #eab308; }
.agent-psychology { background: #22c55e; }
.agent-comments { background: #3b82f6; }
.agent-synthesis { background: var(--purple-primary); }

.agent-icon {
  width: 20px;
  height: 20px;
}

.agent-status-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  border: 2px solid var(--bg-elevated);
}

.status-idle { background: var(--text-tertiary); }
.status-starting { background: #f59e0b; }
.status-working { background: #3b82f6; }
.status-thinking { background: var(--purple-primary); }
.status-complete { background: #10b981; }
.status-error { background: #ef4444; }

.agent-info-section {
  flex: 1;
  min-width: 0;
}

.agent-name {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.agent-message {
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.agent-progress {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: var(--bg-muted);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--purple-gradient);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.progress-text {
  font-size: var(--text-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  min-width: 32px;
  text-align: right;
}

/* ====== AI AGENT STATUS COMPONENTS ====== */
.agent-status-feed {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  max-height: 400px;
  overflow-y: auto;
}

/* Insight Cards */
.insight-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: var(--transition-fast);
}

.insight-card:hover {
  border-color: var(--purple-primary);
  box-shadow: var(--shadow-md);
}

.insight-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.insight-priority {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.priority-high { background: #ef4444; }
.priority-medium { background: #f59e0b; }
.priority-low { background: #10b981; }

.priority-icon {
  width: 16px;
  height: 16px;
}

.insight-category {
  font-size: var(--text-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--purple-primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.insight-title {
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.insight-description {
  margin: 0 0 var(--space-md) 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

.insight-metrics {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.metric-label {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.metric-value {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.insight-actions {
  display: flex;
  gap: var(--space-sm);
}

/* Metric Cards */
.metric-card {
  background: var(--surface-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  backdrop-filter: blur(12px);
  box-shadow: var(--shadow-glass);
  transition: var(--transition-fast);
}

.metric-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.metric-icon {
  width: 40px;
  height: 40px;
  background: var(--bg-overlay);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.metric-icon-svg {
  width: 20px;
  height: 20px;
}

.metric-trend {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.trend-up { background: #10b981; }
.trend-down { background: #ef4444; }
.trend-neutral { background: var(--text-tertiary); }

.trend-icon {
  width: 16px;
  height: 16px;
}

.metric-value {
  margin: 0;
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: 1;
}

.metric-label {
  margin: var(--space-xs) 0 var(--space-sm) 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.metric-change {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.change-value {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--purple-primary);
}

.change-period {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

/* Progress Indicators */
.progress-indicator {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.progress-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.progress-percentage {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-bold);
  color: var(--purple-primary);
}

.progress-bar-container {
  margin-bottom: var(--space-lg);
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-md);
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  flex: 1;
}

.step-indicator {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-full);
  background: var(--bg-muted);
  transition: var(--transition-fast);
}

.progress-step.active .step-indicator {
  background: var(--purple-primary);
}

.progress-step.completed .step-indicator {
  background: #10b981;
}

.step-label {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  text-align: center;
}

.progress-step.active .step-label {
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.progress-message {
  text-align: center;
}

.current-message {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-style: italic;
}

/* Status Feed */
.status-feed {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.status-feed-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  background: var(--bg-overlay);
  border-bottom: 1px solid var(--border-light);
}

.feed-title {
  margin: 0;
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.feed-icon {
  width: 16px;
  height: 16px;
}

.status-feed-content {
  max-height: 300px;
  overflow-y: auto;
  padding: var(--space-sm);
}

.status-feed-item {
  display: flex;
  gap: var(--space-sm);
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
  animation: fadeInUp 0.3s ease-out;
}

.status-feed-item:hover {
  background: var(--bg-overlay);
}

.status-item-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: var(--purple-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.status-icon {
  width: 14px;
  height: 14px;
}

.status-item-content {
  flex: 1;
  min-width: 0;
}

.status-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xs);
}

.status-agent-name {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.status-timestamp {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.status-message {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.status-feed-footer {
  padding: var(--space-sm) var(--space-lg);
  background: var(--bg-overlay);
  border-top: 1px solid var(--border-light);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background: var(--text-tertiary);
}

.connection-status.connected .status-dot {
  background: #10b981;
  animation: pulse 2s infinite;
}

.connection-status.disconnected .status-dot {
  background: #ef4444;
}

/* Loading Skeleton */
.loading-skeleton {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  background: var(--bg-muted);
  border-radius: var(--radius-full);
}

.skeleton-text {
  background: var(--bg-muted);
  border-radius: var(--radius-sm);
  height: 16px;
}

.skeleton-title {
  width: 60%;
  height: 20px;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
}

.skeleton-line {
  width: 100%;
}

.skeleton-line.short {
  width: 70%;
}

.skeleton-footer {
  display: flex;
  gap: var(--space-sm);
}

.skeleton-button {
  height: 32px;
  background: var(--bg-muted);
  border-radius: var(--radius-md);
  width: 80px;
}

.skeleton-button.small {
  width: 60px;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--space-xl);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
}

.error-icon {
  width: 64px;
  height: 64px;
  background: #fef2f2;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-lg);
}

.error-icon-svg {
  width: 32px;
  height: 32px;
  color: #ef4444;
}

.error-title {
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.error-message {
  margin: 0 0 var(--space-lg) 0;
  font-size: var(--text-base);
  color: var(--text-secondary);
  max-width: 400px;
}

.error-actions {
  display: flex;
  gap: var(--space-sm);
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--space-xl);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
}

.empty-icon {
  width: 64px;
  height: 64px;
  background: var(--bg-overlay);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-lg);
}

.empty-icon-svg {
  width: 32px;
  height: 32px;
  color: var(--text-secondary);
}

.empty-title {
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.empty-message {
  margin: 0 0 var(--space-lg) 0;
  font-size: var(--text-base);
  color: var(--text-secondary);
  max-width: 400px;
}

.empty-actions {
  display: flex;
  gap: var(--space-sm);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.agent-status-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  padding: var(--space-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-sm);
  transition: all var(--transition-fast);
}

.agent-status-item:hover {
  background: var(--bg-overlay);
}

.agent-status-item.active {
  background: var(--purple-light);
  border: 1px solid var(--purple-tertiary);
}

.agent-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-bold);
  font-size: var(--text-sm);
  flex-shrink: 0;
}

.agent-avatar.agent-1 { background: var(--agent-1); }
.agent-avatar.agent-2 { background: var(--agent-2); }
.agent-avatar.agent-3 { background: var(--agent-3); }
.agent-avatar.agent-4 { background: var(--agent-4); }
.agent-avatar.agent-5 { background: var(--agent-5); }
.agent-avatar.agent-6 { background: var(--agent-6); }

.agent-status-content {
  flex: 1;
  min-width: 0;
}

.agent-name {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--text-sm);
  margin-bottom: var(--space-xs);
}

.agent-message {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  line-height: 1.5;
}

.agent-timestamp {
  color: var(--text-muted);
  font-size: var(--text-xs);
  margin-top: var(--space-xs);
}

.agent-progress {
  width: 100%;
  height: 4px;
  background: var(--bg-overlay);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-top: var(--space-sm);
}

.agent-progress-fill {
  height: 100%;
  background: var(--purple-gradient);
  border-radius: var(--radius-full);
  transition: width var(--transition-normal);
}

/* ====== FORM COMPONENTS ====== */
.form-group {
  margin-bottom: var(--space-lg);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.form-input {
  width: 100%;
  padding: var(--space-md);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  color: var(--text-primary);
  background: var(--bg-elevated);
  transition: all var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--purple-primary);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--space-md) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: var(--space-2xl);
}

/* ====== ANALYSIS REPORT COMPONENTS ====== */
.analysis-section {
  margin-bottom: var(--space-2xl);
}

.analysis-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.analysis-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--text-xl);
}

.analysis-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

.analysis-subtitle {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin: var(--space-xs) 0 0 0;
}

.metric-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.metric-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: all var(--transition-fast);
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--purple-tertiary);
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-md);
}

.metric-label {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: var(--space-sm) 0;
}

.metric-change {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.metric-change.positive {
  color: var(--success);
}

.metric-change.negative {
  color: var(--error);
}

.metric-change.neutral {
  color: var(--text-muted);
}

/* ====== UTILITY CLASSES ====== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-mono { font-family: var(--font-mono); }
.font-display { font-family: var(--font-display); }

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-muted { color: var(--text-muted); }
.text-purple { color: var(--purple-primary); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-elevated { background-color: var(--bg-elevated); }
.bg-purple { background: var(--purple-gradient); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-purple { box-shadow: var(--shadow-purple); }

/* ====== RESPONSIVE DESIGN ====== */
@media (max-width: 768px) {
  .app-sidebar {
    width: var(--sidebar-collapsed-width);
  }

  .app-main {
    margin-left: var(--sidebar-collapsed-width);
  }

  .app-content {
    padding: var(--space-lg);
  }

  .nav-item span {
    display: none;
  }

  .metric-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    padding: var(--space-lg);
  }

  .card-content {
    padding: var(--space-lg);
  }
}

/* ====== ANIMATIONS & EASTER EGGS ====== */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
  40%, 43% { transform: translate3d(0, -8px, 0); }
  70% { transform: translate3d(0, -4px, 0); }
  90% { transform: translate3d(0, -2px, 0); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-bounce { animation: bounce 1s infinite; }
.animate-fadeInUp { animation: fadeInUp 0.5s ease-out; }

/* Easter Egg: Konami Code Activation */
.konami-activated {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
