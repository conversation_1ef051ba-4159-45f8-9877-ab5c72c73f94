import re
import json
from collections import defaultdict

# Path to the CSS file
token_file_path = 'static/styles/youtube-research-v2-design-system.css'

# Define categories for tokens
categories = {
    'color': ['bg', 'text', 'border', 'purple', 'agent', 'success', 'warning', 'error', 'info', 'primary', 'surface', 'shadow'],
    'spacing': ['space'],
    'radius': ['radius'],
    'typography': ['font', 'text', 'font-weight'],
    'shadow': ['shadow'],
    'transition': ['transition'],
}

# Prepare regex for extracting CSS variables
token_regex = re.compile(r'--([^:]+):\s*([^;]+);')

# Initialize dictionary to hold categorized tokens
tokens = defaultdict(list)

with open(token_file_path, 'r') as file:
    css_content = file.read()
    matches = token_regex.findall(css_content)
    for name, value in matches:
        for category, keywords in categories.items():
            if any(keyword in name for keyword in keywords):
                tokens[category].append({'name': f'--{name}', 'value': value.strip()})
                break

# Generate markdown table and JSON
markdown_lines = ['| Category | Variable | Value |', '|---------|----------|-------|']
json_output = {}

for category, token_list in tokens.items():
    json_output[category] = token_list
    for token in token_list:
        markdown_lines.append(f"| {category} | {token['name']} | {token['value']} |")

# Write markdown and JSON output to files
with open('design-tokens.md', 'w') as md_file:
    md_file.write('\n'.join(markdown_lines))

with open('design-tokens.json', 'w') as json_file:
    json.dump(json_output, json_file, indent=2)

print('Design tokens extracted and files generated: design-tokens.md and design-tokens.json')
