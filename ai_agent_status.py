"""
YouTube Research v2 - AI Agent Status System
Real-time personified status updates with WebSocket integration
"""

import asyncio
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import logging
from fastapi import WebSocket, WebSocketDisconnect
import random

logger = logging.getLogger(__name__)

class AgentStatus(Enum):
    IDLE = "idle"
    STARTING = "starting"
    WORKING = "working"
    THINKING = "thinking"
    ANALYZING = "analyzing"
    COMPLETE = "complete"
    ERROR = "error"

class AgentPersonality(Enum):
    PERFORMANCE = "performance"
    SCRIPT = "script"
    SEO = "seo"
    PSYCHOLOGY = "psychology"
    COMMENTS = "comments"
    SYNTHESIS = "synthesis"

@dataclass
class AgentStatusUpdate:
    agent_id: str
    agent_name: str
    agent_personality: AgentPersonality
    status: AgentStatus
    message: str
    progress: float  # 0.0 to 1.0
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class AnalysisSession:
    session_id: str
    user_id: Optional[str]
    analysis_type: str  # video_analysis, channel_analysis, market_research
    target_url: str
    started_at: datetime
    agents: List[str]
    current_agent: Optional[str] = None
    overall_progress: float = 0.0
    status: str = "starting"
    estimated_completion: Optional[datetime] = None

class AgentPersonalities:
    """Personified messages for each AI agent"""
    
    PERFORMANCE_MESSAGES = {
        AgentStatus.STARTING: [
            "🎯 Performance Intelligence Agent reporting for duty! Time to decode those metrics...",
            "📊 Firing up my analytics engines! Let's see what this content is really made of.",
            "⚡ Performance Agent here - ready to dive deep into the numbers that matter!"
        ],
        AgentStatus.WORKING: [
            "🔍 Crunching view patterns and engagement metrics... This is fascinating!",
            "📈 Analyzing performance trends across multiple dimensions...",
            "🎪 Discovering the secret sauce behind viral moments...",
            "🧮 Running statistical analysis on audience retention curves..."
        ],
        AgentStatus.THINKING: [
            "🤔 Hmm, interesting pattern emerging in the click-through rates...",
            "💭 Cross-referencing performance data with industry benchmarks...",
            "🧠 Connecting the dots between content timing and engagement spikes..."
        ],
        AgentStatus.COMPLETE: [
            "✅ Performance analysis complete! Found some game-changing insights.",
            "🎉 Mission accomplished! The performance secrets have been unlocked.",
            "🏆 Performance Intelligence Agent signing off - data decoded successfully!"
        ]
    }
    
    SCRIPT_MESSAGES = {
        AgentStatus.STARTING: [
            "📝 Script Forensics Agent activated! Time to dissect this narrative...",
            "🔬 Ready to analyze every word, pause, and storytelling technique!",
            "📖 Script Agent here - let's uncover the storytelling DNA!"
        ],
        AgentStatus.WORKING: [
            "🎭 Analyzing narrative structure and emotional beats...",
            "🗣️ Examining dialogue patterns and audience engagement hooks...",
            "📚 Deconstructing storytelling techniques and pacing strategies...",
            "🎬 Identifying key moments that drive viewer retention..."
        ],
        AgentStatus.THINKING: [
            "🤔 This storytelling approach is quite sophisticated...",
            "💭 Analyzing the psychological impact of these narrative choices...",
            "🧠 Connecting script elements to audience engagement patterns..."
        ],
        AgentStatus.COMPLETE: [
            "✅ Script analysis complete! The storytelling secrets are revealed.",
            "🎭 Narrative forensics finished - found the engagement formula!",
            "📝 Script Forensics Agent out - story structure decoded!"
        ]
    }
    
    SEO_MESSAGES = {
        AgentStatus.STARTING: [
            "🔍 SEO & Discoverability Agent online! Time to optimize for discovery...",
            "🎯 Ready to analyze keywords, tags, and algorithmic signals!",
            "📡 SEO Agent reporting - let's crack the discoverability code!"
        ],
        AgentStatus.WORKING: [
            "🏷️ Analyzing keyword density and semantic relevance...",
            "🔗 Examining metadata optimization and tag effectiveness...",
            "📊 Evaluating thumbnail click-through potential...",
            "🎪 Assessing algorithmic ranking factors..."
        ],
        AgentStatus.THINKING: [
            "🤔 These keyword choices are strategically interesting...",
            "💭 Analyzing search intent alignment with content delivery...",
            "🧠 Calculating discoverability optimization opportunities..."
        ],
        AgentStatus.COMPLETE: [
            "✅ SEO analysis complete! Discoverability roadmap ready.",
            "🎯 Mission accomplished - optimization opportunities identified!",
            "🔍 SEO Agent signing off - algorithmic insights unlocked!"
        ]
    }
    
    PSYCHOLOGY_MESSAGES = {
        AgentStatus.STARTING: [
            "🧠 Audience Psychology Agent activated! Time to decode viewer minds...",
            "💭 Ready to analyze emotional triggers and behavioral patterns!",
            "🎭 Psychology Agent here - let's understand what makes audiences tick!"
        ],
        AgentStatus.WORKING: [
            "🎯 Analyzing emotional engagement patterns and triggers...",
            "🧩 Examining psychological hooks and persuasion techniques...",
            "💡 Identifying audience pain points and desires...",
            "🎪 Mapping viewer journey and decision-making processes..."
        ],
        AgentStatus.THINKING: [
            "🤔 Fascinating psychological patterns emerging from this content...",
            "💭 Analyzing the deeper motivations behind audience engagement...",
            "🧠 Connecting emotional triggers to behavioral outcomes..."
        ],
        AgentStatus.COMPLETE: [
            "✅ Psychology analysis complete! Audience mind map ready.",
            "🧠 Mission accomplished - psychological insights decoded!",
            "🎭 Psychology Agent out - viewer motivations unlocked!"
        ]
    }
    
    COMMENTS_MESSAGES = {
        AgentStatus.STARTING: [
            "💬 Comment Intelligence Agent ready! Time to mine audience gold...",
            "🗣️ Preparing to analyze real viewer feedback and sentiment!",
            "📢 Comments Agent here - let's hear what the audience really thinks!"
        ],
        AgentStatus.WORKING: [
            "💭 Processing sentiment analysis across comment threads...",
            "🔍 Identifying recurring themes and audience concerns...",
            "📊 Analyzing engagement patterns in community discussions...",
            "🎯 Extracting actionable insights from viewer feedback..."
        ],
        AgentStatus.THINKING: [
            "🤔 The audience sentiment here is quite revealing...",
            "💭 Analyzing the gap between content intent and audience reception...",
            "🧠 Connecting comment patterns to content performance metrics..."
        ],
        AgentStatus.COMPLETE: [
            "✅ Comment analysis complete! Audience voice decoded.",
            "💬 Mission accomplished - community insights extracted!",
            "🗣️ Comments Agent signing off - viewer feedback analyzed!"
        ]
    }
    
    SYNTHESIS_MESSAGES = {
        AgentStatus.STARTING: [
            "🎯 Strategic Synthesis Agent online! Time to connect all the dots...",
            "🧩 Ready to weave together insights from all analysis streams!",
            "⚡ Synthesis Agent here - let's create the master strategy!"
        ],
        AgentStatus.WORKING: [
            "🔗 Integrating insights from all specialized agents...",
            "🎪 Identifying strategic patterns across multiple data streams...",
            "📊 Building comprehensive growth recommendations...",
            "🎯 Crafting actionable strategic roadmap..."
        ],
        AgentStatus.THINKING: [
            "🤔 Synthesizing complex patterns into clear strategic direction...",
            "💭 Balancing multiple optimization opportunities for maximum impact...",
            "🧠 Creating holistic view from specialized agent insights..."
        ],
        AgentStatus.COMPLETE: [
            "✅ Strategic synthesis complete! Master plan ready for execution.",
            "🎉 Mission accomplished - comprehensive strategy delivered!",
            "🎯 Synthesis Agent out - strategic roadmap unlocked!"
        ]
    }
    
    @classmethod
    def get_message(cls, personality: AgentPersonality, status: AgentStatus) -> str:
        """Get a random personified message for agent status"""
        message_map = {
            AgentPersonality.PERFORMANCE: cls.PERFORMANCE_MESSAGES,
            AgentPersonality.SCRIPT: cls.SCRIPT_MESSAGES,
            AgentPersonality.SEO: cls.SEO_MESSAGES,
            AgentPersonality.PSYCHOLOGY: cls.PSYCHOLOGY_MESSAGES,
            AgentPersonality.COMMENTS: cls.COMMENTS_MESSAGES,
            AgentPersonality.SYNTHESIS: cls.SYNTHESIS_MESSAGES
        }
        
        messages = message_map.get(personality, {}).get(status, ["Working on analysis..."])
        return random.choice(messages)

class AgentStatusManager:
    """Manages AI agent status updates and WebSocket broadcasting"""
    
    def __init__(self):
        self.active_sessions: Dict[str, AnalysisSession] = {}
        self.websocket_connections: Dict[str, List[WebSocket]] = {}
        self.agent_configs = {
            "performance": {"name": "Performance Intelligence", "personality": AgentPersonality.PERFORMANCE},
            "script": {"name": "Script Forensics", "personality": AgentPersonality.SCRIPT},
            "seo": {"name": "SEO & Discoverability", "personality": AgentPersonality.SEO},
            "psychology": {"name": "Audience Psychology", "personality": AgentPersonality.PSYCHOLOGY},
            "comments": {"name": "Comment Intelligence", "personality": AgentPersonality.COMMENTS},
            "synthesis": {"name": "Strategic Synthesis", "personality": AgentPersonality.SYNTHESIS}
        }
    
    async def create_session(self, analysis_type: str, target_url: str, user_id: Optional[str] = None) -> str:
        """Create a new analysis session"""
        session_id = str(uuid.uuid4())
        
        session = AnalysisSession(
            session_id=session_id,
            user_id=user_id,
            analysis_type=analysis_type,
            target_url=target_url,
            started_at=datetime.now(),
            agents=list(self.agent_configs.keys())
        )
        
        self.active_sessions[session_id] = session
        self.websocket_connections[session_id] = []
        
        logger.info(f"Created analysis session {session_id} for {analysis_type}")
        return session_id
    
    async def connect_websocket(self, session_id: str, websocket: WebSocket):
        """Connect WebSocket to session"""
        await websocket.accept()
        
        if session_id not in self.websocket_connections:
            self.websocket_connections[session_id] = []
        
        self.websocket_connections[session_id].append(websocket)
        logger.info(f"WebSocket connected to session {session_id}")
        
        # Send current session status
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            await self.broadcast_session_update(session_id, {
                "type": "session_status",
                "session": asdict(session)
            })
    
    async def disconnect_websocket(self, session_id: str, websocket: WebSocket):
        """Disconnect WebSocket from session"""
        if session_id in self.websocket_connections:
            try:
                self.websocket_connections[session_id].remove(websocket)
                logger.info(f"WebSocket disconnected from session {session_id}")
            except ValueError:
                pass
    
    async def update_agent_status(self, session_id: str, agent_id: str, status: AgentStatus, 
                                progress: float = 0.0, metadata: Optional[Dict[str, Any]] = None):
        """Update agent status and broadcast to connected clients"""
        if session_id not in self.active_sessions:
            logger.warning(f"Session {session_id} not found")
            return
        
        session = self.active_sessions[session_id]
        agent_config = self.agent_configs.get(agent_id, {})
        
        # Generate personified message
        personality = agent_config.get("personality", AgentPersonality.PERFORMANCE)
        message = AgentPersonalities.get_message(personality, status)
        
        # Create status update
        status_update = AgentStatusUpdate(
            agent_id=agent_id,
            agent_name=agent_config.get("name", agent_id.title()),
            agent_personality=personality,
            status=status,
            message=message,
            progress=progress,
            timestamp=datetime.now(),
            metadata=metadata
        )
        
        # Update session
        session.current_agent = agent_id if status in [AgentStatus.WORKING, AgentStatus.THINKING] else None
        
        # Calculate overall progress
        agent_count = len(session.agents)
        completed_agents = sum(1 for agent in session.agents if agent == agent_id and status == AgentStatus.COMPLETE)
        current_agent_progress = progress if session.current_agent == agent_id else 0
        session.overall_progress = (completed_agents + current_agent_progress) / agent_count
        
        # Broadcast update
        await self.broadcast_agent_update(session_id, status_update)
        
        logger.info(f"Agent {agent_id} status updated to {status.value} in session {session_id}")
    
    async def broadcast_agent_update(self, session_id: str, status_update: AgentStatusUpdate):
        """Broadcast agent status update to all connected WebSockets"""
        if session_id not in self.websocket_connections:
            return
        
        message = {
            "type": "agent_update",
            "data": asdict(status_update)
        }
        
        # Convert datetime to ISO string for JSON serialization
        message["data"]["timestamp"] = status_update.timestamp.isoformat()
        message["data"]["agent_personality"] = status_update.agent_personality.value
        message["data"]["status"] = status_update.status.value
        
        await self.broadcast_to_session(session_id, message)
    
    async def broadcast_session_update(self, session_id: str, message: Dict[str, Any]):
        """Broadcast session update to all connected WebSockets"""
        await self.broadcast_to_session(session_id, message)
    
    async def broadcast_to_session(self, session_id: str, message: Dict[str, Any]):
        """Broadcast message to all WebSockets in session"""
        if session_id not in self.websocket_connections:
            return
        
        connections = self.websocket_connections[session_id].copy()
        
        for websocket in connections:
            try:
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting to WebSocket: {e}")
                # Remove failed connection
                try:
                    self.websocket_connections[session_id].remove(websocket)
                except ValueError:
                    pass
    
    async def complete_session(self, session_id: str, results: Optional[Dict[str, Any]] = None):
        """Mark session as complete"""
        if session_id not in self.active_sessions:
            return
        
        session = self.active_sessions[session_id]
        session.status = "completed"
        session.overall_progress = 1.0
        
        await self.broadcast_session_update(session_id, {
            "type": "session_complete",
            "session_id": session_id,
            "results": results
        })
        
        logger.info(f"Session {session_id} completed")
    
    def get_session(self, session_id: str) -> Optional[AnalysisSession]:
        """Get session by ID"""
        return self.active_sessions.get(session_id)
    
    def cleanup_session(self, session_id: str):
        """Clean up session resources"""
        if session_id in self.active_sessions:
            del self.active_sessions[session_id]
        
        if session_id in self.websocket_connections:
            del self.websocket_connections[session_id]
        
        logger.info(f"Session {session_id} cleaned up")

# Global instance
agent_status_manager = AgentStatusManager()
