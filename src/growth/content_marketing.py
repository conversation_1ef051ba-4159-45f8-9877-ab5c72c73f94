"""
Content Marketing System - Viral Video Teardown Reports
Automated content creation for SEO magnet teardown reports
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from pathlib import Path
import google.generativeai as genai
from googleapiclient.discovery import build
import hashlib

@dataclass
class VideoTeardownData:
    """Structure for viral video teardown analysis"""
    video_id: str
    title: str
    channel: str
    views: int
    published_date: str
    growth_rate: float
    viral_triggers: List[str]
    content_analysis: Dict[str, Any]
    seo_keywords: List[str]
    emotional_hooks: List[str]
    
@dataclass
class TeardownReport:
    """Complete teardown report structure"""
    report_id: str
    video_data: VideoTeardownData
    analysis_depth: str
    viral_formula: Dict[str, Any]
    replication_guide: List[str]
    seo_content: Dict[str, str]
    publish_date: str
    featured_insights: List[str]

class ViralVideoDetector:
    """Detect trending/viral videos for teardown analysis"""
    
    def __init__(self, youtube_api_key: str, gemini_api_key: str):
        self.youtube = build('youtube', 'v3', developerKey=youtube_api_key)
        genai.configure(api_key=gemini_api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
    async def find_viral_candidates(self, 
                                  categories: List[str] = None,
                                  min_growth_rate: float = 5.0,
                                  days_back: int = 7) -> List[Dict[str, Any]]:
        """Find viral video candidates based on growth patterns"""
        
        if not categories:
            categories = [
                "Business", "Technology", "Education", "Entertainment", 
                "Gaming", "How-to", "Finance", "Marketing"
            ]
        
        viral_candidates = []
        
        for category in categories:
            # Get trending videos in category
            search_response = self.youtube.search().list(
                q=f'{category} viral trend',
                part='id,snippet',
                type='video',
                publishedAfter=(datetime.now() - timedelta(days=days_back)).isoformat() + 'Z',
                order='viewCount',
                maxResults=20
            ).execute()
            
            for item in search_response['items']:
                video_id = item['id']['videoId']
                
                # Get detailed video statistics
                stats_response = self.youtube.videos().list(
                    part='statistics,snippet',
                    id=video_id
                ).execute()
                
                if stats_response['items']:
                    video_stats = stats_response['items'][0]
                    views = int(video_stats['statistics']['viewCount'])
                    published = video_stats['snippet']['publishedAt']
                    
                    # Calculate growth rate (views per hour since publish)
                    publish_time = datetime.fromisoformat(published.replace('Z', '+00:00'))
                    hours_since_publish = (datetime.now(publish_time.tzinfo) - publish_time).total_seconds() / 3600
                    
                    if hours_since_publish > 0:
                        growth_rate = views / hours_since_publish
                        
                        if growth_rate >= min_growth_rate:
                            viral_candidates.append({
                                'video_id': video_id,
                                'title': video_stats['snippet']['title'],
                                'channel': video_stats['snippet']['channelTitle'],
                                'views': views,
                                'published_date': published,
                                'growth_rate': growth_rate,
                                'category': category,
                                'hours_since_publish': hours_since_publish
                            })
        
        # Sort by growth rate and return top candidates
        return sorted(viral_candidates, key=lambda x: x['growth_rate'], reverse=True)[:10]

class TeardownReportGenerator:
    """Generate comprehensive viral video teardown reports"""
    
    def __init__(self, gemini_api_key: str):
        genai.configure(api_key=gemini_api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
    async def generate_teardown_report(self, video_data: Dict[str, Any]) -> TeardownReport:
        """Generate complete teardown report with viral formula extraction"""
        
        # Get video transcript and analysis
        from src.tools.youtube_tools import get_video_transcript
        from src.crews.video_analyzer.crew import VideoAnalyzerCrew
        
        transcript = await get_video_transcript(video_data['video_id'])
        
        # Run 7-agent analysis system
        analyzer = VideoAnalyzerCrew()
        analysis_result = await analyzer.analyze_video(
            video_url=f"https://youtube.com/watch?v={video_data['video_id']}",
            include_transcript=True
        )
        
        # Extract viral triggers and formula
        viral_analysis_prompt = f"""
        VIRAL VIDEO TEARDOWN ANALYSIS
        
        Video: {video_data['title']}
        Channel: {video_data['channel']}
        Views: {video_data['views']:,}
        Growth Rate: {video_data['growth_rate']:.1f} views/hour
        
        Complete Analysis: {json.dumps(analysis_result, indent=2)}
        
        Extract the VIRAL FORMULA:
        
        1. HOOK ANALYSIS (First 15 seconds):
           - Opening statement/question
           - Visual hook elements
           - Emotional trigger used
           - Curiosity gap created
        
        2. STRUCTURE PATTERNS:
           - Content flow and pacing
           - Retention techniques used
           - Story arc elements
           - Call-to-action placement
        
        3. PSYCHOLOGICAL TRIGGERS:
           - Primary emotions targeted
           - Social proof elements
           - Authority indicators
           - FOMO/urgency factors
        
        4. SEO OPTIMIZATION:
           - Title formula breakdown
           - Thumbnail psychology
           - Description strategy
           - Tag effectiveness
        
        5. REPLICATION FRAMEWORK:
           - Step-by-step formula
           - Adaptable elements
           - Category-specific tweaks
           - Common failure points
        
        Format as detailed analysis report with actionable insights.
        """
        
        viral_formula = await self._get_ai_analysis(viral_analysis_prompt)
        
        # Generate SEO-optimized content
        seo_content = await self._generate_seo_content(video_data, viral_formula)
        
        # Create structured report
        report = TeardownReport(
            report_id=hashlib.md5(f"{video_data['video_id']}{datetime.now().isoformat()}".encode()).hexdigest()[:12],
            video_data=VideoTeardownData(
                video_id=video_data['video_id'],
                title=video_data['title'],
                channel=video_data['channel'],
                views=video_data['views'],
                published_date=video_data['published_date'],
                growth_rate=video_data['growth_rate'],
                viral_triggers=self._extract_viral_triggers(viral_formula),
                content_analysis=analysis_result,
                seo_keywords=self._extract_seo_keywords(viral_formula),
                emotional_hooks=self._extract_emotional_hooks(viral_formula)
            ),
            analysis_depth="professional",
            viral_formula=viral_formula,
            replication_guide=self._extract_replication_steps(viral_formula),
            seo_content=seo_content,
            publish_date=datetime.now().isoformat(),
            featured_insights=self._extract_key_insights(viral_formula)
        )
        
        return report
    
    async def _get_ai_analysis(self, prompt: str) -> Dict[str, Any]:
        """Get AI analysis from Gemini"""
        try:
            response = self.model.generate_content(prompt)
            return {"analysis": response.text, "generated_at": datetime.now().isoformat()}
        except Exception as e:
            return {"error": str(e), "analysis": "Analysis failed"}
    
    async def _generate_seo_content(self, video_data: Dict[str, Any], viral_formula: Dict[str, Any]) -> Dict[str, str]:
        """Generate SEO-optimized blog content"""
        
        seo_prompt = f"""
        Create SEO-optimized blog post content for viral video teardown:
        
        Video: {video_data['title']} ({video_data['views']:,} views)
        
        Generate:
        1. SEO Title (60 chars, includes "Viral Video Teardown")
        2. Meta Description (155 chars)
        3. H1 Headline
        4. Introduction paragraph (hook readers)
        5. Key takeaways bullet points
        6. Conclusion with CTA
        
        Target Keywords: viral video analysis, youtube success formula, content strategy
        """
        
        seo_response = await self._get_ai_analysis(seo_prompt)
        
        return {
            "blog_title": f"Viral Video Teardown: How {video_data['channel']} Got {video_data['views']:,} Views",
            "meta_description": f"Complete breakdown of {video_data['channel']}'s viral success. Extract replicable formulas from {video_data['views']:,}-view video.",
            "h1": f"Viral Formula Breakdown: {video_data['title']}",
            "content": seo_response.get("analysis", ""),
            "target_keywords": [
                "viral video teardown", "youtube success formula", "content strategy",
                "viral video analysis", f"{video_data['channel']} success"
            ]
        }
    
    def _extract_viral_triggers(self, formula: Dict[str, Any]) -> List[str]:
        """Extract viral triggers from analysis"""
        # Implementation would parse the AI analysis for trigger patterns
        return [
            "Curiosity Gap Opening",
            "Emotional Hook",
            "Social Proof",
            "Authority Positioning",
            "FOMO Element"
        ]
    
    def _extract_seo_keywords(self, formula: Dict[str, Any]) -> List[str]:
        """Extract SEO keywords from analysis"""
        return [
            "viral video strategy",
            "youtube success formula", 
            "content creation tips",
            "video marketing",
            "viral content analysis"
        ]
    
    def _extract_emotional_hooks(self, formula: Dict[str, Any]) -> List[str]:
        """Extract emotional hooks from analysis"""
        return [
            "Curiosity",
            "Urgency", 
            "Social Validation",
            "Achievement",
            "Problem Resolution"
        ]
    
    def _extract_replication_steps(self, formula: Dict[str, Any]) -> List[str]:
        """Extract actionable replication steps"""
        return [
            "Copy the opening hook structure within first 10 seconds",
            "Use similar emotional triggers throughout content",
            "Implement the pacing pattern for retention",
            "Adapt the thumbnail psychology approach",
            "Apply the title formula to your niche"
        ]
    
    def _extract_key_insights(self, formula: Dict[str, Any]) -> List[str]:
        """Extract key insights for featured highlights"""
        return [
            "The 3-second rule that hooks viewers",
            "Why this thumbnail psychology works",
            "The retention technique at 2:30 mark",
            "How they triggered the algorithm",
            "The CTA strategy that converts"
        ]

class ContentMarketingOrchestrator:
    """Orchestrate content marketing operations"""
    
    def __init__(self, youtube_api_key: str, gemini_api_key: str):
        self.detector = ViralVideoDetector(youtube_api_key, gemini_api_key)
        self.generator = TeardownReportGenerator(gemini_api_key)
        self.reports_dir = Path("content_marketing/teardown_reports")
        self.reports_dir.mkdir(parents=True, exist_ok=True)
        
    async def daily_content_generation(self) -> List[TeardownReport]:
        """Generate daily teardown reports for content marketing"""
        
        print("🔍 Scanning for viral video candidates...")
        viral_candidates = await self.detector.find_viral_candidates()
        
        reports = []
        
        # Generate reports for top 3 viral videos daily
        for i, candidate in enumerate(viral_candidates[:3]):
            print(f"📊 Generating teardown report {i+1}/3...")
            
            try:
                report = await self.generator.generate_teardown_report(candidate)
                reports.append(report)
                
                # Save report
                report_file = self.reports_dir / f"teardown_{report.report_id}.json"
                with open(report_file, 'w') as f:
                    json.dump(self._serialize_report(report), f, indent=2)
                
                print(f"✅ Report saved: {report.video_data.title}")
                
            except Exception as e:
                print(f"❌ Failed to generate report for {candidate['title']}: {e}")
        
        return reports
    
    def _serialize_report(self, report: TeardownReport) -> Dict[str, Any]:
        """Serialize report for JSON storage"""
        return {
            "report_id": report.report_id,
            "video_data": {
                "video_id": report.video_data.video_id,
                "title": report.video_data.title,
                "channel": report.video_data.channel,
                "views": report.video_data.views,
                "published_date": report.video_data.published_date,
                "growth_rate": report.video_data.growth_rate,
                "viral_triggers": report.video_data.viral_triggers,
                "seo_keywords": report.video_data.seo_keywords,
                "emotional_hooks": report.video_data.emotional_hooks
            },
            "viral_formula": report.viral_formula,
            "replication_guide": report.replication_guide,
            "seo_content": report.seo_content,
            "publish_date": report.publish_date,
            "featured_insights": report.featured_insights
        }
    
    async def publish_to_cms(self, report: TeardownReport, cms_type: str = "wordpress"):
        """Publish teardown report to CMS"""
        # Implementation would integrate with WordPress, Ghost, or other CMS
        # For now, generate HTML file ready for publication
        
        html_content = self._generate_html_report(report)
        
        html_file = self.reports_dir / f"teardown_{report.report_id}.html"
        with open(html_file, 'w') as f:
            f.write(html_content)
            
        print(f"📝 HTML report generated: {html_file}")
        
        return html_file
    
    def _generate_html_report(self, report: TeardownReport) -> str:
        """Generate HTML version of teardown report"""
        
        html_template = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>{report.seo_content['blog_title']}</title>
            <meta description="{report.seo_content['meta_description']}">
            <meta name="keywords" content="{', '.join(report.seo_content['target_keywords'])}">
        </head>
        <body>
            <h1>{report.seo_content['h1']}</h1>
            
            <div class="video-stats">
                <h2>Viral Video Performance</h2>
                <ul>
                    <li><strong>Views:</strong> {report.video_data.views:,}</li>
                    <li><strong>Growth Rate:</strong> {report.video_data.growth_rate:.1f} views/hour</li>
                    <li><strong>Channel:</strong> {report.video_data.channel}</li>
                </ul>
            </div>
            
            <div class="key-insights">
                <h2>Key Viral Insights</h2>
                <ul>
                    {''.join([f'<li>{insight}</li>' for insight in report.featured_insights])}
                </ul>
            </div>
            
            <div class="viral-triggers">
                <h2>Viral Triggers Identified</h2>
                <ul>
                    {''.join([f'<li>{trigger}</li>' for trigger in report.video_data.viral_triggers])}
                </ul>
            </div>
            
            <div class="replication-guide">
                <h2>How to Replicate This Success</h2>
                <ol>
                    {''.join([f'<li>{step}</li>' for step in report.replication_guide])}
                </ol>
            </div>
            
            <div class="cta">
                <h3>Want to Extract Viral DNA from Your Competitors?</h3>
                <p>Our 7-agent AI system analyzes any video to extract replicable success formulas.</p>
                <a href="/signup?utm_source=teardown&utm_campaign=content_marketing" class="cta-button">
                    Analyze Your First Video Free
                </a>
            </div>
        </body>
        </html>
        """
        
        return html_template

# Usage example
async def main():
    """Example usage of content marketing system"""
    
    # Initialize with API keys
    youtube_key = os.getenv('YOUTUBE_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY')
    
    if not youtube_key or not gemini_key:
        print("❌ Missing API keys")
        return
    
    # Create orchestrator
    orchestrator = ContentMarketingOrchestrator(youtube_key, gemini_key)
    
    # Generate daily reports
    reports = await orchestrator.daily_content_generation()
    
    # Publish reports
    for report in reports:
        html_file = await orchestrator.publish_to_cms(report)
        print(f"📄 Published: {html_file}")

if __name__ == "__main__":
    asyncio.run(main())
