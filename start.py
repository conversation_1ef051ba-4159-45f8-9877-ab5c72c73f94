#!/usr/bin/env python3
"""
YouTube Research v2 - Startup Script
Quick launcher for the Channel DNA Analyzer
"""

import os
import sys
import subprocess
from pathlib import Path

def check_environment():
    """Check if environment is properly configured"""
    
    print("🔍 Checking Environment...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # Check .env file
    env_file = Path(".env")
    if not env_file.exists():
        print("⚠️  .env file not found - creating template...")
        create_env_template()
        print("📝 Please edit .env file with your API keys")
        return False
    
    # Load environment variables
    from dotenv import load_dotenv
    load_dotenv()
    
    # Check required API keys
    youtube_key = os.getenv('YOUTUBE_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY')
    
    if not youtube_key:
        print("❌ YOUTUBE_API_KEY not found in .env file")
        return False
    
    print("✅ YouTube API Key found")
    
    if not gemini_key:
        print("⚠️  GEMINI_API_KEY not found - AI insights will be disabled")
    else:
        print("✅ Gemini API Key found - AI insights enabled")
    
    return True

def create_env_template():
    """Create a template .env file"""
    
    template = """# YouTube Research v2 Environment Variables
# Copy your API keys here

# Required: YouTube Data API v3 Key
# Get from: https://console.developers.google.com/
YOUTUBE_API_KEY=your_youtube_api_key_here

# Optional: Google Gemini AI API Key  
# Get from: https://makersuite.google.com/app/apikey
# Required for AI-powered narrative insights
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Supadata API Key for transcript access
SUPADATA_API_KEY=your_supadata_api_key_here
"""
    
    with open(".env", "w") as f:
        f.write(template)

def check_dependencies():
    """Check if required packages are installed"""
    
    print("\n📦 Checking Dependencies...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'google-api-python-client',
        'google-generativeai',
        'python-dotenv'
    ]
    
    missing = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing.append(package)
            print(f"❌ {package}")
    
    if missing:
        print(f"\n📥 Installing missing packages...")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", *missing
            ])
            print("✅ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            print("Please run: pip install", " ".join(missing))
            return False
    
    return True

def main():
    """Main startup function"""
    
    print("🧬 YouTube Research v2 - Channel DNA Analyzer")
    print("=" * 60)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment setup required")
        print("Please configure your .env file and try again")
        return
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency installation failed")
        return
    
    print("\n🚀 Starting Server...")
    print("🌐 Opening http://localhost:8000")
    print("⌨️  Press Ctrl+C to stop")
    print("-" * 60)
    
    # Start the application
    try:
        import app
        import uvicorn
        uvicorn.run(app.app, host="0.0.0.0", port=8000, log_level="info")
    except KeyboardInterrupt:
        print("\n👋 Shutting down...")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")

if __name__ == "__main__":
    main()