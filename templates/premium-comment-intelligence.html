<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Research v2 - Comment Intelligence</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/static/favicon.svg">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- YouTube Research v2 Design System -->
    <link rel="stylesheet" href="/static/styles/youtube-research-v2-design-system.css?v=2025073002">
</head>
<body>
    <div class="app-layout">
        <!-- Premium Sidebar -->
        <aside class="app-sidebar" id="sidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <a href="/" class="sidebar-logo">
                    <div class="sidebar-logo-icon">YR</div>
                    <span class="sidebar-logo-text">YouTube Research</span>
                </a>
                <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                    <i data-lucide="sun" class="theme-icon-light"></i>
                    <i data-lucide="moon" class="theme-icon-dark" style="display: none;"></i>
                </button>
            </div>

            <!-- Navigation Menu -->
            <nav class="sidebar-nav">
                <!-- Overview Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Overview</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/" class="nav-item" data-page="dashboard">
                                <i data-lucide="layout-dashboard" class="nav-icon"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <a href="/analytics" class="nav-item" data-page="analytics">
                                <i data-lucide="bar-chart-3" class="nav-icon"></i>
                                <span>Analytics</span>
                            </a>
                        </li>
                        <li>
                            <a href="/reports" class="nav-item" data-page="reports">
                                <i data-lucide="folder" class="nav-icon"></i>
                                <span>My Reports</span>
                                <span class="nav-badge" id="reportsCount">0</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Analysis Tools Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">AI Analysis Tools</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/video-analyzer" class="nav-item" data-page="video-analyzer">
                                <i data-lucide="video" class="nav-icon"></i>
                                <span>Video Analyzer</span>
                                <span class="nav-badge nav-badge-purple">6 AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/channel-analyzer" class="nav-item" data-page="channel-analyzer">
                                <i data-lucide="users" class="nav-icon"></i>
                                <span>Channel DNA</span>
                                <span class="nav-badge nav-badge-blue">5 AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/market-research" class="nav-item" data-page="market-research">
                                <i data-lucide="trending-up" class="nav-icon"></i>
                                <span>Market Research</span>
                                <span class="nav-badge nav-badge-green">AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/agent/comment" class="nav-item active" data-page="comment-intelligence">
                                <i data-lucide="message-circle" class="nav-icon"></i>
                                <span>Comment Intel</span>
                                <span class="nav-badge nav-badge-orange">AI</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Account Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Account</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/settings" class="nav-item" data-page="settings">
                                <i data-lucide="settings" class="nav-icon"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li>
                            <a href="/help" class="nav-item" data-page="help">
                                <i data-lucide="help-circle" class="nav-icon"></i>
                                <span>Help & Support</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="sidebar-user">
                    <div class="sidebar-user-avatar">A</div>
                    <div class="sidebar-user-info">
                        <div class="sidebar-user-name">Ardi</div>
                        <div class="sidebar-user-plan">Premium Plan</div>
                    </div>
                </div>
                <div class="version-info">
                    <span class="version-label">YouTube Research v2</span>
                    <span class="version-number">2.0.0</span>
                </div>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="app-main">
            <!-- Header -->
            <header class="app-header">
                <div class="header-left">
                    <button class="btn btn-ghost btn-sm" id="sidebar-toggle">
                        <i data-lucide="menu" class="sidebar-nav-icon"></i>
                    </button>
                    <nav class="breadcrumb">
                        <span class="breadcrumb-item">Individual Agents</span>
                        <i data-lucide="chevron-right" class="breadcrumb-separator"></i>
                        <span class="breadcrumb-item">Comment Intelligence</span>
                    </nav>
                </div>
                <div class="header-right">
                    <button class="btn btn-ghost btn-sm">
                        <i data-lucide="history" class="sidebar-nav-icon"></i>
                    </button>
                    <button class="btn btn-ghost btn-sm" id="exportBtn">
                        <i data-lucide="download" class="sidebar-nav-icon"></i>
                    </button>
                </div>
            </header>
            
            <!-- Content -->
            <div class="premium-content">
                <!-- Agent Introduction -->
                <div class="welcome-section">
                    <div>
                        <div class="form-header">
                            <div class="market-icon">
                                <i data-lucide="message-circle"></i>
                            </div>
                            <h1>Comment Intelligence Agent</h1>
                            <p>Extract strategic insights from real audience feedback with AI-powered sentiment analysis, community intelligence, and content opportunity mining.</p>
                        </div>
                    </div>
                    <div class="welcome-stats">
                        <div class="premium-card glass-card">
                            <div class="stat-item">
                                <div class="stat-value">AI</div>
                                <div class="stat-label">Agent</div>
                            </div>
                        </div>
                        <div class="premium-card glass-card">
                            <div class="stat-item">
                                <div class="stat-value">∞</div>
                                <div class="stat-label">Comments</div>
                            </div>
                        </div>
                        <div class="premium-card glass-card">
                            <div class="stat-item">
                                <div class="stat-value">100%</div>
                                <div class="stat-label">Insights</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Agent Capabilities -->
                <div class="capabilities-section">
                    <div class="premium-card">
                        <div class="premium-card-header">
                            <div>
                                <h2 class="premium-card-title">🧠 AI Agent Capabilities</h2>
                                <p class="premium-card-subtitle">Advanced comment analysis and audience intelligence</p>
                            </div>
                        </div>
                        <div class="premium-card-content">
                            <div class="capabilities-grid">
                                <div class="capability-item">
                                    <i data-lucide="heart" class="capability-icon"></i>
                                    <div class="capability-content">
                                        <h4>Sentiment Analysis</h4>
                                        <p>Real comment sentiment analysis with evidence and categorization</p>
                                    </div>
                                </div>
                                <div class="capability-item">
                                    <i data-lucide="users" class="capability-icon"></i>
                                    <div class="capability-content">
                                        <h4>Audience Validation</h4>
                                        <p>Cross-reference comments with other agent predictions</p>
                                    </div>
                                </div>
                                <div class="capability-item">
                                    <i data-lucide="lightbulb" class="capability-icon"></i>
                                    <div class="capability-content">
                                        <h4>Content Opportunities</h4>
                                        <p>Mine actual viewer requests and feedback for content ideas</p>
                                    </div>
                                </div>
                                <div class="capability-item">
                                    <i data-lucide="target" class="capability-icon"></i>
                                    <div class="capability-content">
                                        <h4>Community Intelligence</h4>
                                        <p>Analyze engagement patterns and community behavior</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Analysis Form -->
                <div class="analyzer-form-section">
                    <div class="premium-card">
                        <div class="premium-card-header">
                            <h2 class="premium-card-title">Analyze Video Comments</h2>
                            <div class="agent-status-badge">
                                <span class="status-dot"></span>
                                <span>Ready</span>
                            </div>
                        </div>
                        <div class="premium-card-content">
                            <form class="analyzer-form" id="analysisForm">
                                <div class="form-group">
                                    <label class="form-label">YouTube Video URL</label>
                                    <input type="url" class="form-input" id="videoUrl" placeholder="https://www.youtube.com/watch?v=..." required>
                                </div>
                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary btn-lg" id="analyzeBtn">
                                        <i data-lucide="play" class="btn-icon"></i>
                                        <span class="btn-text">Analyze Comments</span>
                                        <div class="btn-spinner" style="display: none;">
                                            <div class="spinner"></div>
                                        </div>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                
                <!-- Analysis Results -->
                <div class="analyzer-results" id="resultsContainer" style="display: none;">
                    <!-- Video Overview -->
                    <div class="video-overview" id="videoOverview">
                        <div class="premium-card">
                            <div class="premium-card-header">
                                <h2 class="premium-card-title">Video Overview</h2>
                            </div>
                            <div class="premium-card-content">
                                <div class="video-info" id="videoInfo">
                                    <!-- Video metadata will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Comment Analysis -->
                    <div class="analysis-section">
                        <div class="premium-card">
                            <div class="premium-card-header">
                                <div>
                                    <h2 class="premium-card-title">Comment Intelligence Analysis</h2>
                                    <p class="premium-card-subtitle">AI-powered insights from real audience feedback</p>
                                </div>
                                <div class="analysis-status" id="analysisStatus">
                                    <div class="status-indicator processing">
                                        <div class="status-spinner"></div>
                                        <span>Analyzing...</span>
                                    </div>
                                </div>
                            </div>
                            <div class="premium-card-content">
                                <div class="analysis-content" id="analysisContent">
                                    <div class="loading-placeholder">
                                        <div class="placeholder-spinner">
                                            <div class="spinner"></div>
                                        </div>
                                        <div class="placeholder-text">AI agent is analyzing comments...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Navigation Manager
        class NavigationManager {
            constructor() {
                this.currentTheme = localStorage.getItem('theme') || 'dark';
                this.init();
            }

            init() {
                this.setupThemeToggle();
                this.setupNavigation();
                this.updateActiveNavItem();
            }

            setupThemeToggle() {
                const themeToggle = document.getElementById('themeToggle');
                const lightIcon = themeToggle.querySelector('.theme-icon-light');
                const darkIcon = themeToggle.querySelector('.theme-icon-dark');

                // Apply saved theme
                document.documentElement.setAttribute('data-theme', this.currentTheme);
                this.updateThemeIcons(lightIcon, darkIcon);

                themeToggle.addEventListener('click', () => {
                    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
                    document.documentElement.setAttribute('data-theme', this.currentTheme);
                    localStorage.setItem('theme', this.currentTheme);
                    this.updateThemeIcons(lightIcon, darkIcon);

                    // Add theme transition effect
                    document.body.classList.add('theme-transitioning');
                    setTimeout(() => {
                        document.body.classList.remove('theme-transitioning');
                    }, 300);
                });
            }

            updateThemeIcons(lightIcon, darkIcon) {
                if (this.currentTheme === 'dark') {
                    lightIcon.style.display = 'none';
                    darkIcon.style.display = 'block';
                } else {
                    lightIcon.style.display = 'block';
                    darkIcon.style.display = 'none';
                }
            }

            setupNavigation() {
                // Add click handlers for navigation items
                const navItems = document.querySelectorAll('.nav-item');
                navItems.forEach(item => {
                    item.addEventListener('click', (e) => {
                        // Update active state
                        navItems.forEach(nav => nav.classList.remove('active'));
                        item.classList.add('active');

                        // Store active page
                        const page = item.getAttribute('data-page');
                        if (page) {
                            localStorage.setItem('activePage', page);
                        }
                    });
                });
            }

            updateActiveNavItem() {
                const activePage = localStorage.getItem('activePage') || 'comment-intelligence';
                const currentPath = window.location.pathname;

                // Remove all active states
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });

                // Set active based on current path or stored page
                let activeItem = document.querySelector(`[data-page="${activePage}"]`);

                if (!activeItem) {
                    // Fallback to path matching
                    activeItem = document.querySelector(`[href="${currentPath}"]`);
                }

                if (activeItem) {
                    activeItem.classList.add('active');
                }
            }
        }

        // Initialize navigation when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.navigationManager = new NavigationManager();
        });

        // Sidebar toggle functionality
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('collapsed');
        });
        
        // Form submission
        document.getElementById('analysisForm').addEventListener('submit', function(e) {
            e.preventDefault();
            startAnalysis();
        });
        
        // Global variables
        let analysisData = null;
        
        // Analysis functions
        function startAnalysis() {
            const videoUrl = document.getElementById('videoUrl').value;
            
            if (!videoUrl.trim()) {
                alert('Please enter a YouTube video URL');
                return;
            }
            
            showLoadingState();
            analyzeComments(videoUrl);
        }
        
        function showLoadingState() {
            const analyzeBtn = document.getElementById('analyzeBtn');
            const btnText = analyzeBtn.querySelector('.btn-text');
            const btnSpinner = analyzeBtn.querySelector('.btn-spinner');
            
            btnText.style.display = 'none';
            btnSpinner.style.display = 'flex';
            analyzeBtn.disabled = true;
            
            document.getElementById('resultsContainer').style.display = 'block';
            
            // Update status
            const statusIndicator = document.getElementById('analysisStatus').querySelector('.status-indicator');
            statusIndicator.className = 'status-indicator processing';
            statusIndicator.innerHTML = '<div class="status-spinner"></div><span>Analyzing...</span>';
        }
        
        function hideLoadingState() {
            const analyzeBtn = document.getElementById('analyzeBtn');
            const btnText = analyzeBtn.querySelector('.btn-text');
            const btnSpinner = analyzeBtn.querySelector('.btn-spinner');
            
            btnText.style.display = 'inline';
            btnSpinner.style.display = 'none';
            analyzeBtn.disabled = false;
            btnText.textContent = 'Analyze Another Video';
        }
        
        function analyzeComments(videoUrl) {
            // Data for standalone comment agent
            const data = {
                video_url: videoUrl,
                script: "",
                selected_agents: ["comment"],
                use_sequential_thinking: true
            };
            
            fetch('/analyze-standalone', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                
                analysisData = data;
                populateVideoInfo(data.video_data);
                populateAnalysis(data.comment_analysis);
                showCompletedState();
                hideLoadingState();
            })
            .catch(error => {
                console.error('Error:', error);
                showErrorState(error.message);
                hideLoadingState();
            });
        }
        
        function populateVideoInfo(videoData) {
            const videoInfo = document.getElementById('videoInfo');
            if (videoData) {
                const channelContext = videoData.channel_context || {};
                const performanceMetrics = videoData.performance_metrics || {};
                
                videoInfo.innerHTML = `
                    <div class="video-header">
                        <div class="video-details">
                            <h3 class="video-title">${videoData.title || 'Unknown Title'}</h3>
                            <div class="video-stats">
                                <span class="stat-item">
                                    <i data-lucide="eye"></i>
                                    ${formatNumber(videoData.views || 0)} views
                                </span>
                                <span class="stat-item">
                                    <i data-lucide="thumbs-up"></i>
                                    ${formatNumber(videoData.likes || 0)} likes
                                </span>
                                <span class="stat-item">
                                    <i data-lucide="message-circle"></i>
                                    ${formatNumber(videoData.comments || 0)} comments
                                </span>
                            </div>
                            <div class="channel-info">
                                <span class="channel-name">
                                    <i data-lucide="user"></i>
                                    ${channelContext.channel_name || 'Unknown Channel'}
                                </span>
                                <span class="channel-tier">
                                    ${channelContext.channel_tier || 'Unknown Tier'}
                                </span>
                                ${performanceMetrics.performance_note ? `
                                    <span class="performance-note ${performanceMetrics.performance_note.toLowerCase().replace(' ', '-')}">
                                        ${performanceMetrics.performance_note}
                                    </span>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                `;
            }
            
            // Re-initialize icons
            lucide.createIcons();
        }
        
        function populateAnalysis(commentAnalysis) {
            const analysisContent = document.getElementById('analysisContent');
            
            if (commentAnalysis && commentAnalysis.analysis) {
                // Format the analysis text for better display
                let formattedAnalysis = commentAnalysis.analysis;
                
                // Convert markdown-style headers to HTML
                formattedAnalysis = formattedAnalysis
                    .replace(/^### (.*$)/gm, '<h3 class="analysis-header">$1</h3>')
                    .replace(/^## (.*$)/gm, '<h2 class="analysis-header">$1</h2>')
                    .replace(/^\*\*(.*?)\*\*/gm, '<strong>$1</strong>')
                    .replace(/^\* (.*$)/gm, '<li>$1</li>')
                    .replace(/\n\n/g, '</p><p>')
                    .replace(/^(?!<h|<li|<p)(.+)$/gm, '<p>$1</p>');
                
                // Wrap lists
                formattedAnalysis = formattedAnalysis.replace(/(<li>.*?<\/li>)/gs, '<ul>$1</ul>');
                
                analysisContent.innerHTML = `
                    <div class="analysis-text">
                        ${formattedAnalysis}
                    </div>
                `;
            } else {
                analysisContent.innerHTML = `
                    <div class="no-analysis">
                        <i data-lucide="alert-circle"></i>
                        <h3>No Analysis Available</h3>
                        <p>The comment analysis could not be completed. This might be due to:</p>
                        <ul>
                            <li>Comments are disabled on this video</li>
                            <li>No comments are available</li>
                            <li>The video is private or restricted</li>
                        </ul>
                        <p>Please try another video.</p>
                    </div>
                `;
            }
            
            // Re-initialize icons
            lucide.createIcons();
        }
        
        function showCompletedState() {
            const statusIndicator = document.getElementById('analysisStatus').querySelector('.status-indicator');
            statusIndicator.className = 'status-indicator completed';
            statusIndicator.innerHTML = '<i data-lucide="check-circle"></i><span>Analysis Complete</span>';
            
            // Re-initialize icons
            lucide.createIcons();
        }
        
        function showErrorState(errorMessage) {
            const statusIndicator = document.getElementById('analysisStatus').querySelector('.status-indicator');
            statusIndicator.className = 'status-indicator error';
            statusIndicator.innerHTML = '<i data-lucide="alert-circle"></i><span>Analysis Failed</span>';
            
            const analysisContent = document.getElementById('analysisContent');
            analysisContent.innerHTML = `
                <div class="error-state">
                    <i data-lucide="alert-triangle"></i>
                    <h3>Analysis Failed</h3>
                    <p>${errorMessage}</p>
                    <button class="btn btn-secondary" onclick="location.reload()">
                        <i data-lucide="refresh-cw"></i>
                        Try Again
                    </button>
                </div>
            `;
            
            // Re-initialize icons
            lucide.createIcons();
        }
        
        // Utility functions
        function formatNumber(num) {
            if (num >= 1000000000) {
                return (num / 1000000000).toFixed(1) + 'B';
            } else if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'M';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString();
        }
        
        // Export functionality
        document.getElementById('exportBtn').addEventListener('click', function() {
            if (!analysisData) {
                alert('No analysis data to export');
                return;
            }
            
            const exportData = {
                video_data: analysisData.video_data,
                comment_analysis: analysisData.comment_analysis,
                exported_at: new Date().toISOString(),
                agent: 'Comment Intelligence'
            };
            
            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `comment_analysis_${new Date().toISOString().split('T')[0]}.json`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        });
    </script>
    
    <!-- Comment Intelligence styles now in youtube-research-v2-design-system.css -->

</body>
</html>