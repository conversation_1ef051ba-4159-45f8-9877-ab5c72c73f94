#!/usr/bin/env python3
"""
Better debug of content validation
"""

from bs4 import BeautifulSoup

# Get the actual output from the agent test
html_output = '''<div class="metrics-grid">
    <div class="metric-card"><div class="metric-value">100K</div><div class="metric-label">Views</div></div>
    <div class="metric-card"><div class="metric-value">5K</div><div class="metric-label">Likes</div></div>
    <div class="metric-card"><div class="metric-value">5%</div><div class="metric-label">Engagement</div></div>
  </div>
  <div class="insight-card">
    <div class="insight-content">Views: 100K, Likes: 5K, Engagement: 5%. Great performance!</div>
  </div>'''

original_text = "Views: 100K, Likes: 5K, Engagement: 5%. Great performance!"

print("VALIDATION DEBUG:")
print("=" * 50)

# Extract text from HTML like the code does
soup = BeautifulSoup(html_output, 'html.parser')
styled_text = soup.get_text()

print(f"Original: '{original_text}'")
print(f"Extracted raw: '{styled_text}'")
print()

# Normalize like the code does
original_clean = ' '.join(original_text.lower().split())
styled_clean = ' '.join(styled_text.lower().split())

print(f"Original clean: '{original_clean}'")
print(f"Styled clean: '{styled_clean}'")
print()

# Compare words like the code does
original_words = set(original_clean.split())
styled_words = set(styled_clean.split())

print(f"Original words: {original_words}")
print(f"Styled words: {styled_words}")
print()

preserved = original_words & styled_words
missing = original_words - styled_words

print(f"Preserved words: {preserved}")
print(f"Missing words: {missing}")
print()

preservation_ratio = len(preserved) / len(original_words) if original_words else 1
print(f"Preservation ratio: {preservation_ratio:.2f}")

# The bug is that the original text has punctuation attached to words
# But the extracted text might have different punctuation handling
print("\nLET'S CHECK PUNCTUATION HANDLING:")
import re
original_words_no_punct = set(re.findall(r'\b\w+\b', original_text.lower()))
styled_words_no_punct = set(re.findall(r'\b\w+\b', styled_text.lower()))

print(f"Original words (no punct): {original_words_no_punct}")
print(f"Styled words (no punct): {styled_words_no_punct}")

preserved_no_punct = original_words_no_punct & styled_words_no_punct
missing_no_punct = original_words_no_punct - styled_words_no_punct

print(f"Preserved (no punct): {preserved_no_punct}")
print(f"Missing (no punct): {missing_no_punct}")

preservation_ratio_no_punct = len(preserved_no_punct) / len(original_words_no_punct) if original_words_no_punct else 1
print(f"Preservation ratio (no punct): {preservation_ratio_no_punct:.2f}")