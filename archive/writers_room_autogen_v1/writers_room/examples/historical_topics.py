#!/usr/bin/env python3
"""
Historical Topics Examples for Writers' Room System
Demonstrates various historical topics with proper golden nuggets and personas
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core_system'))

from writers_room import WritersRoom, WritersRoomInput
import json

class HistoricalTopicExamples:
    """Collection of historical topic examples for testing and demonstration"""
    
    def __init__(self):
        self.writers_room = WritersRoom()
    
    def generate_example(self, topic_name: str) -> dict:
        """Generate a script for a specific historical topic example"""
        
        examples = {
            "constantinople": self._constantinople_example(),
            "rome_sasanid": self._rome_sasanid_example(),
            "dday": self._dday_example(),
            "mongol_invasions": self._mongol_invasions_example(),
            "suez_crisis": self._suez_crisis_example(),
            "berlin_airlift": self._berlin_airlift_example()
        }
        
        if topic_name not in examples:
            raise ValueError(f"Topic '{topic_name}' not found. Available: {list(examples.keys())}")
        
        input_data = examples[topic_name]
        result = self.writers_room.run_writers_room(input_data)
        
        return {
            "topic": input_data.topic,
            "input": {
                "golden_nuggets": input_data.golden_nuggets,
                "persona": input_data.persona
            },
            "output": {
                "final_script": result.final_script,
                "structure": result.structure,
                "estimated_duration": result.estimated_duration,
                "vocal_annotations": result.vocal_annotations
            }
        }
    
    def _constantinople_example(self) -> WritersRoomInput:
        """The classic Constantinople 1453 example"""
        return WritersRoomInput(
            topic="The Siege of Constantinople 1453 - How a 21-Year-Old Sultan Ended an Empire",
            golden_nuggets=[
                "The Theodosian Walls had never been breached in 1000 years",
                "Mehmed II was only 21 years old",
                "The Orban cannon weighed 17 tons and took 3 hours to reload",
                "The Golden Horn chain was made from massive iron links",
                "70 ships were moved overland in one night"
            ],
            persona={
                "channel": "Frontier",
                "voice": "Awestruck Strategist",
                "focus": "Geography and logistics determining history"
            }
        )
    
    def _rome_sasanid_example(self) -> WritersRoomInput:
        """The 400-year Rome vs Sasanid conflict"""
        return WritersRoomInput(
            topic="Rome vs the Sasanid Empire - The 400-Year War That Broke Two Superpowers",
            golden_nuggets=[
                "The conflict lasted over 400 years (224-651 CE)",
                "Both empires spent 25% of their treasuries fighting each other",
                "The Zagros Mountains created a natural fortress line",
                "Ctesiphon changed hands 5 times in 50 years",
                "Julian the Apostate died trying to capture Ctesiphon in 363 CE",
                "The war bankrupted both empires, leaving them vulnerable to Arab conquest"
            ],
            persona={
                "channel": "Frontier",
                "voice": "Awestruck Strategist",
                "focus": "Geography and logistics determining history"
            }
        )
    
    def _dday_example(self) -> WritersRoomInput:
        """D-Day Normandy landings"""
        return WritersRoomInput(
            topic="D-Day: How Weather and Geography Decided the Fate of Europe",
            golden_nuggets=[
                "Normandy beaches were chosen over Calais due to German expectations",
                "Operation Fortitude deceived Hitler about the landing location",
                "Tide charts determined the exact timing of June 6th",
                "Mulberry harbors were towed across the English Channel",
                "Weather delayed the invasion by 24 crucial hours",
                "The Atlantic Wall had a fatal weakness at Normandy"
            ],
            persona={
                "channel": "Frontier",
                "voice": "Awestruck Strategist",
                "focus": "Geography and logistics determining history"
            }
        )
    
    def _mongol_invasions_example(self) -> WritersRoomInput:
        """Mongol conquest strategy"""
        return WritersRoomInput(
            topic="The Mongol War Machine - How Geography Built the Largest Empire in History",
            golden_nuggets=[
                "The Mongol Empire covered 24 million square kilometers at its peak",
                "Mongol horses could travel 100 miles per day across steppes",
                "The Silk Road provided perfect logistics corridors",
                "Mountain passes determined which civilizations survived",
                "Genghis Khan's army was only 150,000 men at the start",
                "The Gobi Desert served as an impenetrable home base"
            ],
            persona={
                "channel": "Frontier",
                "voice": "Awestruck Strategist",
                "focus": "Geography and logistics determining history"
            }
        )
    
    def _suez_crisis_example(self) -> WritersRoomInput:
        """1956 Suez Crisis"""
        return WritersRoomInput(
            topic="The Suez Crisis - How One Canal Ended the Age of European Empires",
            golden_nuggets=[
                "The Suez Canal carried 75% of Europe's oil in 1956",
                "Nasser nationalized the canal with a 2-hour speech",
                "Britain and France secretly coordinated with Israel",
                "The canal is only 120 miles long but controls global trade",
                "Eisenhower threatened to crash the British pound",
                "The crisis lasted 100 days and changed world power forever"
            ],
            persona={
                "channel": "Frontier",
                "voice": "Awestruck Strategist",
                "focus": "Geography and logistics determining history"
            }
        )
    
    def _berlin_airlift_example(self) -> WritersRoomInput:
        """1948-49 Berlin Airlift"""
        return WritersRoomInput(
            topic="The Berlin Airlift - How Geography Almost Started World War III",
            golden_nuggets=[
                "West Berlin was 110 miles inside communist East Germany",
                "The city needed 4,500 tons of supplies daily to survive",
                "Allied planes landed every 90 seconds for 15 months",
                "Three narrow air corridors were the only legal access routes",
                "Stalin blocked all land routes with a railroad 'repair' excuse",
                "The airlift delivered 2.3 million tons of cargo total"
            ],
            persona={
                "channel": "Frontier",
                "voice": "Awestruck Strategist",
                "focus": "Geography and logistics determining history"
            }
        )

def run_all_examples():
    """Generate scripts for all historical topic examples"""
    
    examples = HistoricalTopicExamples()
    topics = ["constantinople", "rome_sasanid", "dday", "mongol_invasions", "suez_crisis", "berlin_airlift"]
    
    print("🏛️ HISTORICAL TOPICS - WRITERS' ROOM EXAMPLES")
    print("="*80)
    
    results = {}
    
    for topic in topics:
        print(f"\n📜 Generating: {topic.upper()}")
        try:
            result = examples.generate_example(topic)
            results[topic] = result
            
            # Show brief preview
            script_length = len(result['output']['final_script'])
            duration = result['output']['estimated_duration']
            annotations = result['output']['vocal_annotations']
            
            print(f"   ✅ Generated: {script_length} chars, {duration}, {annotations} vocal cues")
            
            # Save individual result
            output_path = f"../outputs/other_tests/{topic}_historical_example.json"
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            with open(output_path, 'w') as f:
                json.dump(result, f, indent=2)
                
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            results[topic] = {"error": str(e)}
    
    # Save combined results
    with open("../outputs/other_tests/all_historical_examples.json", 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n✅ ALL HISTORICAL EXAMPLES COMPLETE")
    print(f"📁 Results saved to: outputs/other_tests/")
    print(f"📊 Generated {len([r for r in results.values() if 'error' not in r])} successful scripts")
    
    return results

if __name__ == "__main__":
    run_all_examples()