<!-- YouTube Research v2 - Redesigned Navigation Component -->
<!-- Based on new design system with theme toggle and usage indicators -->

<aside class="app-sidebar" id="sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header">
        <a href="/" class="sidebar-logo">
            <div class="sidebar-logo-icon">YR</div>
            <span class="sidebar-logo-text">YouTube Research</span>
        </a>
        <button class="theme-toggle" id="themeToggle" title="Toggle theme">
            <i data-lucide="sun" class="theme-icon-light"></i>
            <i data-lucide="moon" class="theme-icon-dark" style="display: none;"></i>
        </button>
    </div>
    
    <!-- User Profile Section (if authenticated) -->
    <div class="sidebar-user-section" id="userSection" style="display: none;">
        <div class="user-profile-card">
            <div class="user-avatar" id="userAvatar">
                <i data-lucide="user"></i>
            </div>
            <div class="user-info">
                <div class="user-name" id="userName">Guest User</div>
                <div class="user-tier" id="userTier">Free Plan</div>
            </div>
        </div>
        
        <!-- Usage Indicator -->
        <div class="usage-indicator">
            <div class="usage-header">
                <span class="usage-label">Credits</span>
                <span class="usage-count" id="creditsCount">10/10</span>
            </div>
            <div class="usage-bar">
                <div class="usage-fill" id="usageFill" style="width: 100%;"></div>
            </div>
            <button class="btn btn-primary btn-sm upgrade-btn" id="upgradeBtn" style="display: none;">
                <i data-lucide="zap" class="btn-icon"></i>
                Upgrade to Pro
            </button>
        </div>
    </div>
    
    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
        <!-- Overview Section -->
        <div class="nav-section">
            <h3 class="nav-section-title">Overview</h3>
            <ul class="nav-items">
                <li>
                    <a href="/" class="nav-item" data-page="dashboard">
                        <i data-lucide="layout-dashboard" class="nav-icon"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li>
                    <a href="/analytics" class="nav-item" data-page="analytics">
                        <i data-lucide="bar-chart-3" class="nav-icon"></i>
                        <span>Analytics</span>
                    </a>
                </li>
                <li>
                    <a href="/reports" class="nav-item" data-page="reports">
                        <i data-lucide="folder" class="nav-icon"></i>
                        <span>My Reports</span>
                        <span class="nav-badge" id="reportsCount">0</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- Analysis Tools Section -->
        <div class="nav-section">
            <h3 class="nav-section-title">AI Analysis Tools</h3>
            <ul class="nav-items">
                <li>
                    <a href="/video-analyzer" class="nav-item" data-page="video-analyzer">
                        <i data-lucide="video" class="nav-icon"></i>
                        <span>Video Analyzer</span>
                        <span class="nav-badge nav-badge-purple">6 AI</span>
                    </a>
                </li>
                <li>
                    <a href="/channel-analyzer" class="nav-item" data-page="channel-analyzer">
                        <i data-lucide="users" class="nav-icon"></i>
                        <span>Channel DNA</span>
                        <span class="nav-badge nav-badge-blue">5 AI</span>
                    </a>
                </li>
                <li>
                    <a href="/market-research" class="nav-item" data-page="market-research">
                        <i data-lucide="trending-up" class="nav-icon"></i>
                        <span>Market Research</span>
                        <span class="nav-badge nav-badge-green">AI</span>
                    </a>
                </li>
                <li>
                    <a href="/competitor-tracking" class="nav-item" data-page="competitor-tracking">
                        <i data-lucide="target" class="nav-icon"></i>
                        <span>Competitor Intel</span>
                        <span class="nav-badge nav-badge-orange">Soon</span>
                    </a>
                </li>
                <li>
                    <a href="/trend-discovery" class="nav-item" data-page="trend-discovery">
                        <i data-lucide="zap" class="nav-icon"></i>
                        <span>Trend Discovery</span>
                        <span class="nav-badge nav-badge-orange">Soon</span>
                    </a>
                </li>
            </ul>
        </div>
        
        <!-- Individual Agents Section -->
        <div class="nav-section">
            <h3 class="nav-section-title">Individual Agents</h3>
            <ul class="nav-items">
                <li>
                    <a href="/agent/performance" class="nav-item" data-page="agent-performance">
                        <i data-lucide="zap" class="nav-icon"></i>
                        <span>Performance Intel</span>
                        <div class="agent-indicator agent-1"></div>
                    </a>
                </li>
                <li>
                    <a href="/agent/script" class="nav-item" data-page="agent-script">
                        <i data-lucide="file-text" class="nav-icon"></i>
                        <span>Script Forensics</span>
                        <div class="agent-indicator agent-2"></div>
                    </a>
                </li>
                <li>
                    <a href="/agent/seo" class="nav-item" data-page="agent-seo">
                        <i data-lucide="search" class="nav-icon"></i>
                        <span>SEO Discovery</span>
                        <div class="agent-indicator agent-3"></div>
                    </a>
                </li>
                <li>
                    <a href="/agent/psychology" class="nav-item" data-page="agent-psychology">
                        <i data-lucide="brain" class="nav-icon"></i>
                        <span>Psychology</span>
                        <div class="agent-indicator agent-4"></div>
                    </a>
                </li>
                <li>
                    <a href="/agent/comments" class="nav-item" data-page="agent-comments">
                        <i data-lucide="message-circle" class="nav-icon"></i>
                        <span>Comment Intel</span>
                        <div class="agent-indicator agent-5"></div>
                    </a>
                </li>
                <li>
                    <a href="/agent/synthesis" class="nav-item" data-page="agent-synthesis">
                        <i data-lucide="layers" class="nav-icon"></i>
                        <span>Strategic Synthesis</span>
                        <div class="agent-indicator agent-6"></div>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Development Tools Section -->
        <div class="nav-section">
            <h3 class="nav-section-title">Development</h3>
            <ul class="nav-items">
                <li>
                    <a href="/component-showcase" class="nav-item" data-page="component-showcase">
                        <i data-lucide="palette" class="nav-icon"></i>
                        <span>Component Showcase</span>
                        <span class="nav-badge nav-badge-gray">Dev</span>
                    </a>
                </li>
            </ul>
        </div>

        <!-- Account Section -->
        <div class="nav-section">
            <h3 class="nav-section-title">Account</h3>
            <ul class="nav-items">
                <li>
                    <a href="/settings" class="nav-item" data-page="settings">
                        <i data-lucide="settings" class="nav-icon"></i>
                        <span>Settings</span>
                    </a>
                </li>
                <li>
                    <a href="/help" class="nav-item" data-page="help">
                        <i data-lucide="help-circle" class="nav-icon"></i>
                        <span>Help & Support</span>
                    </a>
                </li>
                <li id="authSection">
                    <!-- Authentication links will be populated by JavaScript -->
                </li>
            </ul>
        </div>
    </nav>
    
    <!-- Sidebar Footer -->
    <div class="sidebar-footer">
        <div class="version-info">
            <span class="version-label">YouTube Research v2</span>
            <span class="version-number">2.0.0</span>
        </div>
        
        <!-- Easter Egg: Konami Code Hint -->
        <div class="easter-egg-hint" id="konamiHint" style="display: none;">
            <i data-lucide="gamepad-2"></i>
            <span>Try the Konami Code!</span>
        </div>
    </div>
</aside>

<!-- Navigation JavaScript -->
<script>
class NavigationManager {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.currentUser = null;
        this.konamiCode = ['ArrowUp', 'ArrowUp', 'ArrowDown', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowLeft', 'ArrowRight', 'KeyB', 'KeyA'];
        this.konamiIndex = 0;
        
        this.init();
    }
    
    init() {
        this.setupThemeToggle();
        this.setupNavigation();
        this.setupKonamiCode();
        this.loadUserData();
        this.updateActiveNavItem();
    }
    
    setupThemeToggle() {
        const themeToggle = document.getElementById('themeToggle');
        const lightIcon = themeToggle.querySelector('.theme-icon-light');
        const darkIcon = themeToggle.querySelector('.theme-icon-dark');
        
        // Apply saved theme
        document.documentElement.setAttribute('data-theme', this.currentTheme);
        this.updateThemeIcons(lightIcon, darkIcon);
        
        themeToggle.addEventListener('click', () => {
            this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', this.currentTheme);
            localStorage.setItem('theme', this.currentTheme);
            this.updateThemeIcons(lightIcon, darkIcon);
            
            // Add theme transition effect
            document.body.classList.add('theme-transitioning');
            setTimeout(() => {
                document.body.classList.remove('theme-transitioning');
            }, 300);
        });
    }
    
    updateThemeIcons(lightIcon, darkIcon) {
        if (this.currentTheme === 'dark') {
            lightIcon.style.display = 'none';
            darkIcon.style.display = 'block';
        } else {
            lightIcon.style.display = 'block';
            darkIcon.style.display = 'none';
        }
    }
    
    setupNavigation() {
        // Add click handlers for navigation items
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                // Update active state
                navItems.forEach(nav => nav.classList.remove('active'));
                item.classList.add('active');
                
                // Store active page
                const page = item.getAttribute('data-page');
                if (page) {
                    localStorage.setItem('activePage', page);
                }
            });
        });
        
        // Setup upgrade button
        const upgradeBtn = document.getElementById('upgradeBtn');
        if (upgradeBtn) {
            upgradeBtn.addEventListener('click', () => {
                this.showUpgradeModal();
            });
        }
    }
    
    setupKonamiCode() {
        document.addEventListener('keydown', (e) => {
            if (e.code === this.konamiCode[this.konamiIndex]) {
                this.konamiIndex++;
                if (this.konamiIndex === this.konamiCode.length) {
                    this.activateKonamiCode();
                    this.konamiIndex = 0;
                }
            } else {
                this.konamiIndex = 0;
            }
        });
    }
    
    activateKonamiCode() {
        // Easter egg activation
        document.body.classList.add('konami-activated');
        
        // Show special message
        const hint = document.getElementById('konamiHint');
        hint.style.display = 'flex';
        hint.innerHTML = '<i data-lucide="sparkles"></i><span>🎉 Konami Code Activated!</span>';
        
        // Add special effects
        this.createConfetti();
        
        setTimeout(() => {
            document.body.classList.remove('konami-activated');
            hint.style.display = 'none';
        }, 10000);
    }
    
    createConfetti() {
        // Simple confetti effect
        for (let i = 0; i < 50; i++) {
            const confetti = document.createElement('div');
            confetti.className = 'confetti';
            confetti.style.cssText = `
                position: fixed;
                width: 10px;
                height: 10px;
                background: hsl(${Math.random() * 360}, 70%, 60%);
                left: ${Math.random() * 100}vw;
                top: -10px;
                z-index: 10000;
                animation: confetti-fall 3s linear forwards;
            `;
            document.body.appendChild(confetti);
            
            setTimeout(() => confetti.remove(), 3000);
        }
    }
    
    async loadUserData() {
        try {
            // Check if user is authenticated
            const token = localStorage.getItem('auth_token');
            if (!token) {
                this.showGuestState();
                return;
            }
            
            // Fetch user data
            const response = await fetch('/api/auth/me', {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });
            
            if (response.ok) {
                this.currentUser = await response.json();
                this.showAuthenticatedState();
            } else {
                this.showGuestState();
            }
        } catch (error) {
            console.error('Error loading user data:', error);
            this.showGuestState();
        }
    }
    
    showAuthenticatedState() {
        const userSection = document.getElementById('userSection');
        const userName = document.getElementById('userName');
        const userTier = document.getElementById('userTier');
        const creditsCount = document.getElementById('creditsCount');
        const usageFill = document.getElementById('usageFill');
        const upgradeBtn = document.getElementById('upgradeBtn');
        const authSection = document.getElementById('authSection');
        
        userSection.style.display = 'block';
        userName.textContent = this.currentUser.full_name || this.currentUser.email;
        userTier.textContent = this.currentUser.subscription_tier === 'pro' ? 'Pro Plan' : 'Free Plan';
        
        // Update credits
        const remaining = this.currentUser.credits_remaining;
        const used = this.currentUser.credits_used;
        const total = remaining + used;
        creditsCount.textContent = `${remaining}/${total}`;
        usageFill.style.width = `${(remaining / total) * 100}%`;
        
        // Show upgrade button for free users
        if (this.currentUser.subscription_tier === 'free') {
            upgradeBtn.style.display = 'flex';
        }
        
        // Add sign out link
        authSection.innerHTML = `
            <a href="#" class="nav-item" id="signOutBtn">
                <i data-lucide="log-out" class="nav-icon"></i>
                <span>Sign Out</span>
            </a>
        `;
        
        document.getElementById('signOutBtn').addEventListener('click', (e) => {
            e.preventDefault();
            this.signOut();
        });
    }
    
    showGuestState() {
        const userSection = document.getElementById('userSection');
        const authSection = document.getElementById('authSection');
        
        userSection.style.display = 'none';
        
        // Add sign in/up links
        authSection.innerHTML = `
            <a href="/signin" class="nav-item">
                <i data-lucide="log-in" class="nav-icon"></i>
                <span>Sign In</span>
            </a>
        `;
    }
    
    updateActiveNavItem() {
        const activePage = localStorage.getItem('activePage');
        const currentPath = window.location.pathname;
        
        // Remove all active states
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Set active based on current path or stored page
        let activeItem = null;
        if (activePage) {
            activeItem = document.querySelector(`[data-page="${activePage}"]`);
        }
        
        if (!activeItem) {
            // Fallback to path matching
            activeItem = document.querySelector(`[href="${currentPath}"]`);
        }
        
        if (activeItem) {
            activeItem.classList.add('active');
        }
    }
    
    showUpgradeModal() {
        // This would show a modal with upgrade options
        alert('Upgrade to Pro for unlimited AI analysis and advanced features!');
    }
    
    async signOut() {
        try {
            await fetch('/api/auth/signout', { method: 'POST' });
        } catch (error) {
            console.error('Sign out error:', error);
        } finally {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('activePage');
            window.location.reload();
        }
    }
}

// Initialize navigation when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.navigationManager = new NavigationManager();
});

// Add confetti animation CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes confetti-fall {
        to {
            transform: translateY(100vh) rotate(360deg);
        }
    }
    
    .theme-transitioning * {
        transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
    }
`;
document.head.appendChild(style);
</script>
