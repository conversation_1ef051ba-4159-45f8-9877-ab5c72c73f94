#!/usr/bin/env python3
"""
Test script to check the full video analysis workflow including thumbnail analysis
"""

import os
import sys
import json
from working_crewai_app_tabbed import VideoAnalysisCrewAI

def test_full_analysis():
    """Test the full video analysis workflow"""
    
    print("🧪 Testing Full Video Analysis Workflow")
    print("=" * 50)
    
    # Initialize the VideoAnalysisCrewAI
    video_ai = VideoAnalysisCrewAI()
    
    # Check if AI is enabled
    print(f"AI Enabled: {video_ai.ai_enabled}")
    
    if not video_ai.ai_enabled:
        print("❌ AI is not enabled. Check your GEMINI_API_KEY environment variable.")
        return
    
    # Test with a known YouTube video
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Rick Roll
    print(f"\n🎬 Testing with video: {test_url}")
    
    # Run full analysis (this will be expensive, so we'll just check the structure)
    print("\n📊 Running full video analysis...")
    print("⚠️  Note: This will use AI credits. Press Ctrl+C to cancel if needed.")
    
    try:
        # Run the analysis with empty script (will use auto-transcript)
        result = video_ai.analyze_video(test_url, "")
        
        if 'error' in result:
            print(f"❌ Analysis failed: {result['error']}")
            return
        
        print("✅ Full analysis completed successfully!")
        print(f"   Result keys: {list(result.keys())}")
        
        # Check specifically for thumbnail analysis
        if 'thumbnail_analysis' in result:
            thumbnail_data = result['thumbnail_analysis']
            print(f"\n🖼️  Thumbnail Analysis Found:")
            print(f"   Type: {type(thumbnail_data)}")
            print(f"   Keys: {list(thumbnail_data.keys()) if isinstance(thumbnail_data, dict) else 'Not a dict'}")
            
            if isinstance(thumbnail_data, dict):
                if 'analysis' in thumbnail_data:
                    analysis_length = len(thumbnail_data['analysis'])
                    print(f"   Analysis length: {analysis_length} characters")
                    print(f"   Analysis preview: {thumbnail_data['analysis'][:200]}...")
                    
                    # Check if there's an error
                    if 'error' in thumbnail_data:
                        print(f"   ❌ Error in thumbnail data: {thumbnail_data['error']}")
                else:
                    print("   ❌ No 'analysis' key in thumbnail_data")
                    print(f"   Available keys: {list(thumbnail_data.keys())}")
        else:
            print("❌ No thumbnail_analysis in result")
            
        # Save result to file for inspection
        with open('test_analysis_result.json', 'w') as f:
            # Convert to JSON-serializable format
            json_result = {}
            for key, value in result.items():
                if isinstance(value, dict):
                    json_result[key] = value
                else:
                    json_result[key] = str(value)
            json.dump(json_result, f, indent=2)
        
        print(f"\n💾 Full result saved to test_analysis_result.json")
            
    except KeyboardInterrupt:
        print("\n⚠️  Analysis cancelled by user")
    except Exception as e:
        print(f"❌ Exception during analysis: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_full_analysis()
