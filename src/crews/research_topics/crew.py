from crewai import Agent, Crew, Task
from crewai.project import Crew<PERSON><PERSON>, agent, crew, task
import yaml
from pathlib import Path

@CrewBase
class ResearchTopicsCrew():
    """Research Topics Crew for identifying content opportunities"""
    
    agents_config = 'config/research_topics/agents.yaml'
    tasks_config = 'config/research_topics/tasks.yaml'
    
    @agent
    def trend_researcher(self) -> Agent:
        return Agent(
            config=self.agents_config['trend_researcher'],
            tools=[],  # Add trend research tools here
            verbose=True
        )
    
    @agent
    def keyword_strategist(self) -> Agent:
        return Agent(
            config=self.agents_config['keyword_strategist'],
            tools=[],  # Add keyword research tools here
            verbose=True
        )
    
    @agent
    def content_gap_analyst(self) -> Agent:
        return Agent(
            config=self.agents_config['content_gap_analyst'],
            tools=[],  # Add gap analysis tools here
            verbose=True
        )
    
    @agent
    def audience_demand_researcher(self) -> Agent:
        return Agent(
            config=self.agents_config['audience_demand_researcher'],
            tools=[],  # Add audience research tools here
            verbose=True
        )
    
    @task
    def research_trending_topics_task(self) -> Task:
        return Task(
            config=self.tasks_config['research_trending_topics'],
            agent=self.trend_researcher()
        )
    
    @task
    def develop_keyword_strategy_task(self) -> Task:
        return Task(
            config=self.tasks_config['develop_keyword_strategy'],
            agent=self.keyword_strategist()
        )
    
    @task
    def identify_content_gaps_task(self) -> Task:
        return Task(
            config=self.tasks_config['identify_content_gaps'],
            agent=self.content_gap_analyst()
        )
    
    @task
    def analyze_audience_demand_task(self) -> Task:
        return Task(
            config=self.tasks_config['analyze_audience_demand'],
            agent=self.audience_demand_researcher()
        )
    
    @crew
    def crew(self) -> Crew:
        """Creates the Research Topics crew"""
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True
        )