#!/usr/bin/env python3
"""
CORRECTED API Cost Analysis - REAL PRICING
"""

def calculate_real_costs():
    """Calculate ACTUAL API costs using real 2025 pricing"""
    
    print("💰 CORRECTED API Cost Analysis - REAL PRICING")
    print("=" * 55)
    
    # ACTUAL Gemini 2.5 Flash Pricing (2025)
    GEMINI_PRICING = {
        'input_per_million': 0.30,    # $0.30 per 1M input tokens
        'output_per_million': 2.50,   # $2.50 per 1M output tokens (including thinking)
    }
    
    # YouTube API v3 - COMPLETELY FREE
    YOUTUBE_COST = 0.00  # FREE with 10,000 quota units/day
    
    print("📊 ACTUAL Pricing (2025):")
    print(f"   • Gemini 2.5 Flash: ${GEMINI_PRICING['input_per_million']}/M input, ${GEMINI_PRICING['output_per_million']}/M output")
    print(f"   • YouTube API v3: FREE (10,000 units/day)")
    
    # Token estimates for 20-minute video analysis
    # Based on actual system usage
    
    ANALYSIS_TOKENS = {
        'input_tokens': {
            'video_metadata': 500,
            'transcript_20min': 8000,  # ~8K tokens for 20min video
            'comments_100': 3000,      # 100 comments data
            'agent_1_analysis': 14000, # Performance + context
            'agent_2_analysis': 17500, # Script + all context  
            'agent_3_analysis': 16000, # SEO + context
            'agent_4_analysis': 16000, # Psychology + context
            'agent_5_analysis': 20500, # Comments + all previous results
        },
        'output_tokens': {
            'agent_1_output': 3000,    # Performance analysis
            'agent_2_output': 4000,    # Script forensics
            'agent_3_output': 3500,    # SEO analysis
            'agent_4_output': 3500,    # Psychology analysis
            'agent_5_output': 4000,    # Comment intelligence
        }
    }
    
    # Styling agents
    STYLING_CURRENT = {
        'input_tokens': 46000,   # 5 separate styling agents
        'output_tokens': 11400
    }
    
    STYLING_OPTIMIZED = {
        'input_tokens': 25000,   # 1 unified styling agent
        'output_tokens': 8000
    }
    
    # Calculate totals
    total_analysis_input = sum(ANALYSIS_TOKENS['input_tokens'].values())
    total_analysis_output = sum(ANALYSIS_TOKENS['output_tokens'].values())
    
    print(f"\n📊 Token Usage for 20-Minute Video:")
    print(f"   Analysis Agents Input: {total_analysis_input:,} tokens")
    print(f"   Analysis Agents Output: {total_analysis_output:,} tokens")
    
    # Current system
    current_input = total_analysis_input + STYLING_CURRENT['input_tokens']
    current_output = total_analysis_output + STYLING_CURRENT['output_tokens']
    
    # Optimized system  
    optimized_input = total_analysis_input + STYLING_OPTIMIZED['input_tokens']
    optimized_output = total_analysis_output + STYLING_OPTIMIZED['output_tokens']
    
    print(f"\n🔥 CURRENT SYSTEM:")
    print(f"   Total Input: {current_input:,} tokens")
    print(f"   Total Output: {current_output:,} tokens")
    
    print(f"\n💚 OPTIMIZED SYSTEM:")
    print(f"   Total Input: {optimized_input:,} tokens")
    print(f"   Total Output: {optimized_output:,} tokens")
    
    # Calculate costs
    def calculate_gemini_cost(input_tokens, output_tokens):
        input_cost = (input_tokens / 1_000_000) * GEMINI_PRICING['input_per_million']
        output_cost = (output_tokens / 1_000_000) * GEMINI_PRICING['output_per_million']
        return input_cost + output_cost
    
    current_cost = calculate_gemini_cost(current_input, current_output)
    optimized_cost = calculate_gemini_cost(optimized_input, optimized_output)
    
    print(f"\n💸 REAL COST PER 20-MINUTE VIDEO:")
    print("=" * 45)
    
    print(f"\n🔥 CURRENT SYSTEM:")
    print(f"   Gemini Input Cost: ${(current_input/1_000_000) * GEMINI_PRICING['input_per_million']:.4f}")
    print(f"   Gemini Output Cost: ${(current_output/1_000_000) * GEMINI_PRICING['output_per_million']:.4f}")
    print(f"   YouTube API Cost: $0.0000 (FREE)")
    print(f"   TOTAL: ${current_cost:.4f}")
    
    print(f"\n💚 OPTIMIZED SYSTEM:")
    print(f"   Gemini Input Cost: ${(optimized_input/1_000_000) * GEMINI_PRICING['input_per_million']:.4f}")
    print(f"   Gemini Output Cost: ${(optimized_output/1_000_000) * GEMINI_PRICING['output_per_million']:.4f}")
    print(f"   YouTube API Cost: $0.0000 (FREE)")
    print(f"   TOTAL: ${optimized_cost:.4f}")
    
    savings = current_cost - optimized_cost
    savings_percent = (savings / current_cost) * 100
    
    print(f"\n💰 SAVINGS WITH OPTIMIZATION:")
    print(f"   Cost Reduction: ${savings:.4f} per video")
    print(f"   Percentage Savings: {savings_percent:.1f}%")
    
    # Volume projections
    volumes = [10, 50, 100, 500, 1000]
    print(f"\n📊 MONTHLY COST PROJECTIONS:")
    print("   Videos/Month | Current Cost | Optimized Cost | Savings")
    print("   " + "-" * 52)
    for volume in volumes:
        current_monthly = volume * current_cost
        optimized_monthly = volume * optimized_cost
        monthly_savings = current_monthly - optimized_monthly
        print(f"   {volume:11d} | ${current_monthly:11.2f} | ${optimized_monthly:13.2f} | ${monthly_savings:7.2f}")
    
    # Business model analysis
    revenue_per_customer = 99
    videos_break_even_current = revenue_per_customer / current_cost
    videos_break_even_optimized = revenue_per_customer / optimized_cost
    
    print(f"\n🎯 BUSINESS MODEL REALITY CHECK ($99/month):")
    print(f"   Current: {videos_break_even_current:.0f} videos/month break-even")
    print(f"   Optimized: {videos_break_even_optimized:.0f} videos/month break-even")
    print(f"   Additional capacity: {videos_break_even_optimized - videos_break_even_current:.0f} videos")
    
    # Key insights
    print(f"\n🚨 KEY INSIGHTS:")
    print(f"   1. YouTube API is FREE - not a cost factor")
    print(f"   2. Output tokens cost 8.3x more than input tokens")
    print(f"   3. Current cost: ${current_cost:.4f} per 20-min video")
    print(f"   4. At 1000 videos/month: ${current_monthly:.2f} total API costs")
    print(f"   5. API costs are {(current_monthly/revenue_per_customer)*100:.1f}% of revenue")
    
    return {
        'current_cost': current_cost,
        'optimized_cost': optimized_cost,
        'savings': savings
    }

if __name__ == "__main__":
    calculate_real_costs()