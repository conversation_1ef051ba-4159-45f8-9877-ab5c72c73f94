#!/usr/bin/env python3
"""
Export & Reporting Suite for YouTube Channel Analysis
Supports JSON, CSV, and PDF export formats
"""

import json
import csv
import io
from datetime import datetime
from typing import Dict, List, Any
from fastapi import HTTPException
from fastapi.responses import StreamingResponse
import logging

logger = logging.getLogger(__name__)

class ExportSystem:
    """Advanced export system with multiple format support"""
    
    def __init__(self):
        self.supported_formats = ['json', 'csv', 'pdf']
    
    def export_channel_data(self, data: Dict[str, Any], format_type: str, filename: str = None) -> StreamingResponse:
        """
        Export channel analysis data in the specified format
        
        Args:
            data: Channel analysis data (channel_profile + video_library)
            format_type: 'json', 'csv', or 'pdf'
            filename: Optional custom filename
        
        Returns:
            StreamingResponse with the exported data
        """
        if format_type not in self.supported_formats:
            raise HTTPException(status_code=400, detail=f"Unsupported format: {format_type}")
        
        # Generate filename if not provided
        if not filename:
            channel_name = data.get('channel_profile', {}).get('title', 'channel')
            safe_name = "".join(c for c in channel_name if c.isalnum() or c in (' ', '-', '_')).strip()
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{safe_name}_{timestamp}"
        
        # Route to appropriate export method
        if format_type == 'json':
            return self._export_json(data, filename)
        elif format_type == 'csv':
            return self._export_csv(data, filename)
        elif format_type == 'pdf':
            return self._export_pdf(data, filename)
    
    def _export_json(self, data: Dict[str, Any], filename: str) -> StreamingResponse:
        """Export as JSON with enhanced formatting"""
        
        # Add export metadata
        export_data = {
            "export_metadata": {
                "exported_at": datetime.now().isoformat(),
                "format": "json",
                "tool": "YoutubePulse Channel Analyzer"
            },
            "channel_profile": data.get('channel_profile', {}),
            "video_library": data.get('video_library', []),
            "summary": self._generate_summary(data)
        }
        
        # Create JSON string
        json_content = json.dumps(export_data, indent=2, ensure_ascii=False)
        
        # Create streaming response
        output = io.StringIO(json_content)
        
        return StreamingResponse(
            io.BytesIO(json_content.encode('utf-8')),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename={filename}.json"}
        )
    
    def _export_csv(self, data: Dict[str, Any], filename: str) -> StreamingResponse:
        """Export as CSV with comprehensive video data"""
        
        channel_profile = data.get('channel_profile', {})
        video_library = data.get('video_library', [])
        
        # Create CSV content in memory
        output = io.StringIO()
        
        # Write channel summary header
        output.write("# CHANNEL ANALYSIS REPORT\n")
        output.write(f"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        output.write(f"# Tool: YoutubePulse Channel Analyzer\n")
        output.write("\n")
        
        # Channel overview section
        output.write("## CHANNEL OVERVIEW\n")
        channel_writer = csv.writer(output)
        
        # Channel basic info
        channel_writer.writerow(["Metric", "Value"])
        channel_writer.writerow(["Channel Name", channel_profile.get('title', 'Unknown')])
        channel_writer.writerow(["Channel ID", channel_profile.get('id', 'Unknown')])
        channel_writer.writerow(["Created Date", channel_profile.get('publishedAt', 'Unknown')])
        channel_writer.writerow(["Country", channel_profile.get('country', 'Unknown')])
        
        # Channel statistics
        stats = channel_profile.get('statistics', {})
        channel_writer.writerow(["Subscribers", stats.get('subscriberCount', 0)])
        channel_writer.writerow(["Total Videos", stats.get('videoCount', 0)])
        channel_writer.writerow(["Total Views", stats.get('viewCount', 0)])
        
        # Calculate average views
        video_count = int(stats.get('videoCount', 0))
        total_views = int(stats.get('viewCount', 0))
        avg_views = total_views // video_count if video_count > 0 else 0
        channel_writer.writerow(["Average Views per Video", avg_views])
        
        output.write("\n\n")
        
        # Video library section
        output.write("## VIDEO LIBRARY\n")
        video_writer = csv.writer(output)
        
        # Video headers
        video_headers = [
            "Title", "Video ID", "Published Date", "Duration",
            "Views", "Likes", "Comments", "Description Preview",
            "YouTube URL"
        ]
        video_writer.writerow(video_headers)
        
        # Video data
        for video in video_library:
            video_stats = video.get('statistics', {})
            video_row = [
                video.get('title', ''),
                video.get('id', ''),
                video.get('publishedAt', ''),
                video.get('duration', ''),
                video_stats.get('viewCount', 0),
                video_stats.get('likeCount', 0),
                video_stats.get('commentCount', 0),
                (video.get('description', '') or '')[:100] + "..." if len(video.get('description', '') or '') > 100 else video.get('description', ''),
                f"https://youtube.com/watch?v={video.get('id', '')}"
            ]
            video_writer.writerow(video_row)
        
        # Create response
        csv_content = output.getvalue()
        output.close()
        
        return StreamingResponse(
            io.BytesIO(csv_content.encode('utf-8')),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}.csv"}
        )
    
    def _export_pdf(self, data: Dict[str, Any], filename: str) -> StreamingResponse:
        """Export as PDF report (basic implementation)"""
        
        # For now, create a simple text-based PDF-like report
        # In a full implementation, you'd use libraries like ReportLab
        
        channel_profile = data.get('channel_profile', {})
        video_library = data.get('video_library', [])
        stats = channel_profile.get('statistics', {})
        
        # Create PDF-style text report
        report_lines = [
            "YOUTUBE CHANNEL ANALYSIS REPORT",
            "=" * 50,
            "",
            f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Tool: YoutubePulse Channel Analyzer",
            "",
            "CHANNEL OVERVIEW",
            "-" * 20,
            f"Channel Name: {channel_profile.get('title', 'Unknown')}",
            f"Channel ID: {channel_profile.get('id', 'Unknown')}",
            f"Created: {channel_profile.get('publishedAt', 'Unknown')}",
            f"Country: {channel_profile.get('country', 'Unknown')}",
            "",
            "CHANNEL STATISTICS",
            "-" * 20,
            f"Subscribers: {int(stats.get('subscriberCount', 0)):,}",
            f"Total Videos: {int(stats.get('videoCount', 0)):,}",
            f"Total Views: {int(stats.get('viewCount', 0)):,}",
            "",
            f"Average Views per Video: {int(stats.get('viewCount', 0)) // max(int(stats.get('videoCount', 0)), 1):,}",
            "",
            "TOP VIDEOS",
            "-" * 20,
        ]
        
        # Add top 10 videos by views
        sorted_videos = sorted(
            video_library, 
            key=lambda v: int(v.get('statistics', {}).get('viewCount', 0)), 
            reverse=True
        )[:10]
        
        for i, video in enumerate(sorted_videos, 1):
            video_stats = video.get('statistics', {})
            views = int(video_stats.get('viewCount', 0))
            likes = int(video_stats.get('likeCount', 0))
            
            report_lines.extend([
                f"{i}. {video.get('title', 'Unknown Title')}",
                f"   Views: {views:,} | Likes: {likes:,}",
                f"   Published: {video.get('publishedAt', 'Unknown')}",
                f"   URL: https://youtube.com/watch?v={video.get('id', '')}",
                ""
            ])
        
        # Join all lines
        report_content = "\n".join(report_lines)
        
        return StreamingResponse(
            io.BytesIO(report_content.encode('utf-8')),
            media_type="text/plain",
            headers={"Content-Disposition": f"attachment; filename={filename}.txt"}
        )
    
    def _generate_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate analysis summary for JSON exports"""
        
        channel_profile = data.get('channel_profile', {})
        video_library = data.get('video_library', [])
        stats = channel_profile.get('statistics', {})
        
        # Basic calculations
        total_videos = len(video_library)
        total_views = sum(int(v.get('statistics', {}).get('viewCount', 0)) for v in video_library)
        total_likes = sum(int(v.get('statistics', {}).get('likeCount', 0)) for v in video_library)
        total_comments = sum(int(v.get('statistics', {}).get('commentCount', 0)) for v in video_library)
        
        # Average calculations
        avg_views = total_views // total_videos if total_videos > 0 else 0
        avg_likes = total_likes // total_videos if total_videos > 0 else 0
        avg_comments = total_comments // total_videos if total_videos > 0 else 0
        
        # Engagement rate (likes per view)
        engagement_rate = (total_likes / total_views * 100) if total_views > 0 else 0
        
        # Find top performing video
        top_video = max(video_library, key=lambda v: int(v.get('statistics', {}).get('viewCount', 0)), default={})
        
        return {
            "analysis_scope": {
                "videos_analyzed": total_videos,
                "date_range": {
                    "earliest_video": min((v.get('publishedAt') for v in video_library if v.get('publishedAt')), default=None),
                    "latest_video": max((v.get('publishedAt') for v in video_library if v.get('publishedAt')), default=None)
                }
            },
            "performance_metrics": {
                "total_views_analyzed": total_views,
                "total_likes_analyzed": total_likes,
                "total_comments_analyzed": total_comments,
                "average_views_per_video": avg_views,
                "average_likes_per_video": avg_likes,
                "average_comments_per_video": avg_comments,
                "engagement_rate_percent": round(engagement_rate, 2)
            },
            "top_performing_video": {
                "title": top_video.get('title'),
                "views": int(top_video.get('statistics', {}).get('viewCount', 0)),
                "url": f"https://youtube.com/watch?v={top_video.get('id', '')}" if top_video.get('id') else None
            } if top_video else None
        }
    
    def export_video_analysis(self, analysis_data: Dict[str, Any], format_type: str, filename: str = None) -> StreamingResponse:
        """
        Export video analysis data in the specified format
        
        Args:
            analysis_data: Complete video analysis result from 6 AI agents
            format_type: 'json', 'csv', 'pdf'
            filename: Optional custom filename
        
        Returns:
            StreamingResponse with the exported analysis
        """
        if format_type not in self.supported_formats:
            raise HTTPException(status_code=400, detail=f"Unsupported format: {format_type}")
        
        # Generate filename if not provided
        if not filename:
            video_data = analysis_data.get('video_data', {})
            video_title = video_data.get('title', 'video')
            safe_title = "".join(c for c in video_title if c.isalnum() or c in (' ', '-', '_')).strip()
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"VideoAnalysis_{safe_title[:30]}_{timestamp}"
        
        # Route to appropriate export method
        if format_type == 'json':
            return self._export_video_analysis_json(analysis_data, filename)
        elif format_type == 'csv':
            return self._export_video_analysis_csv(analysis_data, filename)
        elif format_type == 'pdf':
            return self._export_video_analysis_pdf(analysis_data, filename)
    
    def _export_video_analysis_json(self, analysis_data: Dict[str, Any], filename: str) -> StreamingResponse:
        """Export video analysis as JSON"""
        
        # Clean and structure the analysis data for export
        export_data = {
            "export_metadata": {
                "export_date": datetime.now().isoformat(),
                "export_type": "video_analysis",
                "analysis_version": "v2.0",
                "agents_used": ["Performance Intelligence", "Script Forensics", "SEO Analysis", "Psychology Analysis", "Comment Intelligence", "Strategic Synthesis"]
            },
            "video_information": analysis_data.get('video_data', {}),
            "analysis_results": {
                "performance_intelligence": self._clean_analysis_section(analysis_data.get('performance_analysis', {})),
                "script_forensics": self._clean_analysis_section(analysis_data.get('script_analysis', {})),
                "seo_discoverability": self._clean_analysis_section(analysis_data.get('seo_analysis', {})),
                "audience_psychology": self._clean_analysis_section(analysis_data.get('psychology_analysis', {})),
                "comment_intelligence": self._clean_analysis_section(analysis_data.get('comment_analysis', {})),
                "strategic_synthesis": self._clean_analysis_section(analysis_data.get('strategic_synthesis', {}))
            },
            "analysis_summary": self._generate_video_analysis_summary(analysis_data)
        }
        
        return StreamingResponse(
            io.BytesIO(json.dumps(export_data, indent=2, ensure_ascii=False).encode('utf-8')),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename={filename}.json"}
        )
    
    def _export_video_analysis_csv(self, analysis_data: Dict[str, Any], filename: str) -> StreamingResponse:
        """Export video analysis as CSV"""
        
        output = io.StringIO()
        
        # Video Information Section
        output.write("# VIDEO ANALYSIS EXPORT\n")
        output.write(f"# Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        # Video metadata
        video_data = analysis_data.get('video_data', {})
        output.write("## VIDEO INFORMATION\n")
        
        video_writer = csv.writer(output)
        video_writer.writerow(["Metric", "Value"])
        video_writer.writerow(["Title", video_data.get('title', 'Unknown')])
        video_writer.writerow(["Video ID", video_data.get('video_id', 'Unknown')])
        video_writer.writerow(["Channel", video_data.get('channel_title', 'Unknown')])
        video_writer.writerow(["Published Date", video_data.get('published_at', 'Unknown')])
        video_writer.writerow(["Duration", video_data.get('duration', 'Unknown')])
        video_writer.writerow(["Views", video_data.get('view_count', 0)])
        video_writer.writerow(["Likes", video_data.get('like_count', 0)])
        video_writer.writerow(["Comments", video_data.get('comment_count', 0)])
        video_writer.writerow(["URL", f"https://youtube.com/watch?v={video_data.get('video_id', '')}"])
        
        output.write("\n\n")
        
        # Analysis Results Summary
        output.write("## AI AGENT ANALYSIS SUMMARY\n")
        summary_writer = csv.writer(output)
        summary_writer.writerow(["Agent", "Key Insights", "Status"])
        
        agents = [
            ("Performance Intelligence", analysis_data.get('performance_analysis', {})),
            ("Script Forensics", analysis_data.get('script_analysis', {})),
            ("SEO Analysis", analysis_data.get('seo_analysis', {})),
            ("Psychology Analysis", analysis_data.get('psychology_analysis', {})),
            ("Comment Intelligence", analysis_data.get('comment_analysis', {})),
            ("Strategic Synthesis", analysis_data.get('strategic_synthesis', {}))
        ]
        
        for agent_name, agent_data in agents:
            analysis_text = agent_data.get('analysis', '')
            key_insights = analysis_text[:200] + "..." if len(analysis_text) > 200 else analysis_text
            status = "✅ Complete" if analysis_text else "❌ Failed"
            summary_writer.writerow([agent_name, key_insights, status])
        
        return StreamingResponse(
            io.BytesIO(output.getvalue().encode('utf-8')),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}.csv"}
        )
    
    def _export_video_analysis_pdf(self, analysis_data: Dict[str, Any], filename: str) -> StreamingResponse:
        """Export video analysis as PDF (text-based for now)"""
        
        # For now, create a detailed text report that can be easily converted to PDF
        video_data = analysis_data.get('video_data', {})
        
        report_lines = [
            "🎬 VIDEO ANALYSIS REPORT",
            "=" * 50,
            "",
            f"📊 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"🔬 Analysis Version: v2.0 (6 AI Agents)",
            "",
            "📹 VIDEO INFORMATION",
            "-" * 30,
            f"Title: {video_data.get('title', 'Unknown')}",
            f"Channel: {video_data.get('channel_title', 'Unknown')}",
            f"Published: {video_data.get('published_at', 'Unknown')}",
            f"Duration: {video_data.get('duration', 'Unknown')}",
            f"Views: {video_data.get('view_count', 0):,}",
            f"Likes: {video_data.get('like_count', 0):,}",
            f"Comments: {video_data.get('comment_count', 0):,}",
            f"URL: https://youtube.com/watch?v={video_data.get('video_id', '')}",
            "",
            "🤖 AI AGENT ANALYSIS RESULTS",
            "=" * 50,
            ""
        ]
        
        # Add each agent's analysis
        agents = [
            ("⚡ PERFORMANCE INTELLIGENCE ANALYST", analysis_data.get('performance_analysis', {})),
            ("📝 SCRIPT FORENSICS SPECIALIST", analysis_data.get('script_analysis', {})),
            ("🔍 SEO & DISCOVERABILITY EXPERT", analysis_data.get('seo_analysis', {})),
            ("🧠 AUDIENCE PSYCHOLOGY ANALYST", analysis_data.get('psychology_analysis', {})),
            ("💬 COMMENT INTELLIGENCE ANALYST", analysis_data.get('comment_analysis', {})),
            ("🎯 STRATEGIC SYNTHESIS EXPERT", analysis_data.get('strategic_synthesis', {}))
        ]
        
        for agent_title, agent_data in agents:
            analysis = agent_data.get('analysis', 'Analysis not available')
            
            report_lines.extend([
                agent_title,
                "-" * len(agent_title),
                "",
                analysis,
                "",
                "=" * 50,
                ""
            ])
        
        # Add summary
        summary = self._generate_video_analysis_summary(analysis_data)
        report_lines.extend([
            "📊 ANALYSIS SUMMARY",
            "-" * 30,
            f"Total Agents: {summary.get('agents_completed', 0)}/6",
            f"Analysis Quality: {summary.get('quality_score', 'Unknown')}",
            f"Key Recommendations: {summary.get('recommendation_count', 0)}",
            "",
            "🎯 This analysis provides professional-grade insights for content optimization.",
            "Generated by YoutubePulse AI Analysis Platform ($99/month)"
        ])
        
        report_content = "\n".join(report_lines)
        
        return StreamingResponse(
            io.BytesIO(report_content.encode('utf-8')),
            media_type="text/plain",
            headers={"Content-Disposition": f"attachment; filename={filename}.txt"}
        )
    
    def _clean_analysis_section(self, section_data: Dict[str, Any]) -> Dict[str, Any]:
        """Clean analysis section for export (remove HTML styling, keep core content)"""
        
        cleaned = {}
        for key, value in section_data.items():
            if key == 'styled_html':
                continue  # Skip HTML formatting for exports
            elif key == 'analysis' and isinstance(value, str):
                # Clean up any remaining formatting artifacts
                cleaned_text = value.replace('\n\n\n', '\n\n').strip()
                cleaned[key] = cleaned_text
            else:
                cleaned[key] = value
        
        return cleaned
    
    def _generate_video_analysis_summary(self, analysis_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate summary statistics for video analysis"""
        
        agents_completed = 0
        total_analysis_length = 0
        recommendation_count = 0
        
        sections = ['performance_analysis', 'script_analysis', 'seo_analysis', 'psychology_analysis', 'comment_analysis', 'strategic_synthesis']
        
        for section in sections:
            section_data = analysis_data.get(section, {})
            if section_data.get('analysis'):
                agents_completed += 1
                analysis_text = section_data.get('analysis', '')
                total_analysis_length += len(analysis_text)
                
                # Count recommendations (lines starting with common recommendation words)
                recommendation_words = ['recommend', 'suggest', 'should', 'consider', 'improve', 'optimize']
                for line in analysis_text.split('\n'):
                    if any(word in line.lower() for word in recommendation_words):
                        recommendation_count += 1
        
        # Calculate quality score based on completeness and depth
        completeness_score = (agents_completed / 6) * 100
        depth_score = min(100, (total_analysis_length / 5000) * 100)  # Assume 5000 chars is good depth
        quality_score = (completeness_score + depth_score) / 2
        
        return {
            "agents_completed": agents_completed,
            "total_analysis_length": total_analysis_length,
            "recommendation_count": recommendation_count,
            "quality_score": f"{quality_score:.1f}/100",
            "completeness_percentage": f"{completeness_score:.1f}%"
        }

# Create global export system instance
export_system = ExportSystem()