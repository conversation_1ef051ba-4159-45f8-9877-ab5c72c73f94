#!/usr/bin/env python3
"""
Performance Monitor - Analyze Video Analyzer Bottlenecks
Monitors actual performance without changing core code
"""

import requests
import time
import json
from datetime import datetime
import threading

class PerformanceMonitor:
    """Monitor video analysis performance in real-time"""
    
    def __init__(self):
        self.performance_data = {}
        self.monitoring = False
    
    def measure_analysis_performance(self, video_url, script="", test_name="default"):
        """Measure complete analysis performance"""
        
        print(f"🔍 Performance Analysis: {test_name}")
        print("=" * 60)
        
        # Store test metadata
        test_data = {
            'test_name': test_name,
            'video_url': video_url,
            'script_length': len(script),
            'start_time': datetime.now().isoformat(),
            'phases': {}
        }
        
        total_start = time.time()
        
        # Phase 1: API Request Initiation
        print("📊 Phase 1: Initiating analysis request...")
        phase1_start = time.time()
        
        try:
            response = requests.post(
                "http://localhost:8003/analyze-video",
                json={
                    "video_url": video_url,
                    "script": script
                },
                headers={"Content-Type": "application/json"},
                timeout=600  # 10 minute timeout
            )
            
            phase1_end = time.time()
            phase1_time = phase1_end - phase1_start
            
            if response.status_code == 200:
                print(f"✅ Analysis completed successfully")
                print(f"⏱️ Total time: {phase1_time:.1f} seconds ({phase1_time/60:.1f} minutes)")
                
                # Analyze response
                response_data = response.json()
                self._analyze_response_quality(response_data)
                
                test_data['phases']['total_analysis'] = {
                    'duration': phase1_time,
                    'status': 'success',
                    'response_size': len(response.content)
                }
                
                return test_data
                
            else:
                print(f"❌ Analysis failed: {response.status_code}")
                test_data['phases']['total_analysis'] = {
                    'duration': phase1_time,
                    'status': 'failed',
                    'error': response.status_code
                }
                return test_data
                
        except requests.exceptions.Timeout:
            phase1_end = time.time()
            phase1_time = phase1_end - phase1_start
            print(f"⏰ Analysis timed out after {phase1_time:.1f} seconds")
            test_data['phases']['total_analysis'] = {
                'duration': phase1_time,
                'status': 'timeout'
            }
            return test_data
            
        except Exception as e:
            phase1_end = time.time()
            phase1_time = phase1_end - phase1_start
            print(f"❌ Analysis error: {e}")
            test_data['phases']['total_analysis'] = {
                'duration': phase1_time,
                'status': 'error',
                'error': str(e)
            }
            return test_data
    
    def _analyze_response_quality(self, response_data):
        """Analyze the quality and completeness of the response"""
        
        print(f"\n📊 Response Quality Analysis:")
        
        # Check which agents completed
        agents = ['performance_analysis', 'script_analysis', 'seo_analysis', 
                 'psychology_analysis', 'comment_analysis', 'strategic_synthesis']
        
        completed_agents = 0
        total_analysis_length = 0
        
        for agent in agents:
            if agent in response_data and response_data[agent]:
                analysis = response_data[agent].get('analysis', '')
                if analysis and len(analysis) > 100:  # Meaningful analysis
                    completed_agents += 1
                    total_analysis_length += len(analysis)
                    print(f"   ✅ {agent}: {len(analysis):,} characters")
                else:
                    print(f"   ❌ {agent}: Failed or empty")
            else:
                print(f"   ❌ {agent}: Missing")
        
        print(f"\n📈 Quality Metrics:")
        print(f"   Agents completed: {completed_agents}/6 ({completed_agents/6*100:.1f}%)")
        print(f"   Total analysis length: {total_analysis_length:,} characters")
        print(f"   Average per agent: {total_analysis_length//max(completed_agents,1):,} characters")
        
        # Check for styling
        styled_count = 0
        for agent in agents:
            if agent in response_data and 'styled_html' in response_data[agent]:
                styled_count += 1
        
        print(f"   Styled sections: {styled_count}/6 ({styled_count/6*100:.1f}%)")
        
        return {
            'completed_agents': completed_agents,
            'total_analysis_length': total_analysis_length,
            'styled_sections': styled_count,
            'quality_score': (completed_agents/6 + styled_count/6) / 2 * 100
        }
    
    def run_performance_test_suite(self):
        """Run comprehensive performance tests"""
        
        print("🚀 Video Analyzer Performance Test Suite")
        print("=" * 60)
        
        # Test cases with different video types
        test_cases = [
            {
                'name': 'Short Video Test',
                'url': 'https://www.youtube.com/watch?v=TlB_eWDSMt4',  # ~1 minute
                'script': 'Test script for short video analysis.',
                'expected_time': '2-4 minutes'
            },
            # Add more test cases if needed
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n🧪 Test {i}/{len(test_cases)}: {test_case['name']}")
            print(f"Expected time: {test_case['expected_time']}")
            print(f"Video URL: {test_case['url']}")
            
            # Run the test
            result = self.measure_analysis_performance(
                test_case['url'], 
                test_case['script'],
                test_case['name']
            )
            
            results.append(result)
            
            # Brief pause between tests
            if i < len(test_cases):
                print(f"\n⏸️ Waiting 30 seconds before next test...")
                time.sleep(30)
        
        # Analyze overall results
        self._analyze_test_suite_results(results)
        
        return results
    
    def _analyze_test_suite_results(self, results):
        """Analyze results from multiple tests"""
        
        print(f"\n📊 PERFORMANCE TEST SUITE RESULTS")
        print("=" * 60)
        
        successful_tests = [r for r in results if r['phases']['total_analysis']['status'] == 'success']
        failed_tests = [r for r in results if r['phases']['total_analysis']['status'] != 'success']
        
        print(f"Tests completed: {len(successful_tests)}/{len(results)}")
        
        if successful_tests:
            times = [r['phases']['total_analysis']['duration'] for r in successful_tests]
            avg_time = sum(times) / len(times)
            min_time = min(times)
            max_time = max(times)
            
            print(f"\n⏱️ TIMING ANALYSIS:")
            print(f"   Average time: {avg_time:.1f}s ({avg_time/60:.1f} minutes)")
            print(f"   Fastest time: {min_time:.1f}s ({min_time/60:.1f} minutes)")
            print(f"   Slowest time: {max_time:.1f}s ({max_time/60:.1f} minutes)")
            print(f"   Time variance: {max_time - min_time:.1f}s")
            
            # Performance categorization
            if avg_time < 120:  # Under 2 minutes
                performance_rating = "🚀 Excellent"
            elif avg_time < 240:  # Under 4 minutes
                performance_rating = "✅ Good"
            elif avg_time < 360:  # Under 6 minutes
                performance_rating = "⚠️ Acceptable"
            else:
                performance_rating = "❌ Slow"
            
            print(f"   Performance rating: {performance_rating}")
        
        if failed_tests:
            print(f"\n❌ FAILED TESTS:")
            for test in failed_tests:
                print(f"   • {test['test_name']}: {test['phases']['total_analysis']['status']}")
    
    def estimate_bottlenecks(self, timing_data):
        """Estimate where bottlenecks are occurring"""
        
        print(f"\n🔍 BOTTLENECK ANALYSIS")
        print("=" * 40)
        
        total_time = timing_data['phases']['total_analysis']['duration']
        
        # Estimated breakdown based on architecture analysis
        estimated_breakdown = {
            'YouTube API calls': (10, 15),
            'Agent 1 (Performance)': (30, 60),
            'Agent 2 (Script)': (30, 60),
            'Agents 3,4 (Parallel)': (30, 60),
            'Agent 5 (Comments)': (30, 60),
            'Agent 6 (Synthesis)': (30, 60),
            'Content styling': (0.5, 2)
        }
        
        print(f"Total measured time: {total_time:.1f}s")
        print(f"\nEstimated breakdown:")
        
        for phase, (min_time, max_time) in estimated_breakdown.items():
            percentage = (min_time + max_time) / 2 / total_time * 100
            print(f"   {phase}: {min_time}-{max_time}s ({percentage:.1f}%)")
        
        # Recommendations based on timing
        print(f"\n💡 OPTIMIZATION RECOMMENDATIONS:")
        
        if total_time > 300:  # Over 5 minutes
            print(f"   🎯 HIGH PRIORITY: Implement enhanced parallelization")
            print(f"   🎯 Consider alternative LLM providers for speed")
            print(f"   🎯 Add progressive results display")
        elif total_time > 180:  # Over 3 minutes
            print(f"   ✅ Performance acceptable, minor optimizations possible")
            print(f"   🎯 Consider parallelization improvements")
        else:
            print(f"   🚀 Excellent performance, system well optimized")

def main():
    """Main performance monitoring function"""
    
    monitor = PerformanceMonitor()
    
    print("🔍 Video Analyzer Performance Monitor")
    print("=" * 60)
    print("This tool measures actual performance without changing code")
    print("Choose your test option:")
    print()
    print("1. Single video performance test")
    print("2. Comprehensive test suite")
    print("3. Quick API connectivity test")
    print()
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice == "1":
        video_url = input("Enter video URL: ").strip()
        if not video_url:
            video_url = "https://www.youtube.com/watch?v=TlB_eWDSMt4"  # Default short video
        
        result = monitor.measure_analysis_performance(video_url, "", "Single Video Test")
        monitor.estimate_bottlenecks(result)
        
    elif choice == "2":
        print("⚠️ Warning: This will run multiple tests and may take 10-20 minutes")
        confirm = input("Continue? (y/n): ").strip().lower()
        if confirm == 'y':
            monitor.run_performance_test_suite()
        else:
            print("Test cancelled")
            
    elif choice == "3":
        print("🔍 Testing API connectivity (no analysis)...")
        try:
            response = requests.get("http://localhost:8003/health", timeout=5)
            if response.status_code == 200:
                print("✅ Server is running and responsive")
                print("✅ Ready for performance testing")
            else:
                print(f"❌ Server responded with status: {response.status_code}")
        except Exception as e:
            print(f"❌ Server connection failed: {e}")
    
    else:
        print("Invalid choice")

if __name__ == "__main__":
    main()