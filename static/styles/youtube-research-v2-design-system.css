/*
YouTube Research v2 - Unified Design System
Professional Enterprise Analytics Platform
Based on Comment Intelligence Template with Monochromatic + Purple Accents
*/

/* ====== DESIGN TOKENS \u0026 FOUNDATION ====== */
:root {
  /* Monochromatic Base Palette */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8F9FA;
  --bg-tertiary: #F1F3F4;
  --bg-elevated: #FFFFFF;
  --bg-glass: rgba(255, 255, 255, 0.95);
  --bg-overlay: rgba(0, 0, 0, 0.05);
  --bg-muted: #F1F3F4;
  
  --text-primary: #1A1A1A;
  --text-secondary: #4A4A4A;
  --text-tertiary: #6B7280;
  --text-muted: #9CA3AF;
  
  --border-light: #E5E7EB;
  --border-medium: #D1D5DB;
  --border-strong: #9CA3AF;
  
  /* Purple Accent System (Existing Brand) */
  --purple-primary: #8B5CF6;
  --purple-secondary: #A78BFA;
  --purple-tertiary: #C4B5FD;
  --purple-light: #EDE9FE;
  --purple-gradient: linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%);
  
  /* AI Agent Personality Colors */
  --agent-1: #8B5CF6;  /* Performance - Purple */
  --agent-2: #3B82F6;  /* Script - Blue */
  --agent-3: #10B981;  /* SEO - Green */
  --agent-4: #F59E0B;  /* Psychology - Orange */
  --agent-5: #EF4444;  /* Comments - Red */
  --agent-6: #6366F1;  /* Synthesis - Indigo */
  
  /* Status & Feedback Colors */
  --success: #10B981;
  --success-light: #D1FAE5;
  --warning: #F59E0B;
  --warning-light: #FEF3C7;
  --error: #EF4444;
  --error-light: #FEE2E2;
  --info: #3B82F6;
  --info-light: #DBEAFE;
  
  /* Typography System */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  --font-display: 'Inter', sans-serif;
  
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  
  /* Spacing System */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */
  --space-3xl: 4rem;     /* 64px */
  
  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
  
  /* Rich Professional Shadows */
  --shadow-sm: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  --shadow-md: 0 8px 16px -4px rgba(0, 0, 0, 0.25), 0 4px 8px -2px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 16px 32px -8px rgba(0, 0, 0, 0.35), 0 8px 16px -4px rgba(0, 0, 0, 0.25);
  --shadow-xl: 0 32px 64px -16px rgba(0, 0, 0, 0.45), 0 16px 32px -8px rgba(0, 0, 0, 0.35);
  --shadow-purple: 0 16px 32px -8px rgba(139, 92, 246, 0.3), 0 8px 16px -4px rgba(139, 92, 246, 0.2);
  --shadow-glass: 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1);
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Layout */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 64px;
  --header-height: 64px;
  --content-max-width: 1400px;
}

/* Rich Dark Mode Variables - Premium Analytics Theme */
[data-theme="dark"] {
  /* Rich Dark Backgrounds with Gradients */
  --bg-primary: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
  --bg-secondary: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e1b4b 100%);
  --bg-tertiary: #1e293b;
  --bg-elevated: rgba(30, 41, 59, 0.95);
  --bg-glass: rgba(255, 255, 255, 0.08);
  --bg-overlay: rgba(255, 255, 255, 0.05);

  /* Surface Cards with Glass Morphism */
  --surface-primary: rgba(30, 41, 59, 0.8);
  --surface-secondary: rgba(51, 65, 85, 0.6);
  --surface-glass: rgba(255, 255, 255, 0.08);
  --surface-card: rgba(30, 41, 59, 0.8);

  /* Rich Text Colors */
  --text-primary: #FAFAFA;
  --text-secondary: #E2E8F0;
  --text-tertiary: #94A3B8;
  --text-muted: #64748B;

  /* Enhanced Borders */
  --border-light: rgba(255, 255, 255, 0.1);
  --border-medium: rgba(255, 255, 255, 0.15);
  --border-strong: rgba(255, 255, 255, 0.2);

  /* Rich Status Colors */
  --success-light: rgba(34, 197, 94, 0.15);
  --warning-light: rgba(251, 191, 36, 0.15);
  --error-light: rgba(239, 68, 68, 0.15);
  --info-light: rgba(59, 130, 246, 0.15);

  /* Enhanced Purple Gradient */
  --primary-gradient: linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%);
  --purple-gradient: linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%);
}

/* ====== RESET & BASE STYLES ====== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-weight-normal);
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-primary);
  overflow-x: hidden;
  transition: all var(--transition-normal);
}

/* Enhanced theme transitions */
* {
  transition: background-color var(--transition-normal), 
              color var(--transition-normal), 
              border-color var(--transition-normal),
              box-shadow var(--transition-normal);
}

/* Theme toggle button enhanced styling */
.theme-toggle {
  width: 36px;
  height: 36px;
  border: none;
  background: var(--bg-overlay);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-secondary);
  position: relative;
  overflow: hidden;
}

.theme-toggle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: var(--purple-gradient);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: all var(--transition-normal);
  z-index: 0;
}

.theme-toggle:hover::before {
  width: 100%;
  height: 100%;
}

.theme-toggle > * {
  position: relative;
  z-index: 1;
  transition: all var(--transition-fast);
}

.theme-toggle:hover {
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.theme-toggle:active {
  transform: scale(0.95);
}

/* Theme transition animation */
.theme-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Loading state for theme switch */
.theme-switching {
  pointer-events: none;
  opacity: 0.7;
}

/* Dark mode specific transitions */
[data-theme="dark"] {
  color-scheme: dark;
}

[data-theme="dark"] .theme-toggle {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
}

[data-theme="dark"] .theme-toggle:hover {
  background: var(--purple-primary);
  color: white;
}

/* Premium Glass Morphism Base */
.glass-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--radius-xl);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .glass-card {
  background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 
    0 16px 40px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* ====== LAYOUT SYSTEM ====== */
.app-layout {
  display: flex;
  min-height: 100vh;
  background: var(--bg-primary);
}

.app-sidebar {
  width: var(--sidebar-width);
  background: var(--bg-elevated);
  border-right: 1px solid var(--border-light);
  display: flex;
  flex-direction: column;
  position: fixed;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-lg);
}

.app-main {
  flex: 1;
  margin-left: var(--sidebar-width);
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  transition: margin-left var(--transition-normal);
  position: relative;
}

.app-header {
  height: var(--header-height);
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--space-xl);
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(20px);
}

.app-content {
  flex: 1;
  padding: var(--space-xl);
  background: var(--bg-secondary);
  max-width: var(--content-max-width);
  margin: 0 auto;
  width: 100%;
}

.premium-content {
  flex: 1;
  padding: var(--space-2xl) var(--space-xl);
  background: var(--bg-secondary);
  max-width: var(--content-max-width);
  margin: 0 auto;
  min-height: calc(100vh - var(--header-height));
}

/* ====== SIDEBAR COMPONENTS ====== */
.sidebar-header {
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  text-decoration: none;
  color: var(--text-primary);
  font-weight: var(--font-weight-bold);
  font-size: var(--text-lg);
}

.sidebar-logo-icon {
  width: 40px;
  height: 40px;
  background: var(--purple-gradient);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-bold);
  font-size: var(--text-lg);
}

.theme-toggle {
  width: 36px;
  height: 36px;
  border: none;
  background: var(--bg-overlay);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-secondary);
}

.theme-toggle:hover {
  background: var(--purple-light);
  color: var(--purple-primary);
  transform: scale(1.05);
}

.theme-toggle:active {
  transform: scale(0.95);
}

/* User Section */
.sidebar-user-section {
  padding: var(--space-md);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-elevated);
}

.user-profile-card {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm);
  background: var(--bg-overlay);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-md);
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: var(--purple-primary);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-semibold);
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--text-sm);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0;
}

.user-tier {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  margin: 0;
}

/* Usage Indicator */
.usage-indicator {
  background: var(--bg-overlay);
  border-radius: var(--radius-md);
  padding: var(--space-sm);
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xs);
}

.usage-label {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.usage-count {
  font-size: var(--text-xs);
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.usage-bar {
  height: 4px;
  background: var(--bg-muted);
  border-radius: var(--radius-sm);
  overflow: hidden;
  margin-bottom: var(--space-sm);
}

.usage-fill {
  height: 100%;
  background: var(--purple-gradient);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.upgrade-btn {
  width: 100%;
  justify-content: center;
  font-size: var(--text-xs);
  padding: var(--space-xs) var(--space-sm);
}

/* ====== NAVIGATION COMPONENTS ====== */
.sidebar-nav {
  flex: 1;
  padding: var(--space-lg);
  overflow-y: auto;
}

.nav-section {
  margin-bottom: var(--space-xl);
}

.nav-section-title {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-tertiary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: var(--space-md);
}

.nav-items {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  font-size: var(--text-sm);
  transition: all var(--transition-fast);
  position: relative;
  margin-bottom: var(--space-xs);
}

.nav-item:hover {
  background: var(--bg-overlay);
  color: var(--text-primary);
  transform: translateX(2px);
}

.nav-item.active {
  background: var(--purple-gradient);
  color: white;
  font-weight: var(--font-weight-semibold);
  box-shadow: var(--shadow-sm);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: -var(--space-md);
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--purple-primary);
  border-radius: 0 2px 2px 0;
}

.nav-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.nav-badge {
  margin-left: auto;
  padding: 2px 6px;
  background: var(--bg-muted);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
}

.nav-badge-purple {
  background: var(--purple-primary);
  color: white;
}

.nav-badge-blue {
  background: #3b82f6;
  color: white;
}

.nav-badge-green {
  background: #10b981;
  color: white;
}

.nav-badge-orange {
  background: #f59e0b;
  color: white;
}

.nav-item.active .nav-badge {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Agent Indicators */
.agent-indicator {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  margin-left: auto;
  background: var(--bg-muted);
  transition: var(--transition-fast);
}

.agent-1 { background: #ef4444; }
.agent-2 { background: #f97316; }
.agent-3 { background: #eab308; }
.agent-4 { background: #22c55e; }
.agent-5 { background: #3b82f6; }
.agent-6 { background: var(--purple-primary); }

.nav-item:hover .agent-indicator {
  transform: scale(1.2);
  box-shadow: 0 0 8px currentColor;
}

/* Sidebar Footer */
.sidebar-footer {
  padding: var(--space-md);
  border-top: 1px solid var(--border-light);
  background: var(--bg-elevated);
}

.sidebar-user {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-md);
  padding: var(--space-sm);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.sidebar-user-avatar {
  width: 32px;
  height: 32px;
  background: var(--purple-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-semibold);
  font-size: var(--text-sm);
  flex-shrink: 0;
}

.sidebar-user-info {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  min-width: 0;
}

.sidebar-user-name {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-user-plan {
  font-size: var(--text-xs);
  color: var(--purple-primary);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.version-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  margin-bottom: var(--space-sm);
}

.version-label {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.version-number {
  font-size: var(--text-xs);
  color: var(--purple-primary);
  font-weight: var(--font-weight-semibold);
}

.easter-egg-hint {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  padding: var(--space-xs);
  background: var(--bg-overlay);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: var(--text-xs);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Konami Code Effects */
.konami-activated .app-sidebar {
  animation: rainbow-border 2s infinite;
}

@keyframes rainbow-border {
  0% { border-right-color: #ef4444; }
  16% { border-right-color: #f97316; }
  33% { border-right-color: #eab308; }
  50% { border-right-color: #22c55e; }
  66% { border-right-color: #3b82f6; }
  83% { border-right-color: var(--purple-primary); }
  100% { border-right-color: #ef4444; }
}

.nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.nav-badge {
  margin-left: auto;
  background: var(--purple-primary);
  color: white;
  font-size: var(--text-xs);
  font-weight: var(--font-weight-semibold);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-full);
}

/* ====== CARD COMPONENTS ====== */
.card {
  background: var(--surface-card);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-glass);
  backdrop-filter: blur(12px);
  transition: all var(--transition-fast);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
  border-color: var(--border-medium);
}

.card-gradient {
  background: var(--primary-gradient);
  border: 1px solid var(--purple-tertiary);
  box-shadow: var(--shadow-purple);
  backdrop-filter: blur(12px);
}

.card-header {
  padding: var(--space-xl);
  border-bottom: 1px solid var(--border-light);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0;
}

.card-subtitle {
  font-size: var(--text-base);
  color: var(--text-secondary);
  margin: var(--space-xs) 0 0 0;
}

.card-content {
  padding: var(--space-xl);
}


/* ====== BUTTON COMPONENTS ====== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  overflow: hidden;
  line-height: 1.4;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--purple-gradient);
  color: white;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-0.5px);
  box-shadow: var(--shadow-md);
  filter: brightness(1.05);
}

.btn-secondary {
  background: var(--bg-elevated);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--bg-overlay);
  border-color: var(--purple-primary);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
}

.btn-ghost:hover:not(:disabled) {
  background: var(--bg-overlay);
  color: var(--text-primary);
}


.btn-sm {
  padding: var(--space-xs) var(--space-sm);
  font-size: var(--text-xs);
  gap: var(--space-xs);
}

.btn-lg {
  padding: var(--space-md) var(--space-lg);
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
}

.btn-icon {
  width: 16px;
  height: 16px;
}

.btn-spinner {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: inherit;
}

/* ====== COMPONENT LIBRARY STYLES ====== */

/* Shared Card Mixin */
.card-base {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  transition: var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.card-base:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* Analysis Cards */
.analysis-card {
  /* Inherit from shared card base */
  overflow: hidden;
}

.analysis-card-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  border-bottom: 1px solid var(--border-light);
  background: var(--bg-elevated);
}

.analysis-card-icon {
  width: 40px;
  height: 40px;
  background: var(--purple-gradient);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.card-icon {
  width: 20px;
  height: 20px;
}

.analysis-card-title-section {
  flex: 1;
  min-width: 0;
}

.analysis-card-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.analysis-card-subtitle {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.analysis-card-actions {
  display: flex;
  gap: var(--space-xs);
}

.card-action-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.analysis-card-content {
  padding: var(--space-lg);
}

.analysis-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  background: var(--bg-overlay);
  border-top: 1px solid var(--border-light);
}

.analysis-card-meta {
  display: flex;
  gap: var(--space-md);
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.analysis-timestamp {
  font-weight: var(--font-weight-medium);
}

.analysis-confidence {
  color: var(--purple-primary);
  font-weight: var(--font-weight-semibold);
}

.analysis-card-tags {
  display: flex;
  gap: var(--space-xs);
}

/* Agent Status Cards */
.agent-status-card {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-md);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
}

.agent-status-card:hover {
  background: var(--bg-overlay);
  border-color: var(--purple-primary);
}

.agent-avatar-section {
  position: relative;
  flex-shrink: 0;
}

.agent-avatar {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-semibold);
}

.agent-performance { background: #ef4444; }
.agent-script { background: #f97316; }
.agent-seo { background: #eab308; }
.agent-psychology { background: #22c55e; }
.agent-comments { background: #3b82f6; }
.agent-synthesis { background: var(--purple-primary); }

.agent-icon {
  width: 20px;
  height: 20px;
}

.agent-status-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: var(--radius-full);
  border: 2px solid var(--bg-elevated);
}

.status-idle { background: var(--text-tertiary); }
.status-starting { background: #f59e0b; }
.status-working { background: #3b82f6; }
.status-thinking { background: var(--purple-primary); }
.status-complete { background: #10b981; }
.status-error { background: #ef4444; }

.agent-info-section {
  flex: 1;
  min-width: 0;
}

.agent-name {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.agent-message {
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.agent-progress {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: var(--bg-muted);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: var(--purple-gradient);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

.progress-text {
  font-size: var(--text-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  min-width: 32px;
  text-align: right;
}

/* ====== AI AGENT STATUS COMPONENTS ====== */
.agent-status-feed {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  max-height: 400px;
  overflow-y: auto;
}

/* Insight Cards */
.insight-card {
  /* Inherit from shared card base */
  padding: var(--space-lg);
}

.insight-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.insight-priority {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.priority-high { background: #ef4444; }
.priority-medium { background: #f59e0b; }
.priority-low { background: #10b981; }

.priority-icon {
  width: 16px;
  height: 16px;
}

.insight-category {
  font-size: var(--text-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--purple-primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.insight-title {
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.insight-description {
  margin: 0 0 var(--space-md) 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

.insight-metrics {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-lg);
}

.metric-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.metric-label {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.metric-value {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
}

.insight-actions {
  display: flex;
  gap: var(--space-sm);
}

/* Metric Cards */
.metric-card {
  /* Inherit from shared card base */
  padding: var(--space-lg);
  backdrop-filter: blur(12px);
}

/* Stat card content styling */
.stat-value {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
}

.stat-trend {
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
}

.stat-trend.up {
  color: var(--success);
}

/* Activity items styling */
.activity-item {
  /* Inherit from shared card base */
  display: flex;
  gap: var(--space-md);
  padding: var(--space-md);
}

.activity-thumbnail {
  width: 60px;
  height: 40px;
  border-radius: var(--radius-sm);
  background: var(--bg-secondary);
}

.activity-details {
  flex: 1;
  min-width: 0;
}

.activity-details h4 {
  margin: 0 0 var(--space-xs) 0;
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.metric-icon {
  width: 40px;
  height: 40px;
  background: var(--bg-overlay);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-secondary);
}

.metric-icon-svg {
  width: 20px;
  height: 20px;
}

.metric-trend {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.trend-up { background: #10b981; }
.trend-down { background: #ef4444; }
.trend-neutral { background: var(--text-tertiary); }

.trend-icon {
  width: 16px;
  height: 16px;
}

.metric-value {
  margin: 0;
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  line-height: 1;
}

.metric-label {
  margin: var(--space-xs) 0 var(--space-sm) 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.metric-change {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.change-value {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--purple-primary);
}

.change-period {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

/* Progress Indicators */
.progress-indicator {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-md);
}

.progress-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.progress-percentage {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-bold);
  color: var(--purple-primary);
}

.progress-bar-container {
  margin-bottom: var(--space-lg);
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-md);
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  flex: 1;
}

.step-indicator {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-full);
  background: var(--bg-muted);
  transition: var(--transition-fast);
}

.progress-step.active .step-indicator {
  background: var(--purple-primary);
}

.progress-step.completed .step-indicator {
  background: #10b981;
}

.step-label {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  text-align: center;
}

.progress-step.active .step-label {
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.progress-message {
  text-align: center;
}

.current-message {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-style: italic;
}

/* Status Feed */
.status-feed {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.status-feed-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md) var(--space-lg);
  background: var(--bg-overlay);
  border-bottom: 1px solid var(--border-light);
}

.feed-title {
  margin: 0;
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.feed-icon {
  width: 16px;
  height: 16px;
}

.status-feed-content {
  max-height: 300px;
  overflow-y: auto;
  padding: var(--space-sm);
}

.status-feed-item {
  display: flex;
  gap: var(--space-sm);
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  transition: var(--transition-fast);
  animation: fadeInUp 0.3s ease-out;
}

.status-feed-item:hover {
  background: var(--bg-overlay);
}

.status-item-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: var(--purple-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.status-icon {
  width: 14px;
  height: 14px;
}

.status-item-content {
  flex: 1;
  min-width: 0;
}

.status-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xs);
}

.status-agent-name {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.status-timestamp {
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.status-message {
  margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.status-feed-footer {
  padding: var(--space-sm) var(--space-lg);
  background: var(--bg-overlay);
  border-top: 1px solid var(--border-light);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--text-xs);
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: var(--radius-full);
  background: var(--text-tertiary);
}

.connection-status.connected .status-dot {
  background: #10b981;
  animation: pulse 2s infinite;
}

.connection-status.disconnected .status-dot {
  background: #ef4444;
}

/* Loading Skeleton */
.loading-skeleton {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  animation: pulse 1.5s ease-in-out infinite;
}

.skeleton-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  background: var(--bg-muted);
  border-radius: var(--radius-full);
}

.skeleton-text {
  background: var(--bg-muted);
  border-radius: var(--radius-sm);
  height: 16px;
}

.skeleton-title {
  width: 60%;
  height: 20px;
}

.skeleton-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
}

.skeleton-line {
  width: 100%;
}

.skeleton-line.short {
  width: 70%;
}

.skeleton-footer {
  display: flex;
  gap: var(--space-sm);
}

.skeleton-button {
  height: 32px;
  background: var(--bg-muted);
  border-radius: var(--radius-md);
  width: 80px;
}

.skeleton-button.small {
  width: 60px;
}

/* Error State */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--space-xl);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
}

.error-icon {
  width: 64px;
  height: 64px;
  background: #fef2f2;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-lg);
}

.error-icon-svg {
  width: 32px;
  height: 32px;
  color: #ef4444;
}

.error-title {
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.error-message {
  margin: 0 0 var(--space-lg) 0;
  font-size: var(--text-base);
  color: var(--text-secondary);
  max-width: 400px;
}

.error-actions {
  display: flex;
  gap: var(--space-sm);
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: var(--space-xl);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
}

.empty-icon {
  width: 64px;
  height: 64px;
  background: var(--bg-overlay);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-lg);
}

.empty-icon-svg {
  width: 32px;
  height: 32px;
  color: var(--text-secondary);
}

.empty-title {
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.empty-message {
  margin: 0 0 var(--space-lg) 0;
  font-size: var(--text-base);
  color: var(--text-secondary);
  max-width: 400px;
}

.empty-actions {
  display: flex;
  gap: var(--space-sm);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}


.agent-avatar {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: var(--font-weight-bold);
  font-size: var(--text-sm);
  flex-shrink: 0;
}

.agent-avatar.agent-1 { background: var(--agent-1); }
.agent-avatar.agent-2 { background: var(--agent-2); }
.agent-avatar.agent-3 { background: var(--agent-3); }
.agent-avatar.agent-4 { background: var(--agent-4); }
.agent-avatar.agent-5 { background: var(--agent-5); }
.agent-avatar.agent-6 { background: var(--agent-6); }

.agent-status-content {
  flex: 1;
  min-width: 0;
}

.agent-name {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--text-sm);
  margin-bottom: var(--space-xs);
}

.agent-message {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  line-height: 1.5;
}


.agent-progress {
  width: 100%;
  height: 4px;
  background: var(--bg-overlay);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-top: var(--space-sm);
}


/* ====== FORM COMPONENTS ====== */
.form-group {
  margin-bottom: var(--space-lg);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.form-input {
  width: 100%;
  padding: var(--space-md);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  color: var(--text-primary);
  background: var(--bg-elevated);
  transition: all var(--transition-fast);
}

.form-input:focus {
  outline: none;
  border-color: var(--purple-primary);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.form-input::placeholder {
  color: var(--text-muted);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--space-md) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: var(--space-2xl);
}

/* ====== ANALYSIS REPORT COMPONENTS ====== */
.analysis-section {
  margin-bottom: var(--space-2xl);
}

.analysis-header {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);
}


.metric-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}



.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-md);
}

.metric-label {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: var(--space-sm) 0;
}

.metric-change {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.metric-change.positive {
  color: var(--success);
}

.metric-change.negative {
  color: var(--error);
}

.metric-change.neutral {
  color: var(--text-muted);
}

/* ====== UTILITY CLASSES ====== */
/* Layout utilities */
.flex-fill {
  flex: 1;
  min-width: 0;
}

/* Hover effects */
.hover-lift {
  transition: all var(--transition-fast);
}

.hover-lift:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-mono { font-family: var(--font-mono); }
.font-display { font-family: var(--font-display); }

.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }
.text-3xl { font-size: var(--text-3xl); }
.text-4xl { font-size: var(--text-4xl); }

.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }
.font-extrabold { font-weight: var(--font-weight-extrabold); }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-tertiary { color: var(--text-tertiary); }
.text-muted { color: var(--text-muted); }
.text-purple { color: var(--purple-primary); }
.text-success { color: var(--success); }
.text-warning { color: var(--warning); }
.text-error { color: var(--error); }

.bg-primary { background-color: var(--bg-primary); }
.bg-secondary { background-color: var(--bg-secondary); }
.bg-elevated { background-color: var(--bg-elevated); }
.bg-purple { background: var(--purple-gradient); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-purple { box-shadow: var(--shadow-purple); }

/* ====== RESPONSIVE DESIGN ====== */
@media (max-width: 768px) {
  .app-sidebar {
    width: var(--sidebar-collapsed-width);
  }

  .app-main {
    margin-left: var(--sidebar-collapsed-width);
  }

  .app-content {
    padding: var(--space-lg);
  }

  .premium-content {
    padding: var(--space-xl) var(--space-lg);
  }

  .nav-item span {
    display: none;
  }

  .metric-grid {
    grid-template-columns: 1fr;
  }

  .card-header {
    padding: var(--space-lg);
  }

  .card-content {
    padding: var(--space-lg);
  }
}

/* ====== ANIMATIONS & EASTER EGGS ====== */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
  40%, 43% { transform: translate3d(0, -8px, 0); }
  70% { transform: translate3d(0, -4px, 0); }
  90% { transform: translate3d(0, -2px, 0); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}


/* Easter Egg: Konami Code Activation */
.konami-activated {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* ====== ANALYZER COMPONENTS ====== */
/* Common analyzer form layouts used across all analysis pages */

.analyzer-form-section {
  margin-bottom: var(--space-2xl);
}

.analyzer-form {
  display: grid;
  gap: var(--space-lg);
}

.analyzer-form.grid-layout {
  grid-template-columns: 1fr 120px auto;
  align-items: end;
}

.analyzer-form.flex-layout {
  display: flex;
  flex-direction: column;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
}

/* Status and loading components */
.agent-status-badge {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  background: var(--bg-glass);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--text-muted);
}

.status-dot.active {
  background: var(--success);
  animation: pulse 2s infinite;
}

.status-dot.processing {
  background: var(--warning);
  animation: pulse 1s infinite;
}

.status-dot.error {
  background: var(--error);
}

.btn-spinner {
  display: none;
  align-items: center;
  gap: var(--space-sm);
}

.btn-spinner.active {
  display: flex;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Analysis results layouts */
.results-section {
  margin-bottom: var(--space-2xl);
}

.analyzer-form-section {
  margin-bottom: var(--space-2xl);
}

.analyzer-form-section:last-child {
  margin-bottom: 0;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-xl);
}

.analysis-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-xl);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.analysis-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.card-subtitle {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin-top: var(--space-xs);
}

/* Section headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-xl);
}

.section-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.section-subtitle {
  font-size: var(--text-base);
  color: var(--text-tertiary);
  margin-top: var(--space-xs);
}

.section-link {
  color: var(--purple-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: color var(--transition-fast);
}

.section-link:hover {
  color: var(--purple-secondary);
}

/* Common responsive patterns */
@media (max-width: 1200px) {
  .analyzer-form.grid-layout {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .results-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .analyzer-form {
    gap: var(--space-md);
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);
  }
}

/* ====== DASHBOARD SPECIFIC COMPONENTS ====== */
/* Unique dashboard layout components */

.welcome-section {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--space-2xl);
  margin-bottom: var(--space-2xl);
  align-items: start;
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.05) 0%,
    rgba(139, 92, 246, 0.02) 50%,
    rgba(139, 92, 246, 0.08) 100%);
  border-radius: var(--radius-lg);
  padding: var(--space-2xl);
  position: relative;
  overflow: hidden;
}

.welcome-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
  animation: welcomeGlow 8s ease-in-out infinite alternate;
}

@keyframes welcomeGlow {
  0% {
    opacity: 0.8;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.02);
  }
}

.welcome-section > * {
  position: relative;
  z-index: 1;
}

.welcome-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.welcome-subtitle {
  font-size: var(--text-lg);
  color: var(--text-tertiary);
}

.welcome-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-lg);
  min-width: 600px;
}

.quick-analysis-section {
  margin-bottom: var(--space-2xl);
}

.quick-analysis-form {
  display: flex;
  gap: var(--space-lg);
  align-items: flex-end;
}

.quick-analysis-form .form-group {
  flex: 1;
  margin-bottom: 0;
}

.tools-section {
  margin-bottom: var(--space-2xl);
}

.tools-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--space-xl);
}

.tool-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tool-icon {
  width: 48px;
  height: 48px;
  background: var(--purple-gradient);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-lg);
}

.tool-icon-svg {
  width: 24px;
  height: 24px;
  color: white;
}

.tool-content {
  flex: 1;
}

.tool-title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.tool-description {
  color: var(--text-tertiary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-lg);
}

.tool-features {
  display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
  flex-wrap: wrap;
}

.tool-feature {
  background: var(--bg-glass);
  color: var(--text-secondary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  border: 1px solid var(--border-medium);
}

.tool-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-lg);
  border-top: 1px solid var(--border-medium);
}

.tool-status {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
}

.tool-status.active {
  background: var(--color-success);
  color: white;
}

.tool-status.coming-soon {
  background: var(--bg-glass);
  color: var(--text-tertiary);
  border: 1px solid var(--border-medium);
}

.recent-section {
  margin-bottom: var(--space-2xl);
}

.recent-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-lg);
}

.recent-card {
  display: flex;
  gap: var(--space-lg);
  padding: var(--space-lg);
}

.recent-thumbnail {
  width: 80px;
  height: 60px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}

.recent-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recent-placeholder {
  width: 100%;
  height: 100%;
  background: var(--bg-quaternary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.recent-placeholder-icon {
  width: 24px;
  height: 24px;
  color: var(--text-muted);
}

.recent-overlay {
  position: absolute;
  top: var(--space-xs);
  right: var(--space-xs);
}

.recent-score {
  background: var(--purple-gradient);
  color: white;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-bold);
}

.recent-content {
  flex: 1;
}

.recent-title {
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recent-meta {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin-bottom: var(--space-sm);
}

.recent-insights {
  display: flex;
  gap: var(--space-xs);
  flex-wrap: wrap;
}

.recent-insight {
  background: var(--bg-glass);
  color: var(--text-secondary);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  border: 1px solid var(--border-medium);
}

.usage-section {
  margin-bottom: var(--space-2xl);
}

.usage-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-xl);
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.usage-title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.usage-price {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-bold);
  color: var(--purple-primary);
}

.usage-progress {
  margin-bottom: var(--space-lg);
}

.progress-bar {
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--space-sm);
}

.progress-fill {
  height: 100%;
  background: var(--purple-gradient);
  transition: width var(--transition-normal);
}

.progress-text {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

.usage-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.usage-feature {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  color: var(--text-secondary);
}

.usage-icon {
  width: 16px;
  height: 16px;
  color: var(--color-success);
}

.stats-title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-lg);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-left: var(--space-lg);
}

.breadcrumb-item {
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
}

.header-left, .header-right {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.metric-icon {
  width: 14px;
  height: 14px;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* Dashboard responsive adjustments */
@media (max-width: 1200px) {
  .welcome-stats {
    min-width: auto;
    grid-template-columns: 1fr;
  }

  .welcome-section {
    grid-template-columns: 1fr;
    gap: var(--space-xl);
  }

  .usage-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .quick-analysis-form {
    flex-direction: column;
    align-items: stretch;
  }

  .tools-grid {
    grid-template-columns: 1fr;
  }

  .recent-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* ====== COMMENT INTELLIGENCE SPECIFIC COMPONENTS ====== */
/* Agent introduction and capabilities display */

.agent-intro-section {
  margin-bottom: var(--space-2xl);
}

.agent-badge-large {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-lg);
  background: var(--bg-glass);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-xl);
}

.agent-icon-large {
  width: 32px;
  height: 32px;
  color: var(--purple-primary);
}

.agent-label {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.agent-capabilities {
  margin-top: var(--space-xl);
}

.capabilities-title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
}

.capabilities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-lg);
}

.capability-item {
  display: flex;
  gap: var(--space-md);
  padding: var(--space-lg);
  background: var(--bg-glass);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-lg);
  transition: all var(--transition-fast);
}

.capability-item:hover {
  background: var(--bg-glass-hover);
  transform: translateY(-1px);
}

.capability-icon {
  width: 20px;
  height: 20px;
  color: var(--purple-primary);
  flex-shrink: 0;
  margin-top: 2px;
}

.capability-content h4 {
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.capability-content p {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  line-height: var(--line-height-relaxed);
}

/* Analysis display components */
.placeholder-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: var(--space-lg);
}

.placeholder-spinner .spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(99, 102, 241, 0.3);
  border-top-color: var(--purple-primary);
}

.placeholder-text {
  color: var(--text-tertiary);
  font-size: var(--text-base);
}

.analysis-text {
  line-height: var(--line-height-relaxed);
  color: var(--text-secondary);
}

.analysis-text h2 {
  color: var(--text-primary);
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  margin: var(--space-xl) 0 var(--space-md);
  border-bottom: 1px solid var(--border-medium);
  padding-bottom: var(--space-sm);
}

.analysis-text h3 {
  color: var(--text-primary);
  font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);
  margin: var(--space-lg) 0 var(--space-sm);
}

.analysis-text p {
  margin-bottom: var(--space-md);
}

.analysis-text ul {
  margin: var(--space-md) 0;
  padding-left: var(--space-xl);
}

.analysis-text li {
  margin-bottom: var(--space-sm);
}

.analysis-text strong {
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
}

.no-analysis, .error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 300px;
  gap: var(--space-lg);
  color: var(--text-tertiary);
}

.no-analysis i, .error-state i {
  width: 48px;
  height: 48px;
  color: var(--text-quaternary);
}

.no-analysis h3, .error-state h3 {
  color: var(--text-secondary);
  font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);
  margin: 0;
}

.no-analysis ul {
  text-align: left;
  max-width: 400px;
}

.breadcrumb-separator {
  width: 16px;
  height: 16px;
  color: var(--text-quaternary);
}

/* Video overview and analysis components */
.video-overview {
  margin-bottom: var(--space-2xl);
}

.video-header {
  display: flex;
  gap: var(--space-lg);
  align-items: start;
}

.video-details {
  flex: 1;
}

.video-title {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  line-height: var(--line-height-tight);
}

.video-stats {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-sm);
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  color: var(--text-tertiary);
  font-size: var(--text-sm);
}

.stat-item i {
  width: 14px;
  height: 14px;
}

.channel-info {
  display: flex;
  gap: var(--space-lg);
  align-items: center;
  flex-wrap: wrap;
}

.channel-name {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
}

.channel-name i {
  width: 14px;
  height: 14px;
}

.channel-tier {
  padding: var(--space-xs) var(--space-sm);
  background: var(--bg-glass);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  color: var(--text-tertiary);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
}

.performance-note {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
}

.performance-note.viral-performance {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.performance-note.strong-performance {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.analysis-section {
  margin-bottom: var(--space-2xl);
}

.analysis-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space-md);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
}

.status-indicator.processing {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

.status-indicator.completed {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-indicator.error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  border-top-color: #3b82f6;
  animation: spin 1s linear infinite;
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: var(--space-lg);
}

/* Comment Intelligence responsive adjustments */
@media (max-width: 768px) {
  .capabilities-grid {
    grid-template-columns: 1fr;
  }

  .video-header {
    flex-direction: column;
  }

  .video-stats, .channel-info {
    flex-direction: column;
    gap: var(--space-sm);
  }
}

/* ====== PREMIUM CARD SYSTEM ====== */
.premium-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-xl);
  padding: var(--space-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.premium-card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

.premium-card-gradient {
  background:
    linear-gradient(135deg, #8b5cf6 0%, #7c3aed 25%, #6d28d9 50%, #5b21b6 75%, #4c1d95 100%),
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
    linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
  color: white;
  border: none;
  position: relative;
  overflow: hidden;
}

.premium-card-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    repeating-linear-gradient(
      45deg,
      transparent,
      transparent 20px,
      rgba(255, 255, 255, 0.01) 20px,
      rgba(255, 255, 255, 0.01) 40px
    );
  pointer-events: none;
}

.premium-card-gradient .premium-card-title,
.premium-card-gradient .premium-card-subtitle {
  color: white;
}

.premium-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-lg);
  padding: var(--space-lg);
  margin: calc(-1 * var(--space-lg)) calc(-1 * var(--space-lg)) var(--space-lg) calc(-1 * var(--space-lg));
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.03) 0%,
    rgba(139, 92, 246, 0.01) 50%,
    rgba(139, 92, 246, 0.05) 100%);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  position: relative;
  overflow: hidden;
}

.premium-card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(45deg, transparent 30%, rgba(139, 92, 246, 0.02) 50%, transparent 70%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.04) 0%, transparent 50%);
  pointer-events: none;
}

.premium-card-header > * {
  position: relative;
  z-index: 1;
}

.premium-card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.premium-card-subtitle {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

.premium-card-icon {
  width: 24px;
  height: 24px;
  color: var(--purple-primary);
  flex-shrink: 0;
}

.premium-card-content {
  flex: 1;
}

.premium-card-description {
  color: var(--text-tertiary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-lg);
}

/* ====== DASHBOARD SPECIFIC CARDS ====== */
.tool-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tool-icon {
  width: 48px;
  height: 48px;
  background: var(--purple-gradient);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-lg);
}

.tool-icon-svg {
  width: 24px;
  height: 24px;
  color: white;
}

.tool-content {
  flex: 1;
}

.tool-title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.tool-description {
  color: var(--text-tertiary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-lg);
}

.tool-features {
  display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
  flex-wrap: wrap;
}

.tool-feature {
  background: var(--bg-glass);
  color: var(--text-secondary);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  border: 1px solid var(--border-medium);
}

.tool-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-lg);
  border-top: 1px solid var(--border-medium);
}

.tool-status {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
}

.tool-status.active {
  background: var(--success);
  color: white;
}

.tool-status.coming-soon {
  background: var(--bg-glass);
  color: var(--text-tertiary);
  border: 1px solid var(--border-medium);
}

.recent-card {
  display: flex;
  gap: var(--space-lg);
  padding: var(--space-lg);
}

.recent-thumbnail {
  width: 80px;
  height: 60px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
}

.recent-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.recent-placeholder {
  width: 100%;
  height: 100%;
  background: var(--bg-tertiary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.recent-placeholder-icon {
  width: 24px;
  height: 24px;
  color: var(--text-muted);
}

.recent-overlay {
  position: absolute;
  top: var(--space-xs);
  right: var(--space-xs);
}

.recent-score {
  background: var(--purple-gradient);
  color: white;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-bold);
}

.recent-content {
  flex: 1;
}

.recent-title {
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.recent-meta {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin-bottom: var(--space-sm);
}

.recent-insights {
  display: flex;
  gap: var(--space-xs);
  flex-wrap: wrap;
}

.recent-insight {
  background: var(--bg-glass);
  color: var(--text-secondary);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
  border: 1px solid var(--border-medium);
}

.usage-card {
  padding: var(--space-xl);
}

.usage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space-lg);
}

.usage-title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.usage-price {
  font-size: var(--text-xl);
  font-weight: var(--font-weight-bold);
  color: var(--purple-primary);
}

.usage-progress {
  margin-bottom: var(--space-lg);
}

.progress-bar {
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--space-sm);
}

.progress-fill {
  height: 100%;
  background: var(--purple-gradient);
  transition: width var(--transition-normal);
}

.progress-text {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

.usage-features {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.usage-feature {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  color: var(--text-secondary);
}

.usage-icon {
  width: 16px;
  height: 16px;
  color: var(--success);
}

.stats-card {
  padding: var(--space-xl);
}

.stats-title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
}

.stat-item {
  text-align: center;
}

.stat-value {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

/* ====== HISTORY DROPDOWN COMPONENTS ====== */
.history-dropdown-container {
  position: relative;
}

.history-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  min-width: 300px;
  max-width: 400px;
  z-index: 1000;
  margin-top: var(--space-xs);
}

.history-header {
  padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid var(--border-light);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--text-sm);
}

.history-empty {
  padding: var(--space-xl);
  text-align: center;
  color: var(--text-tertiary);
  font-size: var(--text-sm);
}

.history-item {
  padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid var(--border-light);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.history-item:hover {
  background: var(--bg-tertiary);
}

.history-item:last-child {
  border-bottom: none;
}

.history-title {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.history-meta {
  font-size: var(--text-xs);
  color: var(--text-tertiary);
}

/* ====== EXPORT SECTION COMPONENTS ====== */
.export-section {
  margin-bottom: var(--space-2xl);
}

.export-status {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--success);
}

.export-buttons {
  display: flex;
  gap: var(--space-md);
  flex-wrap: wrap;
}

.export-buttons .btn {
  flex: 1;
  min-width: 140px;
}

/* ====== ADDITIONAL CARD TYPES ====== */

/* Channel Analyzer Results */
.analyzer-results {
  display: block;
  margin-top: var(--space-xl);
  padding: var(--space-lg);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-sm);
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.channel-overview {
  margin-bottom: var(--space-2xl);
}

.channel-header {
  display: flex;
  align-items: center;
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.channel-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid var(--border-light);
}

.channel-info h2 {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-xs) 0;
}

.channel-info p {
  color: var(--text-secondary);
  margin: 0;
}

.channel-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-xl);
}

.stat-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  text-align: center;
  transition: all var(--transition-normal);
}

.stat-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  background: var(--purple-gradient);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-md);
}

.stat-icon i {
  width: 20px;
  height: 20px;
  color: white;
}

.video-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: all var(--transition-normal);
}

.video-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.video-thumbnail {
  width: 100%;
  aspect-ratio: 16/9;
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-bottom: var(--space-md);
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.insight-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  transition: all var(--transition-normal);
}

.insight-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.insight-card h4 {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  margin-bottom: var(--space-xs);
}

.title-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-md);
  transition: all var(--transition-normal);
}

.title-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.title-text {
  font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  line-height: var(--line-height-relaxed);
}

.trigger-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-md);
  transition: all var(--transition-normal);
}

.trigger-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.trigger-card h4 {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--purple-primary);
  margin-bottom: var(--space-sm);
  text-transform: capitalize;
}

.template-card {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  margin-bottom: var(--space-md);
  transition: all var(--transition-normal);
}

.template-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.template-card h4 {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  margin-bottom: var(--space-sm);
}

/* ====== MARKET RESEARCH HUB SPECIFIC COMPONENTS ====== */
/* Research form layouts and tabbed interfaces */

.input-group {
  display: flex;
  gap: var(--space-md);
}

.input-group .form-input {
  flex: 1;
}

.form-label-icon {
  width: 16px;
  height: 16px;
  color: var(--purple-primary);
}

.loading-section, .error-section {
  margin-bottom: var(--space-2xl);
}

.loading-content, .error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  gap: var(--space-lg);
  text-align: center;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--border-light);
  border-top-color: var(--purple-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text, .error-text {
  color: var(--text-tertiary);
  font-size: var(--text-lg);
}

.error-icon {
  width: 48px;
  height: 48px;
  color: var(--error);
}

/* Tabbed interface components */
.tab-buttons {
  display: flex;
  gap: var(--space-sm);
  flex-wrap: wrap;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md);
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
  min-width: 200px;
}

.tab-button:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-medium);
  color: var(--text-primary);
}

.tab-button.active {
  background: var(--purple-primary);
  border-color: var(--purple-primary);
  color: white;
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.tab-icon {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.tab-content {
  display: none;
  margin-bottom: var(--space-xl);
}

.tab-content.active {
  display: block;
}

/* Statistics display components */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: var(--space-lg);
}

.stat-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--space-lg);
  display: flex;
  align-items: center;
  gap: var(--space-md);
  transition: all 0.2s ease;
}

.stat-card:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-medium);
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  background: var(--purple-primary);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin-top: var(--space-xs);
}

/* Research recommendations and insights */
.recommendations-section {
  margin-top: var(--space-xl);
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.recommendation-card {
  border-left: 4px solid var(--purple-primary);
  transition: all 0.3s ease;
}

.recommendation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.recommendation-layout {
  display: flex;
  gap: var(--space-md);
}

.recommendation-number {
  width: 32px;
  height: 32px;
  background: var(--purple-primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--text-sm);
  flex-shrink: 0;
}

.recommendation-content {
  flex: 1;
}

.recommendation-title {
  margin: 0 0 var(--space-xs) 0;
  color: var(--text-primary);
  font-size: var(--text-base);
  font-weight: 600;
}

.recommendation-description {
  margin: 0 0 var(--space-sm) 0;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  line-height: 1.6;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  display: inline-block;
}

.priority-high {
  background: rgba(239, 68, 68, 0.1);
  color: #EF4444;
}

.priority-medium {
  background: rgba(245, 158, 11, 0.1);
  color: #F59E0B;
}

.priority-low {
  background: rgba(34, 197, 94, 0.1);
  color: #22C55E;
}

/* Video display components */
.video-thumbnail {
  position: relative;
  aspect-ratio: 16/9;
  overflow: hidden;
  background: var(--bg-tertiary);
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.video-card:hover .video-thumbnail img {
  transform: scale(1.05);
}

/* Channel Analyzer Video Grid */
.videos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-xl);
}

.video-content {
  padding: var(--space-md);
}

.video-title {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-sm) 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-stats {
  display: flex;
  gap: var(--space-md);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.stat-item i {
  width: 14px;
  height: 14px;
}

.video-duration {
  position: absolute;
  bottom: var(--space-xs);
  right: var(--space-xs);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
}

.shorts-duration {
  background: var(--purple-primary);
}

.shorts-indicator {
  position: absolute;
  top: var(--space-xs);
  left: var(--space-xs);
  background: var(--purple-primary);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
}

/* Schedule Analysis Styles */
.schedule-analysis-results {
  margin-top: var(--space-xl);
}

.schedule-overview {
  margin-bottom: var(--space-xl);
}

.schedule-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-lg);
}

.schedule-stat {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  padding: var(--space-lg);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
}

.schedule-patterns {
  margin-bottom: var(--space-xl);
}

.day-analysis {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  margin-top: var(--space-lg);
}

.day-bar {
  display: flex;
  align-items: center;
  gap: var(--space-md);
}

.day-name {
  min-width: 80px;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.day-bar-container {
  flex: 1;
  height: 20px;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.day-bar-fill {
  height: 100%;
  background: var(--purple-gradient);
  transition: width var(--transition-normal);
}

.day-percentage {
  min-width: 50px;
  text-align: right;
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

/* Title Analysis Styles */
.title-analysis-results {
  margin-top: var(--space-xl);
}

.title-overview {
  margin-bottom: var(--space-xl);
}

.title-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-lg);
}

.title-stat {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  padding: var(--space-lg);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  text-align: center;
}

.title-performance {
  margin-bottom: var(--space-xl);
}

.top-titles {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
  margin-top: var(--space-lg);
}

.title-card {
  padding: var(--space-lg);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
}

.title-text {
  font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin: 0 0 var(--space-md) 0;
  line-height: 1.4;
}

.title-metrics {
  display: flex;
  gap: var(--space-lg);
  color: var(--text-secondary);
  font-size: var(--text-sm);
}

.metric {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.psychology-analysis {
  margin-bottom: var(--space-xl);
}

.trigger-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg);
  margin-top: var(--space-lg);
}

.trigger-card {
  padding: var(--space-lg);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  text-align: center;
}

.trigger-card h4 {
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0 0 var(--space-md) 0;
}

.trigger-stats {
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.psychology-insights {
  margin-top: var(--space-lg);
  padding: var(--space-lg);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
}

.psychology-insights p {
  margin: 0 0 var(--space-sm) 0;
  color: var(--text-secondary);
}

.psychology-insights p:last-child {
  margin-bottom: 0;
}

.video-overlay {
  position: absolute;
  top: var(--space-sm);
  right: var(--space-sm);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-sm);
  font-size: var(--text-xs);
  font-weight: 600;
}

.video-info {
  padding: var(--space-md);
}

.video-title {
  font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-xs);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--text-sm);
  color: var(--text-tertiary);
}

.video-views {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
}

.video-date {
  font-size: var(--text-xs);
}

/* ====== ENHANCED MARKET RESEARCH HUB STYLING ====== */
.market-research-form {
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.03) 0%,
    rgba(139, 92, 246, 0.01) 50%,
    rgba(139, 92, 246, 0.05) 100%);
  border: 2px solid transparent;
  background-clip: padding-box;
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 20px 40px rgba(139, 92, 246, 0.08),
    0 8px 16px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .market-research-form {
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.08) 0%,
    rgba(139, 92, 246, 0.04) 50%,
    rgba(139, 92, 246, 0.12) 100%);
  box-shadow: 
    0 20px 40px rgba(139, 92, 246, 0.15),
    0 8px 16px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.market-research-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 20%, rgba(139, 92, 246, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(139, 92, 246, 0.04) 0%, transparent 50%),
    linear-gradient(45deg, transparent 30%, rgba(139, 92, 246, 0.02) 50%, transparent 70%);
  pointer-events: none;
  z-index: 0;
  animation: formGlow 12s ease-in-out infinite alternate;
}

@keyframes formGlow {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.01);
  }
}

.market-research-form > * {
  position: relative;
  z-index: 1;
}

.form-header {
  text-align: center;
  margin-bottom: var(--space-2xl);
}

.form-header .market-icon {
  width: 64px;
  height: 64px;
  background: var(--purple-gradient);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-lg);
  box-shadow: 
    0 8px 24px rgba(139, 92, 246, 0.3),
    0 16px 48px rgba(139, 92, 246, 0.15);
}

.form-header .market-icon i {
  width: 32px;
  height: 32px;
  color: white;
}

.form-header h1 {
  font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-sm) 0;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--purple-primary) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.form-header p {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.premium-input-group {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--space-lg);
  align-items: end;
  margin-bottom: var(--space-xl);
}

.premium-form-input {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(139, 92, 246, 0.1);
  border-radius: var(--radius-xl);
  padding: var(--space-lg) var(--space-xl);
  font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  transition: all var(--transition-normal);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

[data-theme="dark"] .premium-form-input {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(139, 92, 246, 0.2);
  color: var(--text-primary);
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.premium-form-input:focus {
  outline: none;
  border-color: var(--purple-primary);
  box-shadow: 
    0 0 0 4px rgba(139, 92, 246, 0.1),
    0 8px 24px rgba(139, 92, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transform: translateY(-1px);
}

[data-theme="dark"] .premium-form-input:focus {
  box-shadow: 
    0 0 0 4px rgba(139, 92, 246, 0.2),
    0 8px 24px rgba(139, 92, 246, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.premium-form-input::placeholder {
  color: var(--text-muted);
  font-style: italic;
}

.premium-analyze-btn {
  background: var(--purple-gradient);
  border: none;
  border-radius: var(--radius-xl);
  padding: var(--space-lg) var(--space-2xl);
  color: white;
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-normal);
  box-shadow: 
    0 8px 24px rgba(139, 92, 246, 0.3),
    0 16px 48px rgba(139, 92, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.premium-analyze-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--transition-slow);
}

.premium-analyze-btn:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 12px 32px rgba(139, 92, 246, 0.4),
    0 20px 64px rgba(139, 92, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.premium-analyze-btn:hover::before {
  left: 100%;
}

.premium-analyze-btn:active {
  transform: translateY(0);
  box-shadow: 
    0 4px 12px rgba(139, 92, 246, 0.4),
    inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.premium-analyze-btn .btn-icon {
  width: 20px;
  height: 20px;
  margin-right: var(--space-sm);
}

.form-options {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);
}

.form-option {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.form-option label {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.form-option .label-icon {
  width: 16px;
  height: 16px;
  color: var(--purple-primary);
}

.premium-select {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(139, 92, 246, 0.1);
  border-radius: var(--radius-lg);
  padding: var(--space-md) var(--space-lg);
  font-size: var(--text-base);
  color: var(--text-primary);
  transition: all var(--transition-normal);
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%238B5CF6' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--space-md) center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: var(--space-2xl);
}

[data-theme="dark"] .premium-select {
  background: rgba(30, 41, 59, 0.8);
  border-color: rgba(139, 92, 246, 0.2);
  color: var(--text-primary);
}

.premium-select:focus {
  outline: none;
  border-color: var(--purple-primary);
  box-shadow: 
    0 0 0 3px rgba(139, 92, 246, 0.1),
    0 4px 12px rgba(139, 92, 246, 0.15);
}

/* Additional form styling for Market Research Hub */
.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.form-label {
  font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.form-label .label-icon {
  width: 16px;
  height: 16px;
  color: var(--purple-primary);
}

/* Enhanced Results Section */
.results-section {
  margin-top: var(--space-2xl);
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--transition-slow);
}

.results-section.visible {
  opacity: 1;
  transform: translateY(0);
}

.results-header {
  text-align: center;
  margin-bottom: var(--space-2xl);
  padding: var(--space-xl) 0;
  border-bottom: 2px solid var(--border-light);
  position: relative;
}

.results-header::before {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 2px;
  background: var(--purple-gradient);
}

.results-title {
  font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin: 0 0 var(--space-sm) 0;
}

.results-subtitle {
  font-size: var(--text-lg);
  color: var(--text-secondary);
  margin: 0;
}

.premium-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-xl);
  margin-bottom: var(--space-2xl);
}

.premium-stat-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(20px);
  border: 2px solid rgba(139, 92, 246, 0.1);
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  text-align: center;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.06),
    0 16px 64px rgba(139, 92, 246, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

[data-theme="dark"] .premium-stat-card {
  background: linear-gradient(135deg,
    rgba(30, 41, 59, 0.9) 0%,
    rgba(30, 41, 59, 0.7) 100%);
  border-color: rgba(139, 92, 246, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    0 16px 64px rgba(139, 92, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.premium-stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 50% 0%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 0% 100%, rgba(139, 92, 246, 0.03) 0%, transparent 50%),
    radial-gradient(circle at 100% 100%, rgba(139, 92, 246, 0.04) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.premium-stat-card > * {
  position: relative;
  z-index: 1;
}

.premium-stat-card:hover {
  transform: translateY(-4px);
  border-color: var(--purple-primary);
  box-shadow: 
    0 16px 48px rgba(0, 0, 0, 0.1),
    0 24px 80px rgba(139, 92, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

[data-theme="dark"] .premium-stat-card:hover {
  box-shadow: 
    0 16px 48px rgba(0, 0, 0, 0.4),
    0 24px 80px rgba(139, 92, 246, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.stat-icon-large {
  width: 72px;
  height: 72px;
  background: var(--purple-gradient);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-lg);
  box-shadow: 
    0 8px 24px rgba(139, 92, 246, 0.3),
    0 16px 48px rgba(139, 92, 246, 0.15),
    inset 0 2px 4px rgba(255, 255, 255, 0.2);
}

.stat-icon-large i {
  width: 36px;
  height: 36px;
  color: white;
}

.stat-value-large {
  font-size: var(--text-4xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--text-primary);
  margin: 0 0 var(--space-sm) 0;
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--purple-primary) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label-large {
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Market Research responsive adjustments */
@media (max-width: 1200px) {
  .premium-input-group {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .form-options {
    grid-template-columns: 1fr;
  }

  .premium-stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .tab-button {
    min-width: auto;
    flex: 1;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .recommendation-layout {
    flex-direction: column;
    align-items: flex-start;
  }

  .recommendation-number {
    align-self: flex-start;
  }

  .market-research-form {
    padding: var(--space-xl);
  }

  .form-header h1 {
    font-size: var(--text-2xl);
  }

  .form-header p {
    font-size: var(--text-base);
  }

  .premium-form-input {
    padding: var(--space-md) var(--space-lg);
    font-size: var(--text-base);
  }

  .premium-analyze-btn {
    padding: var(--space-md) var(--space-xl);
    font-size: var(--text-base);
  }

  .premium-stats-grid {
    grid-template-columns: 1fr;
  }
}
