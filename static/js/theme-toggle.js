/**
 * Enhanced Theme Toggle System
 * Provides smooth theme switching with localStorage persistence
 * and system preference detection
 */

class ThemeManager {
    constructor() {
        this.themes = ['light', 'dark'];
        this.currentTheme = this.getStoredTheme() || this.getPreferredTheme() || 'dark';
        this.transitionClass = 'theme-transition';
        this.switchingClass = 'theme-switching';
        
        this.init();
    }

    init() {
        // Apply current theme immediately to prevent flash
        this.applyTheme(this.currentTheme, false);
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.setupEventListeners();
                this.setupSidebar();
                this.setupResponsiveHandling();
            });
        } else {
            this.setupEventListeners();
            this.setupSidebar();
            this.setupResponsiveHandling();
        }

        // Listen for system theme changes
        this.setupSystemThemeListener();
    }

    setupEventListeners() {
        // Find all theme toggle buttons
        const toggleButtons = document.querySelectorAll('.theme-toggle, [data-theme-toggle]');
        
        toggleButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggle();
            });

            // Update button icon based on current theme
            this.updateToggleButton(button);
        });

        // Keyboard shortcut: Ctrl/Cmd + Shift + T
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                this.toggle();
            }
        });
    }

    setupSystemThemeListener() {
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            mediaQuery.addEventListener('change', (e) => {
                // Only auto-switch if user hasn't manually set a preference
                if (!this.hasManualPreference()) {
                    const newTheme = e.matches ? 'dark' : 'light';
                    this.applyTheme(newTheme);
                }
            });
        }
    }

    toggle() {
        const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
        this.setTheme(newTheme);
    }

    setTheme(theme, animate = true) {
        if (!this.themes.includes(theme)) {
            console.warn(`Theme "${theme}" is not valid. Using default theme.`);
            theme = 'dark';
        }

        if (theme === this.currentTheme) return;

        this.currentTheme = theme;
        this.storeTheme(theme);
        this.applyTheme(theme, animate);
        
        // Mark that user has made a manual choice
        localStorage.setItem('theme-manual-preference', 'true');
        
        // Dispatch custom event
        this.dispatchThemeChangeEvent(theme);
    }

    applyTheme(theme, animate = true) {
        const html = document.documentElement;
        const body = document.body;

        if (animate) {
            // Add transition classes
            html.classList.add(this.transitionClass);
            body.classList.add(this.switchingClass);

            // Remove transition classes after animation
            setTimeout(() => {
                html.classList.remove(this.transitionClass);
                body.classList.remove(this.switchingClass);
            }, 300);
        }

        // Apply theme
        html.setAttribute('data-theme', theme);
        
        // Update meta theme-color for mobile browsers
        this.updateMetaThemeColor(theme);
        
        // Update all theme toggle buttons
        this.updateAllToggleButtons();

        // Update favicon if needed (optional)
        this.updateFavicon(theme);
    }

    updateMetaThemeColor(theme) {
        let themeColor = theme === 'dark' ? '#0f0f23' : '#FFFFFF';
        
        let metaThemeColor = document.querySelector('meta[name="theme-color"]');
        if (!metaThemeColor) {
            metaThemeColor = document.createElement('meta');
            metaThemeColor.name = 'theme-color';
            document.head.appendChild(metaThemeColor);
        }
        metaThemeColor.content = themeColor;
    }

    updateFavicon(theme) {
        // Optional: Switch favicon based on theme
        const favicon = document.querySelector('link[rel="icon"]');
        if (favicon && favicon.dataset.lightIcon && favicon.dataset.darkIcon) {
            favicon.href = theme === 'dark' ? favicon.dataset.darkIcon : favicon.dataset.lightIcon;
        }
    }

    updateAllToggleButtons() {
        const toggleButtons = document.querySelectorAll('.theme-toggle, [data-theme-toggle]');
        toggleButtons.forEach(button => this.updateToggleButton(button));
    }

    updateToggleButton(button) {
        const icon = button.querySelector('i, svg, .theme-icon');
        if (!icon) return;

        // Remove existing theme classes
        icon.classList.remove('lucide-sun', 'lucide-moon', 'fa-sun', 'fa-moon');
        
        // Add appropriate icon class based on current theme
        if (this.currentTheme === 'dark') {
            // Show sun icon (switch to light)
            if (icon.classList.contains('lucide')) {
                icon.classList.add('lucide-sun');
            } else {
                icon.classList.add('fa-sun');
            }
            button.title = 'Switch to light mode';
            button.setAttribute('aria-label', 'Switch to light mode');
        } else {
            // Show moon icon (switch to dark)
            if (icon.classList.contains('lucide')) {
                icon.classList.add('lucide-moon');
            } else {
                icon.classList.add('fa-moon');
            }
            button.title = 'Switch to dark mode';
            button.setAttribute('aria-label', 'Switch to dark mode');
        }
    }

    storeTheme(theme) {
        try {
            localStorage.setItem('preferred-theme', theme);
        } catch (e) {
            console.warn('Failed to store theme preference:', e);
        }
    }

    getStoredTheme() {
        try {
            return localStorage.getItem('preferred-theme');
        } catch (e) {
            console.warn('Failed to retrieve stored theme:', e);
            return null;
        }
    }

    getPreferredTheme() {
        if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
            return 'dark';
        }
        return 'light';
    }

    hasManualPreference() {
        try {
            return localStorage.getItem('theme-manual-preference') === 'true';
        } catch (e) {
            return false;
        }
    }

    dispatchThemeChangeEvent(theme) {
        const event = new CustomEvent('themechange', {
            detail: { theme, previousTheme: this.currentTheme }
        });
        window.dispatchEvent(event);
    }

    // Public API methods
    getCurrentTheme() {
        return this.currentTheme;
    }

    isDark() {
        return this.currentTheme === 'dark';
    }

    isLight() {
        return this.currentTheme === 'light';
    }

    // Reset to system preference
    resetToSystem() {
        localStorage.removeItem('preferred-theme');
        localStorage.removeItem('theme-manual-preference');
        const systemTheme = this.getPreferredTheme();
        this.setTheme(systemTheme);
    }
    // Enhanced Sidebar Management
    setupSidebar() {
        const sidebar = document.getElementById('sidebar');
        const appLayout = document.querySelector('.app-layout');
        
        if (!sidebar || !appLayout) return;

        // Load saved sidebar state
        const savedSidebarState = localStorage.getItem('sidebarCollapsed');
        const isCollapsed = savedSidebarState === 'true';
        
        if (isCollapsed) {
            appLayout.classList.add('sidebar-collapsed');
        }

        // Create mobile toggle button
        this.createMobileToggle();
        
        // Add keyboard shortcut for sidebar toggle (Ctrl/Cmd + B)
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'b') {
                e.preventDefault();
                this.toggleSidebar();
            }
        });
    }

    createMobileToggle() {
        const appMain = document.querySelector('.app-main');
        if (!appMain) return;

        // Create mobile menu toggle button
        const mobileToggle = document.createElement('button');
        mobileToggle.className = 'mobile-nav-toggle';
        mobileToggle.innerHTML = '<i data-lucide="menu"></i>';
        mobileToggle.setAttribute('aria-label', 'Toggle navigation menu');
        
        // Insert at the beginning of main content
        appMain.insertBefore(mobileToggle, appMain.firstChild);

        mobileToggle.addEventListener('click', () => {
            this.toggleSidebar();
        });
    }

    toggleSidebar() {
        const appLayout = document.querySelector('.app-layout');
        const sidebar = document.getElementById('sidebar');
        
        if (!appLayout || !sidebar) return;

        const isCurrentlyCollapsed = appLayout.classList.contains('sidebar-collapsed');
        
        if (isCurrentlyCollapsed) {
            appLayout.classList.remove('sidebar-collapsed');
            localStorage.setItem('sidebarCollapsed', 'false');
        } else {
            appLayout.classList.add('sidebar-collapsed');
            localStorage.setItem('sidebarCollapsed', 'true');
        }

        // Re-initialize icons after DOM changes
        setTimeout(() => {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        }, 100);
    }

    handleResponsiveChanges() {
        const appLayout = document.querySelector('.app-layout');
        const isMobile = window.innerWidth <= 768;
        const isTablet = window.innerWidth <= 1024 && window.innerWidth > 768;
        
        if (!appLayout) return;

        // Auto-collapse sidebar on mobile
        if (isMobile) {
            appLayout.classList.add('mobile-view');
            appLayout.classList.remove('tablet-view', 'desktop-view');
            
            // Auto-collapse on mobile unless explicitly opened
            if (!appLayout.classList.contains('sidebar-force-open')) {
                appLayout.classList.add('sidebar-collapsed');
            }
        } else if (isTablet) {
            appLayout.classList.add('tablet-view');
            appLayout.classList.remove('mobile-view', 'desktop-view');
        } else {
            appLayout.classList.add('desktop-view');
            appLayout.classList.remove('mobile-view', 'tablet-view');
            
            // Restore saved state on desktop
            const savedSidebarState = localStorage.getItem('sidebarCollapsed');
            if (savedSidebarState === 'true') {
                appLayout.classList.add('sidebar-collapsed');
            } else {
                appLayout.classList.remove('sidebar-collapsed');
            }
        }
    }

    // Responsive Handling
    setupResponsiveHandling() {
        const appLayout = document.querySelector('.app-layout');
        if (!appLayout) return;

        // Handle window resize
        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                this.handleResponsiveChanges();
            }, 150);
        });

        // Initial responsive setup
        this.handleResponsiveChanges();
    }
}

// Initialize theme manager
const themeManager = new ThemeManager();

// Export for global access
window.themeManager = themeManager;

// Optional: Add keyboard shortcut hint to console
console.log('💡 Theme Toggle Tip: Press Ctrl/Cmd + Shift + T to toggle between light and dark themes!');

// Listen for theme change events (for other components)
window.addEventListener('themechange', (e) => {
    console.log(`Theme changed to: ${e.detail.theme}`);
    
    // Optional: Analytics tracking
    if (typeof gtag !== 'undefined') {
        gtag('event', 'theme_change', {
            'theme': e.detail.theme,
            'previous_theme': e.detail.previousTheme
        });
    }
});
