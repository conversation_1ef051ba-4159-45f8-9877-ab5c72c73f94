"""
YouTube API Tools for CrewAI
Provides tools for fetching channel data, videos, comments, and transcripts
"""

import os
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import requests
from crewai.tools import BaseTool
from pydantic import BaseModel, Field
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import logging

logger = logging.getLogger(__name__)


class YouTubeChannelInfoTool(BaseTool):
    name: str = "YouTube Channel Info"
    description: str = "Fetches comprehensive information about a YouTube channel including statistics, description, and metadata"
    api_key: str = Field(..., description="YouTube API key")
    
    def __init__(self, api_key: str):
        super().__init__(api_key=api_key)
    
    @property
    def youtube(self):
        """Lazy initialization of YouTube API client"""
        if not hasattr(self, '_youtube'):
            self._youtube = build('youtube', 'v3', developerKey=self.api_key)
        return self._youtube
    
    def _run(self, channel_id: str) -> Dict[str, Any]:
        """
        Fetch channel information from YouTube API
        
        Args:
            channel_id: YouTube channel ID or handle (e.g., @mrbeast)
            
        Returns:
            Dictionary containing channel information
        """
        try:
            # Handle @username format
            if channel_id.startswith('@'):
                # Try to find by handle first
                search_request = self.youtube.channels().list(
                    part='snippet,statistics,contentDetails,brandingSettings',
                    forHandle=channel_id
                )
                response = search_request.execute()
                
                if not response.get('items'):
                    # If handle doesn't work, try searching by custom URL
                    search_request = self.youtube.search().list(
                        part='snippet',
                        q=channel_id,
                        type='channel',
                        maxResults=1
                    )
                    search_response = search_request.execute()
                    
                    if search_response.get('items'):
                        channel_id = search_response['items'][0]['snippet']['channelId']
                    else:
                        return {"error": f"Channel {channel_id} not found"}
            
            # Get channel details by ID
            if not channel_id.startswith('@'):
                request = self.youtube.channels().list(
                    part='snippet,statistics,contentDetails,brandingSettings',
                    id=channel_id
                )
                response = request.execute()
            
            if not response.get('items'):
                return {"error": f"Channel {channel_id} not found"}
            
            channel = response['items'][0]
            
            # Extract relevant information
            channel_info = {
                'id': channel['id'],
                'title': channel['snippet']['title'],
                'description': channel['snippet']['description'],
                'customUrl': channel['snippet'].get('customUrl', ''),
                'publishedAt': channel['snippet']['publishedAt'],
                'thumbnails': channel['snippet']['thumbnails'],
                'statistics': {
                    'viewCount': int(channel['statistics'].get('viewCount', 0)),
                    'subscriberCount': int(channel['statistics'].get('subscriberCount', 0)),
                    'videoCount': int(channel['statistics'].get('videoCount', 0))
                },
                'keywords': channel['brandingSettings'].get('channel', {}).get('keywords', ''),
                'country': channel['snippet'].get('country', ''),
                'uploadPlaylistId': channel['contentDetails']['relatedPlaylists']['uploads']
            }
            
            return channel_info
            
        except HttpError as e:
            logger.error(f"YouTube API error: {e}")
            return {"error": str(e)}
        except Exception as e:
            logger.error(f"Error fetching channel info: {e}")
            return {"error": str(e)}


class YouTubeVideosFetcherTool(BaseTool):
    name: str = "YouTube Videos Fetcher"
    description: str = "Fetches videos from a YouTube channel with detailed metadata"
    api_key: str = Field(..., description="YouTube API key")
    
    def __init__(self, api_key: str):
        super().__init__(api_key=api_key)
    
    @property
    def youtube(self):
        """Lazy initialization of YouTube API client"""
        if not hasattr(self, '_youtube'):
            self._youtube = build('youtube', 'v3', developerKey=self.api_key)
        return self._youtube
    
    def _run(self, channel_id: str, max_videos: int = 100) -> List[Dict[str, Any]]:
        """
        Fetch videos from a YouTube channel
        
        Args:
            channel_id: YouTube channel ID
            max_videos: Maximum number of videos to fetch
            
        Returns:
            List of video information dictionaries
        """
        try:
            videos = []
            
            # First, get the uploads playlist ID
            channel_response = self.youtube.channels().list(
                part='contentDetails',
                id=channel_id
            ).execute()
            
            if not channel_response.get('items'):
                return []
            
            uploads_playlist_id = channel_response['items'][0]['contentDetails']['relatedPlaylists']['uploads']
            
            # Fetch videos from uploads playlist
            next_page_token = None
            
            while len(videos) < max_videos:
                playlist_response = self.youtube.playlistItems().list(
                    part='snippet',
                    playlistId=uploads_playlist_id,
                    maxResults=min(50, max_videos - len(videos)),
                    pageToken=next_page_token
                ).execute()
                
                video_ids = [item['snippet']['resourceId']['videoId'] 
                           for item in playlist_response.get('items', [])]
                
                if video_ids:
                    # Get detailed video information
                    videos_response = self.youtube.videos().list(
                        part='snippet,statistics,contentDetails',
                        id=','.join(video_ids)
                    ).execute()
                    
                    for video in videos_response.get('items', []):
                        video_info = {
                            'id': video['id'],
                            'title': video['snippet']['title'],
                            'description': video['snippet']['description'],
                            'publishedAt': video['snippet']['publishedAt'],
                            'thumbnails': video['snippet']['thumbnails'],
                            'duration': video['contentDetails']['duration'],
                            'statistics': {
                                'viewCount': int(video['statistics'].get('viewCount', 0)),
                                'likeCount': int(video['statistics'].get('likeCount', 0)),
                                'commentCount': int(video['statistics'].get('commentCount', 0))
                            },
                            'tags': video['snippet'].get('tags', [])
                        }
                        videos.append(video_info)
                
                next_page_token = playlist_response.get('nextPageToken')
                if not next_page_token:
                    break
            
            return videos[:max_videos]
            
        except HttpError as e:
            logger.error(f"YouTube API error: {e}")
            return []
        except Exception as e:
            logger.error(f"Error fetching videos: {e}")
            return []


class YouTubeCommentsFetcherTool(BaseTool):
    name: str = "YouTube Comments Fetcher"
    description: str = "Fetches comments from YouTube videos for audience analysis"
    api_key: str = Field(..., description="YouTube API key")
    
    def __init__(self, api_key: str):
        super().__init__(api_key=api_key)
    
    @property
    def youtube(self):
        """Lazy initialization of YouTube API client"""
        if not hasattr(self, '_youtube'):
            self._youtube = build('youtube', 'v3', developerKey=self.api_key)
        return self._youtube
    
    def _run(self, video_ids: List[str], max_comments_per_video: int = 100) -> Dict[str, List[Dict[str, Any]]]:
        """
        Fetch comments from multiple YouTube videos
        
        Args:
            video_ids: List of YouTube video IDs
            max_comments_per_video: Maximum comments to fetch per video
            
        Returns:
            Dictionary mapping video IDs to their comments
        """
        comments_by_video = {}
        
        for video_id in video_ids:
            try:
                comments = []
                next_page_token = None
                
                while len(comments) < max_comments_per_video:
                    response = self.youtube.commentThreads().list(
                        part='snippet',
                        videoId=video_id,
                        maxResults=min(100, max_comments_per_video - len(comments)),
                        pageToken=next_page_token,
                        order='relevance'
                    ).execute()
                    
                    for item in response.get('items', []):
                        comment = item['snippet']['topLevelComment']['snippet']
                        comment_info = {
                            'text': comment['textDisplay'],
                            'authorName': comment['authorDisplayName'],
                            'likeCount': comment['likeCount'],
                            'publishedAt': comment['publishedAt']
                        }
                        comments.append(comment_info)
                    
                    next_page_token = response.get('nextPageToken')
                    if not next_page_token:
                        break
                
                comments_by_video[video_id] = comments[:max_comments_per_video]
                
            except HttpError as e:
                logger.error(f"Error fetching comments for video {video_id}: {e}")
                comments_by_video[video_id] = []
            except Exception as e:
                logger.error(f"Error fetching comments for video {video_id}: {e}")
                comments_by_video[video_id] = []
        
        return comments_by_video


class YouTubeTranscriptTool(BaseTool):
    name: str = "YouTube Transcript Fetcher"
    description: str = "Fetches video transcripts for content analysis"
    supadata_api_key: Optional[str] = Field(None, description="Supadata API key for transcripts")
    
    def __init__(self, supadata_api_key: Optional[str] = None):
        super().__init__(supadata_api_key=supadata_api_key)
    
    def _run(self, video_ids: List[str]) -> Dict[str, str]:
        """
        Fetch transcripts for multiple YouTube videos
        
        Args:
            video_ids: List of YouTube video IDs
            
        Returns:
            Dictionary mapping video IDs to their transcripts
        """
        transcripts = {}
        
        for video_id in video_ids[:5]:  # Limit to 5 videos as per v1 optimization
            transcript = self._fetch_single_transcript(video_id)
            if transcript:
                transcripts[video_id] = transcript
        
        return transcripts
    
    def _fetch_single_transcript(self, video_id: str) -> Optional[str]:
        """Fetch transcript for a single video using multiple methods"""
        
        # Method 1: Try Supadata API if available
        if self.supadata_api_key:
            transcript = self._fetch_from_supadata(video_id)
            if transcript:
                return transcript
        
        # Method 2: Fallback to youtube-transcript-api (requires separate installation)
        try:
            from youtube_transcript_api import YouTubeTranscriptApi
            transcript_list = YouTubeTranscriptApi.get_transcript(video_id)
            return ' '.join([item['text'] for item in transcript_list])
        except:
            # Silently fail - transcripts are optional
            return None
    
    def _fetch_from_supadata(self, video_id: str) -> Optional[str]:
        """Fetch transcript from Supadata API"""
        if not self.supadata_api_key:
            return None
        
        try:
            headers = {
                'Authorization': f'Bearer {self.supadata_api_key}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                f'https://api.supadata.ai/v1/transcripts/{video_id}',
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('transcript', '')
            else:
                # Silently fail - transcripts are optional
                return None
                
        except Exception as e:
            logger.error(f"Error fetching from Supadata: {e}")
            return None


class YouTubeChannelAnalysisTool(BaseTool):
    name: str = "YouTube Channel Analysis"
    description: str = "Comprehensive tool that fetches all channel data needed for DNA analysis"
    youtube_api_key: str = Field(..., description="YouTube API key")
    supadata_api_key: Optional[str] = Field(None, description="Supadata API key for transcripts")
    
    def __init__(self, youtube_api_key: str, supadata_api_key: Optional[str] = None):
        super().__init__(youtube_api_key=youtube_api_key, supadata_api_key=supadata_api_key)
    
    @property
    def channel_tool(self):
        """Lazy initialization of channel info tool"""
        if not hasattr(self, '_channel_tool'):
            self._channel_tool = YouTubeChannelInfoTool(self.youtube_api_key)
        return self._channel_tool
    
    @property
    def videos_tool(self):
        """Lazy initialization of videos fetcher tool"""
        if not hasattr(self, '_videos_tool'):
            self._videos_tool = YouTubeVideosFetcherTool(self.youtube_api_key)
        return self._videos_tool
    
    @property
    def comments_tool(self):
        """Lazy initialization of comments fetcher tool"""
        if not hasattr(self, '_comments_tool'):
            self._comments_tool = YouTubeCommentsFetcherTool(self.youtube_api_key)
        return self._comments_tool
    
    @property
    def transcript_tool(self):
        """Lazy initialization of transcript tool"""
        if not hasattr(self, '_transcript_tool'):
            self._transcript_tool = YouTubeTranscriptTool(self.supadata_api_key)
        return self._transcript_tool
    
    def _run(self, channel_id: str) -> Dict[str, Any]:
        """
        Fetch comprehensive channel data for DNA analysis
        
        Args:
            channel_id: YouTube channel ID
            
        Returns:
            Dictionary containing all channel data
        """
        logger.info(f"Starting comprehensive analysis for channel {channel_id}")
        
        # Fetch channel info
        channel_info = self.channel_tool._run(channel_id)
        if 'error' in channel_info:
            return channel_info
        
        # Get the actual channel ID (in case a handle was passed)
        actual_channel_id = channel_info['id']
        
        # Fetch videos using the resolved channel ID
        videos = self.videos_tool._run(actual_channel_id, max_videos=100)
        
        # Get top video IDs for detailed analysis
        top_video_ids = [v['id'] for v in sorted(
            videos, 
            key=lambda x: x['statistics']['viewCount'], 
            reverse=True
        )[:20]]  # Top 20 videos by views
        
        # Fetch comments from top videos
        comments = self.comments_tool._run(top_video_ids, max_comments_per_video=100)
        
        # Fetch transcripts from top 5 videos
        transcript_video_ids = top_video_ids[:5]
        transcripts = self.transcript_tool._run(transcript_video_ids)
        
        # Compile all data
        analysis_data = {
            'channel': channel_info,
            'videos': videos,
            'comments': comments,
            'transcripts': transcripts,
            'analysis_metadata': {
                'timestamp': datetime.utcnow().isoformat(),
                'total_videos_analyzed': len(videos),
                'total_comments_analyzed': sum(len(c) for c in comments.values()),
                'transcripts_available': len(transcripts)
            }
        }
        
        logger.info(f"Analysis complete. Analyzed {len(videos)} videos, "
                   f"{sum(len(c) for c in comments.values())} comments, "
                   f"{len(transcripts)} transcripts")
        
        return analysis_data
    
    def get_comprehensive_analysis(self, channel_id: str, max_videos: int = 20, max_comments: int = 50) -> Dict[str, Any]:
        """
        Fetch comprehensive channel data for market research analysis
        
        Args:
            channel_id: YouTube channel ID or handle
            max_videos: Maximum number of videos to analyze
            max_comments: Maximum number of comments per video
            
        Returns:
            Dictionary containing all channel data
        """
        logger.info(f"Starting comprehensive analysis for channel {channel_id}")
        
        # Fetch channel info
        channel_info = self.channel_tool._run(channel_id)
        if 'error' in channel_info:
            return channel_info
        
        # Get the actual channel ID (in case a handle was passed)
        actual_channel_id = channel_info['id']
        
        # Fetch videos using the resolved channel ID
        videos = self.videos_tool._run(actual_channel_id, max_videos=max_videos)
        
        # Get top video IDs for detailed analysis
        top_video_ids = [v['id'] for v in sorted(
            videos, 
            key=lambda x: x['statistics']['viewCount'], 
            reverse=True
        )[:max_videos]]  # Top videos by views
        
        # Fetch comments from videos
        comments = self.comments_tool._run(top_video_ids, max_comments_per_video=max_comments)
        
        # Fetch transcripts from top 5 videos
        transcript_video_ids = top_video_ids[:5]
        transcripts = self.transcript_tool._run(transcript_video_ids)
        
        # Compile all data
        analysis_data = {
            'channel': channel_info,
            'videos': videos,
            'comments': comments,
            'transcripts': transcripts,
            'analysis_metadata': {
                'timestamp': datetime.utcnow().isoformat(),
                'total_videos_analyzed': len(videos),
                'total_comments_analyzed': sum(len(c) for c in comments.values()),
                'transcripts_available': len(transcripts)
            }
        }
        
        logger.info(f"Analysis complete. Analyzed {len(videos)} videos, "
                   f"{sum(len(c) for c in comments.values())} comments, "
                   f"{len(transcripts)} transcripts")
        
        return analysis_data