#!/usr/bin/env python3
"""
Debug script to check CSS loading issues
"""

import requests
import os
from urllib.parse import urljoin

def test_css_loading():
    base_url = "http://localhost:8003"
    
    print("🔍 CSS Loading Diagnostic Test")
    print("=" * 50)
    
    # Test 1: Check if CSS file is accessible directly
    css_url = urljoin(base_url, "/static/styles/youtube-research-v2-design-system.css?v=2025073002")
    print(f"\n1. Testing direct CSS access: {css_url}")
    
    try:
        css_response = requests.get(css_url, timeout=10)
        print(f"   Status Code: {css_response.status_code}")
        print(f"   Content-Type: {css_response.headers.get('content-type', 'Not specified')}")
        print(f"   Content-Length: {len(css_response.content)} bytes")
        
        if css_response.status_code == 200:
            # Check if it's actually CSS content
            content = css_response.text[:200]
            if "root" in content or "body" in content or "css" in content.lower():
                print("   ✅ CSS file is accessible and contains CSS content")
            else:
                print("   ❌ CSS file accessible but doesn't contain expected CSS content")
                print(f"   First 200 chars: {content}")
        else:
            print(f"   ❌ CSS file not accessible: {css_response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error accessing CSS file: {e}")
    
    # Test 2: Check dashboard HTML
    dashboard_url = urljoin(base_url, "/")
    print(f"\n2. Testing dashboard HTML: {dashboard_url}")
    
    try:
        html_response = requests.get(dashboard_url, timeout=10)
        print(f"   Status Code: {html_response.status_code}")
        
        if html_response.status_code == 200:
            html_content = html_response.text
            
            # Check for CSS link
            if "youtube-research-v2-design-system.css" in html_content:
                print("   ✅ HTML contains CSS link")
                
                # Extract the CSS link
                import re
                css_link_pattern = r'<link[^>]*href="([^"]*youtube-research-v2-design-system\.css[^"]*)"[^>]*>'
                css_links = re.findall(css_link_pattern, html_content)
                
                if css_links:
                    print(f"   CSS Link found: {css_links[0]}")
                else:
                    print("   ❌ CSS link pattern not found in HTML")
            else:
                print("   ❌ HTML does not contain CSS link")
                
            # Check for design system classes
            design_classes = ["app-layout", "app-sidebar", "app-main"]
            found_classes = []
            for cls in design_classes:
                if cls in html_content:
                    found_classes.append(cls)
            
            if found_classes:
                print(f"   ✅ Design system classes found: {found_classes}")
            else:
                print("   ❌ No design system classes found in HTML")
                
        else:
            print(f"   ❌ Dashboard not accessible: {html_response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error accessing dashboard: {e}")
    
    # Test 3: Check if static files directory exists
    print(f"\n3. Checking static files directory")
    static_dir = "static/styles"
    css_file = "static/styles/youtube-research-v2-design-system.css"
    
    if os.path.exists(static_dir):
        print(f"   ✅ Static directory exists: {static_dir}")
        
        if os.path.exists(css_file):
            print(f"   ✅ CSS file exists: {css_file}")
            file_size = os.path.getsize(css_file)
            print(f"   File size: {file_size} bytes")
            
            # Check first few lines of CSS
            with open(css_file, 'r') as f:
                first_lines = [f.readline().strip() for _ in range(5)]
                print(f"   First 5 lines: {first_lines}")
        else:
            print(f"   ❌ CSS file does not exist: {css_file}")
    else:
        print(f"   ❌ Static directory does not exist: {static_dir}")
    
    print("\n" + "=" * 50)
    print("🏁 Diagnostic complete")

if __name__ == "__main__":
    test_css_loading()
