selector,occurrences_in_templates,files_found,file_list,is_unused_by_purgecss,duplicate_with,recommendation,reason,rules_preview,rule_complexity
.activity-details,9,4,test_dashboard_functionality.html; test_dashboard_styling.html; templates/dashboard.html; templates/dashboard.html,No,.user-info; .analysis-card-title-section; .agent-info-section...,MERGE,"Duplicate rules with: .user-info, .analysis-card-title-section, .agent-info-section","flex: 1;
  min-width: 0;",3
.activity-details h4,9,4,test_dashboard_functionality.html; test_dashboard_styling.html; templates/dashboard.html; templates/dashboard.html,No,,KEEP,In use,"margin: 0 0 var(--space-xs) 0;
  font-size: var(--text-base);
  font-weight: var(--font-weight-semib...",5
.activity-item:hover,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"box-shadow: var(--shadow-sm);
  transform: translateY(-1px);",3
.activity-thumbnail,9,4,test_dashboard_functionality.html; test_dashboard_styling.html; templates/dashboard.html; templates/dashboard.html,No,,KEEP,In use,"width: 60px;
  height: 40px;
  border-radius: var(--radius-sm);
  background: var(--bg-secondary);",5
.agent-1,1,1,templates/components/navigation.html,No,.agent-performance; .status-error; .trend-down...,MERGE,"Duplicate rules with: .agent-performance, .status-error, .trend-down",background: #ef4444;,2
.agent-2,1,1,templates/components/navigation.html,No,.agent-script,MERGE,Duplicate rules with: .agent-script,background: #f97316;,2
.agent-3,1,1,templates/components/navigation.html,No,.agent-seo,MERGE,Duplicate rules with: .agent-seo,background: #eab308;,2
.agent-4,1,1,templates/components/navigation.html,No,.agent-psychology,MERGE,Duplicate rules with: .agent-psychology,background: #22c55e;,2
.agent-5,1,1,templates/components/navigation.html,No,.agent-comments; .status-working,MERGE,"Duplicate rules with: .agent-comments, .status-working",background: #3b82f6;,2
.agent-6,1,1,templates/components/navigation.html,No,.agent-synthesis; .status-thinking; .progress-step.active .step-indicator...,MERGE,"Duplicate rules with: .agent-synthesis, .status-thinking, .progress-step.active .step-indicator",background: var(--purple-primary);,2
.agent-avatar,4,1,templates/components/analysis-cards.html,No,,KEEP,In use,"width: 40px;
  height: 40px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: ce...",11
.agent-avatar-section,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"position: relative;
  flex-shrink: 0;",3
.agent-avatar.agent-1,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,background: var(--agent-1);,2
.agent-avatar.agent-2,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,background: var(--agent-2);,2
.agent-avatar.agent-3,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,background: var(--agent-3);,2
.agent-avatar.agent-4,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,background: var(--agent-4);,2
.agent-avatar.agent-5,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,background: var(--agent-5);,2
.agent-avatar.agent-6,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,background: var(--agent-6);,2
.agent-badge-large,4,4,templates/premium-channel-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-channel-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-sm);
  padding: v...",9
.agent-capabilities,2,2,templates/premium-comment-intelligence.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,margin-top: var(--space-xl);,2
.agent-icon,19,7,templates/premium-channel-analyzer.html; templates/premium-comment-intelligence.html; templates/video-analyzer.html; templates/components/analysis-cards.html; templates/premium-channel-analyzer.html...,No,.card-icon; .metric-icon-svg,MERGE,"Duplicate rules with: .card-icon, .metric-icon-svg","width: 20px;
  height: 20px;",3
.agent-icon-large,4,4,templates/premium-channel-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-channel-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"width: 32px;
  height: 32px;
  color: var(--purple-primary);",4
.agent-label,4,4,templates/premium-channel-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-channel-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);",4
.agent-message,2,1,templates/components/analysis-cards.html,No,,KEEP,In use,"color: var(--text-secondary);
  font-size: var(--text-sm);
  line-height: 1.5;",4
.agent-name,4,1,templates/components/analysis-cards.html,No,,KEEP,In use,"font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--text-sm);...",5
.agent-progress,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"width: 100%;
  height: 4px;
  background: var(--bg-overlay);
  border-radius: var(--radius-full);
  ...",7
.agent-progress-fill,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"height: 100%;
  background: var(--purple-gradient);
  border-radius: var(--radius-full);
  transitio...",5
.agent-status-card:hover,0,0,,No,.btn-secondary:hover:not(:disabled),MERGE,Duplicate rules with: .btn-secondary:hover:not(:disabled),"background: var(--bg-overlay);
  border-color: var(--purple-primary);",3
.agent-status-indicator,3,1,templates/components/analysis-cards.html,No,,KEEP,In use,"position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: v...",8
.agent-status-item,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"display: flex;
  align-items: flex-start;
  gap: var(--space-md);
  padding: var(--space-md);
  bord...",8
.agent-status-item.active,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"background: var(--purple-light);
  border: 1px solid var(--purple-tertiary);",3
.agent-status-item:hover,0,0,,Yes,.status-feed-item:hover,DELETE,Unused in templates and purged by PurgeCSS,background: var(--bg-overlay);,2
.agent-timestamp,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"color: var(--text-muted);
  font-size: var(--text-xs);
  margin-top: var(--space-xs);",4
.analysis-card,38,7,test_syntax_fix.html; test_dashboard_styling.html; templates/component-showcase.html; templates/market-research-hub.html; templates/components/analysis-cards.html...,No,,KEEP,In use,"background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radi...",7
.analysis-card-actions,1,1,templates/components/analysis-cards.html,No,.analysis-card-tags,MERGE,Duplicate rules with: .analysis-card-tags,"display: flex;
  gap: var(--space-xs);",3
.analysis-card-content,2,2,test_dashboard_styling.html; templates/components/analysis-cards.html,No,.app-content; .card-content,MERGE,"Duplicate rules with: .app-content, .card-content",padding: var(--space-lg);,2
.analysis-card-footer,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md) v...",7
.analysis-card-header,2,2,test_dashboard_styling.html; templates/components/analysis-cards.html,No,,KEEP,In use,"display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg);
  border-b...",7
.analysis-card-icon,2,2,test_dashboard_styling.html; templates/components/analysis-cards.html,No,,KEEP,In use,"width: 40px;
  height: 40px;
  background: var(--purple-gradient);
  border-radius: var(--radius-md)...",9
.analysis-card-meta,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"display: flex;
  gap: var(--space-md);
  font-size: var(--text-xs);
  color: var(--text-secondary);",5
.analysis-card-subtitle,3,2,test_dashboard_styling.html; templates/components/analysis-cards.html,No,,KEEP,In use,"margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);",4
.analysis-card-title,5,2,test_dashboard_styling.html; templates/components/analysis-cards.html,No,.progress-title,MERGE,Duplicate rules with: .progress-title,"margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--t...",5
.analysis-card:hover,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"box-shadow: var(--shadow-md);
  transform: translateY(-2px);",3
.analysis-confidence,2,1,templates/components/analysis-cards.html,No,,KEEP,In use,"color: var(--purple-primary);
  font-weight: var(--font-weight-semibold);",3
.analysis-header,8,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,.skeleton-header,MERGE,Duplicate rules with: .skeleton-header,"display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-lg);",5
.analysis-icon,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"width: 48px;
  height: 48px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: cent...",9
.analysis-section,29,11,debug_channel_display.html; templates/premium-channel-analyzer.html; templates/premium-dashboard-new.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,.analyzer-form-section; .quick-analysis-section; .tools-section...,MERGE,"Duplicate rules with: .analyzer-form-section, .quick-analysis-section, .tools-section",margin-bottom: var(--space-2xl);,2
.analysis-status,2,2,templates/premium-comment-intelligence.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"display: flex;
  align-items: center;",3
.analysis-subtitle,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"font-size: var(--text-lg);
  color: var(--text-secondary);
  margin: var(--space-xs) 0 0 0;",4
.analysis-text,4,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"line-height: var(--line-height-relaxed);
  color: var(--text-secondary);",3
.analysis-text h2,4,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"color: var(--text-primary);
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibold);...",7
.analysis-text h3,4,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"color: var(--text-primary);
  font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);
 ...",5
.analysis-text li,4,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,margin-bottom: var(--space-sm);,2
.analysis-text p,4,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,margin-bottom: var(--space-md);,2
.analysis-text strong,4,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,.progress-step.active .step-label,MERGE,Duplicate rules with: .progress-step.active .step-label,"color: var(--text-primary);
  font-weight: var(--font-weight-semibold);",3
.analysis-text ul,4,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"margin: var(--space-md) 0;
  padding-left: var(--space-xl);",3
.analysis-timestamp,2,1,templates/components/analysis-cards.html,No,.font-medium,MERGE,Duplicate rules with: .font-medium,font-weight: var(--font-weight-medium);,2
.analysis-title,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  ...",5
.analyzer-form,20,10,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/video-analyzer.html...,No,,KEEP,In use,gap: var(--space-md);,2
.analyzer-form-section:last-child,0,0,,No,.psychology-insights p:last-child,MERGE,Duplicate rules with: .psychology-insights p:last-child,margin-bottom: 0;,2
.analyzer-form.flex-layout,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"display: flex;
  flex-direction: column;",3
.analyzer-form.grid-layout,0,0,,Yes,.premium-input-group,DELETE,Unused in templates and purged by PurgeCSS,"grid-template-columns: 1fr;
    gap: var(--space-lg);",3
.animate-bounce,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,animation: bounce 1s infinite;,2
.animate-fadeInUp,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,animation: fadeInUp 0.5s ease-out;,2
.animate-pulse,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;",2
.app-header,10,10,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,,KEEP,In use,"height: var(--header-height);
  background: var(--bg-elevated);
  border-bottom: 1px solid var(--bor...",12
.app-main,18,18,templates/component-showcase.html; templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html...,No,,KEEP,In use,margin-left: var(--sidebar-collapsed-width);,2
.app-sidebar,17,17,templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,width: var(--sidebar-collapsed-width);,2
.bg-elevated,22,8,design-preview.html; test_dashboard_functionality.html; test_dashboard_styling.html; templates/component-showcase.html; templates/dashboard.html...,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,background-color: var(--bg-elevated);,2
.bg-primary,11,4,design-preview.html; test_dashboard_functionality.html; test_dashboard_styling.html; templates/components/professional-tool-layout.html,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,background-color: var(--bg-primary);,2
.bg-purple,3,1,reports-style-guide.html,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,background: var(--purple-gradient);,2
.bg-secondary,10,5,design-preview.html; test_dashboard_styling.html; templates/dashboard.html; templates/components/professional-tool-layout.html; templates/dashboard.html,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,background-color: var(--bg-secondary);,2
.breadcrumb,36,10,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,,KEEP,In use,"display: flex;
  align-items: center;
  gap: var(--space-sm);
  margin-left: var(--space-lg);",5
.breadcrumb-item,18,10,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,,KEEP,In use,"color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);",4
.breadcrumb-separator,8,8,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-channel-analyzer.html...,No,,KEEP,In use,"width: 16px;
  height: 16px;
  color: var(--text-quaternary);",4
.btn-ghost,67,22,test_dashboard_functionality.html; templates/component-showcase.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/channel_profile_tool.html...,No,,KEEP,In use,"background: transparent;
  color: var(--text-secondary);",3
.btn-ghost:hover:not(:disabled),0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: var(--bg-overlay);
  color: var(--text-primary);",3
.btn-icon,50,18,design-preview.html; templates/component-showcase.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,.priority-icon; .trend-icon; .feed-icon,MERGE,"Duplicate rules with: .priority-icon, .trend-icon, .feed-icon","width: 16px;
  height: 16px;",3
.btn-lg,10,10,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/premium-dashboard-new.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,,KEEP,In use,"padding: var(--space-md) var(--space-lg);
  font-size: var(--text-base);
  font-weight: var(--font-w...",4
.btn-primary,57,24,test_dashboard_functionality.html; test_dashboard_styling.html; templates/component-showcase.html; templates/dashboard.html; templates/premium-channel-analyzer.html...,No,,KEEP,In use,"background: var(--purple-gradient);
  color: white;
  box-shadow: var(--shadow-md);",4
.btn-primary:hover:not(:disabled),0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"transform: translateY(-0.5px);
  box-shadow: var(--shadow-md);
  filter: brightness(1.05);",4
.btn-secondary,55,19,test_dashboard_functionality.html; templates/component-showcase.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/channel_profile_tool.html...,No,,KEEP,In use,"background: var(--bg-elevated);
  color: var(--text-primary);
  border: 1px solid var(--border-mediu...",4
.btn-sm,67,16,templates/component-showcase.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html...,No,,KEEP,In use,"padding: var(--space-xs) var(--space-sm);
  font-size: var(--text-xs);
  gap: var(--space-xs);",4
.btn-spinner,18,6,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html...,No,,KEEP,In use,"display: none;
  align-items: center;
  gap: var(--space-sm);",4
.btn-spinner.active,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,display: flex;,2
.btn-xs,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"padding: 2px var(--space-xs);
  font-size: 10px;
  gap: 2px;
  border-radius: var(--radius-sm);
  li...",6
.btn:disabled,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"opacity: 0.5;
  cursor: not-allowed;",3
.capabilities-grid,8,4,design-preview.html; templates/premium-comment-intelligence.html; templates/components/professional-tool-layout.html; templates/premium-comment-intelligence.html,No,.metric-grid; .form-row; .results-grid...,MERGE,"Duplicate rules with: .metric-grid, .form-row, .results-grid",grid-template-columns: 1fr;,2
.capabilities-title,6,4,design-preview.html; templates/premium-comment-intelligence.html; templates/components/professional-tool-layout.html; templates/premium-comment-intelligence.html,No,.stats-title,MERGE,Duplicate rules with: .stats-title,"font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);...",5
.capability-content h4,13,4,design-preview.html; templates/premium-comment-intelligence.html; templates/components/professional-tool-layout.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);...",5
.capability-content p,13,4,design-preview.html; templates/premium-comment-intelligence.html; templates/components/professional-tool-layout.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"font-size: var(--text-sm);
  color: var(--text-tertiary);
  line-height: var(--line-height-relaxed);",4
.capability-icon,22,4,design-preview.html; templates/premium-comment-intelligence.html; templates/components/professional-tool-layout.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"width: 20px;
  height: 20px;
  color: var(--purple-primary);
  flex-shrink: 0;
  margin-top: 2px;",6
.capability-item,17,4,design-preview.html; templates/premium-comment-intelligence.html; templates/components/professional-tool-layout.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"display: flex;
  gap: var(--space-md);
  padding: var(--space-lg);
  background: var(--bg-glass);
  ...",8
.capability-item:hover,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: var(--bg-glass-hover);
  transform: translateY(-1px);",3
.card-action-btn,2,1,templates/components/analysis-cards.html,No,,KEEP,In use,"width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content...",7
.card-footer,1,1,templates/components/analysis-cards.html,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,"padding: var(--space-xl);
  border-top: 1px solid var(--border-light);
  background: var(--bg-overla...",4
.card-gradient,14,12,templates/dashboard.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html; templates/premium-comment-intelligence.html...,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,"background: var(--primary-gradient);
  border: 1px solid var(--purple-tertiary);
  box-shadow: var(-...",5
.card-header,72,14,test_dashboard_styling.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html...,Yes,.section-header,MERGE,Duplicate rules with: .section-header,"flex-direction: column;
    align-items: flex-start;
    gap: var(--space-sm);",4
.card-subtitle,39,14,test_dashboard_styling.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html...,Yes,.stat-label,MERGE,Duplicate rules with: .stat-label,"font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin-top: var(--space-xs);",4
.card-title,69,14,test_dashboard_styling.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html...,Yes,.usage-title,MERGE,Duplicate rules with: .usage-title,"font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);",4
.card:hover,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"box-shadow: var(--shadow-md);
  border-color: var(--border-medium);",3
.change-period,1,1,templates/components/analysis-cards.html,No,.status-timestamp,MERGE,Duplicate rules with: .status-timestamp,"font-size: var(--text-xs);
  color: var(--text-secondary);",3
.change-value,2,1,templates/components/analysis-cards.html,No,,KEEP,In use,"font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--purple-primary...",4
.channel-avatar,6,4,templates/premium-channel-analyzer.html; templates/market-research-hub.html; templates/premium-channel-analyzer.html; templates/market-research-hub.html,No,,KEEP,In use,"width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid var(--border-light);",5
.channel-header,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"display: flex;
  align-items: center;
  gap: var(--space-lg);
  margin-bottom: var(--space-xl);",5
.channel-info,6,6,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html...,No,,KEEP,In use,"flex-direction: column;
    gap: var(--space-sm);",3
.channel-info h2,6,6,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html...,No,,KEEP,In use,"font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  ...",5
.channel-info p,6,6,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html...,No,,KEEP,In use,"color: var(--text-secondary);
  margin: 0;",3
.channel-name,6,6,templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"display: flex;
  align-items: center;
  gap: var(--space-xs);
  color: var(--text-secondary);
  font...",7
.channel-name i,6,6,templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,.metric-icon; .status-icon; .stat-item i,MERGE,"Duplicate rules with: .metric-icon, .status-icon, .stat-item i","width: 14px;
  height: 14px;",3
.channel-stats,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg)...",5
.channel-tier,4,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"padding: var(--space-xs) var(--space-sm);
  background: var(--bg-glass);
  border: 1px solid var(--b...",8
.connection-status,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--text-xs);
  color: ...",6
.connection-status.connected .status-dot,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: #10b981;
  animation: pulse 2s infinite;",3
.current-message,4,3,templates/component-showcase.html; templates/components/analysis-cards.html; templates/component-showcase.html,No,,KEEP,In use,"margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  font-style: italic;",5
.day-analysis,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,.top-titles,MERGE,Duplicate rules with: .top-titles,"display: flex;
  flex-direction: column;
  gap: var(--space-md);
  margin-top: var(--space-lg);",5
.day-bar,6,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"display: flex;
  align-items: center;
  gap: var(--space-md);",4
.day-bar-container,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"flex: 1;
  height: 20px;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  ove...",6
.day-bar-fill,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,.progress-fill,MERGE,Duplicate rules with: .progress-fill,"height: 100%;
  background: var(--purple-gradient);
  transition: width var(--transition-normal);",4
.day-name,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"min-width: 80px;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);",4
.day-percentage,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"min-width: 50px;
  text-align: right;
  font-weight: var(--font-weight-medium);
  color: var(--text-...",5
.easter-egg-hint,1,1,templates/components/navigation.html,No,,KEEP,In use,"display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-xs);
  padding: ...",11
.empty-actions,1,1,templates/components/analysis-cards.html,No,.insight-actions; .skeleton-footer; .error-actions,MERGE,"Duplicate rules with: .insight-actions, .skeleton-footer, .error-actions","display: flex;
  gap: var(--space-sm);",3
.empty-icon,2,1,templates/components/analysis-cards.html,No,,KEEP,In use,"width: 64px;
  height: 64px;
  background: var(--bg-overlay);
  border-radius: var(--radius-full);
 ...",9
.empty-icon-svg,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"width: 32px;
  height: 32px;
  color: var(--text-secondary);",4
.empty-message,2,1,templates/components/analysis-cards.html,No,.error-message,MERGE,Duplicate rules with: .error-message,"margin: 0 0 var(--space-lg) 0;
  font-size: var(--text-base);
  color: var(--text-secondary);
  max-...",5
.empty-title,1,1,templates/components/analysis-cards.html,No,.error-title,MERGE,Duplicate rules with: .error-title,"margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-xl);
  font-weight: var(--font-weight-semibol...",5
.error-content,3,3,templates/market-research-hub.html; templates/components/analysis-cards.html; templates/market-research-hub.html,No,.loading-content,MERGE,Duplicate rules with: .loading-content,"display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-hei...",8
.error-icon,4,3,templates/market-research-hub.html; templates/components/analysis-cards.html; templates/market-research-hub.html,No,,KEEP,In use,"width: 48px;
  height: 48px;
  color: var(--error);",4
.error-icon-svg,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"width: 32px;
  height: 32px;
  color: #ef4444;",4
.error-state,11,9,templates/component-showcase.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/components/analysis-cards.html...,No,.no-analysis,MERGE,Duplicate rules with: .no-analysis,"display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-al...",9
.error-state h3,11,9,templates/component-showcase.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/components/analysis-cards.html...,No,.no-analysis h3,MERGE,Duplicate rules with: .no-analysis h3,"color: var(--text-secondary);
  font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);...",5
.error-state i,11,9,templates/component-showcase.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/components/analysis-cards.html...,No,.no-analysis i,MERGE,Duplicate rules with: .no-analysis i,"width: 48px;
  height: 48px;
  color: var(--text-quaternary);",4
.error-text,0,0,,Yes,.loading-text,DELETE,Unused in templates and purged by PurgeCSS,"color: var(--text-tertiary);
  font-size: var(--text-lg);",3
.export-buttons,4,4,templates/premium-video-analyzer.html; templates/video-analyzer.html; templates/premium-video-analyzer.html; templates/video-analyzer.html,No,,KEEP,In use,"display: flex;
  gap: var(--space-md);
  flex-wrap: wrap;",4
.export-buttons .btn,4,4,templates/premium-video-analyzer.html; templates/video-analyzer.html; templates/premium-video-analyzer.html; templates/video-analyzer.html,No,,KEEP,In use,"flex: 1;
  min-width: 140px;",3
.export-status,2,2,templates/premium-video-analyzer.html; templates/premium-video-analyzer.html,No,.stat-item; .metric; .video-views,MERGE,"Duplicate rules with: .stat-item, .metric, .video-views","display: flex;
  align-items: center;
  gap: var(--space-xs);",4
.feed-title,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"margin: 0;
  font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(-...",8
.font-bold,11,3,reports-style-guide.html; templates/channel_profile_tool.html; templates/channel_profile_tool.html,No,,KEEP,In use,font-weight: var(--font-weight-bold);,2
.font-display,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,font-family: var(--font-display);,2
.font-extrabold,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,font-weight: var(--font-weight-extrabold);,2
.font-mono,3,2,design-preview.html; templates/components/professional-tool-layout.html,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,font-family: var(--font-mono);,2
.font-normal,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,font-weight: var(--font-weight-normal);,2
.font-semibold,38,5,reports-style-guide.html; templates/dashboard.html; templates/channel_profile_tool.html; templates/dashboard.html; templates/channel_profile_tool.html,No,,KEEP,In use,font-weight: var(--font-weight-semibold);,2
.form-header,12,4,design-preview.html; templates/market-research-hub.html; templates/components/professional-tool-layout.html; templates/market-research-hub.html,No,,KEEP,In use,"text-align: center;
  margin-bottom: var(--space-2xl);",3
.form-header .market-icon,12,4,design-preview.html; templates/market-research-hub.html; templates/components/professional-tool-layout.html; templates/market-research-hub.html,No,,KEEP,In use,"width: 64px;
  height: 64px;
  background: var(--purple-gradient);
  border-radius: var(--radius-2xl...",10
.form-header .market-icon i,12,4,design-preview.html; templates/market-research-hub.html; templates/components/professional-tool-layout.html; templates/market-research-hub.html,No,,KEEP,In use,"width: 32px;
  height: 32px;
  color: white;",4
.form-header h1,12,4,design-preview.html; templates/market-research-hub.html; templates/components/professional-tool-layout.html; templates/market-research-hub.html,No,.text-2xl,MERGE,Duplicate rules with: .text-2xl,font-size: var(--text-2xl);,2
.form-header p,12,4,design-preview.html; templates/market-research-hub.html; templates/components/professional-tool-layout.html; templates/market-research-hub.html,No,.text-base,MERGE,Duplicate rules with: .text-base,font-size: var(--text-base);,2
.form-input,28,14,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html; templates/premium-comment-intelligence.html...,No,,KEEP,In use,"width: 100%;
  padding: var(--space-md);
  border: 1px solid var(--border-medium);
  border-radius: ...",10
.form-input::placeholder,0,0,,No,.metric-change.neutral; .text-muted,MERGE,"Duplicate rules with: .metric-change.neutral, .text-muted",color: var(--text-muted);,2
.form-input:focus,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"outline: none;
  border-color: var(--purple-primary);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1...",4
.form-label,22,10,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/video-analyzer.html...,No,.form-option label,MERGE,Duplicate rules with: .form-option label,"font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);...",7
.form-label .label-icon,22,10,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/video-analyzer.html...,No,.form-label-icon; .form-option .label-icon,MERGE,"Duplicate rules with: .form-label-icon, .form-option .label-icon","width: 16px;
  height: 16px;
  color: var(--purple-primary);",4
.form-option,8,2,templates/market-research-hub.html; templates/market-research-hub.html,No,.usage-features,MERGE,Duplicate rules with: .usage-features,"display: flex;
  flex-direction: column;
  gap: var(--space-sm);",4
.form-select,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"appearance: none;
  background-image: url(""data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/s...",7
.form-textarea,4,2,templates/video-analyzer.html; templates/video-analyzer.html,No,,KEEP,In use,"resize: vertical;
  min-height: 120px;",3
.header-left,10,10,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,.header-right,MERGE,Duplicate rules with: .header-right,"display: flex;
  align-items: center;
  gap: var(--space-sm);",4
.history-dropdown,10,4,templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-video-analyzer.html; templates/market-research-hub.html,No,,KEEP,In use,"position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-elevated);
  border: 1px solid v...",12
.history-empty,6,4,templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-video-analyzer.html; templates/market-research-hub.html,No,,KEEP,In use,"padding: var(--space-xl);
  text-align: center;
  color: var(--text-tertiary);
  font-size: var(--te...",5
.history-header,4,4,templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-video-analyzer.html; templates/market-research-hub.html,No,,KEEP,In use,"padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid var(--border-light);
  font-wei...",6
.history-item,2,2,templates/premium-video-analyzer.html; templates/premium-video-analyzer.html,No,,KEEP,In use,"padding: var(--space-md) var(--space-lg);
  border-bottom: 1px solid var(--border-light);
  cursor: ...",5
.history-item:hover,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,background: var(--bg-tertiary);,2
.history-item:last-child,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,border-bottom: none;,2
.history-meta,2,2,templates/premium-video-analyzer.html; templates/premium-video-analyzer.html,No,,KEEP,In use,"font-size: var(--text-xs);
  color: var(--text-tertiary);",3
.history-title,2,2,templates/premium-video-analyzer.html; templates/premium-video-analyzer.html,No,,KEEP,In use,"font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-x...",7
.input-group .form-input,2,2,templates/market-research-hub.html; templates/market-research-hub.html,Yes,.tool-content; .recent-content; .video-details...,MERGE,"Duplicate rules with: .tool-content, .recent-content, .video-details",flex: 1;,2
.insight-card,30,8,reports-style-guide.html; templates/component-showcase.html; templates/premium-channel-analyzer.html; templates/market-research-hub.html; templates/components/analysis-cards.html...,No,.video-card,MERGE,Duplicate rules with: .video-card,"background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radi...",6
.insight-card h4,30,8,reports-style-guide.html; templates/component-showcase.html; templates/premium-channel-analyzer.html; templates/market-research-hub.html; templates/components/analysis-cards.html...,No,,KEEP,In use,"font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary...",5
.insight-card:hover,0,0,,No,.metric-card:hover; .video-card:hover; .title-card:hover...,MERGE,"Duplicate rules with: .metric-card:hover, .video-card:hover, .title-card:hover","box-shadow: var(--shadow-md);
  transform: translateY(-1px);",3
.insight-category,2,1,templates/components/analysis-cards.html,No,,KEEP,In use,"font-size: var(--text-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--purple-primary...",6
.insight-description,10,3,templates/market-research-hub.html; templates/components/analysis-cards.html; templates/market-research-hub.html,No,,KEEP,In use,"margin: 0 0 var(--space-md) 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-h...",5
.insight-header,1,1,templates/components/analysis-cards.html,No,.progress-header,MERGE,Duplicate rules with: .progress-header,"display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space...",5
.insight-metrics,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-lg);",4
.insight-priority,3,1,templates/components/analysis-cards.html,No,.metric-trend,MERGE,Duplicate rules with: .metric-trend,"width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: cent...",8
.insight-title,10,3,templates/market-research-hub.html; templates/components/analysis-cards.html; templates/market-research-hub.html,No,,KEEP,In use,"margin: 0 0 var(--space-sm) 0;
  font-size: var(--text-lg);
  font-weight: var(--font-weight-semibol...",5
.loading-placeholder,4,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-hei...",7
.loading-spinner,10,5,templates/market-research-hub.html; templates/video-analyzer.html; templates/components/professional-tool-layout.html; templates/market-research-hub.html; templates/video-analyzer.html,No,,KEEP,In use,"width: 48px;
  height: 48px;
  border: 4px solid var(--border-light);
  border-top-color: var(--purp...",7
.loading-text,5,3,templates/video-analyzer.html; templates/components/professional-tool-layout.html; templates/video-analyzer.html,No,.error-text,MERGE,Duplicate rules with: .error-text,"color: var(--text-tertiary);
  font-size: var(--text-lg);",3
.market-research-form,2,2,templates/market-research-hub.html; templates/market-research-hub.html,No,.usage-card; .stats-card,MERGE,"Duplicate rules with: .usage-card, .stats-card",padding: var(--space-xl);,2
.market-research-form > *,2,2,templates/market-research-hub.html; templates/market-research-hub.html,No,.welcome-section > *; .premium-card-header > *; .premium-stat-card > *,MERGE,"Duplicate rules with: .welcome-section > *, .premium-card-header > *, .premium-stat-card > *","position: relative;
  z-index: 1;",3
.market-research-form::before,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    r...",11
.metric-change,27,7,templates/metrics-dashboard.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html; templates/components/analysis-cards.html; templates/metrics-dashboard.html...,No,,KEEP,In use,"font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  display: flex;
  align-items:...",6
.metric-change.negative,0,0,,No,.text-error,MERGE,Duplicate rules with: .text-error,color: var(--error);,2
.metric-change.positive,0,0,,No,.stat-trend.up; .text-success,MERGE,"Duplicate rules with: .stat-trend.up, .text-success",color: var(--success);,2
.metric-header,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space...",5
.metric-item,36,4,debug_channel_display.html; templates/video-analyzer.html; templates/components/analysis-cards.html; templates/video-analyzer.html,No,,KEEP,In use,"display: flex;
  flex-direction: column;
  gap: var(--space-xs);",4
.metric-label,61,11,reports-style-guide.html; debug_channel_display.html; templates/channel_profile_tool.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html...,No,,KEEP,In use,"font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);...",6
.metric-value,71,13,reports-style-guide.html; debug_channel_display.html; templates/metrics-dashboard.html; templates/channel_profile_tool.html; templates/premium-dashboard-new.html...,No,,KEEP,In use,"font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  ...",5
.nav-badge,137,17,templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"margin-left: auto;
  background: var(--purple-primary);
  color: white;
  font-size: var(--text-xs);...",8
.nav-badge-blue,13,13,templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html...,No,,KEEP,In use,"background: #3b82f6;
  color: white;",3
.nav-badge-green,13,13,templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html...,No,,KEEP,In use,"background: #10b981;
  color: white;",3
.nav-badge-orange,14,13,templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html...,No,,KEEP,In use,"background: #f59e0b;
  color: white;",3
.nav-badge-purple,13,13,templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html...,No,"[data-theme=""dark""] .theme-toggle:hover",MERGE,"Duplicate rules with: [data-theme=""dark""] .theme-toggle:hover","background: var(--purple-primary);
  color: white;",3
.nav-icon,205,17,templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"width: 20px;
  height: 20px;
  flex-shrink: 0;",4
.nav-item,256,19,templates/component-showcase.html; templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html...,No,,KEEP,In use,"display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space...",13
.nav-item span,256,19,templates/component-showcase.html; templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html...,No,,KEEP,In use,display: none;,2
.nav-item.active,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: var(--purple-gradient);
  color: white;
  font-weight: var(--font-weight-semibold);
  bo...",5
.nav-item.active .nav-badge,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: rgba(255, 255, 255, 0.2);
  color: white;",3
.nav-item.active::before,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"content: '';
  position: absolute;
  left: -var(--space-md);
  top: 50%;
  transform: translateY(-50...",10
.nav-item:hover,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: var(--bg-overlay);
  color: var(--text-primary);
  transform: translateX(2px);",4
.nav-item:hover .agent-indicator,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"transform: scale(1.2);
  box-shadow: 0 0 8px currentColor;",3
.nav-items,57,17,templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"list-style: none;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);",5
.nav-section,114,17,templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,.schedule-overview; .schedule-patterns; .title-overview...,MERGE,"Duplicate rules with: .schedule-overview, .schedule-patterns, .title-overview",margin-bottom: var(--space-xl);,2
.nav-section-title,57,17,templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-tertiary)...",7
.no-analysis ul,2,2,templates/premium-comment-intelligence.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"text-align: left;
  max-width: 400px;",3
.performance-note,4,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"padding: var(--space-xs) var(--space-sm);
  border-radius: var(--radius-md);
  font-size: var(--text...",5
.performance-note.strong-performance,0,0,,No,.status-indicator.processing,MERGE,Duplicate rules with: .status-indicator.processing,"background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  border: 1px solid rgba(59, 130, 246, 0.3);",4
.performance-note.viral-performance,0,0,,No,.status-indicator.completed,MERGE,Duplicate rules with: .status-indicator.completed,"background: rgba(34, 197, 94, 0.1);
  color: var(--success);
  border: 1px solid rgba(34, 197, 94, 0...",4
.placeholder-spinner .spinner,4,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"width: 32px;
  height: 32px;
  border: 3px solid rgba(99, 102, 241, 0.3);
  border-top-color: var(--...",5
.placeholder-text,4,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"color: var(--text-tertiary);
  font-size: var(--text-base);",3
.premium-analyze-btn,2,2,templates/market-research-hub.html; templates/market-research-hub.html,No,,KEEP,In use,"padding: var(--space-md) var(--space-xl);
    font-size: var(--text-base);",3
.premium-analyze-btn .btn-icon,2,2,templates/market-research-hub.html; templates/market-research-hub.html,No,,KEEP,In use,"width: 20px;
  height: 20px;
  margin-right: var(--space-sm);",4
.premium-analyze-btn::before,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  backgro...",9
.premium-analyze-btn:active,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"transform: translateY(0);
  box-shadow: 
    0 4px 12px rgba(139, 92, 246, 0.4),
    inset 0 2px 4px...",3
.premium-analyze-btn:hover,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"transform: translateY(-2px);
  box-shadow: 
    0 12px 32px rgba(139, 92, 246, 0.4),
    0 20px 64px...",3
.premium-analyze-btn:hover::before,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,left: 100%;,2
.premium-card-description,10,4,templates/market-research-hub.html; templates/premium-dashboard-new.html; templates/market-research-hub.html; templates/premium-dashboard-new.html,No,.tool-description,MERGE,Duplicate rules with: .tool-description,"color: var(--text-tertiary);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space...",4
.premium-card-gradient,10,10,templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,,KEEP,In use,"background:
    linear-gradient(135deg, #8b5cf6 0%, #7c3aed 25%, #6d28d9 50%, #5b21b6 75%, #4c1d95 1...",6
.premium-card-gradient .premium-card-subtitle,10,10,templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,.premium-card-gradient .premium-card-title,MERGE,Duplicate rules with: .premium-card-gradient .premium-card-title,color: white;,2
.premium-card-gradient::before,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    r...",9
.premium-card-header,70,12,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html; templates/premium-comment-intelligence.html...,No,,KEEP,In use,"display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--s...",11
.premium-card-header::before,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    l...",9
.premium-card-icon,28,8,templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html; templates/premium-video-analyzer.html...,No,,KEEP,In use,"width: 24px;
  height: 24px;
  color: var(--purple-primary);
  flex-shrink: 0;",5
.premium-card-subtitle,36,12,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html; templates/premium-comment-intelligence.html...,No,.progress-text,MERGE,Duplicate rules with: .progress-text,"font-size: var(--text-sm);
  color: var(--text-tertiary);",3
.premium-card-title,64,12,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html; templates/premium-comment-intelligence.html...,No,,KEEP,In use,"font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);...",5
.premium-card:hover,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"box-shadow: var(--shadow-lg);
  transform: translateY(-2px);",3
.premium-content,10,10,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,,KEEP,In use,padding: var(--space-xl) var(--space-lg);,2
.premium-form-input,2,2,templates/market-research-hub.html; templates/market-research-hub.html,No,,KEEP,In use,"padding: var(--space-md) var(--space-lg);
    font-size: var(--text-base);",3
.premium-form-input::placeholder,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"color: var(--text-muted);
  font-style: italic;",3
.premium-form-input:focus,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"outline: none;
  border-color: var(--purple-primary);
  box-shadow: 
    0 0 0 4px rgba(139, 92, 246...",5
.premium-input-group,2,2,templates/market-research-hub.html; templates/market-research-hub.html,No,.analyzer-form.grid-layout,MERGE,Duplicate rules with: .analyzer-form.grid-layout,"grid-template-columns: 1fr;
    gap: var(--space-lg);",3
.premium-select,6,2,templates/market-research-hub.html; templates/market-research-hub.html,No,,KEEP,In use,"background: rgba(255, 255, 255, 0.9);
  border: 2px solid rgba(139, 92, 246, 0.1);
  border-radius: ...",14
.premium-select:focus,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"outline: none;
  border-color: var(--purple-primary);
  box-shadow: 
    0 0 0 3px rgba(139, 92, 246...",4
.premium-stat-card,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.9) 0%,
    rgba(255, 255, 255, 0.7) 10...",11
.premium-stat-card::before,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    r...",10
.premium-stat-card:hover,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"transform: translateY(-4px);
  border-color: var(--purple-primary);
  box-shadow: 
    0 16px 48px r...",4
.priority-badge,2,2,templates/market-research-hub.html; templates/market-research-hub.html,No,,KEEP,In use,"padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  display: inline-blo...",6
.priority-high,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"background: rgba(239, 68, 68, 0.1);
  color: #EF4444;",3
.priority-low,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"background: rgba(34, 197, 94, 0.1);
  color: #22C55E;",3
.priority-medium,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"background: rgba(245, 158, 11, 0.1);
  color: #F59E0B;",3
.progress-bar,23,10,templates/dashboard.html; templates/metrics-dashboard.html; templates/market-research-hub.html; templates/premium-dashboard.html; templates/components/analysis-cards.html...,No,,KEEP,In use,"height: 8px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-full);
  overflow: hidd...",6
.progress-bar-container,1,1,templates/components/analysis-cards.html,No,.usage-progress,MERGE,Duplicate rules with: .usage-progress,margin-bottom: var(--space-lg);,2
.progress-message,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,text-align: center;,2
.progress-percentage,4,3,templates/component-showcase.html; templates/components/analysis-cards.html; templates/component-showcase.html,No,,KEEP,In use,"font-size: var(--text-lg);
  font-weight: var(--font-weight-bold);
  color: var(--purple-primary);",4
.progress-step,15,2,templates/components/analysis-cards.html; templates/components/professional-tool-layout.html,No,,KEEP,In use,"display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  flex: 1;",6
.progress-step.completed .step-indicator,0,0,,No,.status-complete; .trend-up,MERGE,"Duplicate rules with: .status-complete, .trend-up",background: #10b981;,2
.progress-steps,4,2,templates/components/analysis-cards.html; templates/components/professional-tool-layout.html,No,,KEEP,In use,"display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-md);",4
.psychology-insights,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"margin-top: var(--space-lg);
  padding: var(--space-lg);
  background: var(--bg-elevated);
  border:...",6
.psychology-insights p,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"margin: 0 0 var(--space-sm) 0;
  color: var(--text-secondary);",3
.quick-analysis-form,8,6,templates/dashboard.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html; templates/dashboard.html; templates/premium-dashboard-new.html...,No,,KEEP,In use,"flex-direction: column;
    align-items: stretch;",3
.quick-analysis-form .form-group,8,6,templates/dashboard.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html; templates/dashboard.html; templates/premium-dashboard-new.html...,No,,KEEP,In use,"flex: 1;
  margin-bottom: 0;",3
.recent-card,6,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"display: flex;
  gap: var(--space-lg);
  padding: var(--space-lg);",4
.recent-insight,24,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"background: var(--bg-glass);
  color: var(--text-secondary);
  padding: 2px 6px;
  border-radius: va...",8
.recent-insights,6,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"display: flex;
  gap: var(--space-xs);
  flex-wrap: wrap;",4
.recent-meta,12,4,templates/premium-dashboard-new.html; templates/premium-dashboard.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html,No,,KEEP,In use,"font-size: var(--text-sm);
  color: var(--text-tertiary);
  margin-bottom: var(--space-sm);",4
.recent-overlay,6,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"position: absolute;
  top: var(--space-xs);
  right: var(--space-xs);",4
.recent-placeholder,4,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"width: 100%;
  height: 100%;
  background: var(--bg-tertiary);
  display: flex;
  align-items: cente...",7
.recent-placeholder-icon,2,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"width: 24px;
  height: 24px;
  color: var(--text-muted);",4
.recent-score,12,4,templates/premium-dashboard-new.html; templates/premium-dashboard.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html,No,,KEEP,In use,"background: var(--purple-gradient);
  color: white;
  padding: 2px 6px;
  border-radius: var(--radiu...",7
.recent-thumbnail,12,4,templates/premium-dashboard-new.html; templates/premium-dashboard.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html,No,,KEEP,In use,"width: 80px;
  height: 60px;
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: rela...",7
.recent-thumbnail img,12,4,templates/premium-dashboard-new.html; templates/premium-dashboard.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html,No,,KEEP,In use,"width: 100%;
  height: 100%;
  object-fit: cover;",4
.recent-title,12,4,templates/premium-dashboard-new.html; templates/premium-dashboard.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html,No,,KEEP,In use,"font-size: var(--text-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);...",9
.recommendation-card,2,2,templates/market-research-hub.html; templates/market-research-hub.html,No,,KEEP,In use,"border-left: 4px solid var(--purple-primary);
  transition: all 0.3s ease;",3
.recommendation-card:hover,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);",3
.recommendation-description,2,2,templates/market-research-hub.html; templates/market-research-hub.html,No,,KEEP,In use,"margin: 0 0 var(--space-sm) 0;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  line-h...",5
.recommendation-layout,2,2,templates/market-research-hub.html; templates/market-research-hub.html,No,,KEEP,In use,"flex-direction: column;
    align-items: flex-start;",3
.recommendation-number,2,2,templates/market-research-hub.html; templates/market-research-hub.html,No,,KEEP,In use,align-self: flex-start;,2
.recommendation-title,2,2,templates/market-research-hub.html; templates/market-research-hub.html,No,,KEEP,In use,"margin: 0 0 var(--space-xs) 0;
  color: var(--text-primary);
  font-size: var(--text-base);
  font-w...",5
.recommendations-list,2,2,templates/market-research-hub.html; templates/market-research-hub.html,No,,KEEP,In use,"display: flex;
  flex-direction: column;
  gap: var(--space-md);",4
.results-header,6,2,templates/channel_profile_tool.html; templates/channel_profile_tool.html,No,,KEEP,In use,"text-align: center;
  margin-bottom: var(--space-2xl);
  padding: var(--space-xl) 0;
  border-bottom...",6
.results-header::before,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  wid...",9
.results-section.visible,0,0,,Yes,to,DELETE,Unused in templates and purged by PurgeCSS,"opacity: 1;
  transform: translateY(0);",3
.results-subtitle,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"font-size: var(--text-lg);
  color: var(--text-secondary);
  margin: 0;",4
.results-title,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"font-size: var(--text-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  ...",5
.rounded-2xl,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,border-radius: var(--radius-2xl);,2
.rounded-full,2,1,reports-style-guide.html,No,,KEEP,In use,border-radius: var(--radius-full);,2
.rounded-lg,18,1,reports-style-guide.html,No,,KEEP,In use,border-radius: var(--radius-lg);,2
.rounded-md,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,border-radius: var(--radius-md);,2
.rounded-sm,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,border-radius: var(--radius-sm);,2
.rounded-xl,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,border-radius: var(--radius-xl);,2
.schedule-stat,10,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  padding: var(--space-lg);
  backg...",8
.schedule-stats,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,.trigger-grid,MERGE,Duplicate rules with: .trigger-grid,"display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-lg)...",5
.section-link,4,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"color: var(--purple-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  t...",5
.section-link:hover,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,color: var(--purple-secondary);,2
.section-subtitle,23,5,debug_channel_display.html; templates/premium-dashboard.html; templates/video-analyzer.html; templates/premium-dashboard.html; templates/video-analyzer.html,No,,KEEP,In use,"font-size: var(--text-base);
  color: var(--text-tertiary);
  margin-top: var(--space-xs);",4
.section-title,86,20,debug_channel_display.html; templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html...,No,,KEEP,In use,"font-size: var(--text-2xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary)...",4
.shadow-lg,4,3,design-preview.html; templates/channel_profile_tool.html; templates/channel_profile_tool.html,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,box-shadow: var(--shadow-lg);,2
.shadow-md,9,4,design-preview.html; templates/dashboard.html; templates/components/professional-tool-layout.html; templates/dashboard.html,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,box-shadow: var(--shadow-md);,2
.shadow-purple,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,box-shadow: var(--shadow-purple);,2
.shadow-sm,21,7,design-preview.html; test_dashboard_functionality.html; templates/dashboard.html; templates/channel_profile_tool.html; templates/components/professional-tool-layout.html...,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,box-shadow: var(--shadow-sm);,2
.shadow-xl,1,1,design-preview.html,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,box-shadow: var(--shadow-xl);,2
.shorts-indicator,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"position: absolute;
  top: var(--space-xs);
  left: var(--space-xs);
  background: var(--purple-prim...",10
.sidebar-logo,51,17,templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"display: flex;
  align-items: center;
  gap: var(--space-md);
  text-decoration: none;
  color: var(...",8
.sidebar-logo-icon,17,17,templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"width: 40px;
  height: 40px;
  background: var(--purple-gradient);
  border-radius: var(--radius-lg)...",11
.sidebar-user,51,11,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,,KEEP,In use,"display: flex;
  align-items: center;
  gap: var(--space-md);
  margin-bottom: var(--space-md);
  pa...",9
.sidebar-user-avatar,10,10,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,,KEEP,In use,"width: 32px;
  height: 32px;
  background: var(--purple-gradient);
  border-radius: 50%;
  display: ...",12
.sidebar-user-info,10,10,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,,KEEP,In use,"display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  min-width: 0;",5
.sidebar-user-name,10,10,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,,KEEP,In use,"font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
 ...",7
.sidebar-user-plan,10,10,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-dashboard.html...,No,,KEEP,In use,"font-size: var(--text-xs);
  color: var(--purple-primary);
  font-weight: var(--font-weight-medium);...",7
.skeleton-avatar,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"width: 40px;
  height: 40px;
  background: var(--bg-muted);
  border-radius: var(--radius-full);",5
.skeleton-button,2,1,templates/components/analysis-cards.html,No,,KEEP,In use,"height: 32px;
  background: var(--bg-muted);
  border-radius: var(--radius-md);
  width: 80px;",5
.skeleton-button.small,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,width: 60px;,2
.skeleton-content,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);",5
.skeleton-line,3,1,templates/components/analysis-cards.html,No,,KEEP,In use,width: 100%;,2
.skeleton-line.short,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,width: 70%;,2
.skeleton-text,4,1,templates/components/analysis-cards.html,No,,KEEP,In use,"background: var(--bg-muted);
  border-radius: var(--radius-sm);
  height: 16px;",4
.skeleton-title,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"width: 60%;
  height: 20px;",3
.spinner,58,11,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/video-analyzer.html...,No,,KEEP,In use,"width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  b...",7
.stat-card,26,6,templates/premium-channel-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html; templates/premium-channel-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"background: var(--bg-secondary);
  border: 1px solid var(--border-light);
  border-radius: var(--rad...",9
.stat-card:hover,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: var(--bg-tertiary);
  border-color: var(--border-medium);
  transform: translateY(-2px);...",5
.stat-icon,26,6,templates/premium-channel-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html; templates/premium-channel-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"width: 48px;
  height: 48px;
  background: var(--purple-primary);
  border-radius: var(--radius-lg);...",10
.stat-icon i,26,6,templates/premium-channel-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html; templates/premium-channel-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"width: 20px;
  height: 20px;
  color: white;",4
.stat-icon-large,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"width: 72px;
  height: 72px;
  background: var(--purple-gradient);
  border-radius: var(--radius-2xl...",10
.stat-icon-large i,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"width: 36px;
  height: 36px;
  color: white;",4
.stat-label-large,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-seconda...",7
.stat-trend,7,4,test_dashboard_functionality.html; test_dashboard_styling.html; templates/dashboard.html; templates/dashboard.html,No,,KEEP,In use,"font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);",3
.stat-value,65,12,test_dashboard_functionality.html; test_dashboard_styling.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1;",5
.stat-value-large,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"font-size: var(--text-4xl);
  font-weight: var(--font-weight-extrabold);
  color: var(--text-primary...",9
.status-agent-name,2,1,templates/components/analysis-cards.html,No,,KEEP,In use,"font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);",4
.status-dot,9,7,design-preview.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/components/analysis-cards.html; templates/components/professional-tool-layout.html...,No,,KEEP,In use,"width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--success);",5
.status-dot.active,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: var(--success);
  animation: pulse 2s infinite;",3
.status-dot.error,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,background: var(--error);,2
.status-dot.processing,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: var(--warning);
  animation: pulse 1s infinite;",3
.status-feed-content,7,3,templates/component-showcase.html; templates/components/analysis-cards.html; templates/component-showcase.html,No,,KEEP,In use,"max-height: 300px;
  overflow-y: auto;
  padding: var(--space-sm);",4
.status-feed-footer,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"padding: var(--space-sm) var(--space-lg);
  background: var(--bg-overlay);
  border-top: 1px solid v...",4
.status-feed-header,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md) v...",7
.status-feed-item,7,3,templates/component-showcase.html; templates/components/analysis-cards.html; templates/component-showcase.html,No,,KEEP,In use,"display: flex;
  gap: var(--space-sm);
  padding: var(--space-sm);
  border-radius: var(--radius-md)...",7
.status-feed-item:hover,0,0,,No,.agent-status-item:hover,MERGE,Duplicate rules with: .agent-status-item:hover,background: var(--bg-overlay);,2
.status-idle,0,0,,Yes,.trend-neutral,DELETE,Unused in templates and purged by PurgeCSS,background: var(--text-tertiary);,2
.status-indicator,41,10,design-preview.html; test_dashboard_functionality.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html...,No,,KEEP,In use,"display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm) var(--space...",8
.status-indicator.error,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: rgba(239, 68, 68, 0.1);
  color: var(--error);
  border: 1px solid rgba(239, 68, 68, 0.3...",4
.status-item-avatar,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: var(--purple-primary...",10
.status-item-header,1,1,templates/components/analysis-cards.html,No,,KEEP,In use,"display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space...",5
.status-message,18,3,templates/channel_profile_tool.html; templates/components/analysis-cards.html; templates/channel_profile_tool.html,No,,KEEP,In use,"margin: 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
  line-height: 1.4;",5
.status-spinner,6,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,"width: 14px;
  height: 14px;
  border: 2px solid rgba(59, 130, 246, 0.3);
  border-radius: 50%;
  bo...",7
.status-starting,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,background: #f59e0b;,2
.step-indicator,4,1,templates/components/analysis-cards.html,No,,KEEP,In use,"width: 12px;
  height: 12px;
  border-radius: var(--radius-full);
  background: var(--bg-muted);
  t...",6
.step-label,4,1,templates/components/analysis-cards.html,No,,KEEP,In use,"font-size: var(--text-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);...",5
.tab-button,58,6,templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/video-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"min-width: auto;
    flex: 1;",3
.tab-button.active,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: var(--purple-primary);
  border-color: var(--purple-primary);
  color: white;
  box-shad...",5
.tab-button:hover,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: var(--bg-tertiary);
  border-color: var(--border-medium);
  color: var(--text-primary);",4
.tab-content,36,6,templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/video-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"display: none;
  margin-bottom: var(--space-xl);",3
.tab-content.active,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,display: block;,2
.tab-icon,38,6,templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/video-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"width: 16px;
  height: 16px;
  flex-shrink: 0;",4
.template-card,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-radius: var(--radi...",7
.template-card h4,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"font-size: var(--text-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary...",5
.text-3xl,10,3,design-preview.html; reports-style-guide.html; templates/components/professional-tool-layout.html,No,,KEEP,In use,font-size: var(--text-3xl);,2
.text-4xl,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,font-size: var(--text-4xl);,2
.text-left,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,text-align: left;,2
.text-lg,19,5,design-preview.html; reports-style-guide.html; templates/component-showcase.html; templates/components/professional-tool-layout.html; templates/component-showcase.html,No,,KEEP,In use,font-size: var(--text-lg);,2
.text-primary,88,15,design-preview.html; test_dashboard_functionality.html; reports-style-guide.html; test_dashboard_styling.html; templates/component-showcase.html...,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,color: var(--text-primary);,2
.text-purple,18,1,reports-style-guide.html,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,color: var(--purple-primary);,2
.text-right,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,text-align: right;,2
.text-secondary,90,13,design-preview.html; test_dashboard_functionality.html; reports-style-guide.html; test_dashboard_styling.html; templates/metrics-dashboard.html...,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,color: var(--text-secondary);,2
.text-sm,61,7,design-preview.html; test_dashboard_functionality.html; reports-style-guide.html; test_dashboard_styling.html; templates/dashboard.html...,No,,KEEP,In use,font-size: var(--text-sm);,2
.text-tertiary,10,7,design-preview.html; templates/dashboard.html; templates/channel_profile_tool.html; templates/video-analyzer.html; templates/dashboard.html...,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,color: var(--text-tertiary);,2
.text-warning,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,color: var(--warning);,2
.text-xl,16,5,design-preview.html; test_dashboard_functionality.html; reports-style-guide.html; test_dashboard_styling.html; templates/components/professional-tool-layout.html,No,,KEEP,In use,font-size: var(--text-xl);,2
.text-xs,4,2,design-preview.html; reports-style-guide.html,No,.video-date,MERGE,Duplicate rules with: .video-date,font-size: var(--text-xs);,2
.theme-toggle,21,18,design-preview.html; templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html...,No,,KEEP,In use,"width: 36px;
  height: 36px;
  border: none;
  background: var(--bg-overlay);
  border-radius: var(-...",12
.theme-toggle > *,21,18,design-preview.html; templates/base.html; templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html...,No,,KEEP,In use,"position: relative;
  z-index: 1;
  transition: all var(--transition-fast);",4
.theme-toggle::before,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: v...",12
.theme-toggle:active,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,transform: scale(0.95);,2
.theme-toggle:hover,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: var(--purple-light);
  color: var(--purple-primary);
  transform: scale(1.05);",4
.theme-toggle:hover::before,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"width: 100%;
  height: 100%;",3
.title-card,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"padding: var(--space-lg);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);...",5
.title-metrics,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"display: flex;
  gap: var(--space-lg);
  color: var(--text-secondary);
  font-size: var(--text-sm);",5
.title-stat,10,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  padding: var(--space-lg);
  backg...",9
.title-stats,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--space-lg)...",5
.title-text,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"font-size: var(--text-lg);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
 ...",6
.tool-card,14,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"display: flex;
  flex-direction: column;
  height: 100%;",4
.tool-feature,56,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"background: var(--bg-glass);
  color: var(--text-secondary);
  padding: var(--space-xs) var(--space-...",8
.tool-features,14,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"display: flex;
  gap: var(--space-sm);
  margin-bottom: var(--space-lg);
  flex-wrap: wrap;",5
.tool-footer,14,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: var(--space-l...",6
.tool-icon,36,4,design-preview.html; templates/premium-dashboard.html; templates/components/professional-tool-layout.html; templates/premium-dashboard.html,No,,KEEP,In use,"width: 48px;
  height: 48px;
  background: var(--purple-gradient);
  border-radius: var(--radius-xl)...",9
.tool-icon-svg,14,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"width: 24px;
  height: 24px;
  color: white;",4
.tool-status,14,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--space-xs) var(...",5
.tool-status.active,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: var(--success);
  color: white;",3
.tool-status.coming-soon,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"background: var(--bg-glass);
  color: var(--text-tertiary);
  border: 1px solid var(--border-medium)...",4
.tool-title,18,4,design-preview.html; templates/premium-dashboard.html; templates/components/professional-tool-layout.html; templates/premium-dashboard.html,No,,KEEP,In use,"font-size: var(--text-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);...",5
.trend-neutral,0,0,,Yes,.status-idle,DELETE,Unused in templates and purged by PurgeCSS,background: var(--text-tertiary);,2
.trigger-card,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"padding: var(--space-lg);
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);...",6
.trigger-card h4,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"font-size: var(--text-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary...",5
.trigger-stats,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"display: flex;
  flex-direction: column;
  gap: var(--space-xs);
  font-size: var(--text-sm);
  colo...",6
.upgrade-btn,1,1,templates/components/navigation.html,No,,KEEP,In use,"width: 100%;
  justify-content: center;
  font-size: var(--text-xs);
  padding: var(--space-xs) var(...",5
.usage-bar,1,1,templates/components/navigation.html,No,,KEEP,In use,"height: 4px;
  background: var(--bg-muted);
  border-radius: var(--radius-sm);
  overflow: hidden;
 ...",6
.usage-count,1,1,templates/components/navigation.html,No,,KEEP,In use,"font-size: var(--text-xs);
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);",4
.usage-feature,10,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"display: flex;
  align-items: center;
  gap: var(--space-sm);
  color: var(--text-secondary);",5
.usage-fill,1,1,templates/components/navigation.html,No,,KEEP,In use,"height: 100%;
  background: var(--purple-gradient);
  border-radius: var(--radius-sm);
  transition:...",5
.usage-header,3,3,templates/premium-dashboard.html; templates/components/navigation.html; templates/premium-dashboard.html,No,,KEEP,In use,"display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--space...",5
.usage-icon,8,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"width: 16px;
  height: 16px;
  color: var(--success);",4
.usage-label,9,3,templates/premium-dashboard-new.html; templates/components/navigation.html; templates/premium-dashboard-new.html,No,.version-label,MERGE,Duplicate rules with: .version-label,"font-size: var(--text-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);",4
.usage-price,2,2,templates/premium-dashboard.html; templates/premium-dashboard.html,No,,KEEP,In use,"font-size: var(--text-xl);
  font-weight: var(--font-weight-bold);
  color: var(--purple-primary);",4
.user-avatar,17,17,templates/base.html; templates/premium-channel-analyzer.html; templates/channel_profile_tool.html; templates/premium-video-analyzer.html; templates/market-research-hub.html...,No,,KEEP,In use,"width: 40px;
  height: 40px;
  background: var(--purple-primary);
  border-radius: var(--radius-full...",10
.user-name,15,15,templates/base.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-dashboard-new.html...,No,,KEEP,In use,"font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--text-sm);...",8
.user-profile-card,1,1,templates/components/navigation.html,No,,KEEP,In use,"display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-sm);
  backgrou...",8
.user-tier,1,1,templates/components/navigation.html,No,,KEEP,In use,"font-size: var(--text-xs);
  color: var(--text-secondary);
  margin: 0;",4
.version-info,13,13,templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html...,No,,KEEP,In use,"display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  margin-bot...",6
.version-number,13,13,templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html...,No,,KEEP,In use,"font-size: var(--text-xs);
  color: var(--purple-primary);
  font-weight: var(--font-weight-semibold...",4
.video-card:hover .video-thumbnail img,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,transform: scale(1.05);,2
.video-content,4,4,templates/premium-channel-analyzer.html; templates/market-research-hub.html; templates/premium-channel-analyzer.html; templates/market-research-hub.html,No,.video-info,MERGE,Duplicate rules with: .video-info,padding: var(--space-md);,2
.video-duration,4,4,templates/premium-channel-analyzer.html; templates/market-research-hub.html; templates/premium-channel-analyzer.html; templates/market-research-hub.html,No,,KEEP,In use,"position: absolute;
  bottom: var(--space-xs);
  right: var(--space-xs);
  background: rgba(0, 0, 0,...",10
.video-header,4,4,templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html; templates/premium-video-analyzer.html; templates/premium-comment-intelligence.html,No,,KEEP,In use,flex-direction: column;,2
.video-meta,2,2,templates/premium-channel-analyzer.html; templates/premium-channel-analyzer.html,No,,KEEP,In use,"display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--text-sm);...",6
.video-overlay,0,0,,Yes,,DELETE,Unused in templates and purged by PurgeCSS,"position: absolute;
  top: var(--space-sm);
  right: var(--space-sm);
  background: rgba(0, 0, 0, 0....",10
.video-stats,8,8,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-channel-analyzer.html...,No,,KEEP,In use,"display: flex;
  gap: var(--space-md);
  color: var(--text-secondary);
  font-size: var(--text-sm);",5
.video-thumbnail,4,4,templates/premium-channel-analyzer.html; templates/market-research-hub.html; templates/premium-channel-analyzer.html; templates/market-research-hub.html,No,,KEEP,In use,"width: 100%;
  aspect-ratio: 16/9;
  border-radius: var(--radius-md);
  overflow: hidden;
  margin-b...",6
.video-thumbnail img,4,4,templates/premium-channel-analyzer.html; templates/market-research-hub.html; templates/premium-channel-analyzer.html; templates/market-research-hub.html,No,,KEEP,In use,"width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;",5
.video-title,8,8,templates/premium-channel-analyzer.html; templates/premium-video-analyzer.html; templates/market-research-hub.html; templates/premium-comment-intelligence.html; templates/premium-channel-analyzer.html...,No,,KEEP,In use,"font-size: var(--text-base);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(...",9
.welcome-section,10,8,templates/dashboard.html; templates/premium-channel-analyzer.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html; templates/dashboard.html...,No,,KEEP,In use,"grid-template-columns: 1fr;
    gap: var(--space-xl);",3
.welcome-section::before,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,"content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    r...",11
.welcome-stats,6,6,templates/premium-channel-analyzer.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html; templates/premium-channel-analyzer.html; templates/premium-dashboard-new.html...,No,,KEEP,In use,"min-width: auto;
    grid-template-columns: 1fr;",3
.welcome-subtitle,6,6,templates/premium-channel-analyzer.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html; templates/premium-channel-analyzer.html; templates/premium-dashboard-new.html...,No,,KEEP,In use,"font-size: var(--text-lg);
  color: var(--text-tertiary);",3
.welcome-title,6,6,templates/premium-channel-analyzer.html; templates/premium-dashboard-new.html; templates/premium-dashboard.html; templates/premium-channel-analyzer.html; templates/premium-dashboard-new.html...,No,,KEEP,In use,"font-size: var(--text-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  ...",5
0%,177,21,design-preview.html; test_dashboard_functionality.html; reports-style-guide.html; test_dashboard_styling.html; templates/component-showcase.html...,No,,KEEP,In use,"opacity: 0.6;
    transform: scale(1);",3
100%,81,15,design-preview.html; reports-style-guide.html; test_dashboard_styling.html; templates/component-showcase.html; templates/dashboard.html...,No,,KEEP,In use,"opacity: 1;
    transform: scale(1.01);",3
16%,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,border-right-color: #f97316;,2
20%,4,4,templates/metrics-dashboard.html; templates/launch-plan.html; templates/metrics-dashboard.html; templates/launch-plan.html,No,53%; 80%,MERGE,"Duplicate rules with: 53%, 80%","transform: translate3d(0,0,0);",2
33%,2,2,templates/dashboard.html; templates/dashboard.html,No,,KEEP,In use,border-right-color: #eab308;,2
40%,0,0,,No,43%,MERGE,Duplicate rules with: 43%,"transform: translate3d(0, -8px, 0);",2
50%,37,16,design-preview.html; test_dashboard_functionality.html; reports-style-guide.html; templates/component-showcase.html; templates/dashboard.html...,No,,KEEP,In use,background-position: 100% 50%;,2
66%,2,2,templates/dashboard.html; templates/dashboard.html,No,,KEEP,In use,border-right-color: #3b82f6;,2
70%,9,7,templates/dashboard.html; templates/launch-plan.html; templates/video-analyzer.html; templates/components/navigation.html; templates/dashboard.html...,No,,KEEP,In use,"transform: translate3d(0, -4px, 0);",2
83%,0,0,,No,,REVIEW,Not found in templates but kept by PurgeCSS - may be programmatically added,border-right-color: var(--purple-primary);,2
90%,2,2,templates/metrics-dashboard.html; templates/metrics-dashboard.html,No,,KEEP,In use,"transform: translate3d(0, -2px, 0);",2
"[data-theme=""dark""] .glass-card",1,1,design-preview.html,No,,KEEP,In use,"background: rgba(30, 41, 59, 0.6);
  backdrop-filter: blur(24px);
  border: 1px solid rgba(255, 255,...",5
"[data-theme=""dark""] .market-research-form",1,1,design-preview.html,No,,KEEP,In use,"background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.08) 0%,
    rgba(139, 92, 246, 0.04) 50...",3
"[data-theme=""dark""] .premium-form-input",1,1,design-preview.html,No,,KEEP,In use,"background: rgba(30, 41, 59, 0.8);
  border-color: rgba(139, 92, 246, 0.2);
  color: var(--text-prim...",5
"[data-theme=""dark""] .premium-form-input:focus",1,1,design-preview.html,No,,KEEP,In use,"box-shadow: 
    0 0 0 4px rgba(139, 92, 246, 0.2),
    0 8px 24px rgba(139, 92, 246, 0.25),
    ins...",2
"[data-theme=""dark""] .premium-select",1,1,design-preview.html,No,,KEEP,In use,"background: rgba(30, 41, 59, 0.8);
  border-color: rgba(139, 92, 246, 0.2);
  color: var(--text-prim...",4
"[data-theme=""dark""] .premium-stat-card",1,1,design-preview.html,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,"background: linear-gradient(135deg,
    rgba(30, 41, 59, 0.9) 0%,
    rgba(30, 41, 59, 0.7) 100%);
 ...",4
"[data-theme=""dark""] .premium-stat-card:hover",1,1,design-preview.html,Yes,,REVIEW,Purged by PurgeCSS but found in templates - may be dynamic,"box-shadow: 
    0 16px 48px rgba(0, 0, 0, 0.4),
    0 24px 80px rgba(139, 92, 246, 0.25),
    inset...",2
"[data-theme=""dark""] .theme-toggle",1,1,design-preview.html,No,,KEEP,In use,"background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);",3
body,193,35,debug_js.html; test_syntax_fix.html; test_market_research_button.html; test_formatnumber_fix.html; design-preview.html...,No,,KEEP,In use,"font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-weight-no...",9
from,51,25,test_formatnumber_fix.html; design-preview.html; test_dashboard_styling.html; templates/component-showcase.html; templates/dashboard.html...,No,,KEEP,In use,"opacity: 0;
    transform: translateY(20px);",3
html,277,33,debug_js.html; test_syntax_fix.html; test_market_research_button.html; test_formatnumber_fix.html; design-preview.html...,No,,KEEP,In use,"font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothin...",6
to,2586,38,debug_js.html; test_syntax_fix.html; test_market_research_button.html; test_formatnumber_fix.html; design-preview.html...,No,.results-section.visible,MERGE,Duplicate rules with: .results-section.visible,"opacity: 1;
    transform: translateY(0);",3
