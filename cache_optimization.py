"""
Cache Optimization for YouTube Research v2
Reduces API costs from ~$1 per analysis to fraction of that
"""

import os
import json
import hashlib
import time
import logging
from typing import Dict, Any, Optional, Tuple
from functools import lru_cache
from datetime import datetime, timedelta

# Configure LiteLLM caching
import litellm

# Enable local caching for all LiteLLM calls
litellm.enable_cache(
    type="local",  # Use "redis" for production with Redis server
    supported_call_types=["completion", "acompletion"]
)

# Set cache TTL to 24 hours (can be adjusted)
litellm.cache.ttl = 86400  # 24 hours in seconds

logger = logging.getLogger(__name__)


class TranscriptCache:
    """Caches transcript data to avoid passing full text multiple times"""
    
    def __init__(self):
        self._cache = {}
        self._metadata = {}
    
    def store_transcript(self, video_id: str, transcript: str) -> str:
        """Store transcript and return cache key"""
        cache_key = f"transcript_{video_id}"
        
        # Store metadata for analysis without full text
        self._metadata[cache_key] = {
            'length': len(transcript),
            'word_count': len(transcript.split()),
            'line_count': len(transcript.split('\n')),
            'preview': transcript[:500] + '...' if len(transcript) > 500 else transcript,
            'has_timestamps': '[' in transcript and ']' in transcript,
            'stored_at': time.time()
        }
        
        # Store full text
        self._cache[cache_key] = transcript
        
        logger.info(f"Cached transcript for {video_id}: {self._metadata[cache_key]['word_count']} words")
        return cache_key
    
    def get_transcript(self, cache_key: str) -> str:
        """Retrieve full transcript from cache"""
        return self._cache.get(cache_key, '')
    
    def get_metadata(self, cache_key: str) -> Dict[str, Any]:
        """Get transcript metadata without loading full text"""
        return self._metadata.get(cache_key, {})
    
    def get_analysis_prompt(self, cache_key: str) -> str:
        """Get a prompt-friendly version with metadata"""
        metadata = self.get_metadata(cache_key)
        if not metadata:
            return "No transcript available"
        
        return f"""
TRANSCRIPT METADATA:
- Length: {metadata['word_count']} words
- Has Timestamps: {metadata.get('has_timestamps', False)}
- Preview: {metadata['preview']}

[Full transcript cached as {cache_key} - {metadata['word_count']} words available for analysis]
"""


class CommentCache:
    """Optimizes comment data for different agents"""
    
    def __init__(self):
        self._cache = {}
    
    def process_comments(self, video_id: str, comment_data: list) -> Dict[str, Any]:
        """Process and cache comment data efficiently"""
        cache_key = f"comments_{video_id}"
        
        # Extract different levels of comment data
        processed = {
            'total_comments': len(comment_data),
            'top_10_comments': comment_data[:10],  # For most agents
            'top_50_comments': comment_data[:50],  # For deeper analysis
            'full_comments': comment_data,  # Only for comment agent
            
            # Pre-computed summaries
            'sentiment_preview': self._analyze_sentiment_preview(comment_data[:20]),
            'category_preview': self._categorize_preview(comment_data[:20]),
            'question_preview': self._extract_questions_preview(comment_data[:30]),
            
            # Statistics
            'avg_likes': sum(c.get('likeCount', 0) for c in comment_data[:50]) / min(50, len(comment_data)) if comment_data else 0,
            'has_creator_replies': any(c.get('authorChannelId', {}).get('value') == video_id for c in comment_data[:50]),
            'language_diversity': self._check_language_diversity(comment_data[:30])
        }
        
        self._cache[cache_key] = processed
        logger.info(f"Cached comment data for {video_id}: {processed['total_comments']} comments")
        return processed
    
    def get_for_agent(self, video_id: str, agent_type: str) -> Any:
        """Get appropriate comment data for specific agent"""
        cache_key = f"comments_{video_id}"
        cached = self._cache.get(cache_key, {})
        
        if agent_type == 'performance':
            # Performance agent only needs summary
            return {
                'total_comments': cached.get('total_comments', 0),
                'sentiment_preview': cached.get('sentiment_preview', {}),
                'avg_likes': cached.get('avg_likes', 0)
            }
        
        elif agent_type == 'script':
            # Script agent needs minimal comment context
            return {
                'total_comments': cached.get('total_comments', 0),
                'top_comments_preview': [
                    {'text': c['textDisplay'][:100], 'likes': c.get('likeCount', 0)}
                    for c in cached.get('top_10_comments', [])[:5]
                ]
            }
        
        elif agent_type == 'seo':
            # SEO agent looks for keywords in comments
            return {
                'comment_keywords': self._extract_keywords(cached.get('top_50_comments', [])),
                'question_keywords': cached.get('question_preview', {}).get('keywords', [])
            }
        
        elif agent_type == 'psychology':
            # Psychology agent needs emotional indicators
            return {
                'sentiment_distribution': cached.get('sentiment_preview', {}),
                'emotional_comments': self._extract_emotional_comments(cached.get('top_50_comments', [])),
                'engagement_indicators': {
                    'avg_likes': cached.get('avg_likes', 0),
                    'has_creator_replies': cached.get('has_creator_replies', False)
                }
            }
        
        elif agent_type == 'comment':
            # Comment agent gets everything
            return cached
        
        else:
            # Default: minimal data
            return {'total_comments': cached.get('total_comments', 0)}
    
    def _analyze_sentiment_preview(self, comments: list) -> Dict[str, int]:
        """Quick sentiment analysis on subset"""
        positive_words = ['love', 'amazing', 'great', 'awesome', 'fantastic', 'best', 'excellent']
        negative_words = ['hate', 'terrible', 'worst', 'awful', 'horrible', 'bad', 'disappointing']
        
        sentiment = {'positive': 0, 'negative': 0, 'neutral': 0}
        
        for comment in comments:
            text = comment.get('textDisplay', '').lower()
            if any(word in text for word in positive_words):
                sentiment['positive'] += 1
            elif any(word in text for word in negative_words):
                sentiment['negative'] += 1
            else:
                sentiment['neutral'] += 1
        
        return sentiment
    
    def _categorize_preview(self, comments: list) -> Dict[str, int]:
        """Quick categorization of comment types"""
        categories = {
            'questions': 0,
            'feedback': 0,
            'emotional': 0,
            'technical': 0
        }
        
        for comment in comments:
            text = comment.get('textDisplay', '').lower()
            if '?' in text:
                categories['questions'] += 1
            if any(word in text for word in ['should', 'could', 'suggest', 'try']):
                categories['feedback'] += 1
            if any(word in text for word in ['feel', 'love', 'hate', 'amazing']):
                categories['emotional'] += 1
            if any(word in text for word in ['how', 'tutorial', 'explain', 'code']):
                categories['technical'] += 1
        
        return categories
    
    def _extract_questions_preview(self, comments: list) -> Dict[str, Any]:
        """Extract questions for preview"""
        questions = []
        question_keywords = []
        
        for comment in comments:
            text = comment.get('textDisplay', '')
            if '?' in text:
                questions.append(text[:200])
                # Extract potential keywords from questions
                words = text.lower().split()
                question_keywords.extend([w for w in words if len(w) > 4 and w.isalpha()])
        
        return {
            'count': len(questions),
            'samples': questions[:5],
            'keywords': list(set(question_keywords))[:10]
        }
    
    def _check_language_diversity(self, comments: list) -> bool:
        """Check if comments have multiple languages"""
        # Simple check for non-ASCII characters
        non_ascii_count = 0
        for comment in comments:
            text = comment.get('textDisplay', '')
            if any(ord(char) > 127 for char in text):
                non_ascii_count += 1
        
        return non_ascii_count > len(comments) * 0.2
    
    def _extract_keywords(self, comments: list) -> list:
        """Extract SEO-relevant keywords from comments"""
        word_freq = {}
        stop_words = {'the', 'is', 'at', 'which', 'on', 'and', 'a', 'an', 'as', 'are', 'was', 'were', 'i', 'you', 'this', 'that'}
        
        for comment in comments:
            words = comment.get('textDisplay', '').lower().split()
            for word in words:
                word = word.strip('.,!?;:"')
                if len(word) > 3 and word not in stop_words and word.isalpha():
                    word_freq[word] = word_freq.get(word, 0) + 1
        
        # Return top 20 keywords
        sorted_keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
        return [word for word, freq in sorted_keywords[:20] if freq > 1]
    
    def _extract_emotional_comments(self, comments: list) -> list:
        """Extract emotionally charged comments for psychology analysis"""
        emotional_indicators = ['love', 'hate', 'amazing', 'terrible', 'best', 'worst', 'incredible', 'awful', '!', '❤', '😍', '😡']
        emotional_comments = []
        
        for comment in comments[:20]:  # Limit to 20 for performance
            text = comment.get('textDisplay', '')
            if any(indicator in text.lower() for indicator in emotional_indicators):
                emotional_comments.append({
                    'text': text[:200],
                    'likes': comment.get('likeCount', 0)
                })
        
        return emotional_comments[:10]  # Return top 10


class VideoDataOptimizer:
    """Creates agent-specific video data objects"""
    
    @staticmethod
    def create_base_data(video_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract only essential video data"""
        return {
            'video_id': video_data.get('video_id'),
            'title': video_data.get('title'),
            'views': video_data.get('views'),
            'likes': video_data.get('likes'),
            'comments': video_data.get('comments'),
            'duration_minutes': video_data.get('duration_minutes'),
            'duration_seconds': video_data.get('duration_seconds'),
            'published_date': video_data.get('published_date'),
            'days_since_published': video_data.get('days_since_published'),
            'engagement_rate': video_data.get('engagement_rate'),
            'description_snippet': video_data.get('description', '')[:500]  # Only first 500 chars
        }
    
    @staticmethod
    def optimize_for_agent(video_data: Dict[str, Any], agent_type: str, 
                          comment_cache: CommentCache = None,
                          transcript_cache_key: str = None) -> Dict[str, Any]:
        """Create optimized data package for specific agent"""
        
        # Start with base data
        optimized = VideoDataOptimizer.create_base_data(video_data)
        
        # Add agent-specific data
        if agent_type == 'performance':
            # Performance agent needs channel context and metrics
            optimized['channel_context'] = video_data.get('channel_context', {})
            optimized['performance_metrics'] = video_data.get('performance_metrics', {})
            
        elif agent_type == 'script':
            # Script agent needs transcript reference
            if transcript_cache_key:
                optimized['transcript_cache_key'] = transcript_cache_key
                optimized['transcript_metadata'] = {
                    'word_count': video_data.get('transcript_data', {}).get('word_count', 0),
                    'has_timestamps': video_data.get('transcript_data', {}).get('has_timestamps', False)
                }
        
        elif agent_type == 'seo':
            # SEO agent needs title, description, and tags
            optimized['description'] = video_data.get('description', '')  # Full description for SEO
            optimized['tags'] = video_data.get('tags', [])[:20]  # Top 20 tags
            
        elif agent_type == 'psychology':
            # Psychology agent needs engagement metrics and demographics hints
            optimized['channel_info'] = {
                'channel_name': video_data.get('channel_context', {}).get('channel_name'),
                'subscriber_count': video_data.get('channel_context', {}).get('channel_subscribers'),
                'channel_tier': video_data.get('channel_context', {}).get('channel_tier')
            }
            
        elif agent_type == 'comment':
            # Comment agent needs more detailed metrics
            optimized['detailed_metrics'] = {
                'like_ratio': video_data.get('likes', 0) / max(video_data.get('views', 1), 1),
                'comment_ratio': video_data.get('comments', 0) / max(video_data.get('views', 1), 1),
                'engagement_rate': video_data.get('engagement_rate', 0)
            }
        
        # Add comment data from cache if available
        if comment_cache and video_data.get('video_id'):
            comment_data = comment_cache.get_for_agent(video_data['video_id'], agent_type)
            if agent_type == 'comment':
                optimized['comment_data'] = comment_data.get('full_comments', [])
            else:
                optimized['comment_summary'] = comment_data
        
        return optimized


class AnalysisResultCache:
    """Caches complete analysis results to avoid re-running for same videos"""
    
    def __init__(self, cache_dir: str = ".cache/analyses"):
        self.cache_dir = cache_dir
        os.makedirs(cache_dir, exist_ok=True)
        self.ttl_hours = 24  # Cache for 24 hours
    
    def _get_cache_path(self, video_id: str, agent_type: str) -> str:
        """Get cache file path for specific analysis"""
        return os.path.join(self.cache_dir, f"{video_id}_{agent_type}.json")
    
    def get_cached_result(self, video_id: str, agent_type: str) -> Optional[Dict[str, Any]]:
        """Retrieve cached analysis if available and not expired"""
        cache_path = self._get_cache_path(video_id, agent_type)
        
        if not os.path.exists(cache_path):
            return None
        
        try:
            with open(cache_path, 'r') as f:
                cached = json.load(f)
            
            # Check if cache is expired
            cached_time = datetime.fromisoformat(cached['timestamp'])
            if datetime.now() - cached_time > timedelta(hours=self.ttl_hours):
                os.remove(cache_path)
                return None
            
            logger.info(f"Using cached {agent_type} analysis for {video_id}")
            return cached['result']
            
        except Exception as e:
            logger.warning(f"Error reading cache for {video_id}/{agent_type}: {e}")
            return None
    
    def store_result(self, video_id: str, agent_type: str, result: Dict[str, Any]):
        """Store analysis result in cache"""
        cache_path = self._get_cache_path(video_id, agent_type)
        
        try:
            cache_data = {
                'timestamp': datetime.now().isoformat(),
                'video_id': video_id,
                'agent_type': agent_type,
                'result': result
            }
            
            with open(cache_path, 'w') as f:
                json.dump(cache_data, f)
            
            logger.info(f"Cached {agent_type} analysis for {video_id}")
            
        except Exception as e:
            logger.warning(f"Error caching result for {video_id}/{agent_type}: {e}")


# Helper function to calculate hash for content comparison
def content_hash(content: str) -> str:
    """Generate hash for content to detect changes"""
    return hashlib.md5(content.encode()).hexdigest()


# Configuration for prompt optimization
PROMPT_OPTIMIZATION_CONFIG = {
    'use_references': True,  # Use references instead of full text
    'compress_examples': True,  # Compress example outputs
    'batch_similar_analyses': True,  # Batch similar analysis requests
    'cache_prompts': True,  # Cache formatted prompts
}


def optimize_agent_prompts(original_prompt: str, transcript_ref: str = None, 
                          comment_summary: Dict = None) -> str:
    """Optimize prompts to use references instead of full content"""
    
    if transcript_ref and PROMPT_OPTIMIZATION_CONFIG['use_references']:
        # Replace full transcript with reference
        original_prompt = original_prompt.replace(
            '## FULL TRANSCRIPT:',
            f'## TRANSCRIPT REFERENCE: {transcript_ref}\n[Transcript analysis will be performed on cached content]'
        )
    
    if comment_summary and PROMPT_OPTIMIZATION_CONFIG['compress_examples']:
        # Replace full comment data with summary
        original_prompt = original_prompt.replace(
            'Comments: {video_data.get(\'comment_data\', [])}',
            f'Comment Summary: {json.dumps(comment_summary, indent=2)}'
        )
    
    return original_prompt


# Export main components
__all__ = [
    'TranscriptCache',
    'CommentCache', 
    'VideoDataOptimizer',
    'AnalysisResultCache',
    'optimize_agent_prompts',
    'PROMPT_OPTIMIZATION_CONFIG'
]