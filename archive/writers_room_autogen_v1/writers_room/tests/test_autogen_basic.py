#!/usr/bin/env python3
"""
Basic test script for AutoGen Writers' Room
Tests the new implementation with a simple example
"""

import os
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import directly from the module file to avoid __init__.py issues
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'core_system'))
from autogen_writers_room import WritersRoomInput, SequentialWritersRoom
import json


def test_rome_carthage():
    """Test with Rome vs Carthage topic"""
    
    input_data = WritersRoomInput(
        topic="Rome vs Carthage: The Punic Wars",
        golden_nuggets=[
            "The strait between Sicily and Carthage is only 90 miles wide",
            "Rome built 120 warships in 60 days by copying a single Carthaginian wreck",
            "<PERSON> lost 50% of his army crossing the Alps",
            "Carthage's harbor could hold 220 warships invisible from the sea",
            "Rome forced Carthage to move 10 miles inland after victory"
        ],
        persona={
            "channel": "Frontier",
            "voice": "Awestruck Strategist",
            "focus": "How geography shapes the rise and fall of empires"
        },
        max_rounds=10,
        temperature=0.8
    )
    
    print("🎬 AutoGen Writers' Room - Rome vs Carthage Test")
    print("=" * 80)
    print(f"Topic: {input_data.topic}")
    print(f"Golden Nuggets: {len(input_data.golden_nuggets)} facts")
    print(f"Persona: {input_data.persona['voice']}")
    print("=" * 80)
    
    # Check for API key
    if not os.environ.get("OPENAI_API_KEY"):
        print("\n❌ ERROR: OPENAI_API_KEY not found in environment")
        print("Please set: export OPENAI_API_KEY='your-key-here'")
        return
    
    try:
        # Initialize Writers' Room
        print("\n🚀 Initializing Sequential Writers' Room...")
        writers_room = SequentialWritersRoom(model="gpt-4")
        
        # Run the process
        print("📝 Running writers' room process...")
        print("   1️⃣ Director creating structure...")
        print("   2️⃣ Hooksmith crafting hooks...")
        print("   3️⃣ Story-Weaver building narrative...")
        print("   4️⃣ Closer creating ending...")
        print("   5️⃣ Sensory Director adding production cues...\n")
        
        output = writers_room.run_writers_room(input_data)
        
        # Display results
        print(f"\n✅ SUCCESS! Script generated in {output.execution_time:.2f} seconds")
        print(f"\n📊 METRICS:")
        print(f"   - Estimated Duration: {output.estimated_duration}")
        print(f"   - Hooks Created: {len(output.hook_variations)}")
        print(f"   - Visual Cues: {len(output.visual_cues)}")
        print(f"   - Vocal Annotations: {output.vocal_annotations}")
        print(f"   - Total Length: {len(output.final_script)} characters")
        
        # Show hook variations
        if output.hook_variations:
            print(f"\n🎣 HOOK VARIATIONS:")
            for i, hook in enumerate(output.hook_variations[:3], 1):
                print(f"\n{i}. {hook[:150]}..." if len(hook) > 150 else f"\n{i}. {hook}")
        
        # Show structure preview
        if output.structure:
            print(f"\n🏗️ STRUCTURE PREVIEW:")
            print(output.structure[:500] + "..." if len(output.structure) > 500 else output.structure)
        
        # Show final script preview
        print(f"\n📜 FINAL SCRIPT PREVIEW (first 800 chars):")
        print("-" * 80)
        print(output.final_script[:800] + "...")
        
        # Save output
        output_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        output_path = os.path.join(output_dir, "outputs", "autogen_tests", "rome_carthage_autogen.json")
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        writers_room.save_output(output, output_path)
        print(f"\n💾 Full output saved to: {output_path}")
        
        # Save just the final script as text
        script_path = output_path.replace('.json', '_script.txt')
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(f"TOPIC: {input_data.topic}\n")
            f.write("=" * 80 + "\n\n")
            f.write(output.final_script)
        print(f"📄 Script saved to: {script_path}")
        
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()


def test_custom_topic():
    """Test with a custom topic from user input"""
    
    print("\n🎬 AutoGen Writers' Room - Custom Topic")
    print("=" * 80)
    
    # Get topic from user
    topic = input("Enter your topic: ").strip()
    if not topic:
        topic = "The Fall of the Roman Empire"
    
    # Get golden nuggets
    print("\nEnter golden nuggets (facts to include). Press Enter twice when done:")
    golden_nuggets = []
    while True:
        nugget = input(f"Nugget {len(golden_nuggets) + 1}: ").strip()
        if not nugget:
            break
        golden_nuggets.append(nugget)
    
    if not golden_nuggets:
        print("Using default golden nuggets...")
        golden_nuggets = [
            "Rome controlled 2 million square miles at its peak",
            "The empire split into East and West in 285 AD",
            "The last Western Roman Emperor was deposed in 476 AD"
        ]
    
    input_data = WritersRoomInput(
        topic=topic,
        golden_nuggets=golden_nuggets,
        persona={
            "channel": "Frontier",
            "voice": "Awestruck Strategist",
            "focus": "Geographic and strategic factors in historical events"
        }
    )
    
    # Run the same process as above
    if not os.environ.get("OPENAI_API_KEY"):
        print("\n❌ ERROR: OPENAI_API_KEY not found")
        return
    
    try:
        writers_room = SequentialWritersRoom(model="gpt-4")
        output = writers_room.run_writers_room(input_data)
        
        print(f"\n✅ Script generated in {output.execution_time:.2f} seconds")
        print(f"📏 Length: {len(output.final_script)} characters")
        
        # Save output
        safe_topic = "".join(c for c in topic if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_topic = safe_topic.replace(' ', '_')[:50]
        
        output_path = f"/Users/<USER>/Documents/CrewAI/youtube-research-v2/writers_room/outputs/autogen_tests/{safe_topic}_autogen.json"
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        writers_room.save_output(output, output_path)
        print(f"\n💾 Output saved to: {output_path}")
        
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "custom":
        test_custom_topic()
    else:
        test_rome_carthage()