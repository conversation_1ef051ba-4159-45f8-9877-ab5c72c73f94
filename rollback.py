#!/usr/bin/env python3
"""
YouTube Research v2 - Emergency Rollback Script
Restores codebase to pre-optimization state
"""

import os
import subprocess
import sys
import shutil
from pathlib import Path
from datetime import datetime

class RollbackManager:
    def __init__(self):
        self.project_root = Path.cwd()
        self.backup_pattern = "../youtube-research-v2-backup-*"
    
    def find_latest_backup(self):
        """Find the most recent backup directory"""
        backup_dirs = sorted(Path("..").glob("youtube-research-v2-backup-*"))
        if not backup_dirs:
            return None
        return backup_dirs[-1]
    
    def rollback_from_backup(self):
        """Rollback using backup directory"""
        print("🔍 Looking for backup directories...")
        
        latest_backup = self.find_latest_backup()
        if not latest_backup:
            print("❌ No backup directories found!")
            print("   Expected pattern: ../youtube-research-v2-backup-YYYYMMDD_HHMMSS")
            return False
        
        print(f"📁 Found backup: {latest_backup}")
        
        # Confirm rollback
        response = input(f"🚨 This will OVERWRITE current files with backup from {latest_backup.name}. Continue? (yes/no): ")
        if response.lower() != 'yes':
            print("❌ Rollback cancelled")
            return False
        
        try:
            print("🔄 Rolling back from backup...")
            
            # Create a safety backup of current state
            safety_backup = f"../youtube-research-v2-pre-rollback-{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            print(f"💾 Creating safety backup: {safety_backup}")
            shutil.copytree(".", safety_backup)
            
            # Remove current files (except .git)
            print("🗑️ Removing current files...")
            for item in Path(".").iterdir():
                if item.name == ".git":
                    continue
                if item.is_dir():
                    shutil.rmtree(item)
                else:
                    item.unlink()
            
            # Copy backup contents
            print("📋 Restoring from backup...")
            for item in latest_backup.iterdir():
                if item.name == ".git":
                    continue
                if item.is_dir():
                    shutil.copytree(item, item.name)
                else:
                    shutil.copy2(item, item.name)
            
            print("✅ Rollback completed successfully")
            print(f"💾 Safety backup saved to: {safety_backup}")
            return True
            
        except Exception as e:
            print(f"❌ Rollback failed: {e}")
            return False
    
    def rollback_git(self, commits=1):
        """Rollback using git"""
        try:
            print(f"🔄 Rolling back {commits} git commit(s)...")
            
            # Show what will be reset
            result = subprocess.run(
                ["git", "log", f"--oneline", f"-{commits}"],
                capture_output=True, text=True, check=True
            )
            print("📝 Commits to be reset:")
            print(result.stdout)
            
            # Confirm rollback
            response = input("🚨 This will RESET git history. Continue? (yes/no): ")
            if response.lower() != 'yes':
                print("❌ Git rollback cancelled")
                return False
            
            # Perform git reset
            subprocess.run(["git", "reset", "--hard", f"HEAD~{commits}"], check=True)
            print("✅ Git rollback completed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Git rollback failed: {e}")
            return False
        except Exception as e:
            print(f"❌ Git rollback error: {e}")
            return False
    
    def rollback_specific_files(self, files):
        """Rollback specific files from git"""
        try:
            print(f"🔄 Rolling back specific files: {', '.join(files)}")
            
            for file in files:
                if not Path(file).exists():
                    print(f"⚠️ File not found: {file}")
                    continue
                
                subprocess.run(["git", "checkout", "HEAD~1", "--", file], check=True)
                print(f"✅ Restored: {file}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ File rollback failed: {e}")
            return False
    
    def test_rollback(self):
        """Test rollback procedure with a small change"""
        print("🧪 Testing rollback procedure...")
        
        test_file = "ROLLBACK_TEST.txt"
        test_content = f"Rollback test - {datetime.now()}"
        
        try:
            # Create test file
            print("📝 Creating test file...")
            with open(test_file, 'w') as f:
                f.write(test_content)
            
            # Commit test file
            print("💾 Committing test file...")
            subprocess.run(["git", "add", test_file], check=True)
            subprocess.run(["git", "commit", "-m", "Rollback test file"], check=True)
            
            # Rollback the test commit
            print("🔄 Rolling back test commit...")
            success = self.rollback_git(commits=1)
            
            # Verify rollback
            if success and not Path(test_file).exists():
                print("✅ Rollback test PASSED")
                return True
            else:
                print("❌ Rollback test FAILED")
                # Clean up if rollback didn't work
                if Path(test_file).exists():
                    Path(test_file).unlink()
                return False
                
        except Exception as e:
            print(f"❌ Rollback test error: {e}")
            # Clean up
            if Path(test_file).exists():
                Path(test_file).unlink()
            return False
    
    def show_status(self):
        """Show current status and available rollback options"""
        print("📊 ROLLBACK STATUS")
        print("=" * 50)
        
        # Show git status
        try:
            result = subprocess.run(["git", "status", "--porcelain"], 
                                  capture_output=True, text=True, check=True)
            if result.stdout.strip():
                print("📝 Uncommitted changes:")
                print(result.stdout)
            else:
                print("✅ Working directory clean")
        except:
            print("⚠️ Cannot read git status")
        
        # Show recent commits
        try:
            result = subprocess.run(["git", "log", "--oneline", "-5"], 
                                  capture_output=True, text=True, check=True)
            print("\n📚 Recent commits:")
            print(result.stdout)
        except:
            print("⚠️ Cannot read git log")
        
        # Show available backups
        backup_dirs = sorted(Path("..").glob("youtube-research-v2-backup-*"))
        if backup_dirs:
            print(f"\n💾 Available backups ({len(backup_dirs)}):")
            for backup in backup_dirs[-3:]:  # Show last 3
                print(f"   - {backup.name}")
        else:
            print("\n❌ No backup directories found")

def main():
    """Main rollback interface"""
    rollback = RollbackManager()
    
    print("🚨 YOUTUBE RESEARCH V2 - EMERGENCY ROLLBACK")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "status":
            rollback.show_status()
        elif command == "test":
            success = rollback.test_rollback()
            sys.exit(0 if success else 1)
        elif command == "git":
            commits = int(sys.argv[2]) if len(sys.argv) > 2 else 1
            success = rollback.rollback_git(commits)
            sys.exit(0 if success else 1)
        elif command == "backup":
            success = rollback.rollback_from_backup()
            sys.exit(0 if success else 1)
        else:
            print(f"❌ Unknown command: {command}")
            sys.exit(1)
    else:
        # Interactive mode
        rollback.show_status()
        print("\nRollback options:")
        print("1. Rollback from backup directory")
        print("2. Rollback git commits")
        print("3. Test rollback procedure")
        print("4. Show status only")
        
        choice = input("\nSelect option (1-4): ").strip()
        
        if choice == "1":
            success = rollback.rollback_from_backup()
        elif choice == "2":
            commits = input("Number of commits to rollback (default: 1): ").strip()
            commits = int(commits) if commits.isdigit() else 1
            success = rollback.rollback_git(commits)
        elif choice == "3":
            success = rollback.test_rollback()
        elif choice == "4":
            success = True
        else:
            print("❌ Invalid choice")
            success = False
        
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
