"""
Simple Cost Tracker for YouTube Research v2
Tracks token usage and estimates costs
"""
import time
import logging
from datetime import datetime
from typing import Dict, Any

logger = logging.getLogger(__name__)

# Gemini 2.5 Flash pricing (as of Jan 2025)
# https://ai.google.dev/pricing
GEMINI_FLASH_PRICING = {
    'input': 0.30 / 1_000_000,    # $0.30 per 1M input tokens
    'output': 2.50 / 1_000_000,   # $2.50 per 1M output tokens (including thinking tokens)
    'context_cache': 0.075 / 1_000_000,  # $0.075 per 1M tokens for context caching
    'cache_storage': 1.00 / 1_000_000 / 60  # $1.00 per 1M tokens per hour
}

class CostTracker:
    def __init__(self):
        self.session_costs = []
        self.cache_hits = 0
        self.cache_misses = 0
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        
    def estimate_tokens(self, text: str) -> int:
        """Rough estimate: 1 token ≈ 4 characters"""
        return len(text) // 4
    
    def track_agent_call(self, agent_name: str, prompt_text: str, response_text: str, cached: bool = False):
        """Track costs for an agent call"""
        input_tokens = self.estimate_tokens(prompt_text)
        output_tokens = self.estimate_tokens(response_text)
        
        if cached:
            self.cache_hits += 1
            input_cost = 0  # Cached calls are free!
            output_cost = 0
            logger.info(f"💰 CACHE HIT: {agent_name} - Saved ${(input_tokens * GEMINI_FLASH_PRICING['input'] + output_tokens * GEMINI_FLASH_PRICING['output']):.4f}")
        else:
            self.cache_misses += 1
            input_cost = input_tokens * GEMINI_FLASH_PRICING['input']
            output_cost = output_tokens * GEMINI_FLASH_PRICING['output']
            self.total_input_tokens += input_tokens
            self.total_output_tokens += output_tokens
            
        total_cost = input_cost + output_cost
        
        call_data = {
            'timestamp': datetime.now().isoformat(),
            'agent': agent_name,
            'input_tokens': input_tokens,
            'output_tokens': output_tokens,
            'input_cost': input_cost,
            'output_cost': output_cost,
            'total_cost': total_cost,
            'cached': cached
        }
        
        self.session_costs.append(call_data)
        
        if not cached:
            logger.info(f"💵 {agent_name}: {input_tokens} in + {output_tokens} out = ${total_cost:.4f}")
        
        return total_cost
    
    def get_session_summary(self) -> Dict[str, Any]:
        """Get cost summary for current session"""
        total_cost = sum(c['total_cost'] for c in self.session_costs)
        cached_savings = sum(
            c['input_tokens'] * GEMINI_FLASH_PRICING['input'] + 
            c['output_tokens'] * GEMINI_FLASH_PRICING['output']
            for c in self.session_costs if c['cached']
        )
        
        summary = {
            'total_cost': total_cost,
            'cached_savings': cached_savings,
            'total_with_no_cache': total_cost + cached_savings,
            'savings_percentage': (cached_savings / (total_cost + cached_savings) * 100) if (total_cost + cached_savings) > 0 else 0,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'cache_hit_rate': (self.cache_hits / (self.cache_hits + self.cache_misses) * 100) if (self.cache_hits + self.cache_misses) > 0 else 0,
            'total_input_tokens': self.total_input_tokens,
            'total_output_tokens': self.total_output_tokens,
            'agent_breakdown': {}
        }
        
        # Calculate per-agent costs
        for call in self.session_costs:
            agent = call['agent']
            if agent not in summary['agent_breakdown']:
                summary['agent_breakdown'][agent] = {
                    'calls': 0,
                    'total_cost': 0,
                    'cached_calls': 0
                }
            summary['agent_breakdown'][agent]['calls'] += 1
            summary['agent_breakdown'][agent]['total_cost'] += call['total_cost']
            if call['cached']:
                summary['agent_breakdown'][agent]['cached_calls'] += 1
        
        return summary
    
    def print_summary(self):
        """Print a nice summary to logs"""
        summary = self.get_session_summary()
        
        logger.info("=" * 60)
        logger.info("💰 COST TRACKING SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total Cost: ${summary['total_cost']:.4f}")
        logger.info(f"Cached Savings: ${summary['cached_savings']:.4f}")
        logger.info(f"Would Cost Without Cache: ${summary['total_with_no_cache']:.4f}")
        logger.info(f"Savings: {summary['savings_percentage']:.1f}%")
        logger.info(f"Cache Hit Rate: {summary['cache_hit_rate']:.1f}% ({summary['cache_hits']}/{summary['cache_hits'] + summary['cache_misses']})")
        logger.info("-" * 60)
        logger.info("Per-Agent Breakdown:")
        for agent, data in summary['agent_breakdown'].items():
            logger.info(f"  {agent}: ${data['total_cost']:.4f} ({data['calls']} calls, {data['cached_calls']} cached)")
        logger.info("=" * 60)

# Global tracker instance
cost_tracker = CostTracker()