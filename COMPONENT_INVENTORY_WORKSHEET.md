# YouTube Research v2 - Component Inventory & Patterns Worksheet

## Overview
This document catalogues all components found in the design system, their intended states, the design tokens they use, and patterns observed in the codebase.

---

## 1. BUTTON COMPONENTS (`.btn-*`)

### Base Button Class: `.btn`
- **Purpose**: Core button styling foundation
- **Design Tokens Used**:
  - `--space-sm`, `--space-md` (padding)
  - `--radius-md` (border radius)
  - `--font-primary` (font family)
  - `--text-sm` (font size)
  - `--transition-fast` (hover transitions)

### Button Variants:

#### `.btn-primary`
- **Purpose**: Primary call-to-action buttons
- **States**: default, hover, disabled
- **Tokens**: `--purple-gradient`, `--shadow-md`
- **Usage**: "Analyze Now", "Launch Analyzer", form submissions

#### `.btn-secondary` 
- **Purpose**: Secondary actions
- **States**: default, hover, disabled
- **Tokens**: `--bg-elevated`, `--border-medium`, `--text-primary`
- **Usage**: Alternative actions, cancel buttons

#### `.btn-ghost`
- **Purpose**: Subtle actions, navigation elements
- **States**: default, hover, disabled
- **Tokens**: transparent background, `--text-secondary`, `--bg-overlay` on hover
- **Usage**: Icon-only buttons, less prominent actions

### Button Sizes:

#### `.btn-xs`
- **Tokens**: `2px var(--space-xs)` padding, `10px` font-size
- **Usage**: Compact interfaces, tags

#### `.btn-sm`
- **Tokens**: `var(--space-xs) var(--space-sm)` padding, `--text-xs`
- **Usage**: Cards, secondary actions

#### `.btn-lg`
- **Tokens**: `var(--space-md) var(--space-lg)` padding, `--text-base`
- **Usage**: Hero sections, primary CTAs

### Button Elements:
- `.btn-icon`: 16x16px icon containers
- `.btn-spinner`: Loading state overlay

---

## 2. CARD COMPONENTS (`.card-*`)

### Base Card Class: `.card`
- **Purpose**: Content containers with elevation
- **Design Tokens Used**:
  - `--surface-card` (background)
  - `--border-light` (border)
  - `--radius-xl` (border radius)
  - `--shadow-glass` (box shadow)
  - Backdrop filter blur(12px)

### Card Variants:

#### `.card-gradient`
- **Purpose**: Premium/highlighted cards
- **Tokens**: `--primary-gradient`, `--purple-tertiary`, `--shadow-purple`
- **Usage**: Featured modules, premium features

#### Card Structure Classes:
- `.card-header`: Top section with title/actions
- `.card-title`: Main heading (--text-2xl, --font-weight-bold)
- `.card-subtitle`: Secondary text (--text-base, --text-secondary)
- `.card-content`: Main content area
- `.card-footer`: Bottom section with meta/actions

### Specialized Card Types:

#### `.analysis-card`
- **Purpose**: Analysis result containers
- **Components**:
  - `.analysis-card-header`: Icon + title + actions
  - `.analysis-card-icon`: 40x40px colored icon container
  - `.analysis-card-title-section`: Title and subtitle wrapper
  - `.analysis-card-actions`: Action buttons container
  - `.analysis-card-content`: Main content
  - `.analysis-card-footer`: Metadata and tags
  - `.analysis-card-meta`: Timestamp and confidence
  - `.analysis-card-tags`: Tag collection

#### `.metric-card`
- **Purpose**: Statistical data display
- **Components**:
  - `.metric-header`: Icon and trend indicator
  - `.metric-icon`: 40x40px icon background
  - `.metric-value`: Large number (--text-3xl, --font-weight-bold)
  - `.metric-label`: Description text
  - `.metric-change`: Change indicator with trend

#### `.insight-card`
- **Purpose**: AI insights and recommendations
- **Components**:
  - `.insight-header`: Priority indicator + category
  - `.insight-priority`: Color-coded priority (high/medium/low)
  - `.insight-category`: Uppercase category label
  - `.insight-title`: Main insight heading
  - `.insight-description`: Detailed explanation
  - `.insight-metrics`: Key metrics display
  - `.insight-actions`: Action buttons

---

## 3. NAVIGATION COMPONENTS (`.nav-*`)

### Navigation Structure:
- `.nav-section`: Grouped navigation items
- `.nav-section-title`: Section headers (--text-sm, uppercase, --text-tertiary)
- `.nav-items`: List container
- `.nav-item`: Individual navigation links

### Navigation States:
- **Default**: `--text-secondary` color
- **Hover**: `--bg-overlay` background, `translateX(2px)` transform
- **Active**: `--purple-gradient` background, white text, left border indicator

### Navigation Elements:
- `.nav-icon`: 18x18px icon containers
- `.nav-badge`: Status indicators with color variants:
  - `.nav-badge-purple`: Default purple
  - `.nav-badge-blue`: Blue accent
  - `.nav-badge-green`: Green accent
  - `.nav-badge-orange`: Orange accent

---

## 4. ANALYSIS COMPONENTS (`.analysis-*`)

### Analysis Display:
- `.analysis-section`: Main analysis containers
- `.analysis-header`: Title section with icon
- `.analysis-icon`: 48x48px agent/category icons
- `.analysis-title`: Large headings (--text-3xl)
- `.analysis-subtitle`: Secondary headings (--text-lg)

### Analysis States:
- `.analysis-status`: Processing status indicators
- `.status-indicator`: Color-coded status badges
  - `.processing`: Blue (analysis in progress)
  - `.completed`: Green (analysis finished)
  - `.error`: Red (analysis failed)

### Analysis Content:
- `.analysis-text`: Formatted analysis content
- `.analysis-timestamp`: Time metadata
- `.analysis-confidence`: AI confidence scores

---

## 5. AGENT COMPONENTS (`.agent-*`)

### Agent Status Display:
- `.agent-status-card`: Individual agent containers
- `.agent-avatar-section`: Agent icon area
- `.agent-avatar`: Circular agent icons with color coding:
  - `.agent-1`: Performance (--agent-1: #8B5CF6)
  - `.agent-2`: Script (--agent-2: #3B82F6)
  - `.agent-3`: SEO (--agent-3: #10B981)
  - `.agent-4`: Psychology (--agent-4: #F59E0B)
  - `.agent-5`: Comments (--agent-5: #EF4444)
  - `.agent-6`: Synthesis (--agent-6: #6366F1)

### Agent States:
- `.agent-status-indicator`: Status dots
  - `.status-idle`: Gray
  - `.status-starting`: Orange
  - `.status-working`: Blue
  - `.status-thinking`: Purple
  - `.status-complete`: Green
  - `.status-error`: Red

### Agent Progress:
- `.agent-progress`: Progress bar container
- `.agent-progress-fill`: Animated fill with gradient
- `.agent-name`: Agent title
- `.agent-message`: Current status text

---

## 6. METRIC COMPONENTS (`.metric-*`)

### Metric Display:
- `.metric-grid`: Responsive grid layout
- `.metric-header`: Icon and trend container
- `.metric-icon`: Background circle for icons
- `.metric-trend`: Trend direction indicators
  - `.trend-up`: Green upward trend
  - `.trend-down`: Red downward trend
  - `.trend-neutral`: Gray neutral trend

### Metric Content:
- `.metric-value`: Large numbers (--text-3xl, --font-weight-bold)
- `.metric-label`: Descriptive text (--text-sm, --font-weight-medium)
- `.metric-change`: Change indicators
  - `.change-value`: Colored percentage
  - `.change-period`: Time period context

---

## 7. PROGRESS COMPONENTS (`.progress-*`)

### Progress Indicators:
- `.progress-indicator`: Main progress container
- `.progress-header`: Title and percentage display
- `.progress-title`: Progress description
- `.progress-percentage`: Numeric progress

### Progress Visualization:
- `.progress-bar-container`: Wrapper for progress bar
- `.progress-bar`: Background track (--bg-muted)
- `.progress-fill`: Animated fill (--purple-gradient)
- `.progress-text`: Percentage labels

### Progress Steps:
- `.progress-steps`: Step indicator container
- `.progress-step`: Individual step
  - `.active`: Current step (purple)
  - `.completed`: Finished step (green)
- `.step-indicator`: Visual step dots
- `.step-label`: Step descriptions

---

## 8. STATUS COMPONENTS (`.status-*`)

### Status Feed:
- `.status-feed`: Live activity container
- `.status-feed-header`: Title and controls
- `.status-feed-content`: Scrollable content area
- `.status-feed-footer`: Connection status

### Status Items:
- `.status-feed-item`: Individual status entries
- `.status-item-avatar`: Agent/action icons
- `.status-item-content`: Message content
- `.status-item-header`: Agent name and timestamp
- `.status-agent-name`: Agent identification
- `.status-timestamp`: Time information
- `.status-message`: Status description

### Status Indicators:
- `.status-dot`: Connection indicators
  - `.connected`: Green pulsing dot
  - `.disconnected`: Red static dot

---

## 9. FORM COMPONENTS (`.form-*`)

### Form Structure:
- `.form-group`: Input grouping (--space-lg margin)
- `.form-label`: Input labels (--text-sm, --font-weight-medium)
- `.form-input`: Text input styling
- `.form-textarea`: Multi-line input (min-height: 120px)
- `.form-select`: Dropdown styling with custom arrow

### Form States:
- **Default**: `--border-medium` border
- **Focus**: `--purple-primary` border, purple glow shadow
- **Placeholder**: `--text-muted` color

---

## 10. UTILITY COMPONENTS

### Loading States:
- `.loading-skeleton`: Animated placeholder content
- `.skeleton-header`, `.skeleton-content`, `.skeleton-footer`: Layout areas
- `.skeleton-avatar`, `.skeleton-text`, `.skeleton-button`: Element types
- `.spinner`: Rotating loading indicator

### State Components:
- `.error-state`: Error message display
- `.empty-state`: No content message
- `.error-icon`, `.empty-icon`: State-specific iconography

### Theme Components:
- `.theme-toggle`: Theme switcher button
- `.theme-icon-light`, `.theme-icon-dark`: Theme-specific icons

---

## 11. DESIGN TOKEN USAGE PATTERNS

### Color System:
- **Purple Accent**: Primary brand color for CTAs, active states
- **Monochromatic Base**: Grays for text hierarchy and backgrounds
- **Agent Colors**: Distinct colors for each AI agent
- **Status Colors**: Semantic colors for success/warning/error states

### Spacing System:
- **Consistent Scale**: Powers of 2 spacing (4px, 8px, 16px, 24px, 32px, 48px, 64px)
- **Component Padding**: Typically `--space-lg` (24px) for cards
- **Element Gaps**: `--space-md` (16px) for related elements

### Typography System:
- **Hierarchy**: Clear size progression from xs (12px) to 4xl (36px)
- **Weight Levels**: 400 (normal) to 800 (extrabold)
- **Line Height**: 1.6 for body text, tighter for headings

### Shadow System:
- **Elevation Levels**: sm/md/lg/xl for different component hierarchies
- **Interactive States**: Enhanced shadows on hover
- **Glass Morphism**: Backdrop blur effects for modern appearance

---

## 12. COMPONENT STATE PATTERNS

### Interactive States (Common across components):
1. **Default**: Base appearance
2. **Hover**: Subtle elevation, color shift, transform
3. **Active/Focus**: Enhanced colors, shadows, borders
4. **Disabled**: Reduced opacity (0.5), no pointer events
5. **Loading**: Skeleton or spinner states

### Animation Patterns:
- **Transitions**: 150ms fast, 300ms normal, 500ms slow
- **Hover Effects**: `translateY(-1px)` for cards, `translateY(-0.5px)` for buttons
- **Loading**: Pulsing animations at 2s intervals
- **Progress**: Smooth width transitions for progress bars

---

## 13. RESPONSIVE PATTERNS

### Breakpoints:
- **Mobile**: max-width: 768px
- **Tablet**: max-width: 1024px
- **Desktop**: max-width: 1200px

### Responsive Behaviors:
- **Grid Collapse**: Multi-column grids become single column
- **Navigation**: Sidebar collapses to icon-only mode
- **Card Stacking**: Horizontal layouts become vertical
- **Text Scaling**: Font sizes reduce on smaller screens

---

## 14. ACCESSIBILITY CONSIDERATIONS

### Focus Management:
- Clear focus indicators with purple outline
- Keyboard navigation support
- Skip links for screen readers

### Color Contrast:
- High contrast ratios maintained in both light/dark themes
- Status colors meet WCAG guidelines
- Text legibility prioritized

### Semantic Markup:
- Proper heading hierarchy
- ARIA labels for interactive elements
- Role attributes for custom components

---

## 15. DARK/LIGHT THEME SUPPORT

### Theme Variables:
- Automatic switching between light and dark token sets
- Consistent component appearance across themes
- Enhanced shadows and borders in dark mode

### Theme-Specific Adjustments:
- Glass morphism effects enhanced in dark mode
- Background gradients adapted for each theme
- Border opacity adjusted for visibility

---

## 16. IMPLEMENTATION NOTES

### CSS Architecture:
- BEM-style naming for component variants
- Utility classes for common patterns
- CSS custom properties for theming
- Logical component grouping in stylesheets

### JavaScript Integration:
- Component templates for dynamic content
- State management for interactive elements
- Animation coordination for complex interactions

### Performance Considerations:
- Efficient CSS selectors
- Minimal reflows through transform-based animations
- Optimal asset loading strategies

---

*This worksheet serves as a living document for the YouTube Research v2 design system. Components should be updated here as they evolve or new ones are added.*
