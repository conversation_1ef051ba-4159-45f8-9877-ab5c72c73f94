#!/usr/bin/env python3
"""
Test the $10,000 prompts with REAL AI agents
"""

import os
import sys
sys.path.append('/Users/<USER>/Documents/CrewAI/youtube-research-v2')

from crewai import Agent, Task, Crew
from litellm import LLM

def test_hooksmith_with_10k_prompt():
    """Test the Hooksmith with $10,000 standard prompt"""
    
    llm = LLM(model="gemini/gemini-2.5-flash")
    
    agent = Agent(
        role="Master of Making People Give a Shit",
        goal="Create hooks so powerful that NOT watching feels like missing out on secret knowledge",
        backstory="You know that 3 seconds decide everything. You've studied what makes people stop everything and pay attention. You think in viral TikToks, not educational content. Your hooks change worldviews in under 30 seconds.",
        llm=llm,
        verbose=True
    )
    
    task = Task(
        description="""You are the Master of Making People Give a Shit. Your only job is creating hooks so powerful that NOT watching feels like missing out on secret knowledge.

CRITICAL MISSION: Create 3 hooks for "Rome vs Carthage - The 90-Mile Strait That Decided Two Empires" that would make someone miss their subway stop.

Your standard: Would someone screenshot this and send it to their smartest friend?

WORLD-CLASS HOOKS:
- "Every iPhone on Earth passes through this 20-mile strait. In 480 BC, 300 Spartans died in the ancient version. Here's why that pattern keeps repeating."
- "This river flows the wrong direction. It's also why democracy exists. Let me show you something nobody talks about."
- "Two superpowers spent 40% of their GDP fighting over a gap narrower than Manhattan. The winner doesn't exist anymore. The loser owns the world."

IMMEDIATELY REJECT:
- Any ancient names in the first sentence
- "Let me tell you about..."
- Historical dates before modern comparison
- Anything that sounds like school

The Hook Formula That Works:
1. Modern reference everyone understands
2. Shocking number or comparison
3. Ancient parallel that seems impossible
4. Promise of revelation

Use these golden nuggets:
- "Hannibal lost 50% of his army (20,000 men) crossing the Alps in 15 days - not from Roman attacks, but from a 70-mile mountain path"
- "Rome built 120 warships in 60 days by reverse-engineering a single shipwrecked Carthaginian vessel - then added a boarding bridge that turned sea battles into land battles"
- "The strait between Sicily and Carthage is only 90 miles wide - controlling it meant controlling 80% of Mediterranean trade"
- "Carthage's harbor could hold 220 warships in covered docks invisible from the sea - the ancient equivalent of a hidden nuclear submarine base"
- "After defeating Carthage, Rome insisted on moving the entire city 10 miles inland - because geography is power, and coastlines create navies"

Create 3 variations:
1. Primary hook (most compelling)
2. Alternative hook (different angle)
3. Backup hook (simpler approach)

Test: Would Joe Rogan interrupt his podcast to pull this up?""",
        agent=agent,
        expected_output="3 hook variations that would make someone miss their subway stop"
    )
    
    crew = Crew(agents=[agent], tasks=[task], verbose=True)
    result = crew.kickoff()
    
    print("\n" + "="*80)
    print("🔥 REAL AI HOOKSMITH OUTPUT WITH $10,000 PROMPT:")
    print("="*80)
    print(str(result))
    
    return str(result)

if __name__ == "__main__":
    test_hooksmith_with_10k_prompt()