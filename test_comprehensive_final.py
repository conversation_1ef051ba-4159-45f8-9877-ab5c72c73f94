#!/usr/bin/env python3
"""
Comprehensive final test to verify all functionality
"""

import requests
import json
import time

def test_comprehensive():
    """Comprehensive test of all AI analysis functionality"""
    print("🔍 COMPREHENSIVE FINAL VERIFICATION")
    print("=" * 50)
    
    test_queries = [
        {"query": "react tutorial", "expected_topic": "React Development"},
        {"query": "python machine learning", "expected_topic": "ML/AI"},
    ]
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n📝 Test {i}: {test_case['query']}")
        print("-" * 30)
        
        start_time = time.time()
        
        try:
            response = requests.post(
                "http://localhost:8003/api/market-research",
                json={
                    "query": test_case["query"],
                    "time_range": "week", 
                    "max_results": 1,
                    "sort_by": "relevance"
                },
                timeout=60
            )
            
            duration = time.time() - start_time
            
            if response.status_code == 200:
                data = response.json()
                ai_insights = data.get('ai_insights', {})
                
                print(f"⏱️  Completed in {duration:.1f}s")
                print("🧠 AI Insights Analysis:")
                
                real_insights = 0
                total_insights = 6
                
                for insight_type in ['market_summary', 'success_patterns', 'content_strategy', 
                                   'growth_opportunities', 'market_risks', 'recommendations']:
                    content = ai_insights.get(insight_type, 'MISSING')
                    
                    if isinstance(content, str):
                        length = len(content)
                        if length > 1000:
                            status = "✅ REAL AI"
                            real_insights += 1
                        elif length > 100:
                            status = "⚠️ PARTIAL"
                            real_insights += 0.5
                        else:
                            status = "❌ FALLBACK"
                        print(f"   {insight_type}: {status} ({length} chars)")
                    elif isinstance(content, list):
                        status = "✅ STRUCTURED"
                        real_insights += 1
                        print(f"   {insight_type}: {status} ({len(content)} items)")
                    else:
                        print(f"   {insight_type}: ❌ INVALID ({type(content)})")
                
                # Check subscriber data
                comp_analysis = data.get('competition_analysis', {})
                top_performers = comp_analysis.get('top_performers', [])
                if top_performers:
                    sub_count = top_performers[0].get('subscriber_count', 0)
                    sub_status = "✅ REAL DATA" if sub_count > 0 else "❌ NO DATA"
                    print(f"   📊 Subscriber Data: {sub_status} ({sub_count:,})")
                
                # Calculate success rate
                success_rate = (real_insights / total_insights) * 100
                print(f"\n📈 Success Rate: {success_rate:.1f}% ({real_insights}/{total_insights})")
                
                if success_rate >= 80:
                    print("🎉 EXCELLENT - Enterprise ready!")
                elif success_rate >= 60:
                    print("✅ GOOD - Production ready")
                elif success_rate >= 40:
                    print("⚠️ FAIR - Needs improvement")
                else:
                    print("❌ POOR - Major issues")
                    
            else:
                print(f"❌ API Error: {response.status_code}")
                print(f"Response: {response.text[:200]}")
                
        except Exception as e:
            print(f"❌ Test Failed: {e}")
    
    print(f"\n{'='*50}")
    print("🏁 COMPREHENSIVE VERIFICATION COMPLETE")

if __name__ == "__main__":
    test_comprehensive()
