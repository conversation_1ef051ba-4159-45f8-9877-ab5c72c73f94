{"color": [{"name": "--bg-primary", "value": "#FFFFFF"}, {"name": "--bg-secondary", "value": "#F8F9FA"}, {"name": "--bg-tertiary", "value": "#F1F3F4"}, {"name": "--bg-elevated", "value": "#FFFFFF"}, {"name": "--bg-glass", "value": "rgba(255, 255, 255, 0.95)"}, {"name": "--bg-overlay", "value": "rgba(0, 0, 0, 0.05)"}, {"name": "--bg-muted", "value": "#F1F3F4"}, {"name": "--text-primary", "value": "#1A1A1A"}, {"name": "--text-secondary", "value": "#4A4A4A"}, {"name": "--text-tertiary", "value": "#6B7280"}, {"name": "--text-muted", "value": "#9CA3AF"}, {"name": "--border-light", "value": "#E5E7EB"}, {"name": "--border-medium", "value": "#D1D5DB"}, {"name": "--border-strong", "value": "#9CA3AF"}, {"name": "--purple-primary", "value": "#8B5CF6"}, {"name": "--purple-secondary", "value": "#A78BFA"}, {"name": "--purple-tertiary", "value": "#C4B5FD"}, {"name": "--purple-light", "value": "#EDE9FE"}, {"name": "--purple-gradient", "value": "linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%)"}, {"name": "--agent-1", "value": "#8B5CF6"}, {"name": "--agent-2", "value": "#3B82F6"}, {"name": "--agent-3", "value": "#10B981"}, {"name": "--agent-4", "value": "#F59E0B"}, {"name": "--agent-5", "value": "#EF4444"}, {"name": "--agent-6", "value": "#6366F1"}, {"name": "--success", "value": "#10B981"}, {"name": "--success-light", "value": "#D1FAE5"}, {"name": "--warning", "value": "#F59E0B"}, {"name": "--warning-light", "value": "#FEF3C7"}, {"name": "--error", "value": "#EF4444"}, {"name": "--error-light", "value": "#FEE2E2"}, {"name": "--info", "value": "#3B82F6"}, {"name": "--info-light", "value": "#DBEAFE"}, {"name": "--font-primary", "value": "'Inter', -apple-system, BlinkMacSystemFont, sans-serif"}, {"name": "--text-xs", "value": "0.75rem"}, {"name": "--text-sm", "value": "0.875rem"}, {"name": "--text-base", "value": "1rem"}, {"name": "--text-lg", "value": "1.125rem"}, {"name": "--text-xl", "value": "1.25rem"}, {"name": "--text-2xl", "value": "1.5rem"}, {"name": "--text-3xl", "value": "1.875rem"}, {"name": "--text-4xl", "value": "2.25rem"}, {"name": "--shadow-sm", "value": "0 2px 4px 0 rgba(0, 0, 0, 0.15)"}, {"name": "--shadow-md", "value": "0 8px 16px -4px rgba(0, 0, 0, 0.25), 0 4px 8px -2px rgba(0, 0, 0, 0.15)"}, {"name": "--shadow-lg", "value": "0 16px 32px -8px rgba(0, 0, 0, 0.35), 0 8px 16px -4px rgba(0, 0, 0, 0.25)"}, {"name": "--shadow-xl", "value": "0 32px 64px -16px rgba(0, 0, 0, 0.45), 0 16px 32px -8px rgba(0, 0, 0, 0.35)"}, {"name": "--shadow-purple", "value": "0 16px 32px -8px rgba(139, 92, 246, 0.3), 0 8px 16px -4px rgba(139, 92, 246, 0.2)"}, {"name": "--shadow-glass", "value": "0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)"}, {"name": "--bg-primary", "value": "linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%)"}, {"name": "--bg-secondary", "value": "linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e1b4b 100%)"}, {"name": "--bg-tertiary", "value": "#1e293b"}, {"name": "--bg-elevated", "value": "rgba(30, 41, 59, 0.95)"}, {"name": "--bg-glass", "value": "rgba(255, 255, 255, 0.08)"}, {"name": "--bg-overlay", "value": "rgba(255, 255, 255, 0.05)"}, {"name": "--surface-primary", "value": "rgba(30, 41, 59, 0.8)"}, {"name": "--surface-secondary", "value": "rgba(51, 65, 85, 0.6)"}, {"name": "--surface-glass", "value": "rgba(255, 255, 255, 0.08)"}, {"name": "--surface-card", "value": "rgba(30, 41, 59, 0.8)"}, {"name": "--text-primary", "value": "#FAFAFA"}, {"name": "--text-secondary", "value": "#E2E8F0"}, {"name": "--text-tertiary", "value": "#94A3B8"}, {"name": "--text-muted", "value": "#64748B"}, {"name": "--border-light", "value": "rgba(255, 255, 255, 0.1)"}, {"name": "--border-medium", "value": "rgba(255, 255, 255, 0.15)"}, {"name": "--border-strong", "value": "rgba(255, 255, 255, 0.2)"}, {"name": "--success-light", "value": "rgba(34, 197, 94, 0.15)"}, {"name": "--warning-light", "value": "rgba(251, 191, 36, 0.15)"}, {"name": "--error-light", "value": "rgba(239, 68, 68, 0.15)"}, {"name": "--info-light", "value": "rgba(59, 130, 246, 0.15)"}, {"name": "--primary-gradient", "value": "linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%)"}, {"name": "--purple-gradient", "value": "linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%)"}, {"name": "--font-primary);\n  font-size", "value": "var(--text-base)"}, {"name": "--text-primary);\n  background", "value": "var(--bg-primary)"}, {"name": "--bg-overlay);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--purple-gradient);\n  border-radius", "value": "50%"}, {"name": "--text-secondary);\n}\n\n[data-theme=\"dark\"] .theme-toggle", "value": "hover {\n  background: var(--purple-primary)"}, {"name": "--radius-xl);\n  box-shadow", "value": "0 8px 32px rgba(0, 0, 0, 0.3),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1)"}, {"name": "--bg-primary);\n}\n\n.app-sidebar {\n  width", "value": "var(--sidebar-width)"}, {"name": "--bg-elevated);\n  border-right", "value": "1px solid var(--border-light)"}, {"name": "--transition-normal);\n  box-shadow", "value": "var(--shadow-lg)"}, {"name": "--border-light);\n  display", "value": "flex"}, {"name": "--border-light);\n  display", "value": "flex"}, {"name": "--space-md);\n  text-decoration", "value": "none"}, {"name": "--text-primary);\n  font-weight", "value": "var(--font-weight-bold)"}, {"name": "--text-lg);\n}\n\n.sidebar-logo-icon {\n  width", "value": "40px"}, {"name": "--purple-gradient);\n  border-radius", "value": "var(--radius-lg)"}, {"name": "--bg-overlay);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--purple-light);\n  color", "value": "var(--purple-primary)"}, {"name": "--space-md);\n  border-bottom", "value": "1px solid var(--border-light)"}, {"name": "--bg-elevated);\n}\n\n.user-profile-card {\n  display", "value": "flex"}, {"name": "--bg-overlay);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--purple-primary);\n  border-radius", "value": "var(--radius-full)"}, {"name": "--font-weight-semibold);\n}\n\n.user-info {\n  flex", "value": "1"}, {"name": "--text-sm);\n  white-space", "value": "nowrap"}, {"name": "--text-xs);\n  color", "value": "var(--text-secondary)"}, {"name": "--bg-overlay);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--text-secondary);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-xs);\n  color", "value": "var(--text-primary)"}, {"name": "--bg-muted);\n  border-radius", "value": "var(--radius-sm)"}, {"name": "--purple-gradient);\n  border-radius", "value": "var(--radius-sm)"}, {"name": "--text-xs);\n  padding", "value": "var(--space-xs) var(--space-sm)"}, {"name": "--radius-md);\n  text-decoration", "value": "none"}, {"name": "--text-secondary);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-sm);\n  transition", "value": "all var(--transition-fast)"}, {"name": "--text-primary);\n  transform", "value": "translateX(2px)"}, {"name": "--purple-gradient);\n  color", "value": "white"}, {"name": "--font-weight-semibold);\n  box-shadow", "value": "var(--shadow-sm)"}, {"name": "--purple-primary);\n  border-radius", "value": "0 2px 2px 0"}, {"name": "--bg-muted);\n  color", "value": "var(--text-secondary)"}, {"name": "--purple-primary);\n  color", "value": "white"}, {"name": "--bg-muted);\n  transition", "value": "var(--transition-fast)"}, {"name": "--purple-primary); }\n\n.nav-item", "value": "hover .agent-indicator {\n  transform: scale(1.2)"}, {"name": "--space-md);\n  border-top", "value": "1px solid var(--border-light)"}, {"name": "--bg-elevated);\n}\n\n.sidebar-user {\n  display", "value": "flex"}, {"name": "--radius-md);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--purple-gradient);\n  border-radius", "value": "50%"}, {"name": "--text-sm);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-primary);\n  white-space", "value": "nowrap"}, {"name": "--text-xs);\n  color", "value": "var(--purple-primary)"}, {"name": "--text-xs);\n  color", "value": "var(--text-secondary)"}, {"name": "--purple-primary);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--bg-overlay);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--text-secondary);\n  font-size", "value": "var(--text-xs)"}, {"name": "--purple-primary); }\n  100% { border-right-color", "value": "#ef4444"}, {"name": "--purple-primary);\n  color", "value": "white"}, {"name": "--text-xs);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--space-xs) var(--space-sm);\n  border-radius", "value": "var(--radius-full)"}, {"name": "--surface-card);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--radius-xl);\n  box-shadow", "value": "var(--shadow-glass)"}, {"name": "--shadow-md);\n  border-color", "value": "var(--border-medium)"}, {"name": "--primary-gradient);\n  border", "value": "1px solid var(--purple-tertiary)"}, {"name": "--shadow-purple);\n  backdrop-filter", "value": "blur(12px)"}, {"name": "--space-xl);\n  border-bottom", "value": "1px solid var(--border-light)"}, {"name": "--text-2xl);\n  font-weight", "value": "var(--font-weight-bold)"}, {"name": "--text-primary);\n  margin", "value": "0"}, {"name": "--text-base);\n  color", "value": "var(--text-secondary)"}, {"name": "--space-xl);\n  border-top", "value": "1px solid var(--border-light)"}, {"name": "--bg-overlay);\n}\n\n/* ====== BUTTON COMPONENTS ====== */\n.btn {\n  display", "value": "inline-flex"}, {"name": "--text-sm);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--purple-gradient);\n  color", "value": "white"}, {"name": "--shadow-md);\n}\n\n.btn-primary", "value": "hover:not(:disabled) {\n  transform: translateY(-0.5px)"}, {"name": "--shadow-md);\n  filter", "value": "brightness(1.05)"}, {"name": "--bg-elevated);\n  color", "value": "var(--text-primary)"}, {"name": "--border-medium);\n}\n\n.btn-secondary", "value": "hover:not(:disabled) {\n  background: var(--bg-overlay)"}, {"name": "--purple-primary);\n}\n\n.btn-ghost {\n  background", "value": "transparent"}, {"name": "--text-secondary);\n}\n\n.btn-ghost", "value": "hover:not(:disabled) {\n  background: var(--bg-overlay)"}, {"name": "--text-primary);\n}\n\n.btn-xs {\n  padding", "value": "2px var(--space-xs)"}, {"name": "--text-base);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--transition-fast);\n  box-shadow", "value": "var(--shadow-sm)"}, {"name": "--shadow-md);\n  transform", "value": "translateY(-1px)"}, {"name": "--border-light);\n  background", "value": "var(--bg-elevated)"}, {"name": "--purple-gradient);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--text-lg);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-primary);\n}\n\n.analysis-card-subtitle {\n  margin", "value": "0"}, {"name": "--text-sm);\n  color", "value": "var(--text-secondary)"}, {"name": "--border-light);\n}\n\n.analysis-card-meta {\n  display", "value": "flex"}, {"name": "--text-secondary);\n}\n\n.analysis-timestamp {\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--purple-primary);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--space-xs);\n}\n\n/* Agent Status Cards */\n.agent-status-card {\n  display", "value": "flex"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--bg-overlay);\n  border-color", "value": "var(--purple-primary)"}, {"name": "--font-weight-semibold);\n}\n\n.agent-performance { background", "value": "#ef4444"}, {"name": "--purple-primary); }\n\n.agent-icon {\n  width", "value": "20px"}, {"name": "--radius-full);\n  border", "value": "2px solid var(--bg-elevated)"}, {"name": "--text-tertiary); }\n.status-starting { background", "value": "#f59e0b"}, {"name": "--purple-primary); }\n.status-complete { background", "value": "#10b981"}, {"name": "--text-secondary);\n  line-height", "value": "1.4"}, {"name": "--bg-muted);\n  border-radius", "value": "var(--radius-sm)"}, {"name": "--purple-gradient);\n  border-radius", "value": "var(--radius-sm)"}, {"name": "--transition-normal);\n}\n\n.progress-text {\n  font-size", "value": "var(--text-xs)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--shadow-md);\n}\n\n.insight-header {\n  display", "value": "flex"}, {"name": "--text-xs);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--purple-primary);\n  text-transform", "value": "uppercase"}, {"name": "--text-secondary);\n  line-height", "value": "1.5"}, {"name": "--text-secondary);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-lg);\n  font-weight", "value": "var(--font-weight-bold)"}, {"name": "--text-primary);\n}\n\n.insight-actions {\n  display", "value": "flex"}, {"name": "--border-light);\n  border-radius", "value": "var(--radius-lg)"}, {"name": "--shadow-glass);\n  transition", "value": "var(--transition-fast)"}, {"name": "--shadow-md);\n  transform", "value": "translateY(-1px)"}, {"name": "--text-2xl);\n  font-weight", "value": "var(--font-weight-bold)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-xs)"}, {"name": "--text-sm);\n  color", "value": "var(--text-secondary)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--shadow-sm);\n  transform", "value": "translateY(-1px)"}, {"name": "--bg-overlay);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--text-secondary);\n}\n\n.metric-icon-svg {\n  width", "value": "20px"}, {"name": "--text-tertiary); }\n\n.trend-icon {\n  width", "value": "16px"}, {"name": "--text-3xl);\n  font-weight", "value": "var(--font-weight-bold)"}, {"name": "--text-primary);\n  line-height", "value": "1"}, {"name": "--text-secondary);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-xs);\n  color", "value": "var(--text-secondary)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--text-lg);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-primary);\n}\n\n.progress-percentage {\n  font-size", "value": "var(--text-lg)"}, {"name": "--text-xs);\n  color", "value": "var(--text-secondary)"}, {"name": "--font-weight-medium);\n  text-align", "value": "center"}, {"name": "--text-primary);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-sm);\n  color", "value": "var(--text-secondary)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--border-light);\n}\n\n.feed-title {\n  margin", "value": "0"}, {"name": "--text-base);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-primary);\n  display", "value": "flex"}, {"name": "--bg-overlay);\n}\n\n.status-item-avatar {\n  width", "value": "32px"}, {"name": "--space-xs);\n}\n\n.status-agent-name {\n  font-size", "value": "var(--text-sm)"}, {"name": "--text-xs);\n  color", "value": "var(--text-secondary)"}, {"name": "--text-sm);\n  color", "value": "var(--text-secondary)"}, {"name": "--border-light);\n}\n\n.connection-status {\n  display", "value": "flex"}, {"name": "--text-secondary);\n}\n\n.status-dot {\n  width", "value": "8px"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--bg-muted);\n  border-radius", "value": "var(--radius-full)"}, {"name": "--bg-muted);\n  border-radius", "value": "var(--radius-sm)"}, {"name": "--bg-muted);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--border-light);\n  border-radius", "value": "var(--radius-lg)"}, {"name": "--space-lg);\n}\n\n.error-icon-svg {\n  width", "value": "32px"}, {"name": "--text-secondary);\n  max-width", "value": "400px"}, {"name": "--border-light);\n  border-radius", "value": "var(--radius-lg)"}, {"name": "--bg-overlay);\n  border-radius", "value": "var(--radius-full)"}, {"name": "--text-secondary);\n}\n\n.empty-title {\n  margin", "value": "0 0 var(--space-sm) 0"}, {"name": "--text-xl);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-primary);\n}\n\n.empty-message {\n  margin", "value": "0 0 var(--space-lg) 0"}, {"name": "--text-base);\n  color", "value": "var(--text-secondary)"}, {"name": "--transition-fast);\n}\n\n.agent-status-item", "value": "hover {\n  background: var(--bg-overlay)"}, {"name": "--purple-light);\n  border", "value": "1px solid var(--purple-tertiary)"}, {"name": "--agent-1); }\n.agent-avatar.agent-2 { background", "value": "var(--agent-2)"}, {"name": "--agent-3); }\n.agent-avatar.agent-4 { background", "value": "var(--agent-4)"}, {"name": "--agent-5); }\n.agent-avatar.agent-6 { background", "value": "var(--agent-6)"}, {"name": "--text-sm);\n  margin-bottom", "value": "var(--space-xs)"}, {"name": "--text-secondary);\n  font-size", "value": "var(--text-sm)"}, {"name": "--text-muted);\n  font-size", "value": "var(--text-xs)"}, {"name": "--space-xs);\n}\n\n.agent-progress {\n  width", "value": "100%"}, {"name": "--bg-overlay);\n  border-radius", "value": "var(--radius-full)"}, {"name": "--space-sm);\n}\n\n.agent-progress-fill {\n  height", "value": "100%"}, {"name": "--purple-gradient);\n  border-radius", "value": "var(--radius-full)"}, {"name": "--text-sm);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-sm)"}, {"name": "--space-md);\n  border", "value": "1px solid var(--border-medium)"}, {"name": "--text-base);\n  color", "value": "var(--text-primary)"}, {"name": "--bg-elevated);\n  transition", "value": "all var(--transition-fast)"}, {"name": "--purple-primary);\n  box-shadow", "value": "0 0 0 3px rgba(139, 92, 246, 0.1)"}, {"name": "--text-muted);\n}\n\n.form-textarea {\n  resize", "value": "vertical"}, {"name": "--text-xl);\n}\n\n.analysis-title {\n  font-size", "value": "var(--text-3xl)"}, {"name": "--text-lg);\n  color", "value": "var(--text-secondary)"}, {"name": "--text-3xl);\n  font-weight", "value": "var(--font-weight-bold)"}, {"name": "--text-primary);\n  margin", "value": "var(--space-sm) 0"}, {"name": "--text-sm);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--error);\n}\n\n.metric-change.neutral {\n  color", "value": "var(--text-muted)"}, {"name": "--text-xs); }\n.text-sm { font-size", "value": "var(--text-sm)"}, {"name": "--text-base); }\n.text-lg { font-size", "value": "var(--text-lg)"}, {"name": "--text-xl); }\n.text-2xl { font-size", "value": "var(--text-2xl)"}, {"name": "--text-3xl); }\n.text-4xl { font-size", "value": "var(--text-4xl)"}, {"name": "--font-weight-extrabold); }\n\n.text-primary { color", "value": "var(--text-primary)"}, {"name": "--text-secondary); }\n.text-tertiary { color", "value": "var(--text-tertiary)"}, {"name": "--text-muted); }\n.text-purple { color", "value": "var(--purple-primary)"}, {"name": "--success); }\n.text-warning { color", "value": "var(--warning)"}, {"name": "--error); }\n\n.bg-primary { background-color", "value": "var(--bg-primary)"}, {"name": "--bg-secondary); }\n.bg-elevated { background-color", "value": "var(--bg-elevated)"}, {"name": "--purple-gradient); }\n\n.rounded-sm { border-radius", "value": "var(--radius-sm)"}, {"name": "--radius-md); }\n.rounded-lg { border-radius", "value": "var(--radius-lg)"}, {"name": "--radius-xl); }\n.rounded-2xl { border-radius", "value": "var(--radius-2xl)"}, {"name": "--radius-full); }\n\n.shadow-sm { box-shadow", "value": "var(--shadow-sm)"}, {"name": "--shadow-md); }\n.shadow-lg { box-shadow", "value": "var(--shadow-lg)"}, {"name": "--shadow-xl); }\n.shadow-purple { box-shadow", "value": "var(--shadow-purple)"}, {"name": "--space-lg);\n}\n\n/* Status and loading components */\n.agent-status-badge {\n  display", "value": "flex"}, {"name": "--bg-glass);\n  border", "value": "1px solid var(--border-medium)"}, {"name": "--text-sm);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-muted);\n}\n\n.status-dot.active {\n  background", "value": "var(--success)"}, {"name": "--warning);\n  animation", "value": "pulse 1s infinite"}, {"name": "--error);\n}\n\n.btn-spinner {\n  display", "value": "none"}, {"name": "--border-light);\n  border-radius", "value": "var(--radius-lg)"}, {"name": "--space-xl);\n  box-shadow", "value": "var(--shadow-sm)"}, {"name": "--text-sm);\n  color", "value": "var(--text-tertiary)"}, {"name": "--text-base);\n  color", "value": "var(--text-tertiary)"}, {"name": "--purple-secondary);\n}\n\n/* Common responsive patterns */\n@media (max-width", "value": "1200px) {\n  .analyzer-form.grid-layout {\n    grid-template-columns: 1fr"}, {"name": "--text-3xl);\n  font-weight", "value": "var(--font-weight-bold)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-sm)"}, {"name": "--text-lg);\n  color", "value": "var(--text-tertiary)"}, {"name": "--purple-gradient);\n  border-radius", "value": "var(--radius-xl)"}, {"name": "--text-lg);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-sm)"}, {"name": "--text-tertiary);\n  line-height", "value": "var(--line-height-relaxed)"}, {"name": "--bg-glass);\n  color", "value": "var(--text-secondary)"}, {"name": "--space-xs) var(--space-sm);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--text-xs);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--border-medium);\n}\n\n.tool-footer {\n  display", "value": "flex"}, {"name": "--space-lg);\n  border-top", "value": "1px solid var(--border-medium)"}, {"name": "--text-sm);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--space-xs) var(--space-sm);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--color-success);\n  color", "value": "white"}, {"name": "--bg-glass);\n  color", "value": "var(--text-tertiary)"}, {"name": "--border-medium);\n}\n\n.recent-section {\n  margin-bottom", "value": "var(--space-2xl)"}, {"name": "--bg-quaternary);\n  display", "value": "flex"}, {"name": "--text-muted);\n}\n\n.recent-overlay {\n  position", "value": "absolute"}, {"name": "--purple-gradient);\n  color", "value": "white"}, {"name": "--text-base);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-xs)"}, {"name": "--text-sm);\n  color", "value": "var(--text-tertiary)"}, {"name": "--bg-glass);\n  color", "value": "var(--text-secondary)"}, {"name": "--font-weight-medium);\n  border", "value": "1px solid var(--border-medium)"}, {"name": "--text-xl);\n  font-weight", "value": "var(--font-weight-bold)"}, {"name": "--purple-primary);\n}\n\n.usage-progress {\n  margin-bottom", "value": "var(--space-lg)"}, {"name": "--bg-tertiary);\n  border-radius", "value": "var(--radius-full)"}, {"name": "--purple-gradient);\n  transition", "value": "width var(--transition-normal)"}, {"name": "--text-sm);\n  color", "value": "var(--text-tertiary)"}, {"name": "--color-success);\n}\n\n.stats-title {\n  font-size", "value": "var(--text-lg)"}, {"name": "--space-lg);\n}\n\n.stat-item {\n  text-align", "value": "center"}, {"name": "--text-2xl);\n  font-weight", "value": "var(--font-weight-bold)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-xs)"}, {"name": "--text-sm);\n  color", "value": "var(--text-tertiary)"}, {"name": "--text-secondary);\n  font-size", "value": "var(--text-sm)"}, {"name": "--space-2xl);\n}\n\n.agent-badge-large {\n  display", "value": "flex"}, {"name": "--bg-glass);\n  border", "value": "1px solid var(--border-medium)"}, {"name": "--radius-xl);\n}\n\n.agent-icon-large {\n  width", "value": "32px"}, {"name": "--purple-primary);\n}\n\n.agent-label {\n  font-size", "value": "var(--text-sm)"}, {"name": "--bg-glass);\n  border", "value": "1px solid var(--border-medium)"}, {"name": "--bg-glass-hover);\n  transform", "value": "translateY(-1px)"}, {"name": "--purple-primary);\n  flex-shrink", "value": "0"}, {"name": "--text-base);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-xs)"}, {"name": "--text-sm);\n  color", "value": "var(--text-tertiary)"}, {"name": "--purple-primary);\n}\n\n.placeholder-text {\n  color", "value": "var(--text-tertiary)"}, {"name": "--text-base);\n}\n\n.analysis-text {\n  line-height", "value": "var(--line-height-relaxed)"}, {"name": "--text-secondary);\n}\n\n.analysis-text h2 {\n  color", "value": "var(--text-primary)"}, {"name": "--text-xl);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--space-xl) 0 var(--space-md);\n  border-bottom", "value": "1px solid var(--border-medium)"}, {"name": "--space-sm);\n}\n\n.analysis-text h3 {\n  color", "value": "var(--text-primary)"}, {"name": "--text-lg);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--space-lg) 0 var(--space-sm);\n}\n\n.analysis-text p {\n  margin-bottom", "value": "var(--space-md)"}, {"name": "--space-sm);\n}\n\n.analysis-text strong {\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-semibold);\n}\n\n.no-analysis, .error-state {\n  display", "value": "flex"}, {"name": "--text-quaternary);\n}\n\n.no-analysis h3, .error-state h3 {\n  color", "value": "var(--text-secondary)"}, {"name": "--text-lg);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-quaternary);\n}\n\n/* Video overview and analysis components */\n.video-overview {\n  margin-bottom", "value": "var(--space-2xl)"}, {"name": "--text-xl);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-sm)"}, {"name": "--text-sm);\n}\n\n.stat-item i {\n  width", "value": "14px"}, {"name": "--text-sm);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--border-medium);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--text-tertiary);\n  font-size", "value": "var(--text-xs)"}, {"name": "--success);\n  border", "value": "1px solid rgba(34, 197, 94, 0.3)"}, {"name": "--success);\n  border", "value": "1px solid rgba(34, 197, 94, 0.3)"}, {"name": "--error);\n  border", "value": "1px solid rgba(239, 68, 68, 0.3)"}, {"name": "--border-light);\n  border-radius", "value": "var(--radius-xl)"}, {"name": "--space-xl);\n  box-shadow", "value": "var(--shadow-md)"}, {"name": "--shadow-lg);\n  transform", "value": "translateY(-2px)"}, {"name": "--text-lg);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-xs)"}, {"name": "--text-sm);\n  color", "value": "var(--text-tertiary)"}, {"name": "--purple-primary);\n  flex-shrink", "value": "0"}, {"name": "--text-tertiary);\n  line-height", "value": "var(--line-height-relaxed)"}, {"name": "--purple-gradient);\n  border-radius", "value": "var(--radius-xl)"}, {"name": "--text-lg);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-sm)"}, {"name": "--text-tertiary);\n  line-height", "value": "var(--line-height-relaxed)"}, {"name": "--bg-glass);\n  color", "value": "var(--text-secondary)"}, {"name": "--space-xs) var(--space-sm);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--text-xs);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--border-medium);\n}\n\n.tool-footer {\n  display", "value": "flex"}, {"name": "--space-lg);\n  border-top", "value": "1px solid var(--border-medium)"}, {"name": "--text-sm);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--space-xs) var(--space-sm);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--success);\n  color", "value": "white"}, {"name": "--bg-glass);\n  color", "value": "var(--text-tertiary)"}, {"name": "--border-medium);\n}\n\n.recent-card {\n  display", "value": "flex"}, {"name": "--bg-tertiary);\n  display", "value": "flex"}, {"name": "--text-muted);\n}\n\n.recent-overlay {\n  position", "value": "absolute"}, {"name": "--purple-gradient);\n  color", "value": "white"}, {"name": "--text-base);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-xs)"}, {"name": "--text-sm);\n  color", "value": "var(--text-tertiary)"}, {"name": "--bg-glass);\n  color", "value": "var(--text-secondary)"}, {"name": "--font-weight-medium);\n  border", "value": "1px solid var(--border-medium)"}, {"name": "--text-xl);\n  font-weight", "value": "var(--font-weight-bold)"}, {"name": "--purple-primary);\n}\n\n.usage-progress {\n  margin-bottom", "value": "var(--space-lg)"}, {"name": "--bg-tertiary);\n  border-radius", "value": "var(--radius-full)"}, {"name": "--purple-gradient);\n  transition", "value": "width var(--transition-normal)"}, {"name": "--text-sm);\n  color", "value": "var(--text-tertiary)"}, {"name": "--success);\n}\n\n.stats-card {\n  padding", "value": "var(--space-xl)"}, {"name": "--text-lg);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-lg)"}, {"name": "--text-2xl);\n  font-weight", "value": "var(--font-weight-bold)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-xs)"}, {"name": "--text-sm);\n  color", "value": "var(--text-tertiary)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--radius-lg);\n  box-shadow", "value": "var(--shadow-lg)"}, {"name": "--border-light);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-primary);\n  font-size", "value": "var(--text-sm)"}, {"name": "--space-xl);\n  text-align", "value": "center"}, {"name": "--text-tertiary);\n  font-size", "value": "var(--text-sm)"}, {"name": "--space-md) var(--space-lg);\n  border-bottom", "value": "1px solid var(--border-light)"}, {"name": "--text-xs);\n  color", "value": "var(--text-tertiary)"}, {"name": "--success);\n}\n\n.export-buttons {\n  display", "value": "flex"}, {"name": "--bg-primary);\n  border-radius", "value": "var(--radius-lg)"}, {"name": "--border-light);\n  box-shadow", "value": "var(--shadow-sm)"}, {"name": "--border-light);\n}\n\n.channel-info h2 {\n  font-size", "value": "var(--text-2xl)"}, {"name": "--space-xs) 0;\n}\n\n.channel-info p {\n  color", "value": "var(--text-secondary)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--purple-gradient);\n  border-radius", "value": "var(--radius-lg)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--text-sm);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-secondary);\n  margin-bottom", "value": "var(--space-xs)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--shadow-md);\n  transform", "value": "translateY(-1px)"}, {"name": "--text-base);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-sm)"}, {"name": "--border-light);\n  border-radius", "value": "var(--radius-lg)"}, {"name": "--text-sm);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--purple-primary);\n  margin-bottom", "value": "var(--space-sm)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--shadow-md);\n  transform", "value": "translateY(-1px)"}, {"name": "--text-sm);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-secondary);\n  margin-bottom", "value": "var(--space-sm)"}, {"name": "--purple-primary);\n}\n\n.loading-section, .error-section {\n  margin-bottom", "value": "var(--space-2xl)"}, {"name": "--space-lg);\n  text-align", "value": "center"}, {"name": "--border-light);\n  border-top-color", "value": "var(--purple-primary)"}, {"name": "--text-tertiary);\n  font-size", "value": "var(--text-lg)"}, {"name": "--error);\n}\n\n/* Tabbed interface components */\n.tab-buttons {\n  display", "value": "flex"}, {"name": "--bg-secondary);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--bg-tertiary);\n  border-color", "value": "var(--border-medium)"}, {"name": "--text-primary);\n}\n\n.tab-button.active {\n  background", "value": "var(--purple-primary)"}, {"name": "--purple-primary);\n  color", "value": "white"}, {"name": "--border-light);\n  border-radius", "value": "var(--radius-lg)"}, {"name": "--bg-tertiary);\n  border-color", "value": "var(--border-medium)"}, {"name": "--purple-primary);\n  border-radius", "value": "var(--radius-lg)"}, {"name": "--text-2xl);\n  font-weight", "value": "700"}, {"name": "--text-primary);\n  line-height", "value": "1"}, {"name": "--text-sm);\n  color", "value": "var(--text-tertiary)"}, {"name": "--space-md);\n}\n\n.recommendation-card {\n  border-left", "value": "4px solid var(--purple-primary)"}, {"name": "--purple-primary);\n  color", "value": "white"}, {"name": "--text-sm);\n  flex-shrink", "value": "0"}, {"name": "--text-base);\n  font-weight", "value": "600"}, {"name": "--text-sm);\n  line-height", "value": "1.6"}, {"name": "--bg-tertiary);\n}\n\n.video-thumbnail img {\n  width", "value": "100%"}, {"name": "--text-sm);\n}\n\n.stat-item {\n  display", "value": "flex"}, {"name": "--space-xs) var(--space-sm);\n  border-radius", "value": "var(--radius-sm)"}, {"name": "--text-xs);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--purple-primary);\n}\n\n.shorts-indicator {\n  position", "value": "absolute"}, {"name": "--purple-primary);\n  color", "value": "white"}, {"name": "--space-xs) var(--space-sm);\n  border-radius", "value": "var(--radius-sm)"}, {"name": "--text-xs);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--bg-secondary);\n  border-radius", "value": "var(--radius-md)"}, {"name": "--purple-gradient);\n  transition", "value": "width var(--transition-normal)"}, {"name": "--bg-elevated);\n  border", "value": "1px solid var(--border-light)"}, {"name": "--radius-lg);\n  text-align", "value": "center"}, {"name": "--border-light);\n  border-radius", "value": "var(--radius-lg)"}, {"name": "--text-lg);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-primary);\n  margin", "value": "0 0 var(--space-md) 0"}, {"name": "--text-sm);\n}\n\n.metric {\n  display", "value": "flex"}, {"name": "--border-light);\n  border-radius", "value": "var(--radius-lg)"}, {"name": "--text-base);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-primary);\n  margin", "value": "0 0 var(--space-md) 0"}, {"name": "--text-secondary);\n}\n\n.psychology-insights {\n  margin-top", "value": "var(--space-lg)"}, {"name": "--border-light);\n  border-radius", "value": "var(--radius-lg)"}, {"name": "--space-xs) var(--space-sm);\n  border-radius", "value": "var(--radius-sm)"}, {"name": "--text-xs);\n  font-weight", "value": "600"}, {"name": "--text-primary);\n  margin-bottom", "value": "var(--space-xs)"}, {"name": "--text-sm);\n  color", "value": "var(--text-tertiary)"}, {"name": "--purple-gradient);\n  border-radius", "value": "var(--radius-2xl)"}, {"name": "--space-lg);\n  box-shadow", "value": "0 8px 24px rgba(139, 92, 246, 0.3),\n    0 16px 48px rgba(139, 92, 246, 0.15)"}, {"name": "--text-3xl);\n  font-weight", "value": "var(--font-weight-bold)"}, {"name": "--text-primary);\n  margin", "value": "0 0 var(--space-sm) 0"}, {"name": "--text-primary) 0%, var(--purple-primary) 100%);\n  background-clip", "value": "text"}, {"name": "--text-lg);\n  color", "value": "var(--text-secondary)"}, {"name": "--text-lg);\n  font-weight", "value": "var(--font-weight-medium)"}, {"name": "--text-primary);\n  transition", "value": "all var(--transition-normal)"}, {"name": "--text-primary);\n  box-shadow", "value": "0 4px 12px rgba(0, 0, 0, 0.2),\n    inset 0 1px 0 rgba(255, 255, 255, 0.05)"}, {"name": "--purple-primary);\n  box-shadow", "value": "0 0 0 4px rgba(139, 92, 246, 0.1),\n    0 8px 24px rgba(139, 92, 246, 0.15),\n    inset 0 1px 0 rgba(255, 255, 255, 0.8)"}, {"name": "--text-muted);\n  font-style", "value": "italic"}, {"name": "--purple-gradient);\n  border", "value": "none"}, {"name": "--text-lg);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--transition-normal);\n  box-shadow", "value": "0 8px 24px rgba(139, 92, 246, 0.3),\n    0 16px 48px rgba(139, 92, 246, 0.15),\n    inset 0 1px 0 rgba(255, 255, 255, 0.2)"}, {"name": "--purple-primary);\n}\n\n.premium-select {\n  background", "value": "rgba(255, 255, 255, 0.9)"}, {"name": "--text-base);\n  color", "value": "var(--text-primary)"}, {"name": "--text-primary);\n}\n\n.premium-select", "value": "focus {\n  outline: none"}, {"name": "--purple-primary);\n  box-shadow", "value": "0 0 0 3px rgba(139, 92, 246, 0.1),\n    0 4px 12px rgba(139, 92, 246, 0.15)"}, {"name": "--purple-primary);\n}\n\n/* Enhanced Results Section */\n.results-section {\n  margin-top", "value": "var(--space-2xl)"}, {"name": "--border-light);\n  position", "value": "relative"}, {"name": "--purple-gradient);\n}\n\n.results-title {\n  font-size", "value": "var(--text-2xl)"}, {"name": "--text-secondary);\n  margin", "value": "0"}, {"name": "--purple-primary);\n  box-shadow", "value": "0 16px 48px rgba(0, 0, 0, 0.1),\n    0 24px 80px rgba(139, 92, 246, 0.15),\n    inset 0 1px 0 rgba(255, 255, 255, 0.9)"}, {"name": "--purple-gradient);\n  border-radius", "value": "var(--radius-2xl)"}, {"name": "--space-lg);\n  box-shadow", "value": "0 8px 24px rgba(139, 92, 246, 0.3),\n    0 16px 48px rgba(139, 92, 246, 0.15),\n    inset 0 2px 4px rgba(255, 255, 255, 0.2)"}, {"name": "--text-4xl);\n  font-weight", "value": "var(--font-weight-extrabold)"}, {"name": "--text-primary);\n  margin", "value": "0 0 var(--space-sm) 0"}, {"name": "--text-primary) 0%, var(--purple-primary) 100%);\n  background-clip", "value": "text"}, {"name": "--text-base);\n  font-weight", "value": "var(--font-weight-semibold)"}, {"name": "--text-secondary);\n  margin", "value": "0"}, {"name": "--text-base);\n  }\n\n  .premium-form-input {\n    padding", "value": "var(--space-md) var(--space-lg)"}, {"name": "--text-base);\n  }\n\n  .premium-analyze-btn {\n    padding", "value": "var(--space-md) var(--space-xl)"}, {"name": "--text-base);\n  }\n\n  .premium-stats-grid {\n    grid-template-columns", "value": "1fr"}], "typography": [{"name": "--font-mono", "value": "'JetBrains Mono', 'Fira Code', monospace"}, {"name": "--font-display", "value": "'Inter', sans-serif"}, {"name": "--font-weight-normal", "value": "400"}, {"name": "--font-weight-medium", "value": "500"}, {"name": "--font-weight-semibold", "value": "600"}, {"name": "--font-weight-bold", "value": "700"}, {"name": "--font-weight-extrabold", "value": "800"}, {"name": "--font-weight-normal);\n  line-height", "value": "1.6"}, {"name": "--font-weight-bold);\n  font-size", "value": "var(--text-lg)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-semibold);\n}\n\n.usage-bar {\n  height", "value": "4px"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-tertiary)"}, {"name": "--font-weight-medium);\n  transition", "value": "var(--transition-fast)"}, {"name": "--font-weight-semibold);\n  font-size", "value": "var(--text-sm)"}, {"name": "--font-weight-medium);\n}\n\n.version-number {\n  font-size", "value": "var(--text-xs)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-secondary)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-medium);\n}\n\n.stat-trend.up {\n  color", "value": "var(--success)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--purple-primary)"}, {"name": "--font-weight-bold);\n  color", "value": "var(--purple-primary)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-bold);\n  font-size", "value": "var(--text-sm)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-bold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-medium);\n  color", "value": "var(--text-secondary)"}, {"name": "--font-mono); }\n.font-display { font-family", "value": "var(--font-display)"}, {"name": "--font-weight-normal); }\n.font-medium { font-weight", "value": "var(--font-weight-medium)"}, {"name": "--font-weight-semibold); }\n.font-bold { font-weight", "value": "var(--font-weight-bold)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-medium);\n  transition", "value": "color var(--transition-fast)"}, {"name": "--font-weight-bold);\n}\n\n.recent-content {\n  flex", "value": "1"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-medium);\n}\n\n.header-left, .header-right {\n  display", "value": "flex"}, {"name": "--font-weight-medium);\n  color", "value": "var(--text-secondary)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-medium);\n}\n\n.performance-note {\n  padding", "value": "var(--space-xs) var(--space-sm)"}, {"name": "--font-weight-medium);\n}\n\n.performance-note.viral-performance {\n  background", "value": "rgba(34, 197, 94, 0.1)"}, {"name": "--font-weight-medium);\n}\n\n.status-indicator.processing {\n  background", "value": "rgba(59, 130, 246, 0.1)"}, {"name": "--font-weight-bold);\n}\n\n.recent-content {\n  flex", "value": "1"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-medium);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-bold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-medium);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-medium);\n  color", "value": "var(--text-secondary)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-semibold);\n  color", "value": "var(--text-primary)"}, {"name": "--font-weight-bold);\n  color", "value": "var(--text-primary)"}], "spacing": [{"name": "--space-xs", "value": "0.25rem"}, {"name": "--space-sm", "value": "0.5rem"}, {"name": "--space-md", "value": "1rem"}, {"name": "--space-lg", "value": "1.5rem"}, {"name": "--space-xl", "value": "2rem"}, {"name": "--space-2xl", "value": "3rem"}, {"name": "--space-3xl", "value": "4rem"}, {"name": "--space-xl);\n  position", "value": "sticky"}, {"name": "--space-xl);\n  background", "value": "var(--bg-secondary)"}, {"name": "--space-2xl) var(--space-xl);\n  background", "value": "var(--bg-secondary)"}, {"name": "--space-sm);\n  padding", "value": "var(--space-sm)"}, {"name": "--space-md);\n}\n\n.user-avatar {\n  width", "value": "40px"}, {"name": "--space-sm);\n}\n\n.usage-header {\n  display", "value": "flex"}, {"name": "--space-xs);\n}\n\n.usage-label {\n  font-size", "value": "var(--text-xs)"}, {"name": "--space-sm);\n}\n\n.usage-fill {\n  height", "value": "100%"}, {"name": "--space-lg);\n  overflow-y", "value": "auto"}, {"name": "--space-xl);\n}\n\n.nav-section-title {\n  font-size", "value": "var(--text-sm)"}, {"name": "--space-md);\n}\n\n.nav-items {\n  list-style", "value": "none"}, {"name": "--space-xs);\n}\n\n.nav-item {\n  display", "value": "flex"}, {"name": "--space-sm);\n  padding", "value": "var(--space-sm) var(--space-md)"}, {"name": "--space-xs);\n}\n\n.nav-item", "value": "hover {\n  background: var(--bg-overlay)"}, {"name": "--space-md);\n  top", "value": "50%"}, {"name": "--space-md);\n  margin-bottom", "value": "var(--space-md)"}, {"name": "--space-sm);\n  background", "value": "var(--bg-secondary)"}, {"name": "--space-xs);\n  min-width", "value": "0"}, {"name": "--font-weight-medium);\n  white-space", "value": "nowrap"}, {"name": "--space-xs);\n  margin-bottom", "value": "var(--space-sm)"}, {"name": "--space-xs);\n  padding", "value": "var(--space-xs)"}, {"name": "--space-xs) 0 0 0;\n}\n\n.card-content {\n  padding", "value": "var(--space-xl)"}, {"name": "--space-sm);\n  padding", "value": "var(--space-sm) var(--space-md)"}, {"name": "--space-xs) var(--space-sm);\n  font-size", "value": "var(--text-xs)"}, {"name": "--space-xs);\n}\n\n.btn-lg {\n  padding", "value": "var(--space-md) var(--space-lg)"}, {"name": "--space-md);\n  padding", "value": "var(--space-lg)"}, {"name": "--space-xs);\n}\n\n.card-action-btn {\n  width", "value": "32px"}, {"name": "--space-lg);\n}\n\n.analysis-card-footer {\n  display", "value": "flex"}, {"name": "--space-md) var(--space-lg);\n  background", "value": "var(--bg-overlay)"}, {"name": "--space-md);\n  font-size", "value": "var(--text-xs)"}, {"name": "--space-md);\n  padding", "value": "var(--space-md)"}, {"name": "--space-xs) 0;\n  font-size", "value": "var(--text-base)"}, {"name": "--space-sm) 0;\n  font-size", "value": "var(--text-sm)"}, {"name": "--space-sm);\n}\n\n.progress-bar {\n  flex", "value": "1"}, {"name": "--space-md);\n}\n\n.insight-priority {\n  width", "value": "32px"}, {"name": "--space-sm) 0;\n  font-size", "value": "var(--text-lg)"}, {"name": "--space-md) 0;\n  font-size", "value": "var(--text-sm)"}, {"name": "--space-lg);\n  margin-bottom", "value": "var(--space-lg)"}, {"name": "--space-xs);\n}\n\n.metric-label {\n  font-size", "value": "var(--text-xs)"}, {"name": "--space-sm);\n}\n\n/* Metric Cards */\n.metric-card {\n  background", "value": "var(--surface-card)"}, {"name": "--space-lg);\n  backdrop-filter", "value": "blur(12px)"}, {"name": "--space-xs);\n}\n\n.stat-trend {\n  font-size", "value": "var(--text-xs)"}, {"name": "--space-md);\n  padding", "value": "var(--space-md)"}, {"name": "--space-xs) 0;\n  font-size", "value": "var(--text-base)"}, {"name": "--space-md);\n}\n\n.metric-icon {\n  width", "value": "40px"}, {"name": "--space-xs) 0 var(--space-sm) 0;\n  font-size", "value": "var(--text-sm)"}, {"name": "--space-xs);\n}\n\n.change-value {\n  font-size", "value": "var(--text-sm)"}, {"name": "--space-md);\n}\n\n.progress-title {\n  margin", "value": "0"}, {"name": "--space-lg);\n}\n\n.progress-steps {\n  display", "value": "flex"}, {"name": "--space-md);\n}\n\n.progress-step {\n  display", "value": "flex"}, {"name": "--space-xs);\n  flex", "value": "1"}, {"name": "--space-md) var(--space-lg);\n  background", "value": "var(--bg-overlay)"}, {"name": "--space-sm);\n}\n\n.feed-icon {\n  width", "value": "16px"}, {"name": "--space-sm);\n}\n\n.status-feed-item {\n  display", "value": "flex"}, {"name": "--space-sm);\n  padding", "value": "var(--space-sm)"}, {"name": "--space-sm) var(--space-lg);\n  background", "value": "var(--bg-overlay)"}, {"name": "--space-xs);\n  font-size", "value": "var(--text-xs)"}, {"name": "--space-md);\n  margin-bottom", "value": "var(--space-lg)"}, {"name": "--space-sm);\n  margin-bottom", "value": "var(--space-lg)"}, {"name": "--space-sm);\n}\n\n.skeleton-button {\n  height", "value": "32px"}, {"name": "--space-xl);\n  background", "value": "var(--bg-elevated)"}, {"name": "--space-sm) 0;\n  font-size", "value": "var(--text-xl)"}, {"name": "--space-lg) 0;\n  font-size", "value": "var(--text-base)"}, {"name": "--space-sm);\n}\n\n/* Empty State */\n.empty-state {\n  display", "value": "flex"}, {"name": "--space-xl);\n  background", "value": "var(--bg-elevated)"}, {"name": "--space-lg);\n}\n\n.empty-icon-svg {\n  width", "value": "32px"}, {"name": "--space-sm);\n}\n\n/* Animations */\n@keyframes fadeInUp {\n  from {\n    opacity", "value": "0"}, {"name": "--space-md);\n  padding", "value": "var(--space-md)"}, {"name": "--space-md) center;\n  background-repeat", "value": "no-repeat"}, {"name": "--space-2xl);\n}\n\n/* ====== ANALYSIS REPORT COMPONENTS ====== */\n.analysis-section {\n  margin-bottom", "value": "var(--space-2xl)"}, {"name": "--space-md);\n  margin-bottom", "value": "var(--space-lg)"}, {"name": "--space-xs) 0 0 0;\n}\n\n.metric-grid {\n  display", "value": "grid"}, {"name": "--space-lg);\n  margin-bottom", "value": "var(--space-xl)"}, {"name": "--space-md);\n}\n\n.metric-label {\n  font-size", "value": "var(--text-sm)"}, {"name": "--space-xs);\n}\n\n.metric-change.positive {\n  color", "value": "var(--success)"}, {"name": "--space-lg);\n  }\n\n  .premium-content {\n    padding", "value": "var(--space-xl) var(--space-lg)"}, {"name": "--space-lg);\n  }\n\n  .card-content {\n    padding", "value": "var(--space-lg)"}, {"name": "--space-2xl);\n}\n\n.analyzer-form {\n  display", "value": "grid"}, {"name": "--space-lg);\n}\n\n.analyzer-form.grid-layout {\n  grid-template-columns", "value": "1fr 120px auto"}, {"name": "--space-sm);\n  padding", "value": "var(--space-sm) var(--space-md)"}, {"name": "--space-sm);\n}\n\n.btn-spinner.active {\n  display", "value": "flex"}, {"name": "--space-2xl);\n}\n\n.analyzer-form-section {\n  margin-bottom", "value": "var(--space-2xl)"}, {"name": "--space-xl);\n}\n\n.analysis-card {\n  background", "value": "var(--bg-elevated)"}, {"name": "--space-lg);\n}\n\n.card-title {\n  font-size", "value": "var(--text-lg)"}, {"name": "--space-xs);\n}\n\n/* Section headers */\n.section-header {\n  display", "value": "flex"}, {"name": "--space-xl);\n}\n\n.section-title {\n  font-size", "value": "var(--text-2xl)"}, {"name": "--space-xs);\n}\n\n.section-link {\n  color", "value": "var(--purple-primary)"}, {"name": "--space-lg);\n  }\n\n  .results-grid {\n    grid-template-columns", "value": "1fr"}, {"name": "--space-md);\n  }\n\n  .form-row {\n    grid-template-columns", "value": "1fr"}, {"name": "--space-sm);\n  }\n\n  .card-header {\n    flex-direction", "value": "column"}, {"name": "--space-sm);\n  }\n}\n\n/* ====== DASHBOARD SPECIFIC COMPONENTS ====== */\n/* Unique dashboard layout components */\n\n.welcome-section {\n  display", "value": "grid"}, {"name": "--space-2xl);\n  margin-bottom", "value": "var(--space-2xl)"}, {"name": "--space-lg);\n  min-width", "value": "600px"}, {"name": "--space-2xl);\n}\n\n.quick-analysis-form {\n  display", "value": "flex"}, {"name": "--space-lg);\n  align-items", "value": "flex-end"}, {"name": "--space-2xl);\n}\n\n.tools-grid {\n  display", "value": "grid"}, {"name": "--space-xl);\n}\n\n.tool-card {\n  display", "value": "flex"}, {"name": "--space-lg);\n}\n\n.tool-icon-svg {\n  width", "value": "24px"}, {"name": "--space-lg);\n}\n\n.tool-features {\n  display", "value": "flex"}, {"name": "--space-sm);\n  margin-bottom", "value": "var(--space-lg)"}, {"name": "--space-lg);\n}\n\n.recent-card {\n  display", "value": "flex"}, {"name": "--space-lg);\n  padding", "value": "var(--space-lg)"}, {"name": "--space-xs);\n  right", "value": "var(--space-xs)"}, {"name": "--space-sm);\n}\n\n.recent-insights {\n  display", "value": "flex"}, {"name": "--space-xs);\n  flex-wrap", "value": "wrap"}, {"name": "--space-2xl);\n}\n\n.usage-grid {\n  display", "value": "grid"}, {"name": "--space-xl);\n}\n\n.usage-header {\n  display", "value": "flex"}, {"name": "--space-lg);\n}\n\n.usage-title {\n  font-size", "value": "var(--text-lg)"}, {"name": "--space-sm);\n}\n\n.progress-fill {\n  height", "value": "100%"}, {"name": "--space-sm);\n}\n\n.usage-feature {\n  display", "value": "flex"}, {"name": "--space-sm);\n  color", "value": "var(--text-secondary)"}, {"name": "--space-lg);\n}\n\n.stats-grid {\n  display", "value": "grid"}, {"name": "--space-sm);\n  margin-left", "value": "var(--space-lg)"}, {"name": "--space-sm);\n}\n\n.metric-icon {\n  width", "value": "14px"}, {"name": "--space-xl);\n  }\n\n  .usage-grid {\n    grid-template-columns", "value": "1fr"}, {"name": "--space-sm);\n  padding", "value": "var(--space-lg)"}, {"name": "--space-xl);\n}\n\n.capabilities-title {\n  font-size", "value": "var(--text-lg)"}, {"name": "--space-lg);\n}\n\n.capabilities-grid {\n  display", "value": "grid"}, {"name": "--space-lg);\n}\n\n.capability-item {\n  display", "value": "flex"}, {"name": "--space-md);\n  padding", "value": "var(--space-lg)"}, {"name": "--space-lg);\n}\n\n.placeholder-spinner .spinner {\n  width", "value": "32px"}, {"name": "--space-md) 0;\n  padding-left", "value": "var(--space-xl)"}, {"name": "--space-lg);\n  color", "value": "var(--text-tertiary)"}, {"name": "--space-lg);\n  align-items", "value": "start"}, {"name": "--space-lg);\n  margin-bottom", "value": "var(--space-sm)"}, {"name": "--space-xs);\n  color", "value": "var(--text-tertiary)"}, {"name": "--space-lg);\n  align-items", "value": "center"}, {"name": "--space-xs);\n  color", "value": "var(--text-secondary)"}, {"name": "--space-xs) var(--space-sm);\n  background", "value": "var(--bg-glass)"}, {"name": "--space-2xl);\n}\n\n.analysis-status {\n  display", "value": "flex"}, {"name": "--space-sm);\n  padding", "value": "var(--space-sm) var(--space-md)"}, {"name": "--space-lg);\n}\n\n/* Comment Intelligence responsive adjustments */\n@media (max-width", "value": "768px) {\n  .capabilities-grid {\n    grid-template-columns: 1fr"}, {"name": "--space-sm);\n  }\n}\n\n/* ====== PREMIUM CARD SYSTEM ====== */\n.premium-card {\n  background", "value": "var(--bg-elevated)"}, {"name": "--space-lg);\n  padding", "value": "var(--space-lg)"}, {"name": "--space-lg)) calc(-1 * var(--space-lg)) var(--space-lg) calc(-1 * var(--space-lg));\n  background", "value": "linear-gradient(135deg,\n    rgba(139, 92, 246, 0.03) 0%,\n    rgba(139, 92, 246, 0.01) 50%,\n    rgba(139, 92, 246, 0.05) 100%)"}, {"name": "--space-lg);\n}\n\n/* ====== DASHBOARD SPECIFIC CARDS ====== */\n.tool-card {\n  display", "value": "flex"}, {"name": "--space-lg);\n}\n\n.tool-icon-svg {\n  width", "value": "24px"}, {"name": "--space-lg);\n}\n\n.tool-features {\n  display", "value": "flex"}, {"name": "--space-sm);\n  margin-bottom", "value": "var(--space-lg)"}, {"name": "--space-lg);\n  padding", "value": "var(--space-lg)"}, {"name": "--space-xs);\n  right", "value": "var(--space-xs)"}, {"name": "--space-sm);\n}\n\n.recent-insights {\n  display", "value": "flex"}, {"name": "--space-xs);\n  flex-wrap", "value": "wrap"}, {"name": "--space-xl);\n}\n\n.usage-header {\n  display", "value": "flex"}, {"name": "--space-lg);\n}\n\n.usage-title {\n  font-size", "value": "var(--text-lg)"}, {"name": "--space-sm);\n}\n\n.progress-fill {\n  height", "value": "100%"}, {"name": "--space-sm);\n}\n\n.usage-feature {\n  display", "value": "flex"}, {"name": "--space-sm);\n  color", "value": "var(--text-secondary)"}, {"name": "--space-xs);\n}\n\n.history-header {\n  padding", "value": "var(--space-md) var(--space-lg)"}, {"name": "--space-xs);\n  white-space", "value": "nowrap"}, {"name": "--space-2xl);\n}\n\n.export-status {\n  display", "value": "flex"}, {"name": "--space-xs);\n}\n\n.status-dot {\n  width", "value": "8px"}, {"name": "--space-md);\n  flex-wrap", "value": "wrap"}, {"name": "--space-xl);\n  padding", "value": "var(--space-lg)"}, {"name": "--space-2xl);\n}\n\n.channel-header {\n  display", "value": "flex"}, {"name": "--space-lg);\n  margin-bottom", "value": "var(--space-xl)"}, {"name": "--space-lg);\n  margin-top", "value": "var(--space-xl)"}, {"name": "--space-md);\n}\n\n.stat-icon i {\n  width", "value": "20px"}, {"name": "--space-md);\n}\n\n.video-thumbnail img {\n  width", "value": "100%"}, {"name": "--space-md);\n  transition", "value": "all var(--transition-normal)"}, {"name": "--space-lg);\n  margin-bottom", "value": "var(--space-md)"}, {"name": "--space-md);\n  transition", "value": "all var(--transition-normal)"}, {"name": "--space-md);\n}\n\n.input-group .form-input {\n  flex", "value": "1"}, {"name": "--space-sm);\n  flex-wrap", "value": "wrap"}, {"name": "--space-sm);\n  padding", "value": "var(--space-md)"}, {"name": "--space-xl);\n}\n\n.tab-content.active {\n  display", "value": "block"}, {"name": "--space-lg);\n}\n\n.stat-card {\n  background", "value": "var(--bg-secondary)"}, {"name": "--space-lg);\n  display", "value": "flex"}, {"name": "--space-md);\n  transition", "value": "all 0.2s ease"}, {"name": "--space-xs);\n}\n\n/* Research recommendations and insights */\n.recommendations-section {\n  margin-top", "value": "var(--space-xl)"}, {"name": "--space-md);\n}\n\n.recommendation-number {\n  width", "value": "32px"}, {"name": "--space-xs) 0;\n  color", "value": "var(--text-primary)"}, {"name": "--space-sm) 0;\n  color", "value": "var(--text-secondary)"}, {"name": "--space-lg);\n  margin-top", "value": "var(--space-xl)"}, {"name": "--space-md);\n}\n\n.video-title {\n  font-size", "value": "var(--text-lg)"}, {"name": "--space-sm) 0;\n  line-height", "value": "1.4"}, {"name": "--space-md);\n  color", "value": "var(--text-secondary)"}, {"name": "--space-xs);\n}\n\n.stat-item i {\n  width", "value": "14px"}, {"name": "--space-xs);\n  right", "value": "var(--space-xs)"}, {"name": "--space-xs);\n  left", "value": "var(--space-xs)"}, {"name": "--space-xl);\n}\n\n.schedule-overview {\n  margin-bottom", "value": "var(--space-xl)"}, {"name": "--space-lg);\n  margin-top", "value": "var(--space-lg)"}, {"name": "--space-xs);\n  padding", "value": "var(--space-lg)"}, {"name": "--space-md);\n  margin-top", "value": "var(--space-lg)"}, {"name": "--space-md);\n}\n\n.day-name {\n  min-width", "value": "80px"}, {"name": "--space-xl);\n}\n\n.title-overview {\n  margin-bottom", "value": "var(--space-xl)"}, {"name": "--space-lg);\n  margin-top", "value": "var(--space-lg)"}, {"name": "--space-xs);\n  padding", "value": "var(--space-lg)"}, {"name": "--space-xl);\n}\n\n.top-titles {\n  display", "value": "flex"}, {"name": "--space-md);\n  margin-top", "value": "var(--space-lg)"}, {"name": "--space-lg);\n  background", "value": "var(--bg-elevated)"}, {"name": "--space-lg);\n  color", "value": "var(--text-secondary)"}, {"name": "--space-xs);\n}\n\n.psychology-analysis {\n  margin-bottom", "value": "var(--space-xl)"}, {"name": "--space-lg);\n  margin-top", "value": "var(--space-lg)"}, {"name": "--space-lg);\n  background", "value": "var(--bg-elevated)"}, {"name": "--space-xs);\n  font-size", "value": "var(--text-sm)"}, {"name": "--space-lg);\n  background", "value": "var(--bg-elevated)"}, {"name": "--space-sm) 0;\n  color", "value": "var(--text-secondary)"}, {"name": "--space-sm);\n  right", "value": "var(--space-sm)"}, {"name": "--space-md);\n}\n\n.video-title {\n  font-size", "value": "var(--text-base)"}, {"name": "--space-xs);\n}\n\n.video-date {\n  font-size", "value": "var(--text-xs)"}, {"name": "--space-2xl);\n}\n\n.form-header .market-icon {\n  width", "value": "64px"}, {"name": "--space-lg);\n  align-items", "value": "end"}, {"name": "--space-xl);\n}\n\n.premium-form-input {\n  background", "value": "rgba(255, 255, 255, 0.9)"}, {"name": "--space-sm);\n}\n\n.form-options {\n  display", "value": "grid"}, {"name": "--space-lg);\n  margin-bottom", "value": "var(--space-xl)"}, {"name": "--space-sm);\n}\n\n.form-option label {\n  font-size", "value": "var(--text-sm)"}, {"name": "--space-sm);\n}\n\n.form-option .label-icon {\n  width", "value": "16px"}, {"name": "--space-md) center;\n  background-repeat", "value": "no-repeat"}, {"name": "--space-2xl);\n}\n\n[data-theme=\"dark\"] .premium-select {\n  background", "value": "rgba(30, 41, 59, 0.8)"}, {"name": "--space-sm);\n}\n\n.form-label {\n  font-size", "value": "var(--text-sm)"}, {"name": "--space-sm);\n}\n\n.form-label .label-icon {\n  width", "value": "16px"}, {"name": "--space-2xl);\n  padding", "value": "var(--space-xl) 0"}, {"name": "--space-sm) 0;\n}\n\n.results-subtitle {\n  font-size", "value": "var(--text-lg)"}, {"name": "--space-xl);\n  margin-bottom", "value": "var(--space-2xl)"}, {"name": "--space-lg);\n  }\n\n  .form-options {\n    grid-template-columns", "value": "1fr"}, {"name": "--space-xl);\n  }\n\n  .form-header h1 {\n    font-size", "value": "var(--text-2xl)"}], "radius": [{"name": "--radius-sm", "value": "0.375rem"}, {"name": "--radius-md", "value": "0.5rem"}, {"name": "--radius-lg", "value": "0.75rem"}, {"name": "--radius-xl", "value": "1rem"}, {"name": "--radius-2xl", "value": "1.5rem"}, {"name": "--radius-full", "value": "9999px"}, {"name": "--radius-sm);\n  font-size", "value": "var(--text-xs)"}, {"name": "--radius-full);\n  margin-left", "value": "auto"}, {"name": "--radius-md);\n  font-family", "value": "var(--font-primary)"}, {"name": "--radius-sm);\n  line-height", "value": "1.2"}, {"name": "--radius-lg);\n  overflow", "value": "hidden"}, {"name": "--radius-md);\n  transition", "value": "var(--transition-fast)"}, {"name": "--radius-full);\n  display", "value": "flex"}, {"name": "--radius-lg);\n  padding", "value": "var(--space-lg)"}, {"name": "--radius-lg);\n  padding", "value": "var(--space-lg)"}, {"name": "--radius-md);\n  display", "value": "flex"}, {"name": "--radius-md);\n  transition", "value": "var(--transition-fast)"}, {"name": "--radius-sm);\n  background", "value": "var(--bg-secondary)"}, {"name": "--radius-md);\n  display", "value": "flex"}, {"name": "--radius-lg);\n  padding", "value": "var(--space-lg)"}, {"name": "--radius-full);\n  background", "value": "var(--bg-muted)"}, {"name": "--radius-lg);\n  overflow", "value": "hidden"}, {"name": "--radius-md);\n  transition", "value": "var(--transition-fast)"}, {"name": "--radius-full);\n  background", "value": "var(--purple-primary)"}, {"name": "--radius-full);\n  background", "value": "var(--text-tertiary)"}, {"name": "--radius-lg);\n  padding", "value": "var(--space-lg)"}, {"name": "--radius-full);\n  display", "value": "flex"}, {"name": "--radius-md);\n  margin-bottom", "value": "var(--space-sm)"}, {"name": "--radius-full);\n  display", "value": "flex"}, {"name": "--radius-md);\n  font-family", "value": "var(--font-primary)"}, {"name": "--radius-lg);\n  display", "value": "flex"}, {"name": "--radius-md);\n  color", "value": "var(--text-secondary)"}, {"name": "--radius-lg);\n  padding", "value": "var(--space-2xl)"}, {"name": "--radius-lg);\n  overflow", "value": "hidden"}, {"name": "--radius-sm);\n  font-size", "value": "var(--text-xs)"}, {"name": "--radius-sm);\n  font-size", "value": "var(--text-xs)"}, {"name": "--radius-lg);\n  transition", "value": "all var(--transition-fast)"}, {"name": "--radius-md);\n  font-size", "value": "var(--text-xs)"}, {"name": "--radius-md);\n  font-size", "value": "var(--text-sm)"}, {"name": "--radius-md) var(--radius-md) 0 0;\n  position", "value": "relative"}, {"name": "--radius-lg);\n  overflow", "value": "hidden"}, {"name": "--radius-sm);\n  font-size", "value": "var(--text-xs)"}, {"name": "--radius-sm);\n  font-size", "value": "var(--text-xs)"}, {"name": "--radius-lg);\n  padding", "value": "var(--space-lg)"}, {"name": "--radius-lg);\n  padding", "value": "var(--space-lg)"}, {"name": "--radius-md);\n  overflow", "value": "hidden"}, {"name": "--radius-lg);\n  padding", "value": "var(--space-lg)"}, {"name": "--radius-lg);\n  padding", "value": "var(--space-lg)"}, {"name": "--radius-lg);\n  padding", "value": "var(--space-lg)"}, {"name": "--radius-lg);\n  color", "value": "var(--text-secondary)"}, {"name": "--radius-lg);\n}\n\n.schedule-patterns {\n  margin-bottom", "value": "var(--space-xl)"}, {"name": "--radius-2xl);\n  padding", "value": "var(--space-2xl)"}, {"name": "--radius-xl);\n  padding", "value": "var(--space-lg) var(--space-xl)"}, {"name": "--radius-xl);\n  padding", "value": "var(--space-lg) var(--space-2xl)"}, {"name": "--radius-lg);\n  padding", "value": "var(--space-md) var(--space-lg)"}, {"name": "--radius-2xl);\n  padding", "value": "var(--space-2xl)"}], "transition": [{"name": "--transition-fast", "value": "150ms cubic-bezier(0.4, 0, 0.2, 1)"}, {"name": "--transition-normal", "value": "300ms cubic-bezier(0.4, 0, 0.2, 1)"}, {"name": "--transition-slow", "value": "500ms cubic-bezier(0.4, 0, 0.2, 1)"}, {"name": "--transition-normal);\n}\n\n/* Enhanced theme transitions */\n* {\n  transition", "value": "background-color var(--transition-normal), \n              color var(--transition-normal), \n              border-color var(--transition-normal),\n              box-shadow var(--transition-normal)"}, {"name": "--transition-fast);\n  color", "value": "var(--text-secondary)"}, {"name": "--transition-normal);\n  z-index", "value": "0"}, {"name": "--transition-fast);\n}\n\n.theme-toggle", "value": "hover {\n  color: white"}, {"name": "--transition-normal);\n  position", "value": "relative"}, {"name": "--transition-fast);\n  color", "value": "var(--text-secondary)"}, {"name": "--transition-normal);\n}\n\n.upgrade-btn {\n  width", "value": "100%"}, {"name": "--transition-fast);\n  overflow", "value": "hidden"}, {"name": "--transition-fast);\n  position", "value": "relative"}, {"name": "--transition-fast);\n}\n\n.insight-card", "value": "hover {\n  border-color: var(--purple-primary)"}, {"name": "--transition-fast);\n}\n\n.progress-step.active .step-indicator {\n  background", "value": "var(--purple-primary)"}, {"name": "--transition-normal);\n}\n\n/* ====== FORM COMPONENTS ====== */\n.form-group {\n  margin-bottom", "value": "var(--space-lg)"}, {"name": "--transition-normal);\n}\n\n.analysis-card", "value": "hover {\n  box-shadow: var(--shadow-md)"}, {"name": "--transition-normal);\n  position", "value": "relative"}, {"name": "--transition-fast);\n}\n\n.history-item", "value": "hover {\n  background: var(--bg-tertiary)"}, {"name": "--transition-normal);\n}\n\n.stat-card", "value": "hover {\n  box-shadow: var(--shadow-md)"}, {"name": "--transition-normal);\n}\n\n.video-card", "value": "hover {\n  box-shadow: var(--shadow-md)"}, {"name": "--transition-normal);\n}\n\n.insight-card", "value": "hover {\n  box-shadow: var(--shadow-md)"}, {"name": "--transition-normal);\n}\n\n.trigger-card", "value": "hover {\n  box-shadow: var(--shadow-md)"}, {"name": "--transition-slow);\n}\n\n.premium-analyze-btn", "value": "hover {\n  transform: translateY(-2px)"}, {"name": "--transition-normal);\n  appearance", "value": "none"}, {"name": "--transition-slow);\n}\n\n.results-section.visible {\n  opacity", "value": "1"}, {"name": "--transition-normal);\n  position", "value": "relative"}]}