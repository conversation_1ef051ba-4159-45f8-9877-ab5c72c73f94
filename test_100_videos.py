#!/usr/bin/env python3

import requests
import json

def test_100_videos():
    """Test that the API can fetch 100 videos"""
    
    print("🧪 Testing Market Research Hub API - 100 Videos")
    print("=" * 50)
    
    # Test data
    test_data = {
        "query": "AI tutorials",
        "time_range": "month",
        "max_results": 100,
        "sort_by": "relevance"
    }
    
    print(f"🔍 Testing query: '{test_data['query']}'")
    print(f"📊 Requesting: {test_data['max_results']} videos")
    
    # Make API request
    url = "http://localhost:8003/api/market-research"
    print(f"📡 Sending request to: {url}")
    
    try:
        response = requests.post(url, json=test_data, timeout=60)
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API request successful!")
            
            # Check market overview
            market_overview = data.get('market_overview', {})
            total_videos = market_overview.get('total_videos', 0)
            print(f"\n📈 Market Overview:")
            print(f"   Total Videos: {total_videos}")
            print(f"   Total Views: {market_overview.get('total_views', 0):,}")
            print(f"   Unique Channels: {market_overview.get('unique_channels', 0)}")
            print(f"   Avg Engagement: {market_overview.get('avg_engagement', 0):.2f}%")
            
            # Check trending content
            trending_content = data.get('trending_content', [])
            print(f"\n🔥 Trending Content:")
            print(f"   Found {len(trending_content)} trending videos")
            
            if trending_content:
                top_video = trending_content[0]
                print(f"   Top video: {top_video.get('title', 'N/A')}")
                print(f"   Views: {top_video.get('views', 0):,}")
                print(f"   Performance Score: {top_video.get('performance_score', 0)}")
            
            # Verify we got the requested number of videos
            if total_videos >= 100:
                print(f"\n✅ SUCCESS: Got {total_videos} videos (requested 100)")
            elif total_videos >= 50:
                print(f"\n⚠️  PARTIAL: Got {total_videos} videos (requested 100, but this is more than the previous 50 limit)")
            else:
                print(f"\n❌ ISSUE: Only got {total_videos} videos (requested 100)")
            
            # Check if we have channel thumbnails
            if trending_content:
                videos_with_thumbnails = sum(1 for video in trending_content if video.get('channel_thumbnail'))
                print(f"   Videos with channel thumbnails: {videos_with_thumbnails}/{len(trending_content)}")
            
            print(f"\n✅ Market Research Hub 100-video test completed!")
            
        else:
            print(f"❌ API request failed with status: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    test_100_videos()
