<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Research v2 - Dashboard</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/static/favicon.svg">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- YouTube Research v2 Design System -->
    <link rel="stylesheet" href="/static/styles/youtube-research-v2-design-system.css?v=2025073002">
    
    <style>
        /* Dashboard Specific Styles */
        .dashboard-header {
            padding: var(--space-xl) 0 var(--space-lg);
            position: relative;
        }
        
        .welcome-section {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: var(--space-lg);
            margin-bottom: var(--space-xl);
        }
        
        .welcome-content h1 {
            margin-bottom: var(--space-xs);
            font-size: var(--text-3xl);
            font-weight: var(--font-weight-bold);
            background: linear-gradient(135deg, var(--text-primary) 0%, var(--purple-primary) 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .user-stats {
            display: flex;
            gap: var(--space-lg);
        }
        
        /* Updated Quick Analysis to use design system card */
        .quick-analysis {
            background: var(--bg-elevated);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            margin-bottom: var(--space-xl);
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            transition: var(--transition-fast);
        }

        .quick-analysis:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        .quick-analysis::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--purple-gradient);
        }
        
        .quick-analysis-form {
            display: flex;
            gap: var(--space-md);
            align-items: center;
        }
        
        .quick-analysis-input {
            flex: 1;
            position: relative;
        }
        
        .recent-dropdown {
            position: absolute;
            right: var(--space-sm);
            top: 50%;
            transform: translateY(-50%);
        }
        
        .modules-section {
            margin-bottom: var(--space-xl);
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-lg);
        }
        
        .activity-insights-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--space-lg);
            margin-bottom: var(--space-xl);
        }
        
        @media (max-width: 1024px) {
            .activity-insights-grid {
                grid-template-columns: 1fr;
            }
        }
        
        /* Updated insights card to use design system */
        .insights-card {
            background: var(--bg-elevated);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            height: 100%;
            box-shadow: var(--shadow-sm);
            transition: var(--transition-fast);
        }

        .insights-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }
        
        .insight-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-sm) 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .insight-item:last-child {
            border-bottom: none;
        }
        
        /* Updated usage widget to use design system */
        .usage-widget {
            background: var(--bg-elevated);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-sm);
            transition: var(--transition-fast);
        }

        .usage-widget:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }
        
        /* Updated progress bar to use design system colors */
        .usage-progress {
            width: 100%;
            height: 8px;
            background: var(--bg-secondary);
            border-radius: var(--radius-full);
            margin: var(--space-md) 0;
            overflow: hidden;
        }

        .usage-progress-bar {
            height: 100%;
            background: var(--purple-gradient);
            border-radius: var(--radius-full);
            transition: width var(--transition-slow);
        }

        
        /* Floating elements for visual interest */
        .floating-orb {
            position: absolute;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(139, 92, 246, 0.2) 0%, transparent 70%);
            filter: blur(60px);
            pointer-events: none;
            animation: float 20s ease-in-out infinite;
        }
        
        .orb-1 {
            top: -150px;
            right: -150px;
            animation-delay: 0s;
        }
        
        .orb-2 {
            bottom: -150px;
            left: -150px;
            background: radial-gradient(circle, rgba(236, 72, 153, 0.2) 0%, transparent 70%);
            animation-delay: 10s;
        }
        
        @keyframes float {
            0%, 100% { transform: translate(0, 0); }
            33% { transform: translate(30px, -30px); }
            66% { transform: translate(-20px, 20px); }
        }
        
        /* Module specific icons with emoji fallbacks */
        .module-icon {
            font-size: 2rem;
        }
        
        /* Hover effects */
        .feature-card {
            transform-style: preserve-3d;
            transition: transform var(--transition-base);
        }
        
        .feature-card:hover {
            transform: translateY(-4px) rotateX(-2deg);
        }
        
        /* Loading animation for analysis */
        .analyzing {
            position: relative;
            overflow: hidden;
        }
        
        .analyzing::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(139, 92, 246, 0.3) 50%, transparent 100%);
            animation: shimmer 1.5s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- Include Navigation Component -->
        <aside class="app-sidebar" id="sidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <a href="/" class="sidebar-logo">
                    <div class="sidebar-logo-icon">YR</div>
                    <span class="sidebar-logo-text">YouTube Research</span>
                </a>
                <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                    <i data-lucide="sun" class="theme-icon-light"></i>
                    <i data-lucide="moon" class="theme-icon-dark" style="display: none;"></i>
                </button>
            </div>

            <!-- Navigation Menu -->
            <nav class="sidebar-nav">
                <!-- Overview Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Overview</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/" class="nav-item active" data-page="dashboard">
                                <i data-lucide="layout-dashboard" class="nav-icon"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <a href="/analytics" class="nav-item" data-page="analytics">
                                <i data-lucide="bar-chart-3" class="nav-icon"></i>
                                <span>Analytics</span>
                            </a>
                        </li>
                        <li>
                            <a href="/reports" class="nav-item" data-page="reports">
                                <i data-lucide="folder" class="nav-icon"></i>
                                <span>My Reports</span>
                                <span class="nav-badge" id="reportsCount">0</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Analysis Tools Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">AI Analysis Tools</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/video-analyzer" class="nav-item" data-page="video-analyzer">
                                <i data-lucide="video" class="nav-icon"></i>
                                <span>Video Analyzer</span>
                                <span class="nav-badge nav-badge-purple">6 AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/channel-analyzer" class="nav-item" data-page="channel-analyzer">
                                <i data-lucide="users" class="nav-icon"></i>
                                <span>Channel DNA</span>
                                <span class="nav-badge nav-badge-blue">5 AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/market-research" class="nav-item" data-page="market-research">
                                <i data-lucide="trending-up" class="nav-icon"></i>
                                <span>Market Research</span>
                                <span class="nav-badge nav-badge-green">AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/comment-intelligence" class="nav-item" data-page="comment-intelligence">
                                <i data-lucide="message-circle" class="nav-icon"></i>
                                <span>Comment Intel</span>
                                <span class="nav-badge nav-badge-orange">AI</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Account Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Account</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/settings" class="nav-item" data-page="settings">
                                <i data-lucide="settings" class="nav-icon"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li>
                            <a href="/help" class="nav-item" data-page="help">
                                <i data-lucide="help-circle" class="nav-icon"></i>
                                <span>Help & Support</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="version-info">
                    <span class="version-label">YouTube Research v2</span>
                    <span class="version-number">2.0.0</span>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="app-main">
            <div class="main-content">
            <!-- Welcome Section -->
            <header class="dashboard-header">
                <div class="welcome-section">
                    <div class="welcome-content">
                        <h1 class="heading-1">Welcome back, Ardi! 👋</h1>
                        <p class="text-body">Ready to uncover YouTube insights? Your AI agents are standing by.</p>
                    </div>
                    <div class="user-stats">
                        <div class="metric-card">
                            <div class="stat-value">127</div>
                            <div class="stat-label">Analyses this month</div>
                            <div class="stat-trend up">↑ 23% from last month</div>
                        </div>
                        <div class="metric-card">
                            <div class="stat-value">4.8</div>
                            <div class="stat-label">Avg. video score</div>
                            <div class="stat-trend up">↑ 0.3 points</div>
                        </div>
                    </div>
                </div>
            </header>
            
            <!-- Quick Analysis Bar -->
            <section class="quick-analysis animate-fade-in">
                <h2 class="heading-3 mb-3">Quick Analysis</h2>
                <form class="quick-analysis-form" id="quickAnalysisForm">
                    <div class="quick-analysis-input">
                        <input 
                            type="url" 
                            class="input" 
                            placeholder="Paste any YouTube video URL to start analyzing..."
                            id="quickAnalysisInput"
                        >
                        <button type="button" class="btn btn-ghost recent-dropdown">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12 6 12 12 16 14"></polyline>
                            </svg>
                            Recent
                        </button>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                        Analyze Now
                    </button>
                </form>
            </section>
            
            <!-- Feature Modules -->
            <section class="modules-section">
                <div class="section-header">
                    <h2 class="heading-2">Your Analysis Toolkit</h2>
                    <button class="btn btn-ghost">View all features →</button>
                </div>
                
                <div class="grid grid-cols-3">
                    <!-- Video Analyzer - Active -->
                    <div class="card card-gradient-border feature-card animate-fade-in" onclick="window.location.href='/video-analyzer'">
                        <span class="feature-badge">Active</span>
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <span class="module-icon">🎬</span>
                            </div>
                            <h3 class="heading-3">Video Analyzer</h3>
                            <p class="text-body">Deep dive into any YouTube video with 5 specialized AI agents analyzing performance, scripts, SEO, psychology, and comments.</p>
                            <div class="mt-auto">
                                <button class="btn btn-primary">Launch Analyzer</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Channel Profile Exporter - New Tool -->
                    <div class="card card-gradient-border feature-card animate-fade-in" style="animation-delay: 0.05s;" onclick="window.location.href='/tools/channel-profile'">
                        <span class="feature-badge" style="background-color: #10b981;">New Tool</span>
                        <div class="feature-card-content">
                            <div class="feature-icon" style="background-color: #10b981;">
                                <span class="module-icon">📈</span>
                            </div>
                            <h3 class="heading-3">Channel Profile Exporter</h3>
                            <p class="text-body">Extract comprehensive raw data for any YouTube channel, including channel details and video library.</p>
                            <div class="mt-auto">
                                <button class="btn btn-primary" style="background-color: #10b981;">Launch Exporter</button>
                            </div>
                        </div>
                    </div>

                    <!-- Channel Analyzer - Coming Soon -->
                    <div class="card feature-card animate-fade-in" style="animation-delay: 0.1s;">
                        <span class="feature-badge coming-soon">Coming Feb 2025</span>
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <span class="module-icon">📊</span>
                            </div>
                            <h3 class="heading-3">Channel Analyzer</h3>
                            <p class="text-body">Comprehensive channel audits, growth trajectory analysis, content strategy insights, and competitor benchmarking.</p>
                            <div class="mt-auto">
                                <button class="btn btn-secondary" disabled>Coming Soon</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Competitor Tracking -->
                    <div class="card feature-card animate-fade-in" style="animation-delay: 0.2s;">
                        <span class="feature-badge coming-soon">Coming Soon</span>
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <span class="module-icon">🎯</span>
                            </div>
                            <h3 class="heading-3">Competitor Tracking</h3>
                            <p class="text-body">Monitor your competition in real-time. Track their best performing content and strategies.</p>
                            <div class="mt-auto">
                                <button class="btn btn-secondary" disabled>Coming Soon</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Trend Discovery -->
                    <div class="card feature-card animate-fade-in" style="animation-delay: 0.3s;">
                        <span class="feature-badge coming-soon">Coming Soon</span>
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <span class="module-icon">🚀</span>
                            </div>
                            <h3 class="heading-3">Trend Discovery</h3>
                            <p class="text-body">Spot viral content before it explodes. AI-powered trend prediction and content opportunity alerts.</p>
                            <div class="mt-auto">
                                <button class="btn btn-secondary" disabled>Coming Soon</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Content Calendar -->
                    <div class="card feature-card animate-fade-in" style="animation-delay: 0.4s;">
                        <span class="feature-badge coming-soon">Coming Soon</span>
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <span class="module-icon">📅</span>
                            </div>
                            <h3 class="heading-3">Content Calendar</h3>
                            <p class="text-body">AI-optimized posting schedule based on your audience behavior and platform algorithms.</p>
                            <div class="mt-auto">
                                <button class="btn btn-secondary" disabled>Coming Soon</button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Analytics Hub -->
                    <div class="card feature-card animate-fade-in" style="animation-delay: 0.5s;">
                        <span class="feature-badge coming-soon">Coming Soon</span>
                        <div class="feature-card-content">
                            <div class="feature-icon">
                                <span class="module-icon">📈</span>
                            </div>
                            <h3 class="heading-3">Analytics Hub</h3>
                            <p class="text-body">Unified dashboard for all your YouTube metrics, insights, and growth tracking in one place.</p>
                            <div class="mt-auto">
                                <button class="btn btn-secondary" disabled>Coming Soon</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            
            <!-- Recent Activity & Insights -->
            <div class="activity-insights-grid">
                <!-- Recent Activity -->
                <section class="activity-section">
                    <div class="section-header">
                        <h2 class="heading-3">Recent Analyses</h2>
                        <button class="btn btn-ghost">View all</button>
                    </div>
                    
                    <div class="activity-list" style="display: flex; flex-direction: column; gap: var(--space-sm);">
                        <div class="activity-item">
                            <div class="activity-thumbnail skeleton"></div>
                            <div class="activity-details">
                                <h4 class="font-semibold">How I Make $100k/Month with AI</h4>
                                <p class="text-small">Analyzed 2 hours ago • Score: 4.8/5</p>
                                <div style="display: flex; gap: var(--space-xs);">
                                    <span class="text-small" style="color: var(--accent-green);">High engagement</span>
                                    <span class="text-small">•</span>
                                    <span class="text-small" style="color: var(--accent-purple);">Trending topic</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-thumbnail skeleton"></div>
                            <div class="activity-details">
                                <h4 class="font-semibold">The Truth About Dropshipping in 2025</h4>
                                <p class="text-small">Analyzed 5 hours ago • Score: 4.2/5</p>
                                <div style="display: flex; gap: var(--space-xs);">
                                    <span class="text-small" style="color: var(--accent-orange);">Good SEO</span>
                                    <span class="text-small">•</span>
                                    <span class="text-small">Mixed sentiment</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="activity-item">
                            <div class="activity-thumbnail skeleton"></div>
                            <div class="activity-details">
                                <h4 class="font-semibold">ChatGPT Changed Everything - Here's How</h4>
                                <p class="text-small">Analyzed yesterday • Score: 4.9/5</p>
                                <div style="display: flex; gap: var(--space-xs);">
                                    <span class="text-small" style="color: var(--accent-green);">Viral potential</span>
                                    <span class="text-small">•</span>
                                    <span class="text-small" style="color: var(--accent-purple);">Perfect timing</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- Insights Widget -->
                <section class="insights-section">
                    <div class="section-header">
                        <h2 class="heading-3">This Week's Insights</h2>
                        <button class="btn btn-ghost">Customize</button>
                    </div>
                    
                    <div class="insights-card">
                        <h4 class="font-semibold mb-3">Trending Topics in Your Niche</h4>
                        <div class="insight-item">
                            <span>AI Automation</span>
                            <span class="text-gradient">+245%</span>
                        </div>
                        <div class="insight-item">
                            <span>No-Code Tools</span>
                            <span class="text-gradient">+182%</span>
                        </div>
                        <div class="insight-item">
                            <span>ChatGPT Tutorials</span>
                            <span class="text-gradient">+156%</span>
                        </div>
                        <div class="insight-item">
                            <span>Remote Work</span>
                            <span style="color: var(--accent-orange);">+89%</span>
                        </div>
                        <div class="insight-item">
                            <span>Crypto News</span>
                            <span style="color: var(--text-tertiary);">-12%</span>
                        </div>
                    </div>
                </section>
            </div>
            
            <!-- Usage & Billing Widget -->
            <section class="usage-widget">
                <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: var(--space-md);">
                    <div>
                        <h3 class="heading-3">Usage & Billing</h3>
                        <p class="text-body">Premium Plan • $99/month</p>
                    </div>
                    <button class="btn btn-secondary">Manage Subscription</button>
                </div>
                
                <div style="margin-bottom: var(--space-lg);">
                    <div style="display: flex; justify-content: space-between; margin-bottom: var(--space-xs);">
                        <span class="text-small">Analyses Used</span>
                        <span class="text-small">127 / 500</span>
                    </div>
                    <div class="usage-progress">
                        <div class="usage-progress-bar" style="width: 25.4%;"></div>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: var(--space-md);">
                    <div>
                        <p class="text-small">Next billing</p>
                        <p class="font-semibold">July 23, 2025</p>
                    </div>
                    <div>
                        <p class="text-small">API Calls</p>
                        <p class="font-semibold">2,341 / 10,000</p>
                    </div>
                    <div>
                        <p class="text-small">Team Members</p>
                        <p class="font-semibold">1 / 5</p>
                    </div>
                </div>
            </section>
            </div>
        </main>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Navigation Manager
        class NavigationManager {
            constructor() {
                this.currentTheme = localStorage.getItem('theme') || 'dark';
                this.init();
            }

            init() {
                this.setupThemeToggle();
                this.setupNavigation();
                this.updateActiveNavItem();
            }

            setupThemeToggle() {
                const themeToggle = document.getElementById('themeToggle');
                const lightIcon = themeToggle.querySelector('.theme-icon-light');
                const darkIcon = themeToggle.querySelector('.theme-icon-dark');

                // Apply saved theme
                document.documentElement.setAttribute('data-theme', this.currentTheme);
                this.updateThemeIcons(lightIcon, darkIcon);

                themeToggle.addEventListener('click', () => {
                    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
                    document.documentElement.setAttribute('data-theme', this.currentTheme);
                    localStorage.setItem('theme', this.currentTheme);
                    this.updateThemeIcons(lightIcon, darkIcon);

                    // Add theme transition effect
                    document.body.classList.add('theme-transitioning');
                    setTimeout(() => {
                        document.body.classList.remove('theme-transitioning');
                    }, 300);
                });
            }

            updateThemeIcons(lightIcon, darkIcon) {
                if (this.currentTheme === 'dark') {
                    lightIcon.style.display = 'none';
                    darkIcon.style.display = 'block';
                } else {
                    lightIcon.style.display = 'block';
                    darkIcon.style.display = 'none';
                }
            }

            setupNavigation() {
                // Add click handlers for navigation items
                const navItems = document.querySelectorAll('.nav-item');
                navItems.forEach(item => {
                    item.addEventListener('click', (e) => {
                        // Update active state
                        navItems.forEach(nav => nav.classList.remove('active'));
                        item.classList.add('active');

                        // Store active page
                        const page = item.getAttribute('data-page');
                        if (page) {
                            localStorage.setItem('activePage', page);
                        }
                    });
                });
            }

            updateActiveNavItem() {
                const activePage = localStorage.getItem('activePage') || 'dashboard';
                const currentPath = window.location.pathname;

                // Remove all active states
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });

                // Set active based on current path or stored page
                let activeItem = document.querySelector(`[data-page="${activePage}"]`);

                if (!activeItem) {
                    // Fallback to path matching
                    activeItem = document.querySelector(`[href="${currentPath}"]`);
                }

                if (activeItem) {
                    activeItem.classList.add('active');
                }
            }
        }

        // Initialize navigation when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.navigationManager = new NavigationManager();
        });

        // Quick Analysis Form Handler
        document.getElementById('quickAnalysisForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const url = document.getElementById('quickAnalysisInput').value;
            if (url) {
                // Add analyzing animation
                this.classList.add('analyzing');
                
                // Redirect to video analyzer with URL
                setTimeout(() => {
                    window.location.href = `/video-analyzer?url=${encodeURIComponent(url)}`;
                }, 500);
            }
        });
        
        // Add hover sound effects (optional)
        document.querySelectorAll('.btn, .feature-card').forEach(element => {
            element.addEventListener('mouseenter', () => {
                // Could add subtle hover sound here
            });
        });
        
        // Animate numbers on scroll
        const observerOptions = {
            threshold: 0.5,
            rootMargin: '0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);
        
        // Observe all stat values
        document.querySelectorAll('.stat-value').forEach(el => {
            observer.observe(el);
        });

        // BULLETPROOF NAVIGATION HEIGHT FIX - MEASURE AND ADAPT
        function fixNavigationOverlap() {
            const navHeader = document.querySelector('.nav-header');
            if (navHeader) {
                // Measure actual navigation height
                const navHeight = navHeader.offsetHeight;
                
                // Update CSS custom property with actual measured height
                document.documentElement.style.setProperty('--nav-height', navHeight + 'px');
                
                console.log(`🔧 Navigation height measured: ${navHeight}px - Layout updated dynamically`);
            }
        }

        // Fix navigation on page load and resize
        window.addEventListener('load', fixNavigationOverlap);
        window.addEventListener('resize', fixNavigationOverlap);
        
        // Also fix immediately in case navigation is already rendered
        fixNavigationOverlap();
    </script>
</body>
</html>