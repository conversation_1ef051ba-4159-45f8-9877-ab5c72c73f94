#!/usr/bin/env python3
"""
COMPLETE WORKFLOW: Agent 6 → Research → Writers' Room
Fixes the "fucking boring" script issue by feeding agents rich content
"""

import json
import time
import re
from typing import Dict, List, Any

# Import existing systems
from writers_room.real_futurehouse_research import RealFutureHouseResearchAgent, ResearchQuery
from writers_room.core_system.autogen_writers_room_6agents import FixedAutoGenWritersRoom, WritersRoomInput


def extract_research_brief_from_agent6(agent6_output: str) -> Dict[str, List[str]]:
    """Extract research requirements from Agent 6 output"""
    
    research_brief = {}
    
    # Look for Research Intelligence Brief section
    research_section = re.search(
        r'### RESEARCH INTELLIGENCE BRIEF(.*?)(?=###|$)', 
        agent6_output, 
        re.DOTALL | re.IGNORECASE
    )
    
    if research_section:
        research_text = research_section.group(1)
        
        # Extract Research Topic Focus
        topic_match = re.search(r'\*\*Research Topic Focus\*\*:\s*\[(.*?)\]', research_text)
        if topic_match:
            research_brief['topic_focus'] = [topic_match.group(1)]
        
        # Extract Fact Categories Needed  
        facts_match = re.search(r'\*\*Fact Categories Needed\*\*:\s*\[(.*?)\]', research_text)
        if facts_match:
            research_brief['fact_categories'] = [facts_match.group(1)]
        
        # Extract Authority Sources
        sources_match = re.search(r'\*\*Authority Sources\*\*:\s*\[(.*?)\]', research_text)
        if sources_match:
            research_brief['authority_sources'] = [sources_match.group(1)]
        
        # Extract Viral Content Gaps
        gaps_match = re.search(r'\*\*Viral Content Gaps\*\*:\s*\[(.*?)\]', research_text)
        if gaps_match:
            research_brief['viral_gaps'] = [gaps_match.group(1)]
    
    # Fallback if no research brief found
    if not research_brief:
        research_brief = {
            'topic_focus': ["Historical water conflicts and engineering"],
            'fact_categories': ["Engineering statistics", "Battle outcomes", "Economic impacts"],
            'authority_sources': ["Archaeological studies", "Historical records"],
            'viral_gaps': ["Shocking death tolls", "Engineering failures", "Modern parallels"]
        }
    
    return research_brief


def conduct_research_intelligence(research_brief: Dict[str, List[str]], topic: str) -> str:
    """Use REAL FutureHouse API to gather rich research content"""
    
    print("🔬 CONDUCTING REAL FUTUREHOUSE RESEARCH INTELLIGENCE...")
    
    try:
        # Create REAL research agent
        research_agent = RealFutureHouseResearchAgent()
        print("✅ REAL FutureHouse API connected successfully!")
    except Exception as e:
        print(f"⚠️ FutureHouse API unavailable: {e}")
        print("📚 Falling back to mock research agent...")
        
        # Fallback to mock research agent
        from writers_room.research_intelligence import FutureHouseResearchAgent
        research_agent = FutureHouseResearchAgent()
    
    # Prepare research query
    focus_areas = (
        research_brief.get('topic_focus', []) + 
        research_brief.get('fact_categories', []) +
        research_brief.get('viral_gaps', [])
    )
    
    # Create query compatible with both research agents
    query = ResearchQuery(
        topic=topic,
        focus_areas=focus_areas[:5],  # Limit to top 5 areas
        depth_level="comprehensive"
    )
    
    # Execute research
    result = research_agent.research_topic(query)
    
    print(f"✅ Research complete: {len(result.facts)} facts, {len(result.quotes)} quotes")
    
    return result.research_brief


def run_complete_workflow(topic: str, golden_nuggets: List[str], 
                         mock_agent6_output: str = None) -> Dict[str, Any]:
    """Run the complete workflow: Agent 6 → Research → Writers' Room"""
    
    print("🚀 STARTING COMPLETE WORKFLOW")
    print("=" * 60)
    
    start_time = time.time()
    
    # Step 1: Extract research requirements (normally from Agent 6)
    print("📋 STEP 1: Extract Research Requirements")
    if mock_agent6_output:
        research_brief = extract_research_brief_from_agent6(mock_agent6_output)
    else:
        # Mock research brief for testing
        research_brief = {
            'topic_focus': ["Roman aqueduct engineering failures", "Sasanid water control military strategies"],
            'fact_categories': ["Engineering statistics", "Battle death tolls", "Economic collapse data"],
            'authority_sources': ["Archaeological studies", "Military historians"],
            'viral_gaps': ["How many people died in water wars", "Specific engineering failures"]
        }
    
    print(f"✅ Research brief extracted: {len(research_brief)} categories")
    
    # Step 2: Conduct research intelligence
    print("\n🔬 STEP 2: Conduct Research Intelligence")
    research_content = conduct_research_intelligence(research_brief, topic)
    
    # Step 3: Feed rich content to Writers' Room
    print("\n🎬 STEP 3: Writers' Room with Rich Content")
    
    writers_room = FixedAutoGenWritersRoom(model='gpt-3.5-turbo', test_mode=True)
    
    input_data = WritersRoomInput(
        topic=topic,
        golden_nuggets=golden_nuggets,
        persona={'target_audience': 'History enthusiasts and viral content consumers'},
        research_context=research_content,  # RICH CONTENT FROM RESEARCH
        target_length='8-10 minutes',
        max_rounds=20,  # Reduced for testing
        quality_threshold=8.0
    )
    
    # Generate script with rich content
    result = writers_room.run_collaborative_session(input_data)
    
    # Step 4: Save results
    print("\n💾 STEP 4: Save Complete Results")
    
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    filename = f'outputs/complete_workflow/RESEARCH_ENHANCED_{timestamp}.json'
    
    complete_result = {
        'workflow_version': 'Agent6_Research_WritersRoom_v1.0',
        'topic': topic,
        'research_brief': research_brief,
        'research_content_length': len(research_content),
        'writers_room_result': {
            'final_script': result.final_script,
            'structure': result.structure,
            'hooks': result.hooks,
            'quality_score': result.collaboration_metrics.final_quality_score,
            'execution_time': result.execution_time
        },
        'total_workflow_time': time.time() - start_time,
        'timestamp': timestamp
    }
    
    # Ensure output directory exists
    import os
    os.makedirs('outputs/complete_workflow', exist_ok=True)
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(complete_result, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Complete workflow saved: {filename}")
    print(f"📊 Final Quality Score: {result.collaboration_metrics.final_quality_score}")
    print(f"⏱️ Total Time: {time.time() - start_time:.1f}s")
    
    return complete_result


if __name__ == "__main__":
    # Test the complete workflow
    topic = "The 400-Year Water War That Decided Two Empires: Rome vs Sasanid Empire"
    golden_nuggets = [
        "Roman aqueducts were engineering marvels that supplied cities with fresh water",
        "Sasanid Empire controlled the Euphrates and Tigris rivers through advanced irrigation", 
        "Water control was crucial for empire survival and expansion",
        "Battles over river access shaped 400 years of conflict",
        "Climate change accelerated the downfall of both empires",
        "Modern Phoenix water crisis mirrors ancient water management failures"
    ]
    
    result = run_complete_workflow(topic, golden_nuggets)
    
    print("\n🎉 WORKFLOW COMPLETE!")
    print(f"Script length: {len(result['writers_room_result']['final_script'])} characters")
    print(f"Quality improvement with research: {result['writers_room_result']['quality_score']}/10")