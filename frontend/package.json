{"name": "youtube-research-v2-frontend", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.3.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "swr": "^2.2.4", "socket.io-client": "^4.7.2", "axios": "^1.6.2", "recharts": "^2.10.3", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "react-hot-toast": "^2.4.1", "clsx": "^2.0.0", "date-fns": "^2.30.0"}, "devDependencies": {"@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "prettier": "^3.1.1", "@typescript-eslint/parser": "^6.15.0", "@typescript-eslint/eslint-plugin": "^6.15.0"}}