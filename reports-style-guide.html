<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Report Generator - Developer Guide</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e1b4b 100%);
            color: #e2e8f0;
            line-height: 1.6;
        }
        
        .code-block {
            background: #0f172a;
            border: 1px solid #334155;
            border-radius: 8px;
            overflow-x: auto;
        }
        
        .highlight {
            background: linear-gradient(90deg, #8b5cf6, #a855f7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .section-card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid #475569;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        
        .feature-icon {
            background: linear-gradient(135deg, #8b5cf6, #a855f7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .step-number {
            background: linear-gradient(135deg, #8b5cf6, #a855f7);
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }
        
        pre {
            background: #0f172a;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .keyword { color: #a855f7; }
        .string { color: #22d3ee; }
        .comment { color: #64748b; font-style: italic; }
        .function { color: #f59e0b; }
        .variable { color: #10b981; }
        
        .architecture-diagram {
            background: rgba(15, 23, 42, 0.9);
            border: 2px solid #475569;
            border-radius: 12px;
            padding: 20px;
        }
        
        .flow-arrow {
            color: #8b5cf6;
            font-size: 20px;
            margin: 0 10px;
        }
        
        .data-flow {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        
        .flow-box {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid #8b5cf6;
            border-radius: 8px;
            padding: 10px 15px;
            font-weight: 500;
            min-width: 120px;
            text-align: center;
        }
        
        @media print {
            body { background: white; color: black; }
            .section-card { background: white; border: 1px solid #ddd; }
            .highlight { color: #8b5cf6; }
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="container mx-auto px-6 py-8 max-w-6xl">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-5xl font-bold mb-4">
                <span class="highlight">Dynamic Report Generator</span>
            </h1>
            <p class="text-xl text-gray-300 mb-6">Complete Developer Guide for Adaptive AI Analysis Reports</p>
            <div class="flex justify-center items-center space-x-8 text-sm text-gray-400">
                <div class="flex items-center">
                    <i class="fas fa-code mr-2 feature-icon"></i>
                    JavaScript/HTML/CSS
                </div>
                <div class="flex items-center">
                    <i class="fas fa-mobile-alt mr-2 feature-icon"></i>
                    Responsive Design
                </div>
                <div class="flex items-center">
                    <i class="fas fa-chart-bar mr-2 feature-icon"></i>
                    Data-Driven Layouts
                </div>
            </div>
        </div>

        <!-- Architecture Overview -->
        <div class="section-card rounded-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 flex items-center">
                <i class="fas fa-sitemap mr-3 feature-icon"></i>
                System Architecture
            </h2>
            
            <div class="architecture-diagram mb-6">
                <h3 class="text-xl font-semibold mb-4 text-center">Data Flow Pipeline</h3>
                <div class="data-flow">
                    <div class="flow-box">AI Analysis Data</div>
                    <i class="fas fa-arrow-right flow-arrow"></i>
                    <div class="flow-box">Data Parser</div>
                    <i class="fas fa-arrow-right flow-arrow"></i>
                    <div class="flow-box">Layout Engine</div>
                    <i class="fas fa-arrow-right flow-arrow"></i>
                    <div class="flow-box">Dynamic HTML</div>
                </div>
            </div>
            
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-slate-800 p-6 rounded-lg">
                    <h4 class="text-lg font-semibold mb-3 text-purple-400">Input Data Types</h4>
                    <ul class="space-y-2 text-sm">
                        <li><i class="fas fa-chart-line mr-2"></i>Performance Metrics</li>
                        <li><i class="fas fa-file-alt mr-2"></i>Script Analysis</li>
                        <li><i class="fas fa-search mr-2"></i>SEO Data</li>
                        <li><i class="fas fa-users mr-2"></i>Audience Psychology</li>
                        <li><i class="fas fa-comments mr-2"></i>Comment Intelligence</li>
                    </ul>
                </div>
                <div class="bg-slate-800 p-6 rounded-lg">
                    <h4 class="text-lg font-semibold mb-3 text-purple-400">Output Components</h4>
                    <ul class="space-y-2 text-sm">
                        <li><i class="fas fa-th-large mr-2"></i>Metrics Dashboard</li>
                        <li><i class="fas fa-chart-bar mr-2"></i>Visual Charts</li>
                        <li><i class="fas fa-list mr-2"></i>Insight Cards</li>
                        <li><i class="fas fa-lightbulb mr-2"></i>Recommendations</li>
                        <li><i class="fas fa-trophy mr-2"></i>Performance Grades</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Core Implementation -->
        <div class="section-card rounded-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 flex items-center">
                <i class="fas fa-cogs mr-3 feature-icon"></i>
                Core Implementation
            </h2>
            
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 flex items-center">
                    <div class="step-number mr-3">1</div>
                    Data Structure Parser
                </h3>
                <p class="text-gray-300 mb-4">First, create a parser that analyzes incoming data and determines the optimal layout structure.</p>
                
                <div class="code-block">
                    <pre><code><span class="keyword">class</span> <span class="function">ReportDataParser</span> {
    <span class="keyword">constructor</span>(<span class="variable">rawData</span>) {
        <span class="keyword">this</span>.<span class="variable">data</span> = <span class="variable">rawData</span>;
        <span class="keyword">this</span>.<span class="variable">sections</span> = [];
        <span class="keyword">this</span>.<span class="variable">layoutType</span> = <span class="string">'default'</span>;
    }

    <span class="function">analyzeDataStructure</span>() {
        <span class="comment">// Determine data types and content density</span>
        <span class="keyword">const</span> <span class="variable">metrics</span> = <span class="keyword">this</span>.<span class="function">extractMetrics</span>();
        <span class="keyword">const</span> <span class="variable">textContent</span> = <span class="keyword">this</span>.<span class="function">extractTextContent</span>();
        <span class="keyword">const</span> <span class="variable">recommendations</span> = <span class="keyword">this</span>.<span class="function">extractRecommendations</span>();
        
        <span class="comment">// Determine layout based on content types</span>
        <span class="keyword">if</span> (<span class="variable">metrics</span>.<span class="variable">length</span> > <span class="string">6</span>) {
            <span class="keyword">this</span>.<span class="variable">layoutType</span> = <span class="string">'metrics-heavy'</span>;
        } <span class="keyword">else if</span> (<span class="variable">textContent</span>.<span class="variable">wordCount</span> > <span class="string">1000</span>) {
            <span class="keyword">this</span>.<span class="variable">layoutType</span> = <span class="string">'text-heavy'</span>;
        } <span class="keyword">else</span> {
            <span class="keyword">this</span>.<span class="variable">layoutType</span> = <span class="string">'balanced'</span>;
        }
        
        <span class="keyword">return</span> <span class="keyword">this</span>.<span class="function">generateSectionConfig</span>();
    }

    <span class="function">extractMetrics</span>() {
        <span class="comment">// Parse numerical data and performance indicators</span>
        <span class="keyword">const</span> <span class="variable">metrics</span> = [];
        <span class="keyword">const</span> <span class="variable">patterns</span> = [
            <span class="string">/(\d+(?:\.\d+)?)\s*%/g</span>, <span class="comment">// Percentages</span>
            <span class="string">/(\d{1,3}(?:,\d{3})*(?:\.\d+)?)/g</span>, <span class="comment">// Numbers with commas</span>
            <span class="string">/(\d+(?:\.\d+)?)\s*(views|likes|comments)/gi</span> <span class="comment">// Engagement metrics</span>
        ];
        
        <span class="comment">// Extract and categorize metrics</span>
        <span class="keyword">for</span> (<span class="keyword">const</span> <span class="variable">pattern</span> <span class="keyword">of</span> <span class="variable">patterns</span>) {
            <span class="keyword">const</span> <span class="variable">matches</span> = <span class="keyword">this</span>.<span class="variable">data</span>.<span class="function">match</span>(<span class="variable">pattern</span>);
            <span class="keyword">if</span> (<span class="variable">matches</span>) {
                <span class="variable">metrics</span>.<span class="function">push</span>(...<span class="variable">matches</span>);
            }
        }
        
        <span class="keyword">return</span> <span class="variable">metrics</span>;
    }
}</code></pre>
                </div>
            </div>

            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 flex items-center">
                    <div class="step-number mr-3">2</div>
                    Dynamic Layout Engine
                </h3>
                <p class="text-gray-300 mb-4">Create a layout engine that adapts the visual structure based on content analysis.</p>
                
                <div class="code-block">
                    <pre><code><span class="keyword">class</span> <span class="function">DynamicLayoutEngine</span> {
    <span class="keyword">constructor</span>(<span class="variable">dataParser</span>) {
        <span class="keyword">this</span>.<span class="variable">parser</span> = <span class="variable">dataParser</span>;
        <span class="keyword">this</span>.<span class="variable">layouts</span> = {
            <span class="string">'metrics-heavy'</span>: <span class="keyword">this</span>.<span class="function">createMetricsDashboard</span>,
            <span class="string">'text-heavy'</span>: <span class="keyword">this</span>.<span class="function">createTextFocusedLayout</span>,
            <span class="string">'balanced'</span>: <span class="keyword">this</span>.<span class="function">createBalancedLayout</span>
        };
    }

    <span class="function">generateLayout</span>() {
        <span class="keyword">const</span> <span class="variable">config</span> = <span class="keyword">this</span>.<span class="variable">parser</span>.<span class="function">analyzeDataStructure</span>();
        <span class="keyword">const</span> <span class="variable">layoutFunction</span> = <span class="keyword">this</span>.<span class="variable">layouts</span>[<span class="keyword">this</span>.<span class="variable">parser</span>.<span class="variable">layoutType</span>];
        
        <span class="keyword">return</span> <span class="variable">layoutFunction</span>.<span class="function">call</span>(<span class="keyword">this</span>, <span class="variable">config</span>);
    }

    <span class="function">createMetricsDashboard</span>(<span class="variable">config</span>) {
        <span class="keyword">return</span> <span class="string">`
            &lt;div class="metrics-dashboard"&gt;
                &lt;div class="hero-metrics grid grid-cols-2 md:grid-cols-4 gap-4 mb-8"&gt;
                    ${config.primaryMetrics.map(metric =&gt; `
                        &lt;div class="metric-card"&gt;
                            &lt;div class="metric-value"&gt;${metric.value}&lt;/div&gt;
                            &lt;div class="metric-label"&gt;${metric.label}&lt;/div&gt;
                            &lt;div class="metric-trend ${metric.trend}"&gt;
                                &lt;i class="fas fa-arrow-${metric.trend === 'up' ? 'up' : 'down'}"&gt;&lt;/i&gt;
                                ${metric.change}
                            &lt;/div&gt;
                        &lt;/div&gt;
                    `).join('')}
                &lt;/div&gt;
                ${this.generateSecondaryContent(config)}
            &lt;/div&gt;
        `</span>;
    }

    <span class="function">createTextFocusedLayout</span>(<span class="variable">config</span>) {
        <span class="keyword">return</span> <span class="string">`
            &lt;div class="text-focused-layout"&gt;
                &lt;div class="summary-section mb-8"&gt;
                    ${this.generateExecutiveSummary(config)}
                &lt;/div&gt;
                &lt;div class="content-sections space-y-6"&gt;
                    ${config.sections.map(section =&gt; `
                        &lt;div class="expandable-section"&gt;
                            &lt;h3 class="section-header" onclick="toggleSection('${section.id}')"&gt;
                                &lt;i class="fas fa-chevron-right transition-transform"&gt;&lt;/i&gt;
                                ${section.title}
                            &lt;/h3&gt;
                            &lt;div id="${section.id}" class="section-content"&gt;
                                ${this.formatTextContent(section.content)}
                            &lt;/div&gt;
                        &lt;/div&gt;
                    `).join('')}
                &lt;/div&gt;
            &lt;/div&gt;
        `</span>;
    }
}</code></pre>
                </div>
            </div>

            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 flex items-center">
                    <div class="step-number mr-3">3</div>
                    Responsive Component System
                </h3>
                <p class="text-gray-300 mb-4">Build reusable components that adapt to different screen sizes and content lengths.</p>
                
                <div class="code-block">
                    <pre><code><span class="keyword">class</span> <span class="function">ResponsiveComponents</span> {
    <span class="keyword">static</span> <span class="function">createMetricCard</span>(<span class="variable">data</span>) {
        <span class="keyword">const</span> <span class="variable">trendIcon</span> = <span class="variable">data</span>.<span class="variable">trend</span> === <span class="string">'positive'</span> ? <span class="string">'fa-arrow-up text-green-400'</span> : <span class="string">'fa-arrow-down text-red-400'</span>;
        <span class="keyword">const</span> <span class="variable">cardSize</span> = <span class="variable">data</span>.<span class="variable">importance</span> === <span class="string">'high'</span> ? <span class="string">'col-span-2'</span> : <span class="string">'col-span-1'</span>;
        
        <span class="keyword">return</span> <span class="string">`
            &lt;div class="metric-card ${cardSize} bg-slate-800 p-6 rounded-lg border border-slate-600"&gt;
                &lt;div class="flex items-center justify-between mb-2"&gt;
                    &lt;h4 class="text-sm font-medium text-gray-400"&gt;${data.label}&lt;/h4&gt;
                    &lt;i class="fas ${trendIcon}"&gt;&lt;/i&gt;
                &lt;/div&gt;
                &lt;div class="text-2xl font-bold text-white mb-1"&gt;${data.value}&lt;/div&gt;
                &lt;div class="text-xs text-gray-500"&gt;${data.description}&lt;/div&gt;
                ${data.benchmark ? `
                    &lt;div class="mt-3 bg-slate-700 rounded-full h-2"&gt;
                        &lt;div class="bg-purple-500 h-2 rounded-full" style="width: ${data.benchmarkPercentage}%"&gt;&lt;/div&gt;
                    &lt;/div&gt;
                    &lt;div class="text-xs text-gray-400 mt-1"&gt;vs. benchmark: ${data.benchmark}&lt;/div&gt;
                ` : ''}
            &lt;/div&gt;
        `</span>;
    }

    <span class="keyword">static</span> <span class="function">createInsightCard</span>(<span class="variable">insight</span>) {
        <span class="keyword">const</span> <span class="variable">iconMap</span> = {
            <span class="string">'performance'</span>: <span class="string">'fa-chart-line'</span>,
            <span class="string">'audience'</span>: <span class="string">'fa-users'</span>,
            <span class="string">'content'</span>: <span class="string">'fa-file-alt'</span>,
            <span class="string">'seo'</span>: <span class="string">'fa-search'</span>
        };
        
        <span class="keyword">return</span> <span class="string">`
            &lt;div class="insight-card bg-slate-800 p-6 rounded-lg border border-slate-600 hover:border-purple-500 transition-colors"&gt;
                &lt;div class="flex items-start space-x-4"&gt;
                    &lt;div class="bg-purple-500 p-3 rounded-lg"&gt;
                        &lt;i class="fas ${iconMap[insight.category]} text-white"&gt;&lt;/i&gt;
                    &lt;/div&gt;
                    &lt;div class="flex-1"&gt;
                        &lt;h4 class="text-lg font-semibold text-white mb-2"&gt;${insight.title}&lt;/h4&gt;
                        &lt;p class="text-gray-300 mb-3"&gt;${insight.description}&lt;/p&gt;
                        ${insight.recommendations ? `
                            &lt;div class="recommendations"&gt;
                                &lt;h5 class="text-sm font-medium text-purple-400 mb-2"&gt;Recommendations:&lt;/h5&gt;
                                &lt;ul class="text-sm text-gray-300 space-y-1"&gt;
                                    ${insight.recommendations.map(rec =&gt; `
                                        &lt;li class="flex items-start"&gt;
                                            &lt;i class="fas fa-chevron-right text-purple-400 mr-2 mt-1 text-xs"&gt;&lt;/i&gt;
                                            ${rec}
                                        &lt;/li&gt;
                                    `).join('')}
                                &lt;/ul&gt;
                            &lt;/div&gt;
                        ` : ''}
                    &lt;/div&gt;
                &lt;/div&gt;
            &lt;/div&gt;
        `</span>;
    }
}</code></pre>
                </div>
            </div>
        </div>

        <!-- Styling System -->
        <div class="section-card rounded-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 flex items-center">
                <i class="fas fa-palette mr-3 feature-icon"></i>
                Dynamic Styling System
            </h2>
            
            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-4">CSS Custom Properties for Theming</h3>
                <p class="text-gray-300 mb-4">Use CSS custom properties to create adaptable themes that match your app's design system.</p>
                
                <div class="code-block">
                    <pre><code><span class="comment">/* Dynamic theme variables */</span>
<span class="keyword">:root</span> {
    <span class="variable">--primary-bg</span>: <span class="string">#1e1b4b</span>;
    <span class="variable">--secondary-bg</span>: <span class="string">#1e293b</span>;
    <span class="variable">--card-bg</span>: <span class="string">rgba(30, 41, 59, 0.8)</span>;
    <span class="variable">--text-primary</span>: <span class="string">#e2e8f0</span>;
    <span class="variable">--text-secondary</span>: <span class="string">#94a3b8</span>;
    <span class="variable">--accent-color</span>: <span class="string">#8b5cf6</span>;
    <span class="variable">--success-color</span>: <span class="string">#22c55e</span>;
    <span class="variable">--warning-color</span>: <span class="string">#f59e0b</span>;
    <span class="variable">--error-color</span>: <span class="string">#ef4444</span>;
}

<span class="comment">/* Responsive grid system */</span>
<span class="keyword">.dynamic-grid</span> {
    <span class="variable">display</span>: <span class="variable">grid</span>;
    <span class="variable">gap</span>: <span class="string">1.5rem</span>;
    <span class="variable">grid-template-columns</span>: <span class="function">repeat</span>(<span class="variable">auto-fit</span>, <span class="function">minmax</span>(<span class="string">300px</span>, <span class="string">1fr</span>));
}

<span class="comment">/* Content-aware layouts */</span>
<span class="keyword">.content-heavy</span> {
    <span class="variable">grid-template-columns</span>: <span class="string">1fr</span>;
    <span class="variable">max-width</span>: <span class="string">800px</span>;
    <span class="variable">margin</span>: <span class="string">0 auto</span>;
}

<span class="keyword">.metrics-heavy</span> {
    <span class="variable">grid-template-columns</span>: <span class="function">repeat</span>(<span class="variable">auto-fit</span>, <span class="function">minmax</span>(<span class="string">250px</span>, <span class="string">1fr</span>));
}

<span class="keyword">.balanced-layout</span> {
    <span class="variable">grid-template-columns</span>: <span class="function">repeat</span>(<span class="variable">auto-fit</span>, <span class="function">minmax</span>(<span class="string">350px</span>, <span class="string">1fr</span>));
}</code></pre>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-4">JavaScript Styling Controller</h3>
                <p class="text-gray-300 mb-4">Dynamically apply styles based on content analysis and user preferences.</p>
                
                <div class="code-block">
                    <pre><code><span class="keyword">class</span> <span class="function">DynamicStyleController</span> {
    <span class="keyword">constructor</span>() {
        <span class="keyword">this</span>.<span class="variable">baseStyles</span> = {
            <span class="string">'dark'</span>: {
                <span class="string">'--primary-bg'</span>: <span class="string">'#1e1b4b'</span>,
                <span class="string">'--card-bg'</span>: <span class="string">'rgba(30, 41, 59, 0.8)'</span>,
                <span class="string">'--text-primary'</span>: <span class="string">'#e2e8f0'</span>
            },
            <span class="string">'light'</span>: {
                <span class="string">'--primary-bg'</span>: <span class="string">'#f8fafc'</span>,
                <span class="string">'--card-bg'</span>: <span class="string">'rgba(255, 255, 255, 0.9)'</span>,
                <span class="string">'--text-primary'</span>: <span class="string">'#1e293b'</span>
            }
        };
    }

    <span class="function">applyContentBasedStyles</span>(<span class="variable">contentType</span>, <span class="variable">density</span>) {
        <span class="keyword">const</span> <span class="variable">root</span> = <span class="variable">document</span>.<span class="variable">documentElement</span>;
        
        <span class="comment">// Adjust spacing based on content density</span>
        <span class="keyword">if</span> (<span class="variable">density</span> === <span class="string">'high'</span>) {
            <span class="variable">root</span>.<span class="variable">style</span>.<span class="function">setProperty</span>(<span class="string">'--section-spacing'</span>, <span class="string">'1rem'</span>);
            <span class="variable">root</span>.<span class="variable">style</span>.<span class="function">setProperty</span>(<span class="string">'--card-padding'</span>, <span class="string">'1rem'</span>);
        } <span class="keyword">else</span> {
            <span class="variable">root</span>.<span class="variable">style</span>.<span class="function">setProperty</span>(<span class="string">'--section-spacing'</span>, <span class="string">'2rem'</span>);
            <span class="variable">root</span>.<span class="variable">style</span>.<span class="function">setProperty</span>(<span class="string">'--card-padding'</span>, <span class="string">'1.5rem'</span>);
        }
        
        <span class="comment">// Adjust typography based on content type</span>
        <span class="keyword">switch</span> (<span class="variable">contentType</span>) {
            <span class="keyword">case</span> <span class="string">'metrics'</span>:
                <span class="variable">root</span>.<span class="variable">style</span>.<span class="function">setProperty</span>(<span class="string">'--heading-size'</span>, <span class="string">'1.25rem'</span>);
                <span class="variable">root</span>.<span class="variable">style</span>.<span class="function">setProperty</span>(<span class="string">'--metric-size'</span>, <span class="string">'2.5rem'</span>);
                <span class="keyword">break</span>;
            <span class="keyword">case</span> <span class="string">'text'</span>:
                <span class="variable">root</span>.<span class="variable">style</span>.<span class="function">setProperty</span>(<span class="string">'--heading-size'</span>, <span class="string">'1.875rem'</span>);
                <span class="variable">root</span>.<span class="variable">style</span>.<span class="function">setProperty</span>(<span class="string">'--line-height'</span>, <span class="string">'1.7'</span>);
                <span class="keyword">break</span>;
        }
    }

    <span class="function">generateResponsiveClasses</span>(<span class="variable">screenSize</span>) {
        <span class="keyword">const</span> <span class="variable">classes</span> = [];
        
        <span class="keyword">if</span> (<span class="variable">screenSize</span> === <span class="string">'mobile'</span>) {
            <span class="variable">classes</span>.<span class="function">push</span>(<span class="string">'grid-cols-1'</span>, <span class="string">'text-sm'</span>, <span class="string">'p-4'</span>);
        } <span class="keyword">else if</span> (<span class="variable">screenSize</span> === <span class="string">'tablet'</span>) {
            <span class="variable">classes</span>.<span class="function">push</span>(<span class="string">'grid-cols-2'</span>, <span class="string">'text-base'</span>, <span class="string">'p-6'</span>);
        } <span class="keyword">else</span> {
            <span class="variable">classes</span>.<span class="function">push</span>(<span class="string">'grid-cols-3'</span>, <span class="string">'text-base'</span>, <span class="string">'p-8'</span>);
        }
        
        <span class="keyword">return</span> <span class="variable">classes</span>.<span class="function">join</span>(<span class="string">' '</span>);
    }
}</code></pre>
                </div>
            </div>
        </div>

        <!-- Data Processing Pipeline -->
        <div class="section-card rounded-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 flex items-center">
                <i class="fas fa-database mr-3 feature-icon"></i>
                Data Processing Pipeline
            </h2>
            
            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-4">AI Report Text Parser</h3>
                <p class="text-gray-300 mb-4">Convert unstructured AI analysis text into structured data for dynamic rendering.</p>
                
                <div class="code-block">
                    <pre><code><span class="keyword">class</span> <span class="function">AIReportParser</span> {
    <span class="keyword">constructor</span>(<span class="variable">rawText</span>) {
        <span class="keyword">this</span>.<span class="variable">rawText</span> = <span class="variable">rawText</span>;
        <span class="keyword">this</span>.<span class="variable">sections</span> = {};
        <span class="keyword">this</span>.<span class="variable">metrics</span> = [];
        <span class="keyword">this</span>.<span class="variable">insights</span> = [];
        <span class="keyword">this</span>.<span class="variable">recommendations</span> = [];
    }

    <span class="function">parseReport</span>() {
        <span class="comment">// Extract main sections using headers</span>
        <span class="keyword">const</span> <span class="variable">sectionPattern</span> = <span class="string">/###\s*([A-Z\s]+)\s*([\s\S]*?)(?=###|$)/g</span>;
        <span class="keyword">let</span> <span class="variable">match</span>;
        
        <span class="keyword">while</span> ((<span class="variable">match</span> = <span class="variable">sectionPattern</span>.<span class="function">exec</span>(<span class="keyword">this</span>.<span class="variable">rawText</span>)) !== <span class="keyword">null</span>) {
            <span class="keyword">const</span> [, <span class="variable">title</span>, <span class="variable">content</span>] = <span class="variable">match</span>;
            <span class="keyword">this</span>.<span class="variable">sections</span>[<span class="variable">title</span>.<span class="function">trim</span>()] = <span class="variable">content</span>.<span class="function">trim</span>();
        }
        
        <span class="comment">// Parse specific data types</span>
        <span class="keyword">this</span>.<span class="function">extractMetrics</span>();
        <span class="keyword">this</span>.<span class="function">extractInsights</span>();
        <span class="keyword">this</span>.<span class="function">extractRecommendations</span>();
        
        <span class="keyword">return</span> {
            <span class="variable">sections</span>: <span class="keyword">this</span>.<span class="variable">sections</span>,
            <span class="variable">metrics</span>: <span class="keyword">this</span>.<span class="variable">metrics</span>,
            <span class="variable">insights</span>: <span class="keyword">this</span>.<span class="variable">insights</span>,
            <span class="variable">recommendations</span>: <span class="keyword">this</span>.<span class="variable">recommendations</span>
        };
    }

    <span class="function">extractMetrics</span>() {
        <span class="comment">// Patterns for different metric types</span>
        <span class="keyword">const</span> <span class="variable">patterns</span> = {
            <span class="variable">percentage</span>: <span class="string">/([a-zA-Z\s]+):\s*(\d+(?:\.\d+)?)\s*%/g</span>,
            <span class="variable">count</span>: <span class="string">/([a-zA-Z\s]+):\s*(\d{1,3}(?:,\d{3})*(?:\.\d+)?)/g</span>,
            <span class="variable">grade</span>: <span class="string">/([a-zA-Z\s]+):\s*([A-F][+-]?)/g</span>,
            <span class="variable">tier</span>: <span class="string">/([a-zA-Z\s]+):\s*(Top\s+\d+%)/g</span>
        };
        
        <span class="keyword">for</span> (<span class="keyword">const</span> [<span class="variable">type</span>, <span class="variable">pattern</span>] <span class="keyword">of</span> <span class="variable">Object</span>.<span class="function">entries</span>(<span class="variable">patterns</span>)) {
            <span class="keyword">let</span> <span class="variable">match</span>;
            <span class="keyword">while</span> ((<span class="variable">match</span> = <span class="variable">pattern</span>.<span class="function">exec</span>(<span class="keyword">this</span>.<span class="variable">rawText</span>)) !== <span class="keyword">null</span>) {
                <span class="keyword">this</span>.<span class="variable">metrics</span>.<span class="function">push</span>({
                    <span class="variable">label</span>: <span class="variable">match</span>[<span class="string">1</span>].<span class="function">trim</span>(),
                    <span class="variable">value</span>: <span class="variable">match</span>[<span class="string">2</span>],
                    <span class="variable">type</span>: <span class="variable">type</span>,
                    <span class="variable">trend</span>: <span class="keyword">this</span>.<span class="function">determineTrend</span>(<span class="variable">match</span>[<span class="string">0</span>])
                });
            }
        }
    }

    <span class="function">extractInsights</span>() {
        <span class="comment">// Look for insight patterns</span>
        <span class="keyword">const</span> <span class="variable">insightPatterns</span> = [
            <span class="string">/\d+\.\s*([^:]+):\s*([^-]+)\s*-\s*([^.]+)\./g</span>,
            <span class="string">/\*\*([^*]+)\*\*:\s*([^*]+)/g</span>
        ];
        
        <span class="variable">insightPatterns</span>.<span class="function">forEach</span>(<span class="variable">pattern</span> => {
            <span class="keyword">let</span> <span class="variable">match</span>;
            <span class="keyword">while</span> ((<span class="variable">match</span> = <span class="variable">pattern</span>.<span class="function">exec</span>(<span class="keyword">this</span>.<span class="variable">rawText</span>)) !== <span class="keyword">null</span>) {
                <span class="keyword">this</span>.<span class="variable">insights</span>.<span class="function">push</span>({
                    <span class="variable">title</span>: <span class="variable">match</span>[<span class="string">1</span>].<span class="function">trim</span>(),
                    <span class="variable">description</span>: <span class="variable">match</span>[<span class="string">2</span>].<span class="function">trim</span>(),
                    <span class="variable">category</span>: <span class="keyword">this</span>.<span class="function">categorizeInsight</span>(<span class="variable">match</span>[<span class="string">1</span>])
                });
            }
        });
    }
}</code></pre>
                </div>
            </div>
        </div>

        <!-- Integration Guide -->
        <div class="section-card rounded-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 flex items-center">
                <i class="fas fa-plug mr-3 feature-icon"></i>
                Integration Guide
            </h2>
            
            <div class="grid md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-xl font-semibold mb-4">Backend Integration</h3>
                    <div class="bg-slate-800 p-4 rounded-lg mb-4">
                        <h4 class="font-medium mb-2 text-purple-400">API Endpoint Structure</h4>
                        <div class="code-block">
                            <pre><code><span class="comment">// POST /api/generate-report</span>
{
  <span class="string">"analysisType"</span>: <span class="string">"video_performance"</span>,
  <span class="string">"rawData"</span>: <span class="string">"... AI analysis text ..."</span>,
  <span class="string">"theme"</span>: <span class="string">"dark"</span>,
  <span class="string">"layout"</span>: <span class="string">"auto"</span>
}

<span class="comment">// Response</span>
{
  <span class="string">"html"</span>: <span class="string">"... generated HTML ..."</span>,
  <span class="string">"css"</span>: <span class="string">"... custom CSS ..."</span>,
  <span class="string">"metadata"</span>: {
    <span class="string">"layoutType"</span>: <span class="string">"metrics-heavy"</span>,
    <span class="string">"sectionsCount"</span>: <span class="string">5</span>
  }
}</code></pre>
                        </div>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-xl font-semibold mb-4">Frontend Implementation</h3>
                    <div class="bg-slate-800 p-4 rounded-lg mb-4">
                        <h4 class="font-medium mb-2 text-purple-400">React Component Example</h4>
                        <div class="code-block">
                            <pre><code><span class="keyword">const</span> <span class="function">DynamicReport</span> = ({ <span class="variable">reportData</span> }) => {
  <span class="keyword">const</span> [<span class="variable">html</span>, <span class="function">setHtml</span>] = <span class="function">useState</span>(<span class="string">''</span>);
  <span class="keyword">const</span> [<span class="variable">loading</span>, <span class="function">setLoading</span>] = <span class="function">useState</span>(<span class="keyword">true</span>);

  <span class="function">useEffect</span>(() => {
    <span class="keyword">const</span> <span class="function">generateReport</span> = <span class="keyword">async</span> () => {
      <span class="keyword">const</span> <span class="variable">response</span> = <span class="keyword">await</span> <span class="function">fetch</span>(<span class="string">'/api/generate-report'</span>, {
        <span class="variable">method</span>: <span class="string">'POST'</span>,
        <span class="variable">body</span>: <span class="function">JSON</span>.<span class="function">stringify</span>(<span class="variable">reportData</span>)
      });
      
      <span class="keyword">const</span> <span class="variable">result</span> = <span class="keyword">await</span> <span class="variable">response</span>.<span class="function">json</span>();
      <span class="function">setHtml</span>(<span class="variable">result</span>.<span class="variable">html</span>);
      <span class="function">setLoading</span>(<span class="keyword">false</span>);
    };

    <span class="function">generateReport</span>();
  }, [<span class="variable">reportData</span>]);

  <span class="keyword">return</span> (
    &lt;<span class="variable">div</span> 
      <span class="variable">dangerouslySetInnerHTML</span>={{ <span class="variable">__html</span>: <span class="variable">html</span> }}
      <span class="variable">className</span>=<span class="string">"dynamic-report"</span>
    /&gt;
  );
};</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Performance Optimization -->
        <div class="section-card rounded-lg p-8 mb-8">
            <h2 class="text-3xl font-bold mb-6 flex items-center">
                <i class="fas fa-rocket mr-3 feature-icon"></i>
                Performance Optimization
            </h2>
            
            <div class="grid md:grid-cols-3 gap-6">
                <div class="bg-slate-800 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold mb-3 flex items-center">
                        <i class="fas fa-tachometer-alt mr-2 text-green-400"></i>
                        Loading Optimization
                    </h3>
                    <ul class="text-sm space-y-2">
                        <li>• Lazy load non-critical sections</li>
                        <li>• Use skeleton screens during generation</li>
                        <li>• Cache frequently used layouts</li>
                        <li>• Optimize image loading</li>
                        <li>• Minimize DOM manipulation</li>
                    </ul>
                </div>
                
                <div class="bg-slate-800 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold mb-3 flex items-center">
                        <i class="fas fa-mobile-alt mr-2 text-blue-400"></i>
                        Mobile Optimization
                    </h3>
                    <ul class="text-sm space-y-2">
                        <li>• Touch-friendly interaction areas</li>
                        <li>• Simplified layouts for small screens</li>
                        <li>• Optimized font sizes</li>
                        <li>• Reduced animation complexity</li>
                        <li>• Efficient scroll handling</li>
                    </ul>
                </div>
                
                <div class="bg-slate-800 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold mb-3 flex items-center">
                        <i class="fas fa-file-pdf mr-2 text-red-400"></i>
                        PDF Export Ready
                    </h3>
                    <ul class="text-sm space-y-2">
                        <li>• Single continuous page layout</li>
                        <li>• Print-optimized CSS</li>
                        <li>• Proper page break handling</li>
                        <li>• High contrast for printing</li>
                        <li>• Vector-based graphics</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Implementation Checklist -->
        <div class="section-card rounded-lg p-8">
            <h2 class="text-3xl font-bold mb-6 flex items-center">
                <i class="fas fa-tasks mr-3 feature-icon"></i>
                Implementation Checklist
            </h2>
            
            <div class="grid md:grid-cols-2 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-purple-400">Core Features</h3>
                    <div class="space-y-3">
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-purple-500">
                            <span>Data structure parser implementation</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-purple-500">
                            <span>Dynamic layout engine setup</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-purple-500">
                            <span>Responsive component system</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-purple-500">
                            <span>Theme and styling controller</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-purple-500">
                            <span>AI text parsing algorithms</span>
                        </label>
                    </div>
                </div>
                
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-purple-400">Advanced Features</h3>
                    <div class="space-y-3">
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-purple-500">
                            <span>Interactive chart generation</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-purple-500">
                            <span>Performance optimization</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-purple-500">
                            <span>PDF export optimization</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-purple-500">
                            <span>Accessibility compliance</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-5 w-5 text-purple-500">
                            <span>Cross-browser testing</span>
                        </label>
                    </div>
                </div>
            </div>
            
            <div class="mt-8 p-6 bg-purple-900 bg-opacity-30 rounded-lg border border-purple-500">
                <h3 class="text-lg font-semibold mb-3 flex items-center">
                    <i class="fas fa-lightbulb mr-2 text-yellow-400"></i>
                    Pro Tips for Success
                </h3>
                <div class="grid md:grid-cols-2 gap-4 text-sm">
                    <ul class="space-y-2">
                        <li>• Start with the most common report types first</li>
                        <li>• Build comprehensive test cases with various data sizes</li>
                        <li>• Create fallback layouts for edge cases</li>
                    </ul>
                    <ul class="space-y-2">
                        <li>• Monitor performance with large datasets</li>
                        <li>• Implement progressive enhancement</li>
                        <li>• Document all layout patterns for maintenance</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Interactive functionality for the guide
        function toggleSection(sectionId) {
            const section = document.getElementById(sectionId);
            const header = section.previousElementSibling.querySelector('i');
            
            if (section.style.display === 'none' || !section.style.display) {
                section.style.display = 'block';
                header.classList.add('rotate-90');
            } else {
                section.style.display = 'none';
                header.classList.remove('rotate-90');
            }
        }

        // Smooth scrolling for internal links
        document.addEventListener('DOMContentLoaded', function() {
            const links = document.querySelectorAll('a[href^="#"]');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });
        });

        // Syntax highlighting enhancement
        function enhanceSyntaxHighlighting() {
            const codeBlocks = document.querySelectorAll('pre code');
            codeBlocks.forEach(block => {
                let html = block.innerHTML;
                
                // Enhanced syntax highlighting patterns
                html = html.replace(/\b(class|function|const|let|var|if|else|for|while|return|async|await)\b/g, '<span class="keyword">$1</span>');
                html = html.replace(/'([^']*)'/g, '<span class="string">\'$1\'</span>');
                html = html.replace(/"([^"]*)"/g, '<span class="string">"$1"</span>');
                html = html.replace(/\/\/(.*?)$/gm, '<span class="comment">//$1</span>');
                html = html.replace(/\/\*([\s\S]*?)\*\//g, '<span class="comment">/*$1*/</span>');
                
                block.innerHTML = html;
            });
        }

        // Initialize enhancements
        enhanceSyntaxHighlighting();
    </script>
</body>
</html>
