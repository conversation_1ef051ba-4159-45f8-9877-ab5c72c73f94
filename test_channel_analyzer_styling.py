#!/usr/bin/env python3
"""
Test Channel Analyzer Styling and Layout
"""

import requests
import time
import subprocess
import sys
import os
import signal
from datetime import datetime

class ChannelAnalyzerTester:
    def __init__(self):
        self.base_url = "http://localhost:8003"
        self.server_process = None
    
    def start_server(self, timeout=30):
        """Start the main application server"""
        print("🚀 Starting server...")
        try:
            # Start server in background
            self.server_process = subprocess.Popen(
                [sys.executable, "working_crewai_app_tabbed.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )
            
            # Wait for server to be ready
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=5)
                    if response.status_code == 200:
                        print("✅ Server started successfully")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)
                    continue
            
            print("❌ Server failed to start within timeout")
            return False
            
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return False
    
    def stop_server(self):
        """Stop the server"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                print("🛑 Server stopped")
            except subprocess.TimeoutExpired:
                self.server_process.kill()
                print("🛑 Server force killed")
            except Exception as e:
                print(f"⚠️ Error stopping server: {e}")
    
    def test_channel_analyzer_page(self):
        """Test if the channel analyzer page loads with proper styling"""
        print("\n📄 Testing Channel Analyzer Page...")
        try:
            response = requests.get(f"{self.base_url}/channel-analyzer", timeout=10)
            if response.status_code == 200:
                print("✅ Channel Analyzer page loads successfully")
                
                # Check for key CSS classes in the HTML
                required_classes = [
                    'analyzer-results',
                    'channel-overview', 
                    'channel-stats',
                    'stat-card',
                    'video-card',
                    'videos-grid'
                ]
                
                missing_classes = []
                for css_class in required_classes:
                    if css_class not in response.text:
                        missing_classes.append(css_class)
                
                if missing_classes:
                    print(f"⚠️ Missing CSS classes in HTML: {missing_classes}")
                else:
                    print("✅ All required CSS classes found in HTML")
                
                # Check if CSS file is referenced
                if 'youtube-research-v2-design-system.css' in response.text:
                    print("✅ Design system CSS file is referenced")
                else:
                    print("❌ Design system CSS file not found")
                
                return len(missing_classes) == 0
            else:
                print(f"❌ Page failed to load: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error loading page: {e}")
            return False
    
    def test_css_file_availability(self):
        """Test if the CSS file is accessible and contains required styles"""
        print("\n🎨 Testing CSS File Availability...")
        try:
            response = requests.get(f"{self.base_url}/static/styles/youtube-research-v2-design-system.css", timeout=10)
            if response.status_code == 200:
                print("✅ CSS file is accessible")
                
                # Check for key CSS rules
                required_styles = [
                    '.analyzer-results',
                    '.channel-overview',
                    '.channel-stats',
                    '.stat-card',
                    '.video-card',
                    '.videos-grid',
                    '.schedule-analysis-results',
                    '.title-analysis-results'
                ]
                
                missing_styles = []
                css_content = response.text
                
                for style in required_styles:
                    if style not in css_content:
                        missing_styles.append(style)
                
                if missing_styles:
                    print(f"❌ Missing CSS styles: {missing_styles}")
                    return False
                else:
                    print("✅ All required CSS styles found")
                    return True
            else:
                print(f"❌ CSS file not accessible: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error accessing CSS file: {e}")
            return False
    
    def run_tests(self):
        """Run all tests"""
        print("🧪 Channel Analyzer Styling Test")
        print("=" * 50)
        
        # Start server
        if not self.start_server():
            return False
        
        try:
            # Test page loading
            page_test = self.test_channel_analyzer_page()
            
            # Test CSS availability
            css_test = self.test_css_file_availability()
            
            # Summary
            print(f"\n📊 Test Results:")
            print(f"   Page Loading: {'✅ PASS' if page_test else '❌ FAIL'}")
            print(f"   CSS Availability: {'✅ PASS' if css_test else '❌ FAIL'}")
            
            if page_test and css_test:
                print(f"\n🎉 All tests passed! Channel Analyzer styling is working correctly.")
                return True
            else:
                print(f"\n⚠️ Some tests failed. Channel analyzer styling needs attention.")
                return False
                
        finally:
            self.stop_server()

if __name__ == "__main__":
    tester = ChannelAnalyzerTester()
    success = tester.run_tests()
    sys.exit(0 if success else 1)
