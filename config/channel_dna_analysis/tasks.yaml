extract_channel_profile:
  description: |
    Create a comprehensive, multi-dimensional channel profile that goes beyond basic metrics to reveal the strategic 
    architecture and growth dynamics of this YouTube channel. Your analysis must include:
    
    **Core Channel Architecture:**
    - Complete channel metadata analysis (name, description, branding consistency, optimization signals)
    - Subscriber acquisition patterns with month-over-month growth rate analysis and inflection point identification
    - View velocity metrics including average views per video, view-to-subscriber ratios, and traffic source distribution
    - Upload cadence analysis with consistency scoring and seasonal pattern identification
    - Channel evolution timeline mapping major content strategy shifts and their impact on growth
    
    **Advanced Performance Indicators:**
    - Growth trajectory classification (exponential, linear, declining, stagnant, or cyclical)
    - Engagement health scoring based on like-to-view, comment-to-view, and share-to-view ratios
    - Audience retention quality indicators including repeat viewing patterns and subscriber loyalty metrics
    - Revenue optimization signals from content monetization patterns and sponsor integration strategies
    - Cross-platform presence analysis and traffic cross-pollination effects
    
    **Strategic Context Analysis:**
    - Channel positioning within its niche hierarchy and market maturity assessment
    - Creator authority indicators including expertise demonstration, audience trust signals, and thought leadership markers
    - Community building effectiveness through subscriber engagement quality and parasocial relationship strength
    - Content strategy evolution patterns showing how the channel adapted to algorithm changes and market shifts
    
    Your analysis should identify specific examples from the channel's content that illustrate each major finding, 
    comparing performance metrics to relevant benchmarks within the channel's niche and subscriber tier.
  expected_output: |
    A 3-4 paragraph comprehensive channel profile report that synthesizes quantitative metrics with strategic insights. 
    The report must include specific video examples, statistical benchmarks, growth pattern analysis, and actionable 
    optimization recommendations. Each paragraph should focus on a different dimension: growth trajectory and health, 
    content strategy effectiveness, audience relationship quality, and strategic positioning with competitive context.
  agent: channel_profiler

analyze_content_patterns:
  description: |
    Conduct an exhaustive analysis of the channel's content ecosystem to reveal both obvious and hidden patterns that 
    drive engagement and growth. Your analysis must decode the strategic architecture behind content decisions and 
    identify optimization opportunities that the creator may not consciously recognize:
    
    **Content Taxonomy and Theme Analysis:**
    - Map complete content categorization system with primary, secondary, and tertiary themes
    - Identify content pillars and their performance hierarchy (which themes drive 80% of engagement)
    - Analyze topic evolution patterns and trend adaptation strategies
    - Reveal content seasonality patterns and cyclical theme rotation
    - Identify "gateway content" that converts casual viewers to subscribers at exceptional rates
    
    **Format Strategy Deconstruction:**
    - Catalog all content formats used (tutorials, vlogs, reviews, interviews, etc.) with performance analysis
    - Identify format-topic combinations that consistently overperform vs. underperform
    - Analyze video length strategies and their correlation with different content types and audience retention
    - Examine storytelling structures and narrative arc patterns across different video types
    - Identify signature format innovations that differentiate this channel from competitors
    
    **Optimization Pattern Recognition:**
    - Decode title formulas and psychological triggers used across top-performing videos
    - Analyze thumbnail design patterns including visual elements, text usage, and emotional expressions
    - Identify posting schedule patterns and their correlation with performance metrics
    - Examine series architecture and playlist strategies including sequence optimization
    - Reveal cross-video linking strategies and content ecosystem navigation design
    
    **Strategic Content Intelligence:**
    - Identify unconscious content patterns that drive success (creator may not realize they're doing this)
    - Analyze content velocity patterns (ideation-to-publication cycles) and their impact on topical relevance
    - Map content decay patterns (how long different content types remain relevant and discoverable)
    - Identify content cannibalization risks where multiple videos compete for the same audience attention
    
    For each pattern identified, provide specific examples from the channel's library and explain the strategic 
    reasoning behind why these patterns work for this specific audience and niche.
  expected_output: |
    A 3-4 paragraph strategic content pattern analysis that reveals the underlying content architecture driving 
    channel success. First paragraph: content taxonomy and theme hierarchy with specific examples of top-performing 
    content pillars. Second paragraph: format strategy analysis with format-performance correlations and signature 
    innovations. Third paragraph: optimization patterns across titles, thumbnails, and scheduling with psychological 
    trigger analysis. Fourth paragraph: strategic recommendations for content pattern optimization including 
    identification of underexploited patterns and content gap opportunities.
  agent: content_pattern_analyzer

analyze_audience_psychology:
  description: |
    Decode the complex psychological dynamics between this creator and their audience community to reveal the emotional 
    contracts, identity formation patterns, and behavioral triggers that drive deep engagement and loyalty. Your analysis 
    must uncover both conscious and unconscious psychological mechanisms at work:
    
    **Parasocial Relationship Architecture:**
    - Analyze the specific psychological bond between creator and audience (mentor, friend, aspirational figure, etc.)
    - Identify vulnerability displays and authenticity signals that strengthen parasocial connections
    - Map audience identity validation patterns (how viewers use this content to construct their self-image)
    - Examine community belonging signals and tribal identity formation around the channel
    - Identify the "psychological contract" - unspoken expectations between creator and audience
    
    **Engagement Psychology Mapping:**
    - Decode comment sentiment flows and emotional journey patterns across different content types
    - Analyze behavioral triggers that convert passive viewers into active community participants
    - Identify gamification elements and social proof mechanisms driving engagement behaviors
    - Map audience emotional needs hierarchy (entertainment, education, validation, inspiration, etc.)
    - Examine fear-of-missing-out (FOMO) and social belonging drivers in community participation
    
    **Audience Segmentation Psychology:**
    - Identify distinct psychological profiles within the audience community
    - Analyze how different audience segments engage with content differently
    - Map viewer lifecycle psychology (casual → regular → superfan conversion triggers)
    - Identify audience pain points, aspirations, and identity gaps the channel addresses
    - Examine cross-generational audience dynamics and different psychological needs by demographic
    
    **Retention and Loyalty Psychology:**
    - Analyze specific content elements that trigger neurochemical engagement responses
    - Identify habit formation patterns in viewing behavior and community participation
    - Map emotional investment escalation (what makes viewers care more over time)
    - Examine creator authority establishment and trust-building psychological mechanisms
    - Identify potential psychological fatigue risks and community burnout prevention strategies
    
    Provide specific examples from actual comments, engagement patterns, and community interactions that illustrate 
    each psychological dynamic. Include analysis of what competing channels in the niche do differently in terms of 
    audience psychology management.
  expected_output: |
    A 3-4 paragraph deep psychological analysis of the creator-audience relationship that reveals the emotional and 
    behavioral drivers behind community engagement. First paragraph: parasocial relationship architecture and identity 
    validation patterns with specific community behavior examples. Second paragraph: engagement psychology triggers 
    and conversion mechanisms that transform passive viewers into active community members. Third paragraph: audience 
    segmentation psychology and demographic-specific needs fulfillment. Fourth paragraph: strategic recommendations 
    for strengthening psychological bonds and building more engaged, loyal community while avoiding manipulation or 
    exploitation of psychological vulnerabilities.
  agent: audience_psychologist

evaluate_performance_metrics:
  description: |
    Conduct a comprehensive, algorithmically-informed analysis of video performance patterns to identify the precise 
    mathematical relationships between content variables and success outcomes. Your analysis must reveal both surface-level 
    metrics and the deeper algorithmic signals that drive YouTube's recommendation system decisions:
    
    **Algorithmic Performance Intelligence:**
    - Analyze click-through rates (CTR) across different thumbnail and title combinations with statistical significance testing
    - Examine audience retention curves to identify specific retention patterns that trigger algorithmic amplification
    - Evaluate traffic source distribution and identify which discovery methods yield highest-value viewers
    - Map engagement velocity patterns (likes/comments/shares in first 24-48 hours) and their correlation with long-term performance
    - Identify algorithmic ranking factors specific to this channel's niche and audience behavior patterns
    
    **Performance Pattern Deconstruction:**
    - Analyze view-to-subscriber ratios across content types to identify which formats best convert casual viewers
    - Map engagement rate patterns (like ratios, comment density, share rates) and their relationship to content themes
    - Identify viral content DNA - the specific combination of factors that creates breakout performance
    - Examine seasonal performance patterns and identify optimal timing strategies for different content types
    - Analyze underperformance patterns to identify content strategy blind spots and optimization opportunities
    
    **Success Factor Mathematical Modeling:**
    - Create performance prediction models based on title, thumbnail, topic, and timing variables
    - Identify leading indicators of viral potential in the first hours after publication
    - Map the compound effects of consistent performance patterns on overall channel growth velocity
    - Analyze cross-video performance correlation (how one video's success impacts subsequent video performance)
    - Identify content strategy inflection points where performance patterns changed significantly
    
    **Revenue Optimization Analysis:**
    - Correlate performance metrics with monetization effectiveness across different content types
    - Analyze sponsor integration performance and identify optimal integration strategies
    - Examine long-tail performance patterns and identify evergreen content opportunities
    - Map audience lifetime value indicators based on engagement and retention patterns
    - Identify content ROI optimization opportunities based on production effort vs. performance outcomes
    
    For each performance insight, provide specific video examples with quantitative data, benchmarking against both 
    the channel's historical performance and niche-relevant competitors. Include predictive insights about which 
    performance strategies will remain effective as the YouTube algorithm continues evolving.
  expected_output: |
    A 3-4 paragraph algorithmic performance analysis that reveals the mathematical drivers of video success and provides 
    predictive insights for optimization. First paragraph: algorithmic performance patterns including CTR, retention, 
    and traffic source analysis with specific high-performing video examples. Second paragraph: engagement pattern 
    analysis and viral content DNA identification with quantitative benchmarks. Third paragraph: performance prediction 
    modeling and success factor correlation analysis. Fourth paragraph: strategic performance optimization recommendations 
    including revenue correlation insights and algorithm-evolution-proof strategies.
  agent: performance_analyst

assess_competitive_position:
  description: |
    Conduct comprehensive competitive intelligence analysis to map the complete strategic landscape surrounding this 
    channel, identifying not just who the competitors are, but their strategic vulnerabilities, market positioning 
    gaps, and opportunities for sustainable competitive advantage. Your analysis must reveal multi-dimensional 
    competitive dynamics that most creators never recognize:
    
    **Competitive Landscape Mapping:**
    - Identify and categorize direct competitors (same niche, similar audience) and indirect competitors (competing for attention)
    - Analyze competitor content strategies, upload frequencies, and audience engagement patterns
    - Map competitor growth trajectories and identify which channels are gaining vs. losing market share
    - Examine competitor monetization strategies and revenue diversification approaches
    - Identify emerging threats from new channels or format innovations that could disrupt the niche
    
    **Strategic Positioning Analysis:**
    - Decode each major competitor's unique value proposition and audience positioning strategy
    - Identify competitor content gaps and underserved audience needs within the niche
    - Analyze competitor brand positioning, personality archetypes, and audience relationship styles
    - Map competitor collaboration networks and strategic partnership patterns
    - Examine competitor cross-platform strategies and audience migration patterns
    
    **Market Opportunity Intelligence:**
    - Identify "blue ocean" opportunities where no major competitor is effectively serving audience needs
    - Analyze content format gaps where successful formats from adjacent niches could be adapted
    - Map seasonal or trending topic opportunities that competitors are consistently missing
    - Identify audience segments that are underserved by existing competitors
    - Examine emerging platform opportunities (YouTube Shorts, Community posts, etc.) and competitor adoption rates
    
    **Competitive Advantage Development:**
    - Identify this channel's existing competitive moats and how defensible they are
    - Analyze competitor weaknesses and strategic blind spots that create opportunity windows
    - Map potential collaboration opportunities with non-competing channels that share audience overlap
    - Identify content strategy pivots that could create differentiation advantages
    - Examine network effects and community building approaches that could create sustainable competitive advantages
    
    **Competitive Threat Assessment:**
    - Evaluate which competitors pose the greatest long-term threat and why
    - Analyze competitor innovation rates and their ability to adapt to platform changes
    - Identify potential competitive responses to this channel's growth strategies
    - Map competitive collaboration risks (competitors working together against this channel)
    - Assess niche maturity level and whether consolidation pressures are increasing
    
    Provide specific examples of competitor content, strategies, and positioning choices, with quantitative analysis 
    of their effectiveness. Include predictive analysis of how competitive dynamics might evolve based on platform 
    changes and market trends.
  expected_output: |
    A 3-4 paragraph strategic competitive intelligence report that reveals positioning opportunities and competitive 
    advantages within the market landscape. First paragraph: competitive landscape overview with major players 
    categorization and market share analysis including specific competitor examples and positioning strategies. 
    Second paragraph: strategic gap analysis identifying blue ocean opportunities and underserved audience segments 
    with quantitative market sizing. Third paragraph: competitive advantage assessment including this channel's 
    defensive moats and competitor vulnerability analysis. Fourth paragraph: strategic recommendations for market 
    positioning, differentiation strategies, and competitive advantage development including specific tactical 
    implementations and risk mitigation strategies.
  agent: competitor_analyst