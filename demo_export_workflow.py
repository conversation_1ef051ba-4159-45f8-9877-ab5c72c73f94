#!/usr/bin/env python3
"""
Live Demo of Export & Reporting Suite Workflow
"""
import requests
import json

def demo_complete_workflow():
    """Demonstrate the complete export workflow"""
    
    print("🎬 LIVE DEMO: Export & Reporting Suite Workflow")
    print("=" * 60)
    
    # Step 1: Analyze a channel (what user does in UI)
    print("📊 Step 1: Analyzing MrBeast channel...")
    channel_request = {
        "channel_id": "@MrBeast",
        "max_videos": 3  # Small sample for demo
    }
    
    response = requests.post("http://localhost:8003/api/tools/channel_profile", json=channel_request)
    if response.status_code != 200:
        print("❌ Failed to get channel data")
        return
    
    channel_data = response.json()
    channel_name = channel_data['channel_profile']['title']
    video_count = len(channel_data.get('video_library', []))
    subscriber_count = channel_data['channel_profile']['statistics']['subscriberCount']
    
    print(f"✅ Analyzed: {channel_name}")
    print(f"   📈 Subscribers: {int(subscriber_count):,}")
    print(f"   🎥 Videos analyzed: {video_count}")
    
    # Step 2: Demonstrate each export format
    print(f"\n📥 Step 2: Demonstrating export formats...")
    
    # JSON Export Demo
    print(f"\n🔹 JSON Export Demo:")
    json_export = {
        "data": channel_data,
        "format_type": "json",
        "filename": f"{channel_name}_analysis"
    }
    
    json_response = requests.post("http://localhost:8003/api/export/channel", json=json_export)
    if json_response.status_code == 200:
        json_data = json.loads(json_response.content.decode('utf-8'))
        print(f"   ✅ JSON Export: {len(json_response.content)} bytes")
        print(f"   📋 Contains: export_metadata, channel_profile, video_library, summary")
        print(f"   🎯 Use case: API integration, detailed analysis")
        
        # Show sample of the analysis summary
        summary = json_data.get('summary', {})
        if summary.get('performance_metrics'):
            metrics = summary['performance_metrics']
            print(f"   📊 Sample insight: Avg {metrics['average_views_per_video']:,} views/video")
    
    # CSV Export Demo  
    print(f"\n🔹 CSV Export Demo:")
    csv_export = json_export.copy()
    csv_export["format_type"] = "csv"
    
    csv_response = requests.post("http://localhost:8003/api/export/channel", json=csv_export)
    if csv_response.status_code == 200:
        csv_content = csv_response.content.decode('utf-8')
        lines = csv_content.split('\n')[:10]  # First 10 lines
        print(f"   ✅ CSV Export: {len(csv_response.content)} bytes")
        print(f"   📋 Contains: Channel overview + video library in spreadsheet format")
        print(f"   🎯 Use case: Excel analysis, data visualization")
        print(f"   📄 Sample content:")
        for line in lines:
            if line.strip():
                print(f"      {line}")
    
    # PDF Export Demo
    print(f"\n🔹 PDF/Report Export Demo:")
    pdf_export = json_export.copy()
    pdf_export["format_type"] = "pdf"
    
    pdf_response = requests.post("http://localhost:8003/api/export/channel", json=pdf_export)
    if pdf_response.status_code == 200:
        pdf_content = pdf_response.content.decode('utf-8')
        lines = pdf_content.split('\n')[:15]  # First 15 lines
        print(f"   ✅ PDF Export: {len(pdf_response.content)} bytes")
        print(f"   📋 Contains: Executive summary with top videos")
        print(f"   🎯 Use case: Client presentations, reports")
        print(f"   📄 Sample content:")
        for line in lines:
            if line.strip():
                print(f"      {line}")
    
    # Step 3: Show format comparison
    print(f"\n📊 Step 3: Format Comparison Summary:")
    print(f"┌─────────────────┬──────────────────┬─────────────────────────────────────┐")
    print(f"│ Format          │ File Size        │ Best For                            │")
    print(f"├─────────────────┼──────────────────┼─────────────────────────────────────┤")
    print(f"│ JSON            │ {len(json_response.content):,} bytes       │ Developers, API integration        │")
    print(f"│ CSV             │ {len(csv_response.content):,} bytes        │ Spreadsheet analysis, data viz     │")
    print(f"│ PDF/Report      │ {len(pdf_response.content):,} bytes        │ Client presentations, executives   │")
    print(f"└─────────────────┴──────────────────┴─────────────────────────────────────┘")
    
    print(f"\n🎉 Export & Reporting Suite Demo Complete!")
    print(f"💼 Business Value: Professional multi-format exports justify $99/month pricing")

if __name__ == "__main__":
    demo_complete_workflow()