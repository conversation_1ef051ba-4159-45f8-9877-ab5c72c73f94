#!/usr/bin/env python3
"""
Quick verification test to check current status
"""

import requests
import json

def test_quick_verification():
    """Quick test to see current AI insights status"""
    print("🔍 Quick Verification Test")
    print("=" * 40)
    
    try:
        response = requests.post(
            "http://localhost:8003/api/market-research",
            json={
                "query": "javascript",
                "time_range": "week",
                "max_results": 1,
                "sort_by": "relevance"
            },
            timeout=45
        )
        
        if response.status_code == 200:
            data = response.json()
            ai_insights = data.get('ai_insights', {})
            
            print("📊 AI Insights Status:")
            for insight_type in ['market_summary', 'success_patterns', 'content_strategy', 
                               'growth_opportunities', 'market_risks', 'recommendations']:
                content = ai_insights.get(insight_type, 'MISSING')
                if isinstance(content, str):
                    length = len(content)
                    status = "✅ REAL" if length > 1000 else "⚠️ SHORT" if length > 50 else "❌ FALLBACK"
                    print(f"   {insight_type}: {status} ({length} chars)")
                elif isinstance(content, list):
                    print(f"   {insight_type}: ✅ LIST ({len(content)} items)")
                else:
                    print(f"   {insight_type}: ❌ {type(content)}")
            
            # Check subscriber data
            comp_analysis = data.get('competition_analysis', {})
            top_performers = comp_analysis.get('top_performers', [])
            if top_performers:
                sub_count = top_performers[0].get('subscriber_count', 0)
                print(f"\n📊 Subscriber Data: {'✅ WORKING' if sub_count > 0 else '❌ FAILED'} ({sub_count:,})")
            
        else:
            print(f"❌ API Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Test Failed: {e}")

if __name__ == "__main__":
    test_quick_verification()
