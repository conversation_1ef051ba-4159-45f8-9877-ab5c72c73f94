<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Styling Test</title>
    <link rel="stylesheet" href="static/styles/youtube-research-v2-design-system.css">
    <style>
        body {
            margin: 0;
            padding: var(--space-lg);
            background: var(--bg-primary);
            font-family: var(--font-primary);
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: var(--space-2xl);
        }
        
        .test-title {
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--space-lg);
            padding-bottom: var(--space-sm);
            border-bottom: 2px solid var(--purple-primary);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--text-primary); margin-bottom: var(--space-2xl);">Dashboard Styling Test</h1>
        
        <!-- Test Metric Cards -->
        <div class="test-section">
            <h2 class="test-title">Metric Cards</h2>
            <div style="display: flex; gap: var(--space-lg);">
                <div class="metric-card">
                    <div class="stat-value">127</div>
                    <div class="stat-label">Analyses this month</div>
                    <div class="stat-trend up">↑ 23% from last month</div>
                </div>
                <div class="metric-card">
                    <div class="stat-value">4.8</div>
                    <div class="stat-label">Avg. video score</div>
                    <div class="stat-trend up">↑ 0.3 points</div>
                </div>
            </div>
        </div>
        
        <!-- Test Analysis Cards -->
        <div class="test-section">
            <h2 class="test-title">Analysis Cards</h2>
            <div class="analysis-card">
                <div class="analysis-card-header">
                    <div class="analysis-card-icon">🎬</div>
                    <div class="analysis-card-title-section">
                        <h3 class="analysis-card-title">Video Analyzer</h3>
                        <p class="analysis-card-subtitle">Deep dive analysis</p>
                    </div>
                </div>
                <div class="analysis-card-content">
                    <p>Professional video analysis with AI agents.</p>
                </div>
            </div>
        </div>
        
        <!-- Test Progress Bar -->
        <div class="test-section">
            <h2 class="test-title">Progress Bar</h2>
            <div style="background: var(--bg-elevated); padding: var(--space-lg); border-radius: var(--radius-lg); border: 1px solid var(--border-light);">
                <div style="display: flex; justify-content: space-between; margin-bottom: var(--space-xs);">
                    <span>Analyses Used</span>
                    <span>127 / 500</span>
                </div>
                <div style="width: 100%; height: 8px; background: var(--bg-secondary); border-radius: var(--radius-full); overflow: hidden;">
                    <div style="height: 100%; background: var(--purple-gradient); border-radius: var(--radius-full); width: 25.4%; transition: width var(--transition-slow);"></div>
                </div>
            </div>
        </div>
        
        <!-- Test Activity Items -->
        <div class="test-section">
            <h2 class="test-title">Activity Items</h2>
            <div style="display: flex; flex-direction: column; gap: var(--space-sm);">
                <div class="activity-item">
                    <div class="activity-thumbnail"></div>
                    <div class="activity-details">
                        <h4>How I Make $100k/Month with AI</h4>
                        <p style="font-size: var(--text-sm); color: var(--text-secondary); margin: 0;">Analyzed 2 hours ago • Score: 4.8/5</p>
                    </div>
                </div>
                <div class="activity-item">
                    <div class="activity-thumbnail"></div>
                    <div class="activity-details">
                        <h4>The Truth About Dropshipping in 2025</h4>
                        <p style="font-size: var(--text-sm); color: var(--text-secondary); margin: 0;">Analyzed 5 hours ago • Score: 4.2/5</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Theme Toggle -->
        <div class="test-section">
            <h2 class="test-title">Theme Toggle</h2>
            <button onclick="toggleTheme()" class="btn btn-primary">Toggle Light/Dark Theme</button>
            <p style="margin-top: var(--space-md); color: var(--text-secondary);">Click to test light/dark theme compatibility</p>
        </div>
    </div>
    
    <script>
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            html.setAttribute('data-theme', newTheme);
        }
    </script>
</body>
</html>
