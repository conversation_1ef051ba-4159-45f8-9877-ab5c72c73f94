#!/usr/bin/env python3
"""
Test script to verify the critical bug fixes:
1. Subscriber data collection working
2. Recommendations processing not breaking the API
"""

import requests
import json
import time

def test_market_research_fixes():
    """Test both critical fixes"""
    print("🧪 Testing Critical Bug Fixes for Market Research Tool")
    print("=" * 60)
    
    # Test data
    test_queries = [
        {"query": "gaming tutorial", "max_results": 3},
        {"query": "cooking recipe", "max_results": 3},
        {"query": "fitness workout", "max_results": 3}
    ]
    
    base_url = "http://localhost:8003"
    
    for i, test_data in enumerate(test_queries, 1):
        print(f"\n🔍 Test {i}: {test_data['query']}")
        print("-" * 40)
        
        try:
            # Make API request
            response = requests.post(
                f"{base_url}/api/market-research",
                json={
                    "query": test_data["query"],
                    "time_range": "week",
                    "max_results": test_data["max_results"],
                    "sort_by": "relevance"
                },
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Test 1: Check if API request completes successfully
                print("✅ API Request: SUCCESS")
                
                # Test 2: Check subscriber data
                top_performers = data.get('competition_analysis', {}).get('top_performers', [])
                if top_performers:
                    subscriber_counts = [p.get('subscriber_count', 0) for p in top_performers]
                    non_zero_subs = [s for s in subscriber_counts if s > 0]
                    
                    if non_zero_subs:
                        print(f"✅ Subscriber Data: SUCCESS")
                        print(f"   📊 Found {len(non_zero_subs)} channels with subscriber data")
                        print(f"   📈 Subscriber counts: {[f'{s:,}' for s in non_zero_subs[:3]]}")
                    else:
                        print("❌ Subscriber Data: FAILED - All showing 0")
                        print(f"   📊 Subscriber counts: {subscriber_counts}")
                else:
                    print("❌ Subscriber Data: FAILED - No top_performers found")
                
                # Test 3: Check if AI insights are present
                ai_insights = data.get('ai_insights', {})
                if ai_insights:
                    print("✅ AI Insights: SUCCESS")
                    insight_keys = list(ai_insights.keys())
                    print(f"   🧠 Available insights: {len(insight_keys)} types")
                    
                    # Check recommendations specifically
                    recommendations = ai_insights.get('recommendations', [])
                    if recommendations:
                        print(f"   💡 Recommendations: {len(recommendations)} items")
                    else:
                        print("   ⚠️  Recommendations: Empty (but not breaking)")
                else:
                    print("❌ AI Insights: FAILED - No insights found")
                
                # Test 4: Check overall data structure
                required_keys = ['market_overview', 'competition_analysis', 'ai_insights']
                missing_keys = [key for key in required_keys if key not in data]
                
                if not missing_keys:
                    print("✅ Data Structure: SUCCESS")
                else:
                    print(f"❌ Data Structure: MISSING - {missing_keys}")
                    
            else:
                print(f"❌ API Request: FAILED - Status {response.status_code}")
                print(f"   Error: {response.text}")
                
        except requests.exceptions.Timeout:
            print("❌ API Request: TIMEOUT - Request took too long")
        except Exception as e:
            print(f"❌ API Request: ERROR - {e}")
        
        # Small delay between tests
        if i < len(test_queries):
            time.sleep(2)
    
    print("\n" + "=" * 60)
    print("🏁 Critical Bug Fix Testing Complete")
    print("\n📋 Summary:")
    print("   1. Subscriber data should show real numbers (not 0)")
    print("   2. API requests should complete successfully")
    print("   3. AI insights should be generated")
    print("   4. No 'Analysis Failed' errors in UI")

if __name__ == "__main__":
    test_market_research_fixes()
