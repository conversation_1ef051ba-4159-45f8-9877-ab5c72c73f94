<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Analyzer - Layout Templates for Designer</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        /* BASIC STYLING FOR PREVIEW - YOUR DESIGNER WILL REPLACE THIS */
        body {
            font-family: 'Inter', sans-serif;
            background: #0f0f23;
            color: #ffffff;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        
        .template-section {
            background: rgba(30, 41, 59, 0.8);
            border: 2px solid #8B5CF6;
            border-radius: 16px;
            padding: 32px;
            margin: 32px 0;
            position: relative;
        }
        
        .template-title {
            background: #8B5CF6;
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            position: absolute;
            top: -20px;
            left: 20px;
            font-weight: 700;
            font-size: 14px;
        }
        
        .placeholder {
            background: rgba(139, 92, 246, 0.1);
            border: 2px dashed #8B5CF6;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            text-align: center;
            color: #8B5CF6;
            font-weight: 600;
        }
        
        .grid-2 { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .grid-3 { display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; }
        .grid-4 { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; }
        
        @media (max-width: 768px) {
            .grid-2, .grid-3, .grid-4 { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #8B5CF6; margin-bottom: 40px;">
        📐 VIDEO ANALYZER LAYOUT TEMPLATES
    </h1>
    <p style="text-align: center; color: #94A3B8; margin-bottom: 60px;">
        Complete layout structures for your designer to customize
    </p>

    <!-- PERFORMANCE INTELLIGENCE TAB -->
    <div class="template-section">
        <div class="template-title">PERFORMANCE INTELLIGENCE TAB</div>
        
        <!-- Grade Hero Section -->
        <div class="placeholder">
            🏆 GRADE HERO SECTION<br>
            Grade Circle (A+) + Title + Subtitle + Badge<br>
            Data: grade, performance_tier, viral_potential
        </div>
        
        <!-- Metrics Grid -->
        <div class="grid-4">
            <div class="placeholder">
                📊 METRIC CARD<br>
                Icon + Trend + Value + Label<br>
                Views Per Day
            </div>
            <div class="placeholder">
                💙 METRIC CARD<br>
                Icon + Trend + Value + Label<br>
                Engagement Rate
            </div>
            <div class="placeholder">
                💬 METRIC CARD<br>
                Icon + Trend + Value + Label<br>
                Comment Rate
            </div>
            <div class="placeholder">
                ⏱️ METRIC CARD<br>
                Icon + Trend + Value + Label<br>
                Views/Minute
            </div>
        </div>
        
        <!-- Benchmark Analysis -->
        <div class="grid-2">
            <div class="placeholder">
                📈 BENCHMARK CARD<br>
                Your Performance vs YouTube Average<br>
                Progress bars or comparison charts
            </div>
            <div class="placeholder">
                🎯 BENCHMARK CARD<br>
                Engagement Analysis<br>
                Performance insights
            </div>
        </div>
        
        <!-- Recommendations -->
        <div class="placeholder">
            🚀 RECOMMENDATIONS SECTION<br>
            Numbered recommendation items (1, 2, 3...)<br>
            Each with title + description
        </div>
    </div>

    <!-- SCRIPT FORENSICS TAB -->
    <div class="template-section">
        <div class="template-title">SCRIPT FORENSICS TAB</div>
        
        <!-- Script Metrics -->
        <div class="grid-4">
            <div class="placeholder">
                📝 WORD COUNT<br>
                4,940 words<br>
                Status: Excellent
            </div>
            <div class="placeholder">
                🎤 SPEAKING PACE<br>
                189 WPM<br>
                Status: Fast
            </div>
            <div class="placeholder">
                ⏰ DURATION<br>
                26.1 min<br>
                Status: Optimal
            </div>
            <div class="placeholder">
                🧠 ENGAGEMENT<br>
                8.2/10<br>
                Status: High
            </div>
        </div>
        
        <!-- Psychological Triggers -->
        <div class="grid-2">
            <div class="placeholder">
                👁️ CURIOSITY & ANTICIPATION<br>
                Progress bars for:<br>
                • Mystery Building (Strong)<br>
                • Future Pacing (High)<br>
                • Cliffhangers (Moderate)
            </div>
            <div class="placeholder">
                👥 SOCIAL PROOF & AUTHORITY<br>
                Progress bars for:<br>
                • Expert References (High)<br>
                • Community Building (Present)<br>
                • Credibility Markers (Strong)
            </div>
        </div>
        
        <!-- Script Structure -->
        <div class="grid-3">
            <div class="placeholder">
                🎣 OPENING HOOK<br>
                Effectiveness: 95%<br>
                Pattern breakdown<br>
                Key elements
            </div>
            <div class="placeholder">
                📋 CONTENT FLOW<br>
                Logical Progression: 88%<br>
                Structure analysis<br>
                Transitions quality
            </div>
            <div class="placeholder">
                🎯 RETENTION ELEMENTS<br>
                Engagement Hooks: 92%<br>
                Techniques used<br>
                Frequency analysis
            </div>
        </div>
        
        <!-- Final Grade -->
        <div class="placeholder">
            🏆 SCRIPT GRADE HERO<br>
            A+ Grade Circle + Script Performance Description
        </div>
    </div>

    <!-- SEO & DISCOVERABILITY TAB -->
    <div class="template-section">
        <div class="template-title">SEO & DISCOVERABILITY TAB</div>
        
        <!-- SEO Score Overview -->
        <div class="placeholder">
            🎯 SEO GRADE HERO<br>
            B+ Grade + Overall Score Description<br>
            4 mini-metrics below (Title, Description, Keywords, Discoverability)
        </div>
        
        <!-- Keyword Analysis -->
        <div class="grid-2">
            <div class="placeholder">
                🔑 PRIMARY KEYWORDS<br>
                Keyword tags with volume indicators:<br>
                • Midjourney (High Volume)<br>
                • AI Video (Medium Volume)<br>
                • Video Comparison (Low Competition)
            </div>
            <div class="placeholder">
                🏷️ LONG-TAIL KEYWORDS<br>
                List with status indicators:<br>
                • Midjourney Video Deep Dive ✓<br>
                • AI Video Comparison Ultimate ✓<br>
                • Video Generation Tutorial ⚠️
            </div>
        </div>
        
        <!-- Content Optimization -->
        <div class="placeholder">
            ✏️ CONTENT OPTIMIZATION ANALYSIS<br>
            Two-column layout:<br>
            Title Analysis | Description Analysis<br>
            Character counts, keyword placement, etc.
        </div>
        
        <!-- Recommendations -->
        <div class="grid-2">
            <div class="placeholder">
                🔥 HIGH IMPACT RECOMMENDATIONS<br>
                Green-highlighted action items<br>
                3-4 high-priority optimizations
            </div>
            <div class="placeholder">
                ⚡ MEDIUM IMPACT RECOMMENDATIONS<br>
                Yellow-highlighted action items<br>
                3-4 medium-priority optimizations
            </div>
        </div>
    </div>

    <!-- AUDIENCE PSYCHOLOGY TAB -->
    <div class="template-section">
        <div class="template-title">AUDIENCE PSYCHOLOGY TAB</div>
        
        <!-- Psychology Metrics -->
        <div class="grid-4">
            <div class="placeholder">
                🤔 CURIOSITY GAP<br>
                8.5/10<br>
                Drives viewer interest
            </div>
            <div class="placeholder">
                👨‍💼 AUTHORITY SCORE<br>
                7.8/10<br>
                Expert positioning
            </div>
            <div class="placeholder">
                👥 SOCIAL PROOF<br>
                9.2/10<br>
                Community validation
            </div>
            <div class="placeholder">
                🎯 PERSONAL RELEVANCE<br>
                8.7/10<br>
                Individual connection
            </div>
        </div>
        
        <!-- Audience Motivations -->
        <div class="grid-2">
            <div class="placeholder">
                💡 KNOWLEDGE ACQUISITION<br>
                95% (High)<br>
                Learn new AI video techniques
            </div>
            <div class="placeholder">
                🏆 ACHIEVEMENT/STATUS<br>
                87% (High)<br>
                Master trending tools
            </div>
            <div class="placeholder">
                ⭐ ENTERTAINMENT VALUE<br>
                78% (Medium)<br>
                Engaging visual content
            </div>
            <div class="placeholder">
                🤝 SOCIAL CONNECTION<br>
                72% (Medium)<br>
                Connect with creators
            </div>
        </div>
        
        <!-- Psychological Triggers -->
        <div class="grid-2">
            <div class="placeholder">
                🎯 ACTIVE TRIGGERS<br>
                Progress bars:<br>
                • Curiosity Gaps (85%)<br>
                • Authority Positioning (78%)<br>
                • Social Proof (92%)<br>
                • FOMO Activation (67%)
            </div>
            <div class="placeholder">
                🧠 ENGAGEMENT PSYCHOLOGY<br>
                Insight cards:<br>
                • Hook Psychology<br>
                • Cognitive Load<br>
                • Interaction Psychology
            </div>
        </div>
        
        <!-- Demographics -->
        <div class="placeholder">
            👥 DEMOGRAPHICS & BEHAVIORAL PATTERNS<br>
            3-column stats: Age Range (25-35) | Gender (72% Male) | Mobile Viewing (89%)<br>
            Plus behavioral insights below
        </div>
    </div>

    <!-- COMMENT INTELLIGENCE TAB -->
    <div class="template-section">
        <div class="template-title">COMMENT INTELLIGENCE TAB</div>
        
        <!-- Comment Metrics -->
        <div class="grid-4">
            <div class="placeholder">
                📝 TOTAL COMMENTS<br>
                534<br>
                ↑ High Engagement
            </div>
            <div class="placeholder">
                😊 POSITIVE SENTIMENT<br>
                81%<br>
                Excellent Reception
            </div>
            <div class="placeholder">
                😐 NEUTRAL COMMENTS<br>
                15%<br>
                Balanced Feedback
            </div>
            <div class="placeholder">
                😞 NEGATIVE SENTIMENT<br>
                4%<br>
                Very Low Critical
            </div>
        </div>
        
        <!-- Sentiment & Performance -->
        <div class="grid-2">
            <div class="placeholder">
                📊 SENTIMENT DISTRIBUTION<br>
                Visual breakdown:<br>
                • Positive: 81% (432 comments)<br>
                • Neutral: 15% (80 comments)<br>
                • Negative: 4% (22 comments)
            </div>
            <div class="placeholder">
                🔗 PERFORMANCE CORRELATION<br>
                Impact metrics:<br>
                • Engagement Impact: +23%<br>
                • View Duration: +18%<br>
                • Social Sharing: +31%
            </div>
        </div>
        
        <!-- Top Comments -->
        <div class="placeholder">
            🏆 TOP PERFORMING COMMENTS<br>
            Quote blocks with:<br>
            • Username + engagement stats<br>
            • Comment text in quotes<br>
            • Impact category description
        </div>
        
        <!-- Engagement Patterns -->
        <div class="grid-2">
            <div class="placeholder">
                📈 ENGAGEMENT PATTERNS<br>
                Timeline insights:<br>
                • Peak Activity<br>
                • Reply Chains<br>
                • Community Building<br>
                • Question Threads
            </div>
            <div class="placeholder">
                👥 COMMUNITY INSIGHTS<br>
                User categories:<br>
                • Knowledge Seekers (45%)<br>
                • Experience Sharers (32%)<br>
                • Value Validators (23%)
            </div>
        </div>
        
        <!-- Final Grade -->
        <div class="placeholder">
            🏆 COMMUNITY ENGAGEMENT GRADE<br>
            A+ Grade + 3 summary metrics:<br>
            81% Positive | 4.2 Avg Thread | 534 Comments
        </div>
    </div>

    <!-- RESPONSIVE BREAKPOINTS -->
    <div class="template-section">
        <div class="template-title">RESPONSIVE DESIGN NOTES</div>
        
        <div class="placeholder">
            📱 MOBILE (< 768px)<br>
            • All grids become single column<br>
            • Grade circles smaller (6rem → 4rem)<br>
            • Reduced padding and margins<br>
            • Stack metric cards vertically
        </div>
        
        <div class="placeholder">
            💻 TABLET (768px - 1024px)<br>
            • 2-column layouts for most grids<br>
            • Medium-sized components<br>
            • Maintain readability<br>
            • Touch-friendly interactions
        </div>
        
        <div class="placeholder">
            🖥️ DESKTOP (> 1024px)<br>
            • Full 3-4 column layouts<br>
            • Larger components and spacing<br>
            • Hover effects and animations<br>
            • Maximum visual impact
        </div>
    </div>

    <div style="text-align: center; margin: 60px 0; color: #94A3B8;">
        <h2 style="color: #8B5CF6;">🎨 Ready for Your Designer!</h2>
        <p>These templates show the exact structure and data available for each tab.<br>
        Your designer can now create beautiful, data-driven layouts that match your vision.</p>
    </div>

</body>
</html>