#!/usr/bin/env python3
"""
QUICK TEST for Unified Video Styling Agent
Simple test to verify basic functionality
"""

import os
import sys
import time
from dotenv import load_dotenv
load_dotenv()

sys.path.append('/Users/<USER>/Documents/CrewAI/youtube-research-v2')

from unified_styling_agent import UnifiedVideoStylingAgent
from crewai.llm import LLM

def quick_test():
    """Quick functionality test"""
    
    print("🧪 QUICK UNIFIED STYLING TEST")
    print("=" * 40)
    
    # Initialize
    llm = LLM(model="gemini/gemini-2.5-flash")
    agent = UnifiedVideoStylingAgent(llm)
    
    # Simple test data
    simple_data = {
        'performance_analysis': {
            'analysis': 'Views: 100K, Likes: 5K, Engagement: 5%. Great performance!'
        },
        'script_analysis': {
            'analysis': 'Word count: 500 words. Hook effectiveness: 8/10. Good structure.'
        },
        'seo_analysis': {
            'analysis': 'Title score: 7/10. Keywords: AI, tutorial. Competition: medium.'
        },
        'psychology_analysis': {
            'analysis': 'Audience: developers. Motivation: learning. Engagement: high.'
        },
        'comment_analysis': {
            'analysis': 'Comments: 100. Sentiment: 80% positive. Great feedback.'
        }
    }
    
    video_data = {
        'title': 'Test Video',
        'statistics': {'viewCount': '100000', 'likeCount': '5000', 'commentCount': '100'}
    }
    
    print("✅ Test data prepared")
    print("🔧 Running unified formatting...")
    
    start = time.time()
    
    try:
        result = agent.format_all_analyses(simple_data, video_data)
        duration = time.time() - start
        
        print(f"✅ Completed in {duration:.1f}s")
        print(f"✅ Token savings: {result['token_savings']['savings_percentage']:.0f}%")
        
        # Check output structure
        sections = result['styled_sections']
        for key in ['performance_html', 'script_html', 'seo_html', 'psychology_html', 'comments_html']:
            if key in sections and sections[key]:
                print(f"✅ {key}: {len(sections[key])} chars")
            else:
                print(f"❌ {key}: Missing")
                return False
        
        print("🎉 BASIC TEST PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

if __name__ == "__main__":
    quick_test()