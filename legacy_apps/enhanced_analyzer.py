"""
Enhanced Channel Analyzer with Gemini AI Integration
Provides world-class narrative insights alongside quantitative analysis
"""

import os
import json
import google.generativeai as genai
from datetime import datetime
from test_channel_analysis import ChannelAnalyzer
import logging

logger = logging.getLogger(__name__)

class EnhancedChannelAnalyzer(ChannelAnalyzer):
    """Enhanced analyzer with AI-powered narrative insights"""
    
    def __init__(self, youtube_api_key, gemini_api_key=None):
        super().__init__(youtube_api_key)
        
        # Initialize Gemini AI
        self.gemini_api_key = gemini_api_key or os.getenv('GEMINI_API_KEY')
        if self.gemini_api_key:
            genai.configure(api_key=self.gemini_api_key)
            self.model = genai.GenerativeModel('gemini-2.0-flash-exp')
            self.ai_enabled = True
            logger.info("Gemini AI integration enabled")
        else:
            self.ai_enabled = False
            logger.warning("Gemini API key not found - AI insights disabled")
    
    def analyze_channel(self, channel_id, max_videos=20, max_comments=50):
        """Enhanced channel analysis with AI insights"""
        
        print(f"🧬 Starting Enhanced Channel DNA Analysis")
        print(f"📊 Channel ID: {channel_id}")
        print(f"🤖 AI Insights: {'Enabled' if self.ai_enabled else 'Disabled'}")
        print("=" * 60)
        
        # Run base analysis
        base_analysis = super().analyze_channel(channel_id, max_videos, max_comments)
        
        if not base_analysis:
            return None
        
        # Add AI-powered narrative insights
        if self.ai_enabled:
            print(f"\n5️⃣ Generating AI narrative insights...")
            ai_insights = self._generate_ai_insights(base_analysis, channel_id)
            base_analysis['ai_insights'] = ai_insights
            print(f"✅ AI insights generated!")
        
        return base_analysis
    
    def _generate_ai_insights(self, analysis, channel_id):
        """Generate AI-powered narrative insights"""
        
        insights = {}
        
        try:
            # Channel Strategic Analysis
            insights['channel_strategy'] = self._analyze_channel_strategy(analysis)
            
            # Content DNA Narrative
            insights['content_narrative'] = self._analyze_content_narrative(analysis)
            
            # Audience Psychology Deep Dive
            insights['audience_psychology'] = self._analyze_audience_psychology_ai(analysis)
            
            # Competitive Positioning
            insights['competitive_analysis'] = self._analyze_competitive_positioning(analysis)
            
            # Strategic Recommendations
            insights['strategic_roadmap'] = self._generate_strategic_roadmap(analysis)
            
        except Exception as e:
            logger.error(f"Error generating AI insights: {e}")
            insights['error'] = str(e)
        
        return insights
    
    def _analyze_channel_strategy(self, analysis):
        """AI analysis of overall channel strategy"""
        
        prompt = f"""
        You are a world-class YouTube strategy consultant. Analyze this channel data and provide a comprehensive strategic assessment.

        CHANNEL PROFILE:
        {json.dumps(analysis.get('channel_profile', {}), indent=2)}

        CONTENT PATTERNS:
        {json.dumps(analysis.get('content_patterns', {}), indent=2)}

        PERFORMANCE METRICS:
        {json.dumps(analysis.get('performance_metrics', {}), indent=2)}

        Provide a 3-4 paragraph strategic analysis covering:

        1. **Growth Trajectory Assessment**: Analyze the channel's growth pattern, subscriber tier positioning, and content velocity. What does this tell us about their strategic approach?

        2. **Content Strategy Mastery**: Evaluate their content patterns, title optimization, viral elements, and format consistency. How sophisticated is their content strategy?

        3. **Performance Intelligence**: Assess engagement rates, consistency scores, and outlier performance. What are the key performance drivers?

        4. **Strategic Positioning**: Based on all metrics, how well-positioned is this channel in the YouTube ecosystem? What's their competitive advantage?

        Write in a professional, insightful tone with specific data points and strategic implications.
        """
        
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            logger.error(f"Error in channel strategy analysis: {e}")
            return f"Error generating channel strategy analysis: {str(e)}"
    
    def _analyze_content_narrative(self, analysis):
        """AI analysis of content patterns and strategy"""
        
        content_data = analysis.get('content_patterns', {})
        
        prompt = f"""
        You are a content strategy expert specializing in viral content analysis. Analyze this channel's content patterns:

        TITLE PATTERNS:
        - Average length: {content_data.get('title_patterns', {}).get('avg_length', 'N/A')} characters
        - Uses numbers: {content_data.get('title_patterns', {}).get('uses_numbers', 'N/A')}
        - Common words: {content_data.get('title_patterns', {}).get('common_words', [])}

        PERFORMANCE PATTERNS:
        {json.dumps(content_data.get('performance_patterns', {}), indent=2)}

        VIRAL ELEMENTS:
        {content_data.get('viral_elements', [])}

        Provide a 3-4 paragraph content strategy analysis covering:

        1. **Content DNA Profile**: What is this channel's unique content personality? How do they approach audience engagement through content?

        2. **Viral Formula Analysis**: What specific elements make their content viral? Analyze their title strategy, hooks, and format choices.

        3. **Content Evolution Strategy**: How has their content strategy evolved? What patterns indicate strategic thinking vs. organic growth?

        4. **Optimization Opportunities**: Based on the data, what specific content optimizations could drive 20-50% improvement in performance?

        Include specific examples and data-driven insights.
        """
        
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            logger.error(f"Error in content narrative analysis: {e}")
            return f"Error generating content analysis: {str(e)}"
    
    def _analyze_audience_psychology_ai(self, analysis):
        """AI analysis of audience psychology from comment data"""
        
        audience_data = analysis.get('audience_psychology', {})
        
        if audience_data.get('error'):
            return "Audience psychology analysis unavailable due to limited comment data."
        
        prompt = f"""
        You are a behavioral psychology expert specializing in online community analysis. Analyze this audience data:

        ENGAGEMENT METRICS:
        - Total comments analyzed: {audience_data.get('total_comments_analyzed', 0)}
        - Unique commenters: {audience_data.get('audience_demographics', {}).get('unique_commenters', 0)}
        - Average comment length: {audience_data.get('engagement_indicators', {}).get('avg_comment_length', 0)} characters

        PSYCHOLOGICAL INDICATORS:
        {json.dumps(audience_data.get('engagement_indicators', {}), indent=2)}

        COMMUNITY DYNAMICS:
        {json.dumps(audience_data.get('content_response', {}), indent=2)}

        Provide a 3-4 paragraph audience psychology analysis covering:

        1. **Audience Psychology Profile**: What drives this audience? What are their core motivations, pain points, and aspirations based on engagement patterns?

        2. **Community Culture Analysis**: How does this audience interact with content and each other? What's the parasocial relationship dynamic?

        3. **Engagement Psychology**: What psychological triggers drive high engagement? How does the audience express appreciation, make requests, and build community?

        4. **Growth Opportunities**: Based on audience psychology, what untapped audience segments or engagement strategies could drive 10x growth?

        Focus on behavioral insights and psychological patterns rather than basic demographics.
        """
        
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            logger.error(f"Error in audience psychology analysis: {e}")
            return f"Error generating audience analysis: {str(e)}"
    
    def _analyze_competitive_positioning(self, analysis):
        """AI analysis of competitive positioning"""
        
        channel_profile = analysis.get('channel_profile', {})
        performance = analysis.get('performance_metrics', {})
        
        prompt = f"""
        You are a competitive intelligence expert for the creator economy. Analyze this channel's competitive positioning:

        CHANNEL METRICS:
        - Subscriber tier: {channel_profile.get('subscriber_tier', 'unknown')}
        - Growth trajectory: {channel_profile.get('growth_trajectory', 'unknown')}
        - Average performance: {channel_profile.get('average_performance', 'unknown')}
        - Recent performance: {channel_profile.get('recent_performance', 'unknown')}

        PERFORMANCE BENCHMARKS:
        - Total views analyzed: {performance.get('overall_performance', {}).get('total_views', 0):,}
        - Engagement rate: {performance.get('overall_performance', {}).get('avg_engagement_rate', 0):.4f}
        - Performance consistency: {performance.get('video_performance_distribution', {}).get('performance_consistency', 0)}

        Provide a 3-4 paragraph competitive analysis covering:

        1. **Market Position Assessment**: Where does this channel sit in the competitive landscape? What tier of creator are they, and how do they compare to peers?

        2. **Competitive Advantages**: What unique strengths differentiate this channel? What moats have they built that competitors can't easily replicate?

        3. **Vulnerability Analysis**: What competitive threats should they be aware of? Where could new entrants or existing players challenge their position?

        4. **Blue Ocean Opportunities**: Based on their positioning, what unexplored market opportunities could they capture? How can they expand their competitive moat?

        Focus on strategic positioning and competitive dynamics in the creator economy.
        """
        
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            logger.error(f"Error in competitive analysis: {e}")
            return f"Error generating competitive analysis: {str(e)}"
    
    def _generate_strategic_roadmap(self, analysis):
        """AI-generated strategic roadmap"""
        
        recommendations = analysis.get('strategic_recommendations', {})
        
        prompt = f"""
        You are a master strategist for YouTube creators. Based on this comprehensive analysis, create a strategic roadmap:

        CURRENT STRATEGIC RECOMMENDATIONS:
        {json.dumps(recommendations, indent=2)}

        FULL ANALYSIS CONTEXT:
        - Channel Profile: {analysis.get('channel_profile', {}).get('growth_trajectory', 'unknown')} growth
        - Content Performance: {analysis.get('performance_metrics', {}).get('overall_performance', {}).get('avg_engagement_rate', 0):.4f} engagement rate
        - Audience Size: {analysis.get('audience_psychology', {}).get('total_comments_analyzed', 0)} active commenters analyzed

        Create a comprehensive 3-4 paragraph strategic roadmap covering:

        1. **90-Day Sprint Plan**: What are the top 3 immediate actions this channel should take in the next 90 days to drive measurable growth? Include specific, time-bound goals.

        2. **6-Month Strategic Initiatives**: What medium-term strategic projects should they launch? Consider content diversification, audience expansion, and platform optimization.

        3. **1-Year Vision & Goals**: What's the bold vision for where this channel should be in 12 months? Include specific subscriber, view, and revenue targets with justification.

        4. **Success Metrics & Tracking**: What KPIs should they track weekly, monthly, and quarterly to ensure they're on track? How should they measure ROI on strategic initiatives?

        Make recommendations specific, measurable, achievable, relevant, and time-bound (SMART goals).
        """
        
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            logger.error(f"Error in strategic roadmap generation: {e}")
            return f"Error generating strategic roadmap: {str(e)}"