<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Component Showcase - YouTube Research v2</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/static/favicon.svg">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    
    <!-- Design System CSS -->
    <link rel="stylesheet" href="/static/styles/youtube-research-v2-design-system.css">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <style>
        .showcase-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-xl);
        }
        
        .showcase-section {
            margin-bottom: var(--space-3xl);
        }
        
        .showcase-title {
            font-size: var(--text-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--space-lg);
            padding-bottom: var(--space-md);
            border-bottom: 2px solid var(--purple-primary);
        }
        
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-lg);
            margin-bottom: var(--space-xl);
        }
        
        .component-demo {
            background: var(--bg-elevated);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
        }
        
        .demo-title {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin-bottom: var(--space-md);
        }
        
        .demo-controls {
            display: flex;
            gap: var(--space-sm);
            margin-bottom: var(--space-md);
        }
    </style>
</head>
<body>
    <div class="app-layout">
        <!-- Include Navigation -->
        <div id="navigation-container"></div>
        
        <main class="app-main">
            <div class="app-content">
                <div class="showcase-container">
                    <header class="showcase-header">
                        <h1 class="page-title">Component Showcase</h1>
                        <p class="page-subtitle">Interactive demonstration of YouTube Research v2 component library</p>
                        
                        <div class="demo-controls">
                            <button class="btn btn-primary" onclick="toggleTheme()">
                                <i data-lucide="palette" class="btn-icon"></i>
                                Toggle Theme
                            </button>
                            <button class="btn btn-secondary" onclick="generateSampleData()">
                                <i data-lucide="refresh-cw" class="btn-icon"></i>
                                Generate Sample Data
                            </button>
                        </div>
                    </header>
                    
                    <!-- Analysis Cards Section -->
                    <section class="showcase-section">
                        <h2 class="showcase-title">Analysis Cards</h2>
                        <div class="component-grid">
                            <div class="component-demo">
                                <h3 class="demo-title">Analysis Result Card</h3>
                                <div id="analysis-card-demo"></div>
                            </div>
                            
                            <div class="component-demo">
                                <h3 class="demo-title">Insight Card</h3>
                                <div id="insight-card-demo"></div>
                            </div>
                            
                            <div class="component-demo">
                                <h3 class="demo-title">Metric Card</h3>
                                <div id="metric-card-demo"></div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Agent Status Section -->
                    <section class="showcase-section">
                        <h2 class="showcase-title">Agent Status Components</h2>
                        <div class="component-grid">
                            <div class="component-demo">
                                <h3 class="demo-title">Agent Status Cards</h3>
                                <div id="agent-status-demo"></div>
                            </div>
                            
                            <div class="component-demo">
                                <h3 class="demo-title">Progress Indicator</h3>
                                <div id="progress-demo"></div>
                                <div class="demo-controls">
                                    <button class="btn btn-sm btn-secondary" onclick="updateProgress(25)">25%</button>
                                    <button class="btn btn-sm btn-secondary" onclick="updateProgress(50)">50%</button>
                                    <button class="btn btn-sm btn-secondary" onclick="updateProgress(75)">75%</button>
                                    <button class="btn btn-sm btn-secondary" onclick="updateProgress(100)">100%</button>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- Status Feed Section -->
                    <section class="showcase-section">
                        <h2 class="showcase-title">Status Feed</h2>
                        <div class="component-grid">
                            <div class="component-demo">
                                <h3 class="demo-title">Live Status Feed</h3>
                                <div id="status-feed-demo"></div>
                                <div class="demo-controls">
                                    <button class="btn btn-sm btn-primary" onclick="addStatusMessage()">
                                        <i data-lucide="plus" class="btn-icon"></i>
                                        Add Message
                                    </button>
                                    <button class="btn btn-sm btn-ghost" onclick="clearStatusFeed()">
                                        <i data-lucide="trash-2" class="btn-icon"></i>
                                        Clear
                                    </button>
                                </div>
                            </div>
                        </div>
                    </section>
                    
                    <!-- State Components Section -->
                    <section class="showcase-section">
                        <h2 class="showcase-title">State Components</h2>
                        <div class="component-grid">
                            <div class="component-demo">
                                <h3 class="demo-title">Loading State</h3>
                                <div id="loading-demo"></div>
                            </div>
                            
                            <div class="component-demo">
                                <h3 class="demo-title">Error State</h3>
                                <div id="error-demo"></div>
                            </div>
                            
                            <div class="component-demo">
                                <h3 class="demo-title">Empty State</h3>
                                <div id="empty-demo"></div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Include Component Library -->
    <div id="component-templates" style="display: none;"></div>
    
    <script>
        // Load component templates
        fetch('/templates/components/analysis-cards.html')
            .then(response => response.text())
            .then(html => {
                const container = document.getElementById('component-templates');
                container.innerHTML = html;

                // Extract and execute script tags manually (innerHTML doesn't execute scripts)
                const scripts = container.querySelectorAll('script');
                scripts.forEach(script => {
                    const newScript = document.createElement('script');
                    if (script.src) {
                        newScript.src = script.src;
                    } else {
                        newScript.textContent = script.textContent;
                    }
                    document.head.appendChild(newScript);
                });

                // Wait for the script to execute and ComponentLibrary to be available
                setTimeout(() => {
                    initializeShowcase();
                }, 300);
            });

        // Load navigation
        fetch('/templates/components/navigation.html')
            .then(response => response.text())
            .then(html => {
                document.getElementById('navigation-container').innerHTML = html;
                lucide.createIcons();

                // Set Component Showcase as active
                setTimeout(() => {
                    const componentShowcaseItem = document.querySelector('[data-page="component-showcase"]');
                    if (componentShowcaseItem) {
                        // Remove all active states
                        document.querySelectorAll('.nav-item').forEach(item => {
                            item.classList.remove('active');
                        });
                        // Set Component Showcase as active
                        componentShowcaseItem.classList.add('active');
                        localStorage.setItem('activePage', 'component-showcase');
                    }
                }, 100);
            });

        function initializeShowcase() {
            // Check if ComponentLibrary is available
            if (!window.componentLibrary) {
                console.error('ComponentLibrary not available yet, retrying...');
                setTimeout(initializeShowcase, 300);
                return;
            }

            // Initialize Lucide icons
            lucide.createIcons();

            // Generate initial demo content
            generateSampleData();
        }
        
        function generateSampleData() {
            try {
                // Check if ComponentLibrary is available
                if (!window.componentLibrary) {
                    console.error('ComponentLibrary not available');
                    return;
                }

                console.log('Generating sample data...');

                // Analysis Card Demo
                const analysisCard = window.componentLibrary.create('analysis-result-card', {
                    title: 'Performance Analysis Complete',
                    subtitle: 'Video engagement metrics analyzed',
                    icon: 'bar-chart-3',
                    content: '<p>Your video shows strong engagement with 89% retention rate and above-average click-through rates.</p>',
                    timestamp: '2 minutes ago',
                    confidence: 94
                });

                const analysisCardContainer = document.getElementById('analysis-card-demo');
                if (analysisCardContainer && analysisCard) {
                    analysisCardContainer.innerHTML = '';
                    analysisCardContainer.appendChild(analysisCard);
                    console.log('Analysis card created successfully');
                } else {
                    console.error('Failed to create analysis card or find container');
                }

                // Insight Card Demo
                const insightCard = window.componentLibrary.create('insight-card', {
                    title: 'Optimize Upload Timing',
                    description: 'Your audience is most active on Tuesdays at 3 PM EST. Consider scheduling uploads during this peak engagement window.',
                    category: 'Timing',
                    priority: 'high'
                });

                const insightCardContainer = document.getElementById('insight-card-demo');
                if (insightCardContainer && insightCard) {
                    insightCardContainer.innerHTML = '';
                    insightCardContainer.appendChild(insightCard);
                    console.log('Insight card created successfully');
                } else {
                    console.error('Failed to create insight card or find container');
                }

                // Metric Card Demo
                const metricCard = window.componentLibrary.create('metric-card', {
                    value: '2.4M',
                    label: 'Total Views',
                    change: '+18.2%',
                    trend: 'up'
                });

                const metricCardContainer = document.getElementById('metric-card-demo');
                if (metricCardContainer && metricCard) {
                    metricCardContainer.innerHTML = '';
                    metricCardContainer.appendChild(metricCard);
                    console.log('Metric card created successfully');
                } else {
                    console.error('Failed to create metric card or find container');
                }

                // Agent Status Demo
                const agents = [
                    { name: 'Performance Intelligence', message: 'Analyzing engagement metrics...', progress: 85, status: 'working', agentType: 'performance' },
                    { name: 'Script Forensics', message: 'Analysis complete!', progress: 100, status: 'complete', agentType: 'script' },
                    { name: 'SEO Discovery', message: 'Starting keyword analysis...', progress: 15, status: 'starting', agentType: 'seo' }
                ];

                const agentContainer = document.getElementById('agent-status-demo');
                if (agentContainer) {
                    agentContainer.innerHTML = '';
                    agents.forEach(agent => {
                        const agentCard = window.componentLibrary.create('agent-status-card', agent);
                        if (agentCard) {
                            agentContainer.appendChild(agentCard);
                        }
                    });
                    console.log('Agent status cards created successfully');
                }

                // Progress Demo
                const progressIndicator = window.componentLibrary.create('progress-indicator', {
                    title: 'Video Analysis Progress',
                    percentage: 65,
                    message: 'Processing audience psychology insights...'
                });

                const progressContainer = document.getElementById('progress-demo');
                if (progressContainer && progressIndicator) {
                    progressContainer.innerHTML = '';
                    progressContainer.appendChild(progressIndicator);
                    console.log('Progress indicator created successfully');
                }

                // Status Feed Demo
                const statusFeed = window.componentLibrary.create('status-feed');
                const statusFeedContainer = document.getElementById('status-feed-demo');
                if (statusFeedContainer && statusFeed) {
                    statusFeedContainer.innerHTML = '';
                    statusFeedContainer.appendChild(statusFeed);
                    console.log('Status feed created successfully');
                }

                // Add some sample status messages
                addSampleStatusMessages();

                // Loading Demo
                const loadingSkeleton = window.componentLibrary.create('loading-skeleton');
                const loadingContainer = document.getElementById('loading-demo');
                if (loadingContainer && loadingSkeleton) {
                    loadingContainer.innerHTML = '';
                    loadingContainer.appendChild(loadingSkeleton);
                    console.log('Loading skeleton created successfully');
                }

                // Error Demo
                const errorState = window.componentLibrary.create('error-state');
                const errorContainer = document.getElementById('error-demo');
                if (errorContainer && errorState) {
                    errorContainer.innerHTML = '';
                    errorContainer.appendChild(errorState);
                    console.log('Error state created successfully');
                }

                // Empty Demo
                const emptyState = window.componentLibrary.create('empty-state');
                const emptyContainer = document.getElementById('empty-demo');
                if (emptyContainer && emptyState) {
                    emptyContainer.innerHTML = '';
                    emptyContainer.appendChild(emptyState);
                    console.log('Empty state created successfully');
                }

                // Reinitialize icons
                lucide.createIcons();
                console.log('Sample data generation completed successfully');

            } catch (error) {
                console.error('Error generating sample data:', error);
                // Show error message to user
                const errorMessage = document.createElement('div');
                errorMessage.className = 'error-message';
                errorMessage.style.cssText = 'background: var(--red-50); color: var(--red-600); padding: var(--space-md); border-radius: var(--radius-md); margin: var(--space-md);';
                errorMessage.innerHTML = `<strong>Error:</strong> Failed to generate sample data. Please check the console for details.`;
                document.querySelector('.showcase-container').prepend(errorMessage);
            }
        }
        
        function addSampleStatusMessages() {
            try {
                if (!window.componentLibrary) {
                    console.error('ComponentLibrary not available for status messages');
                    return;
                }

                const messages = [
                    { agentName: 'Performance Intelligence', message: '🎯 Found 3 key engagement patterns in your content!', timestamp: '2:34 PM', icon: 'zap' },
                    { agentName: 'Script Forensics', message: '📝 Analyzing narrative structure and pacing...', timestamp: '2:33 PM', icon: 'file-text' },
                    { agentName: 'SEO Discovery', message: '🔍 Discovered 12 high-impact keywords for optimization', timestamp: '2:32 PM', icon: 'search' }
                ];

                const feedContent = document.querySelector('#status-feed-demo .status-feed-content');
                if (feedContent) {
                    messages.forEach(msg => {
                        const statusItem = window.componentLibrary.create('status-feed-item', msg);
                        if (statusItem) {
                            feedContent.appendChild(statusItem);
                        }
                    });
                    lucide.createIcons();
                    console.log('Sample status messages added successfully');
                } else {
                    console.error('Status feed content container not found');
                }
            } catch (error) {
                console.error('Error adding sample status messages:', error);
            }
        }
        
        function updateProgress(percentage) {
            const progressFill = document.querySelector('#progress-demo .progress-fill');
            const progressText = document.querySelector('#progress-demo .progress-percentage');
            const progressMessage = document.querySelector('#progress-demo .current-message');
            
            if (progressFill && progressText) {
                progressFill.style.width = `${percentage}%`;
                progressText.textContent = `${percentage}%`;
                
                const messages = {
                    25: 'Initializing analysis agents...',
                    50: 'Processing video content...',
                    75: 'Generating insights...',
                    100: 'Analysis complete!'
                };
                
                if (progressMessage) {
                    progressMessage.textContent = messages[percentage] || 'Processing...';
                }
            }
        }
        
        function addStatusMessage() {
            try {
                if (!window.componentLibrary) {
                    console.error('ComponentLibrary not available for adding status message');
                    return;
                }

                const agents = ['Performance Intelligence', 'Script Forensics', 'SEO Discovery', 'Psychology Agent', 'Comment Intelligence'];
                const messages = [
                    '🚀 Starting deep analysis...',
                    '🔍 Found interesting patterns!',
                    '💡 Generating recommendations...',
                    '✨ Analysis complete!',
                    '🎯 Key insights discovered!'
                ];
                const icons = ['zap', 'file-text', 'search', 'brain', 'message-circle'];

                const randomAgent = agents[Math.floor(Math.random() * agents.length)];
                const randomMessage = messages[Math.floor(Math.random() * messages.length)];
                const randomIcon = icons[Math.floor(Math.random() * icons.length)];
                const timestamp = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

                const statusItem = window.componentLibrary.create('status-feed-item', {
                    agentName: randomAgent,
                    message: randomMessage,
                    timestamp: timestamp,
                    icon: randomIcon
                });

                const feedContent = document.querySelector('#status-feed-demo .status-feed-content');
                if (feedContent && statusItem) {
                    feedContent.insertBefore(statusItem, feedContent.firstChild);
                    lucide.createIcons();
                    console.log('Status message added successfully');
                } else {
                    console.error('Failed to add status message - container or item not found');
                }
            } catch (error) {
                console.error('Error adding status message:', error);
            }
        }
        
        function clearStatusFeed() {
            const feedContent = document.querySelector('#status-feed-demo .status-feed-content');
            if (feedContent) {
                feedContent.innerHTML = '';
            }
        }
        
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        }
        
        // Initialize theme from localStorage
        const savedTheme = localStorage.getItem('theme') || 'light';
        document.documentElement.setAttribute('data-theme', savedTheme);
    </script>
</body>
</html>
