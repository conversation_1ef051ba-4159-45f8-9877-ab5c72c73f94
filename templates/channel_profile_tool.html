<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YoutubePulse - Channel Profile Exporter</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/static/favicon.svg">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=JetBrains+Mono&display=swap" rel="stylesheet">
    
    <!-- YouTube Research v2 Design System -->
    <link rel="stylesheet" href="/static/styles/youtube-research-v2-design-system.css?v=2025073003">
    
    <style>
        /* Specific styles for this tool page */
        .tool-header {
            padding: var(--space-xl) 0 var(--space-lg);
            text-align: center;
        }
        .tool-container {
            max-width: 900px;
            margin: var(--space-lg) auto;
            padding: var(--space-lg);
            background: var(--surface-primary);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-lg);
        }
        .form-section {
            display: flex;
            gap: var(--space-md);
            align-items: flex-end;
            margin-bottom: var(--space-lg);
        }
        .form-group {
            flex: 1;
        }
        .form-group label {
            display: block;
            margin-bottom: var(--space-xs);
            color: var(--text-secondary);
            font-weight: var(--font-semibold);
        }
        .form-group input[type="text"], .form-group input[type="number"] {
            width: 100%;
            padding: var(--space-sm) var(--space-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            background: var(--surface-secondary);
            color: var(--text-primary);
            font-size: var(--font-size-body);
        }
        .form-group input:focus {
            outline: none;
            border-color: var(--accent-purple);
        }
        .results-section {
            margin-top: var(--space-xl);
        }
        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--space-lg);
        }
        .results-header h3 {
            color: var(--text-primary);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: var(--space-md);
            margin-bottom: var(--space-lg);
        }
        .metric-card {
            background: var(--surface-secondary);
            border-radius: var(--radius-md);
            padding: var(--space-md);
            text-align: center;
            box-shadow: var(--shadow-sm);
        }
        .metric-value {
            font-size: var(--font-size-heading-2);
            font-weight: var(--font-bold);
            color: var(--accent-green);
            margin-bottom: var(--space-xs);
        }
        .metric-label {
            font-size: var(--font-size-small);
            color: var(--text-tertiary);
            text-transform: uppercase;
            font-weight: var(--font-semibold);
        }
        .description-box {
            background: var(--surface-secondary);
            border-radius: var(--radius-md);
            padding: var(--space-md);
            margin-bottom: var(--space-lg);
            color: var(--text-secondary);
            font-size: var(--font-size-body);
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .video-table-container {
            max-height: 400px;
            overflow-y: auto;
            background: var(--surface-secondary);
            border-radius: var(--radius-md);
            padding: var(--space-md);
        }
        .video-table {
            width: 100%;
            border-collapse: collapse;
        }
        .video-table th, .video-table td {
            padding: var(--space-sm);
            border-bottom: 1px solid var(--border-color);
            text-align: left;
            color: var(--text-secondary);
        }
        .video-table th {
            color: var(--text-primary);
            font-weight: var(--font-semibold);
        }
        .video-table tbody tr:last-child td {
            border-bottom: none;
        }
        .status-message {
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--radius-md);
            margin-top: var(--space-md);
            font-size: var(--font-size-body);
        }
        .status-message.info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            color: #60a5fa;
        }
        .status-message.success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            color: #4ade80;
        }
        .status-message.error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            color: #f87171;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="nav-header">
        <div class="container">
            <div class="nav-content">
                <a href="/" class="nav-logo">
                    <span class="text-gradient">YoutubePulse</span>
                </a>
                <div class="nav-menu">
                    <a href="/" class="btn btn-ghost">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z"></path>
                            <path d="M2 17L12 22L22 17"></path>
                            <path d="M2 12L12 17L22 12"></path>
                        </svg>
                        Dashboard
                    </a>
                    <button class="btn btn-ghost">Settings</button>
                    <div class="user-avatar" style="width: 40px; height: 40px; border-radius: 50%; background: var(--primary-gradient); display: flex; align-items: center; justify-content: center; font-weight: 600;">
                        A
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <main class="dashboard-layout">
        <div class="container">
            <header class="tool-header">
                <h1 class="heading-1">Channel Profile Exporter</h1>
                <p class="text-body">Fetch comprehensive raw data for any YouTube channel.</p>
            </header>

            <div class="tool-container card-glass animate-fade-in">
                <form id="channelProfileForm" class="form-section">
                    <div class="form-group">
                        <label for="channelId">YouTube Channel ID or Handle</label>
                        <input type="text" id="channelId" placeholder="e.g., @MrBeast or UCX6OQ3DkcsbYNE6H8uQQuVA" required>
                    </div>
                    <div class="form-group" style="flex: 0.3;">
                        <label for="maxVideos">Max Videos</label>
                        <input type="number" id="maxVideos" value="50" min="1" max="200">
                    </div>
                    <button type="submit" class="btn btn-primary" id="fetchBtn">Fetch Data</button>
                </form>
                <div id="statusMessage" class="status-message"></div>

                <div id="resultsContainer" class="results-section" style="display: none;">
                    <div class="results-header">
                        <div style="display: flex; align-items: center; gap: var(--space-md);">
                            <img id="channelThumbnail" src="" alt="Channel Thumbnail" style="width: 60px; height: 60px; border-radius: 50%; object-fit: cover;">
                            <h3 class="heading-3" id="channelTitle"></h3>
                        </div>
                        <button class="btn btn-secondary" id="exportJsonBtn">Export as JSON</button>
                    </div>
                    
                    <div class="metrics-grid" id="channelMetrics"></div>

                    <h4 class="heading-4 mt-lg">Channel Description</h4>
                    <div class="description-box" id="channelDescription"></div>

                    <h4 class="heading-4 mt-lg">Video Library (<span id="videoCount"></span> fetched)</h4>
                    <div class="video-table-container">
                        <table class="video-table">
                            <thead>
                                <tr>
                                    <th></th>
                                    <th>Title</th>
                                    <th>Views</th>
                                    <th>Published</th>
                                    <th>Duration</th>
                                </tr>
                            </thead>
                            <tbody id="videoTableBody"></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        let currentProfileData = null;

        document.getElementById('channelProfileForm').addEventListener('submit', async function(event) {
            event.preventDefault();
            const channelId = document.getElementById('channelId').value;
            const maxVideos = document.getElementById('maxVideos').value;
            const fetchBtn = document.getElementById('fetchBtn');
            const statusMessage = document.getElementById('statusMessage');
            const resultsContainer = document.getElementById('resultsContainer');

            fetchBtn.disabled = true;
            fetchBtn.textContent = 'Fetching...';
            statusMessage.style.display = 'block';
            statusMessage.className = 'status-message info';
            statusMessage.textContent = 'Fetching data...';
            resultsContainer.style.display = 'none';

            try {
                const response = await fetch('/api/tools/channel_profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        channel_id: channelId,
                        max_videos: parseInt(maxVideos)
                    }),
                });

                const data = await response.json();

                if (response.ok) {
                    currentProfileData = data;
                    displayResults(data);
                    statusMessage.className = 'status-message success';
                    statusMessage.textContent = 'Data fetched successfully!';
                    resultsContainer.style.display = 'block';
                } else {
                    throw new Error(data.detail || 'Failed to fetch data');
                }
            } catch (error) {
                statusMessage.className = 'status-message error';
                statusMessage.textContent = `Error: ${error.message}`;
                resultsContainer.style.display = 'none';
            } finally {
                fetchBtn.disabled = false;
                fetchBtn.textContent = 'Fetch Data';
            }
        });

        document.getElementById('exportJsonBtn').addEventListener('click', function() {
            if (currentProfileData) {
                const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(currentProfileData, null, 4));
                const downloadAnchorNode = document.createElement('a');
                downloadAnchorNode.setAttribute("href", dataStr);
                const filename = `${currentProfileData.channel_profile.title.replace(/[^a-zA-Z0-9]/g, '_')}_profile.json`;
                downloadAnchorNode.setAttribute("download", filename);
                document.body.appendChild(downloadAnchorNode);
                downloadAnchorNode.click();
                downloadAnchorNode.remove();
            }
        });

        function displayResults(data) {
            const profile = data.channel_profile;
            const videos = data.video_library;

            document.getElementById('channelTitle').textContent = profile.title;
            document.getElementById('channelDescription').textContent = profile.description;
            document.getElementById('videoCount').textContent = videos.length;
            document.getElementById('channelThumbnail').src = profile.thumbnails.default.url;

            // Display metrics
            const metricsHtml = `
                <div class="metric-card">
                    <div class="metric-value">${formatNumber(profile.statistics.subscriberCount)}</div>
                    <div class="metric-label">Subscribers</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${formatNumber(profile.statistics.viewCount)}</div>
                    <div class="metric-label">Total Views</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${formatNumber(profile.statistics.videoCount)}</div>
                    <div class="metric-label">Total Videos</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">${new Date(profile.publishedAt).toLocaleDateString()}</div>
                    <div class="metric-label">Published</div>
                </div>
            `;
            document.getElementById('channelMetrics').innerHTML = metricsHtml;

            // Display video table
            const videoTableBody = document.getElementById('videoTableBody');
            videoTableBody.innerHTML = '';
            videos.forEach(video => {
                const row = videoTableBody.insertRow();
                row.innerHTML = `
                    <td><img src="${video.thumbnails.default.url}" alt="${video.title}" style="width: 80px; height: auto; border-radius: 4px;"></td>
                    <td>${video.title}</td>
                    <td>${formatNumber(video.statistics.viewCount)}</td>
                    <td>${new Date(video.publishedAt).toLocaleDateString()}</td>
                    <td>${formatDuration(video.duration)}</td>
                `;
            });
        }

        function formatNumber(num) {
            if (num === undefined || num === null) return 'N/A';
            return new Intl.NumberFormat('en-US', { notation: 'compact', maximumFractionDigits: 1 }).format(num);
        }

        function formatDuration(isoDuration) {
            const match = isoDuration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
            if (!match) return isoDuration;

            const hours = parseInt(match[1] || 0);
            const minutes = parseInt(match[2] || 0);
            const seconds = parseInt(match[3] || 0);

            let parts = [];
            if (hours > 0) parts.push(`${hours}h`);
            if (minutes > 0) parts.push(`${minutes}m`);
            if (seconds > 0 || parts.length === 0) parts.push(`${seconds}s`); // Ensure seconds are shown if no other parts

            return parts.join(' ');
        }

        // BULLETPROOF NAVIGATION HEIGHT FIX - MEASURE AND ADAPT
        function fixNavigationOverlap() {
            const navHeader = document.querySelector('.nav-header');
            if (navHeader) {
                const navHeight = navHeader.offsetHeight;
                document.documentElement.style.setProperty('--nav-height', navHeight + 'px');
                console.log(`🔧 Navigation height measured: ${navHeight}px - Layout updated dynamically`);
            }
        }

        window.addEventListener('load', fixNavigationOverlap);
        window.addEventListener('resize', fixNavigationOverlap);
        fixNavigationOverlap();
    </script>
</body>
</html>