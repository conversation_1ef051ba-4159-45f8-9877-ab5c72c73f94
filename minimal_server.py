"""
Minimal server for testing Dashboard styling
"""
from fastapi import FastAPI
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
import uvicorn

app = FastAPI()

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/", response_class=HTMLResponse)
async def dashboard():
    with open("templates/dashboard.html", "r") as f:
        return HTMLResponse(content=f.read())

@app.get("/component-showcase", response_class=HTMLResponse)
async def component_showcase():
    with open("templates/component-showcase.html", "r") as f:
        return HTMLResponse(content=f.read())

@app.get("/templates/components/{filename}")
async def get_component_template(filename: str):
    return FileResponse(f"templates/components/{filename}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8003)
