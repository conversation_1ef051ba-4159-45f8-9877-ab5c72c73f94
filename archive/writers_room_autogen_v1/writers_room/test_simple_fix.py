#!/usr/bin/env python3
"""
SIMPLE FIX TEST: Enhanced prompts with mandatory content requirements
"""

import sys
import os
sys.path.append('/Users/<USER>/Documents/CrewAI/youtube-research-v2/writers_room/core_system')

from fixed_autogen_writers_room import FixedAutoGenWritersRoom, WritersRoomInput

def test_enhanced_prompts():
    """Test with enhanced prompts that FORCE specific content"""
    
    print("🎯 TESTING ENHANCED PROMPTS WITH MANDATORY CONTENT")
    print("=" * 70)
    
    # API key
    api_key = "********************************************************************************************************************************************************************"
    
    # Create SPECIFIC golden nuggets with exact details
    golden_nuggets = [
        "Rome and Sasanid Persia fought a 400-year water war over Mesopotamian rivers feeding 50 million people",
        "When Romans cut Sasanid water in 627 CE, 500,000 people died in just 3 months",
        "Phoenix uses the exact same Roman water law from Emperor Diocletian that caused ancient crisis",
        "Saudi company Fondomonte pumps MORE water than all of Phoenix (5 million people) combined",
        "Colorado River hasn't reached the ocean since 1998 - same death pattern as ancient Mesopotamia"
    ]
    
    # Enhanced input with SPECIFIC research context
    input_data = WritersRoomInput(
        topic="The Ancient Water War That's Destroying Phoenix",
        golden_nuggets=golden_nuggets,
        persona={
            "channel": "Frontier",
            "voice": "Shocked investigator uncovering conspiracy",
            "style": "Fast-paced revelations with specific numbers and dates"
        },
        research_context="""
        SPECIFIC SHOCKING FACTS TO INCLUDE:
        - Phoenix consumes 89 gallons per person per day (2x NYC's 45 gallons)
        - Rome-Sasanid wars lasted exactly 400 years (224-628 CE)
        - Sasanid qanat system: 50,000 miles of underground channels
        - Roman siege of Ctesiphon (283 CE): Diocletian diverted Tigris
        - Julian's campaign (363 CE): Destroyed irrigation, created 1000-year desert
        - Saudi Fondomonte: 15,000 acres pumping Arizona groundwater for alfalfa
        - Glen Canyon Dam = single point of failure (modern parallel to cut aqueducts)
        - Ancient Mesopotamia: still 90% desert today, never recovered
        - By 2030: 40 million Americans face water cutoffs
        """,
        target_length="8-10 minutes",
        max_rounds=10  # Shorter for testing
    )
    
    # Create enhanced Writers' Room
    writers_room = FixedAutoGenWritersRoom(
        model="gpt-3.5-turbo",
        api_key=api_key,
        test_mode=True
    )
    
    # Test just Phase 2 (content creation) with enhanced prompts
    print("🎬 TESTING PHASE 2 CONTENT CREATION WITH ENHANCED PROMPTS")
    
    try:
        # Simulate approved structure
        writers_room.approved_structure = """
ENHANCED 5-BEAT STRUCTURE:

Beat 1: Shock Opening (0:00-2:00)
- MUST include: Phoenix 89 gallons vs NYC 45 gallons stat
- MUST include: 400-year Rome-Sasanid war mention
- Hook: "Phoenix uses 2x NYC water in a desert - and it's the same mistake that killed 500,000 people"

Beat 2: Historical Parallel (2:00-4:00) 
- MUST include: 500,000 deaths in 3 months when Romans cut water
- MUST include: Specific date (627 CE) and method
- MUST include: 50,000 miles of Sasanid channels

Beat 3: Modern Colonialism (4:00-6:00)
- MUST include: Saudi Fondomonte pumping more than Phoenix
- MUST include: 15,000 acres of alfalfa for Saudi Arabia
- MUST include: Modern water colonialism comparison

Beat 4: Legal Connection (6:00-8:00)
- MUST include: Diocletian's water law still used in Phoenix
- MUST include: Colorado River not reaching ocean since 1998
- MUST include: Glen Canyon Dam as single point of failure

Beat 5: Future Trajectory (8:00-10:00)
- MUST include: 40 million Americans facing cutoffs by 2030
- MUST include: Mesopotamia still 90% desert after 1400 years
- MUST include: Same death pattern emerging
"""
        
        # Store context
        writers_room.current_topic = input_data.topic
        writers_room.current_nuggets = input_data.golden_nuggets
        writers_room.current_persona = input_data.persona
        
        # Run content creation with enhanced prompts
        result = writers_room._phase_2_content(input_data)
        
        print(f"\n✅ CONTENT PHASE COMPLETE")
        print(f"📊 Rounds: {result['rounds']}")
        print(f"💬 Messages: {len(result['conversation'])}")
        
        # Extract and analyze content
        content = writers_room._extract_content(result['conversation'])
        
        print(f"\n📜 CONTENT ANALYSIS:")
        print(f"Length: {len(content)} characters")
        
        # Check for specific required facts
        required_checks = [
            ("500,000", "Death toll mentioned"),
            ("3 months", "Timeframe mentioned"),
            ("400 year", "War duration mentioned"),
            ("Diocletian", "Roman law connection"),
            ("Saudi", "Saudi farms mentioned"),
            ("2x NYC", "Water consumption comparison"),
            ("1998", "Colorado River fact"),
            ("89 gallons", "Specific Phoenix consumption")
        ]
        
        print(f"\n🔍 CONTENT VALIDATION:")
        for check, description in required_checks:
            found = check in content
            status = "✅" if found else "❌"
            print(f"{status} {description}: {check}")
        
        # Show content preview
        print(f"\n📝 CONTENT PREVIEW:")
        print("=" * 50)
        print(content[:1000] + "..." if len(content) > 1000 else content)
        print("=" * 50)
        
        # Save result
        output_dir = "/Users/<USER>/Documents/CrewAI/youtube-research-v2/writers_room/outputs/enhanced_test"
        os.makedirs(output_dir, exist_ok=True)
        
        import json
        with open(f"{output_dir}/enhanced_content_test.json", 'w') as f:
            json.dump({
                'content': content,
                'conversation': result['conversation'],
                'validation_checks': {check: check in content for check, desc in required_checks}
            }, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_dir}/enhanced_content_test.json")
        
        return result
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_prompts()