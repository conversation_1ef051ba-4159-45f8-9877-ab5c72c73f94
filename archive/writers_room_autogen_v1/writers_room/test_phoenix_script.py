#!/usr/bin/env python3
"""
Test the enhanced 6-agent Writers' Room with Phoenix script generation
Based on the "5 Million People in America's Hottest City" analysis
"""

import os
import sys
from pathlib import Path

# Add the writers_room directory to the path  
writers_room_path = Path(__file__).parent
sys.path.append(str(writers_room_path / "core_system"))

try:
    # Import without the broken __init__.py
    from real_autogen_writers_room import RealAutoGenWritersRoom, WritersRoomInput
    
    def test_phoenix_script_generation():
        """Generate a viral script about Phoenix using 6-agent Writers' Room"""
        
        print("🔥 TESTING 6-AGENT WRITERS' ROOM: PHOENIX SCRIPT")
        print("=" * 70)
        print("📄 Based on: '5 Million People in America's Hottest City' Analysis")
        print("🎯 Goal: Create emotionally-optimized viral script")
        print("=" * 70)
        
        # Phoenix script input based on the analysis
        input_data = WritersRoomInput(
            topic="The $60 Billion Gamble That Could Save America's Hottest City",
            golden_nuggets=[
                "Phoenix consumes TWICE the water of NYC despite having half the population - and it's running out fast",
                "Taiwan makes 90% of the world's advanced chips, but China threatens invasion - so America bet $60 BILLION on Phoenix as backup", 
                "Arizona's 1922 water deal was based on 'abnormally high' rainfall data - they've been stealing water that doesn't exist for 100 years",
                "Phoenix hit 113°F for 54 straight days in 2023, breaking every heat record in the city's history",
                "Saudi Arabia was secretly pumping unlimited Arizona groundwater for FREE to grow alfalfa in the desert - until the state finally caught on",
                "One proposed solution: a $5 billion desalination plant in Mexico connected by a 200-mile pipeline through protected desert",
                "If Phoenix fails, America loses its $60 billion hedge against Chinese chip monopoly - and 5 million people lose their homes"
            ],
            persona={
                "channel": "Frontier",
                "voice": "Shocked investigator revealing a climate conspiracy", 
                "focus": "How bad decisions 100 years ago created today's $60B crisis"
            },
            quality_threshold=9.0,  # High bar for emotional impact
            max_rounds=40  # Allow for deep collaboration
        )
        
        print(f"📍 Topic: {input_data.topic}")
        print(f"💎 Golden Nuggets: {len(input_data.golden_nuggets)}")
        print(f"🎭 Persona: {input_data.persona['voice']}")
        print(f"🎯 Quality Threshold: {input_data.quality_threshold}/10")
        print(f"🔄 Max Rounds: {input_data.max_rounds}")
        
        # Check if we have OpenAI API key
        if not os.environ.get("OPENAI_API_KEY"):
            print("\n❌ OPENAI_API_KEY not found in environment")
            print("This is a DRY RUN - showing what would happen:")
            print("\n🎬 6-AGENT COLLABORATION PREVIEW:")
            print("Phase 1: Structure Wars (Director + EmotionalArchitect map crisis arc)")
            print("Phase 2: Content Battle (Hooksmith crafts climate shock hooks)")
            print("Phase 3: Production Polish (Producer adds visual desert cues)")
            print("Phase 4: Quality Gate (All 6 agents validate viral potential)")
            
            print(f"\n📜 EXPECTED OUTPUT:")
            print(f"- Emotional roller coaster about climate crisis")
            print(f"- $60B geopolitical stakes")
            print(f"- Historical conspiracy elements")
            print(f"- Desert city survival drama")
            print(f"- Production-ready with visual cues")
            
            return None
        
        try:
            # Initialize 6-agent Writers' Room
            print(f"\n🎬 INITIALIZING 6-AGENT WRITERS' ROOM...")
            writers_room = RealAutoGenWritersRoom(model="gpt-4")
            
            # Verify emotional architect is present
            if 'emotional_architect' in writers_room.agents:
                print(f"✅ Emotional Arc Specialist: ACTIVE")
            else:
                print(f"❌ Emotional Arc Specialist: MISSING")
                return None
            
            # Run the collaborative session
            print(f"\n🚀 STARTING PHOENIX SCRIPT COLLABORATION...")
            print(f"🔥 Topic: Crisis management in America's hottest city")
            print(f"💰 Stakes: $60 billion + 5 million residents")
            print(f"🎭 Emotional Arc: Climate anxiety → geopolitical stakes → uncertain future")
            
            output = writers_room.run_collaborative_session(input_data)
            
            # Display results
            print(f"\n🎯 COLLABORATION RESULTS:")
            print(f"⏱️ Total Time: {output.execution_time:.1f} seconds")
            print(f"🔄 Total Rounds: {output.collaboration_metrics.total_rounds}")
            print(f"❌ Rejections: {output.collaboration_metrics.rejections}")
            print(f"🔄 Revisions: {output.collaboration_metrics.revisions}")
            print(f"🏆 Final Quality: {output.collaboration_metrics.final_quality_score:.1f}/10")
            
            # Check emotional architect contributions
            emotional_contributions = sum(1 for msg in output.conversation_transcript 
                                        if msg.get('name') == 'EmotionalArchitect')
            print(f"🎭 Emotional Architect Contributions: {emotional_contributions}")
            
            # Show script preview
            if len(output.final_script) > 0:
                print(f"\n📜 PHOENIX SCRIPT PREVIEW:")
                print(f"📊 Script Length: {len(output.final_script):,} characters")
                
                # Show first 500 characters
                preview = output.final_script[:500] + "..." if len(output.final_script) > 500 else output.final_script
                print(f"\n{preview}")
                
                # Save the output
                output_dir = writers_room_path / "outputs" / "phoenix_test"
                output_dir.mkdir(parents=True, exist_ok=True)
                
                output_file = output_dir / "phoenix_60_billion_gamble_6_agents.json"
                writers_room.save_output(output, str(output_file))
                print(f"\n💾 Full output saved to: {output_file}")
                
                # Success evaluation
                success = (
                    output.collaboration_metrics.final_quality_score >= input_data.quality_threshold and
                    emotional_contributions > 0 and
                    len(output.final_script) > 2000  # Substantial script
                )
                
                if success:
                    print(f"\n🎉 6-AGENT PHOENIX SCRIPT: SUCCESS!")
                    print(f"🔥 Emotional engineering: ACTIVE")
                    print(f"💰 Geopolitical stakes: INTEGRATED")
                    print(f"🎭 Viral potential: OPTIMIZED")
                else:
                    print(f"\n⚠️ Script generated but below quality threshold")
                    print(f"Quality: {output.collaboration_metrics.final_quality_score:.1f}/10")
                
                return output
            else:
                print(f"\n❌ No script generated - check collaboration logs")
                return None
                
        except Exception as e:
            print(f"\n💥 ERROR: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    
    if __name__ == "__main__":
        print("🔥 PHOENIX SCRIPT GENERATION TEST")
        print("🎭 Enhanced 6-Agent Writers' Room")
        print("📄 Based on RealLifeLore analysis")
        print("-" * 50)
        
        result = test_phoenix_script_generation()
        
        if result:
            print(f"\n✅ PHOENIX SCRIPT SUCCESSFULLY GENERATED!")
            print(f"🎭 Emotional Arc Specialist: CONTRIBUTED")
            print(f"🚀 6-agent collaboration: COMPLETE")
        else:
            print(f"\n📝 Test completed (see results above)")

except ImportError as e:
    print(f"❌ Import failed: {e}")
    print(f"📋 To run full test:")
    print(f"1. Install AutoGen: pip install pyautogen")
    print(f"2. Set API key: export OPENAI_API_KEY='your-key'")
    print(f"3. Run: python test_phoenix_script.py")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()