/**
 * YouTube Research v2 - AI Agent Status Client
 * Real-time WebSocket client for agent status updates
 */

class AgentStatusClient {
    constructor(sessionId, options = {}) {
        this.sessionId = sessionId;
        this.websocket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
        this.reconnectDelay = options.reconnectDelay || 1000;
        this.pingInterval = options.pingInterval || 30000;
        this.pingTimer = null;
        
        // Event handlers
        this.onAgentUpdate = options.onAgentUpdate || this.defaultAgentUpdateHandler;
        this.onSessionComplete = options.onSessionComplete || this.defaultSessionCompleteHandler;
        this.onConnectionChange = options.onConnectionChange || this.defaultConnectionChangeHandler;
        this.onError = options.onError || this.defaultErrorHandler;
        
        // Agent status tracking
        this.agentStatuses = new Map();
        this.sessionData = null;
        
        this.connect();
    }
    
    connect() {
        try {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/analysis/${this.sessionId}`;
            
            console.log(`🔌 Connecting to WebSocket: ${wsUrl}`);
            this.websocket = new WebSocket(wsUrl);
            
            this.websocket.onopen = this.handleOpen.bind(this);
            this.websocket.onmessage = this.handleMessage.bind(this);
            this.websocket.onclose = this.handleClose.bind(this);
            this.websocket.onerror = this.handleError.bind(this);
            
        } catch (error) {
            console.error('❌ WebSocket connection error:', error);
            this.onError(error);
        }
    }
    
    handleOpen(event) {
        console.log('✅ WebSocket connected successfully');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        this.onConnectionChange(true);
        
        // Start ping timer
        this.startPingTimer();
        
        // Request current status
        this.requestStatus();
    }
    
    handleMessage(event) {
        try {
            const message = JSON.parse(event.data);
            
            switch (message.type) {
                case 'connection_established':
                    console.log('🚀 Connection established:', message.message);
                    break;
                    
                case 'agent_update':
                    this.handleAgentUpdate(message.data);
                    break;
                    
                case 'session_status':
                    this.handleSessionStatus(message.session);
                    break;
                    
                case 'session_complete':
                    this.handleSessionComplete(message);
                    break;
                    
                case 'pong':
                    // Ping response received
                    break;
                    
                default:
                    console.log('📨 Unknown message type:', message.type);
            }
            
        } catch (error) {
            console.error('❌ Error parsing WebSocket message:', error);
        }
    }
    
    handleClose(event) {
        console.log('🔌 WebSocket connection closed:', event.code, event.reason);
        this.isConnected = false;
        this.onConnectionChange(false);
        this.stopPingTimer();
        
        // Attempt reconnection if not intentionally closed
        if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.attemptReconnect();
        }
    }
    
    handleError(event) {
        console.error('❌ WebSocket error:', event);
        this.onError(event);
    }
    
    handleAgentUpdate(agentData) {
        // Update agent status tracking
        this.agentStatuses.set(agentData.agent_id, agentData);
        
        // Call user-defined handler
        this.onAgentUpdate(agentData);
        
        // Update UI elements
        this.updateAgentStatusUI(agentData);
    }
    
    handleSessionStatus(sessionData) {
        this.sessionData = sessionData;
        this.updateSessionStatusUI(sessionData);
    }
    
    handleSessionComplete(data) {
        console.log('🎉 Analysis session completed!');
        this.onSessionComplete(data);
        this.updateCompletionUI(data);
    }
    
    updateAgentStatusUI(agentData) {
        // Update agent status in the UI
        const agentElement = document.getElementById(`agent-${agentData.agent_id}`);
        if (agentElement) {
            // Update agent avatar status
            const avatar = agentElement.querySelector('.agent-avatar');
            if (avatar) {
                avatar.className = `agent-avatar agent-${agentData.agent_id} status-${agentData.status}`;
            }
            
            // Update agent message
            const messageElement = agentElement.querySelector('.agent-message');
            if (messageElement) {
                messageElement.textContent = agentData.message;
                messageElement.classList.add('animate-fadeInUp');
            }
            
            // Update progress bar
            const progressBar = agentElement.querySelector('.agent-progress-fill');
            if (progressBar) {
                progressBar.style.width = `${agentData.progress * 100}%`;
            }
            
            // Update timestamp
            const timestampElement = agentElement.querySelector('.agent-timestamp');
            if (timestampElement) {
                timestampElement.textContent = this.formatTimestamp(agentData.timestamp);
            }
            
            // Add active class for current agent
            if (agentData.status === 'working' || agentData.status === 'thinking') {
                agentElement.classList.add('active');
            } else {
                agentElement.classList.remove('active');
            }
        }
        
        // Update status feed
        this.addToStatusFeed(agentData);
    }
    
    updateSessionStatusUI(sessionData) {
        // Update overall progress
        const overallProgress = document.getElementById('overall-progress');
        if (overallProgress) {
            overallProgress.style.width = `${sessionData.overall_progress * 100}%`;
        }
        
        // Update current agent indicator
        const currentAgentElement = document.getElementById('current-agent');
        if (currentAgentElement && sessionData.current_agent) {
            const agentConfig = this.getAgentConfig(sessionData.current_agent);
            currentAgentElement.textContent = `${agentConfig.name} is working...`;
        }
    }
    
    updateCompletionUI(data) {
        // Show completion message
        const statusContainer = document.getElementById('analysis-status');
        if (statusContainer) {
            statusContainer.innerHTML = `
                <div class="completion-message">
                    <div class="completion-icon">🎉</div>
                    <h3>Analysis Complete!</h3>
                    <p>All AI agents have finished their analysis. Your insights are ready!</p>
                </div>
            `;
        }
        
        // Enable results section
        const resultsSection = document.getElementById('results-section');
        if (resultsSection) {
            resultsSection.style.display = 'block';
            resultsSection.scrollIntoView({ behavior: 'smooth' });
        }
    }
    
    addToStatusFeed(agentData) {
        const statusFeed = document.getElementById('agent-status-feed');
        if (!statusFeed) return;
        
        const statusItem = document.createElement('div');
        statusItem.className = 'agent-status-item animate-fadeInUp';
        statusItem.innerHTML = `
            <div class="agent-avatar agent-${agentData.agent_id}">
                ${this.getAgentIcon(agentData.agent_id)}
            </div>
            <div class="agent-status-content">
                <div class="agent-name">${agentData.agent_name}</div>
                <div class="agent-message">${agentData.message}</div>
                <div class="agent-timestamp">${this.formatTimestamp(agentData.timestamp)}</div>
            </div>
        `;
        
        // Add to top of feed
        statusFeed.insertBefore(statusItem, statusFeed.firstChild);
        
        // Limit feed items to prevent memory issues
        const maxItems = 50;
        while (statusFeed.children.length > maxItems) {
            statusFeed.removeChild(statusFeed.lastChild);
        }
    }
    
    getAgentIcon(agentId) {
        const icons = {
            performance: '📊',
            script: '📝',
            seo: '🔍',
            psychology: '🧠',
            comments: '💬',
            synthesis: '🎯'
        };
        return icons[agentId] || '🤖';
    }
    
    getAgentConfig(agentId) {
        const configs = {
            performance: { name: 'Performance Intelligence' },
            script: { name: 'Script Forensics' },
            seo: { name: 'SEO & Discoverability' },
            psychology: { name: 'Audience Psychology' },
            comments: { name: 'Comment Intelligence' },
            synthesis: { name: 'Strategic Synthesis' }
        };
        return configs[agentId] || { name: agentId.charAt(0).toUpperCase() + agentId.slice(1) };
    }
    
    formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString();
    }
    
    attemptReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // Exponential backoff
        
        console.log(`🔄 Attempting reconnection ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay}ms`);
        
        setTimeout(() => {
            this.connect();
        }, delay);
    }
    
    startPingTimer() {
        this.pingTimer = setInterval(() => {
            if (this.isConnected) {
                this.ping();
            }
        }, this.pingInterval);
    }
    
    stopPingTimer() {
        if (this.pingTimer) {
            clearInterval(this.pingTimer);
            this.pingTimer = null;
        }
    }
    
    ping() {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify({
                type: 'ping',
                timestamp: Date.now()
            }));
        }
    }
    
    requestStatus() {
        if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            this.websocket.send(JSON.stringify({
                type: 'request_status'
            }));
        }
    }
    
    disconnect() {
        console.log('🔌 Disconnecting WebSocket');
        this.stopPingTimer();
        
        if (this.websocket) {
            this.websocket.close(1000, 'Client disconnect');
        }
    }
    
    // Default event handlers
    defaultAgentUpdateHandler(agentData) {
        console.log('🤖 Agent update:', agentData.agent_name, '-', agentData.message);
    }
    
    defaultSessionCompleteHandler(data) {
        console.log('🎉 Session complete:', data);
    }
    
    defaultConnectionChangeHandler(isConnected) {
        console.log('🔌 Connection status:', isConnected ? 'Connected' : 'Disconnected');
    }
    
    defaultErrorHandler(error) {
        console.error('❌ Agent status client error:', error);
    }
    
    // Public API methods
    getAgentStatus(agentId) {
        return this.agentStatuses.get(agentId);
    }
    
    getAllAgentStatuses() {
        return Object.fromEntries(this.agentStatuses);
    }
    
    getSessionData() {
        return this.sessionData;
    }
    
    isAgentComplete(agentId) {
        const status = this.agentStatuses.get(agentId);
        return status && status.status === 'complete';
    }
    
    areAllAgentsComplete() {
        const expectedAgents = ['performance', 'script', 'seo', 'psychology', 'comments', 'synthesis'];
        return expectedAgents.every(agentId => this.isAgentComplete(agentId));
    }
}

// Global utility functions
window.AgentStatusClient = AgentStatusClient;

// Auto-initialize if session ID is available
document.addEventListener('DOMContentLoaded', function() {
    const sessionIdElement = document.getElementById('analysis-session-id');
    if (sessionIdElement) {
        const sessionId = sessionIdElement.value || sessionIdElement.textContent;
        if (sessionId) {
            window.agentStatusClient = new AgentStatusClient(sessionId, {
                onAgentUpdate: function(agentData) {
                    // Custom handler can be defined by the page
                    if (window.handleAgentUpdate) {
                        window.handleAgentUpdate(agentData);
                    }
                },
                onSessionComplete: function(data) {
                    // Custom handler can be defined by the page
                    if (window.handleSessionComplete) {
                        window.handleSessionComplete(data);
                    }
                }
            });
        }
    }
});
