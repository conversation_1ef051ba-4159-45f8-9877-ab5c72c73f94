"""
YouTube Research v2 - Working FastAPI Application
Channel DNA Analysis with Gemini AI Integration
"""

import os
import uuid
import asyncio
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import <PERSON><PERSON><PERSON>, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import our enhanced analyzer
from enhanced_analyzer import EnhancedChannelAnalyzer

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="YouTube Research v2 - Channel DNA Analyzer",
    description="CrewAI-powered YouTube channel analysis with world-class strategic insights",
    version="2.0.0"
)

# Enable CORS for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage for demo (replace with database in production)
analysis_store: Dict[str, Dict[str, Any]] = {}

# Initialize analyzer
youtube_analyzer = None

def get_analyzer():
    """Get or create Enhanced YouTube analyzer instance"""
    global youtube_analyzer
    if youtube_analyzer is None:
        youtube_api_key = os.getenv('YOUTUBE_API_KEY')
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        
        if not youtube_api_key:
            raise HTTPException(status_code=500, detail="YouTube API key not configured")
        
        youtube_analyzer = EnhancedChannelAnalyzer(youtube_api_key, gemini_api_key)
    return youtube_analyzer

# Pydantic models
class AnalysisRequest(BaseModel):
    channel_id: str
    max_videos: int = 20
    max_comments: int = 50

class AnalysisResponse(BaseModel):
    analysis_id: str
    status: str
    message: str
    estimated_duration_seconds: int = 30

class AnalysisStatus(BaseModel):
    analysis_id: str
    status: str
    progress_percentage: int
    current_step: str
    estimated_remaining_seconds: int
    channel_name: Optional[str] = None
    results: Optional[Dict[str, Any]] = None

# API Routes

@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve the main web interface"""
    return get_web_interface()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "2.0.0"
    }

@app.post("/api/analyze", response_model=AnalysisResponse)
async def start_analysis(request: AnalysisRequest, background_tasks: BackgroundTasks):
    """Start a new Channel DNA analysis"""
    
    try:
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        
        # Create initial analysis record
        analysis_record = {
            "analysis_id": analysis_id,
            "channel_id": request.channel_id,
            "status": "pending",
            "created_at": datetime.utcnow(),
            "progress": 0,
            "current_step": "Initializing analysis...",
            "results": None,
            "error": None
        }
        
        analysis_store[analysis_id] = analysis_record
        
        # Start background analysis
        background_tasks.add_task(
            run_channel_analysis,
            analysis_id,
            request.channel_id,
            request.max_videos,
            request.max_comments
        )
        
        logger.info(f"Started analysis {analysis_id} for channel {request.channel_id}")
        
        return AnalysisResponse(
            analysis_id=analysis_id,
            status="pending",
            message=f"Analysis started for channel {request.channel_id}",
            estimated_duration_seconds=30
        )
        
    except Exception as e:
        logger.error(f"Error starting analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/status/{analysis_id}", response_model=AnalysisStatus)
async def get_analysis_status(analysis_id: str):
    """Get the status of an analysis"""
    
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    record = analysis_store[analysis_id]
    
    # Calculate progress and remaining time
    progress = record.get("progress", 0)
    estimated_remaining = max(0, 30 - int((datetime.utcnow() - record["created_at"]).total_seconds()))
    
    return AnalysisStatus(
        analysis_id=analysis_id,
        status=record["status"],
        progress_percentage=progress,
        current_step=record.get("current_step", "Processing..."),
        estimated_remaining_seconds=estimated_remaining,
        channel_name=record.get("channel_name"),
        results=record.get("results") if record["status"] == "completed" else None
    )

@app.get("/api/results/{analysis_id}")
async def get_analysis_results(analysis_id: str):
    """Get complete analysis results"""
    
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    record = analysis_store[analysis_id]
    
    if record["status"] != "completed":
        raise HTTPException(
            status_code=400,
            detail=f"Analysis not completed. Current status: {record['status']}"
        )
    
    return record["results"]

@app.get("/api/recent")
async def get_recent_analyses():
    """Get recent analyses"""
    
    recent = sorted(
        analysis_store.values(),
        key=lambda x: x["created_at"],
        reverse=True
    )[:10]
    
    return [
        {
            "analysis_id": r["analysis_id"],
            "channel_id": r["channel_id"],
            "channel_name": r.get("channel_name", "Unknown"),
            "status": r["status"],
            "created_at": r["created_at"]
        }
        for r in recent
    ]

@app.delete("/api/analysis/{analysis_id}")
async def delete_analysis(analysis_id: str):
    """Delete an analysis"""
    
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    del analysis_store[analysis_id]
    return {"message": f"Analysis {analysis_id} deleted"}

# Background task function
async def run_channel_analysis(analysis_id: str, channel_id: str, max_videos: int, max_comments: int):
    """Run the channel analysis in the background"""
    
    try:
        # Update status
        analysis_store[analysis_id]["status"] = "in_progress"
        analysis_store[analysis_id]["current_step"] = "Fetching channel data..."
        analysis_store[analysis_id]["progress"] = 10
        
        # Get analyzer
        analyzer = get_analyzer()
        
        # Run analysis
        analysis_store[analysis_id]["current_step"] = "Analyzing channel patterns..."
        analysis_store[analysis_id]["progress"] = 30
        
        results = analyzer.analyze_channel(channel_id, max_videos, max_comments)
        
        if results:
            # Extract channel name
            channel_name = "Unknown Channel"
            if "channel_profile" in results:
                # We need to get channel name from the original data
                try:
                    channel_info = analyzer._get_channel_info(channel_id)
                    if channel_info:
                        channel_name = channel_info['snippet']['title']
                except:
                    pass
            
            # Update with results
            analysis_store[analysis_id].update({
                "status": "completed",
                "progress": 100,
                "current_step": "Analysis complete",
                "channel_name": channel_name,
                "results": results,
                "completed_at": datetime.utcnow()
            })
            
            logger.info(f"Completed analysis {analysis_id} for {channel_name}")
            
        else:
            raise Exception("Analysis returned no results")
            
    except Exception as e:
        logger.error(f"Error in analysis {analysis_id}: {e}")
        analysis_store[analysis_id].update({
            "status": "failed",
            "progress": 0,
            "current_step": f"Error: {str(e)}",
            "error": str(e)
        })

def get_web_interface():
    """Return the HTML web interface"""
    return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Research v2 - Channel DNA Analyzer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .input-section {
            display: grid;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .input-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .input-row {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr;
            gap: 15px;
        }
        
        label {
            font-weight: 600;
            color: #555;
        }
        
        input, select {
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .status-section {
            margin-top: 30px;
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e1e5e9;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .status-text {
            color: #666;
            margin-bottom: 10px;
        }
        
        .results-section {
            margin-top: 30px;
            display: none;
        }
        
        .dna-component {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #667eea;
        }
        
        .dna-component h3 {
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .metric:last-child {
            border-bottom: none;
        }
        
        .metric-label {
            font-weight: 500;
            color: #555;
        }
        
        .metric-value {
            color: #333;
            font-weight: 600;
        }
        
        .error {
            background: #fee;
            color: #c33;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .success {
            background: #efe;
            color: #363;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .recent-analyses {
            margin-top: 30px;
        }
        
        .recent-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .recent-channel {
            font-weight: 600;
            color: #333;
        }
        
        .recent-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-completed {
            background: #d4edda;
            color: #155724;
        }
        
        .status-failed {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-in_progress {
            background: #fff3cd;
            color: #856404;
        }
        
        .ai-insights {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-left: 4px solid #28a745;
        }
        
        .ai-insight {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .ai-insight h4 {
            color: #28a745;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
        }
        
        .ai-content {
            line-height: 1.6;
            color: #333;
        }
        
        .ai-content p {
            margin-bottom: 15px;
        }
        
        .ai-content strong {
            color: #28a745;
            font-weight: 600;
        }
        
        .ai-content em {
            color: #6c757d;
            font-style: italic;
        }
        
        .ai-insight.error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .ai-insight.error h4 {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 YouTube Research v2</h1>
            <p>Channel DNA Analyzer - Powered by CrewAI</p>
        </div>
        
        <div class="card">
            <h2>Channel Analysis</h2>
            <div class="input-section">
                <div class="input-row">
                    <div class="input-group">
                        <label for="channelId">YouTube Channel ID or @Handle</label>
                        <input type="text" id="channelId" placeholder="e.g., UCX6OQ3DkcsbYNE6H8uQQuVA or @MrBeast" />
                    </div>
                    <div class="input-group">
                        <label for="maxVideos">Max Videos</label>
                        <input type="number" id="maxVideos" value="20" min="5" max="50" />
                    </div>
                    <div class="input-group">
                        <label for="maxComments">Max Comments</label>
                        <input type="number" id="maxComments" value="50" min="10" max="100" />
                    </div>
                </div>
                <button class="btn" onclick="startAnalysis()">🚀 Start Channel DNA Analysis</button>
            </div>
            
            <div class="status-section" id="statusSection">
                <h3>Analysis Status</h3>
                <div class="status-text" id="statusText">Preparing analysis...</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div id="estimatedTime"></div>
            </div>
        </div>
        
        <div class="card results-section" id="resultsSection">
            <h2>Channel DNA Results</h2>
            <div id="resultsContent"></div>
        </div>
        
        <div class="card">
            <h2>Recent Analyses</h2>
            <div id="recentAnalyses">
                <div class="loading">
                    <div class="spinner"></div>
                    Loading recent analyses...
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentAnalysisId = null;
        let statusInterval = null;
        
        // Load recent analyses on page load
        window.onload = function() {
            loadRecentAnalyses();
        };
        
        async function startAnalysis() {
            const channelId = document.getElementById('channelId').value.trim();
            const maxVideos = parseInt(document.getElementById('maxVideos').value);
            const maxComments = parseInt(document.getElementById('maxComments').value);
            
            if (!channelId) {
                showError('Please enter a channel ID or handle');
                return;
            }
            
            // Extract channel ID from handle if needed
            const cleanChannelId = channelId.startsWith('@') ? channelId.substring(1) : channelId;
            
            try {
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        channel_id: cleanChannelId,
                        max_videos: maxVideos,
                        max_comments: maxComments
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                currentAnalysisId = data.analysis_id;
                
                // Show status section
                document.getElementById('statusSection').style.display = 'block';
                document.getElementById('resultsSection').style.display = 'none';
                
                // Start polling for status
                startStatusPolling();
                
                showSuccess(`Analysis started! ID: ${data.analysis_id}`);
                
            } catch (error) {
                showError(`Error starting analysis: ${error.message}`);
            }
        }
        
        function startStatusPolling() {
            if (statusInterval) {
                clearInterval(statusInterval);
            }
            
            statusInterval = setInterval(async () => {
                if (!currentAnalysisId) return;
                
                try {
                    const response = await fetch(`/api/status/${currentAnalysisId}`);
                    const status = await response.json();
                    
                    updateStatusDisplay(status);
                    
                    if (status.status === 'completed') {
                        clearInterval(statusInterval);
                        await loadResults(currentAnalysisId);
                        loadRecentAnalyses(); // Refresh recent analyses
                    } else if (status.status === 'failed') {
                        clearInterval(statusInterval);
                        showError(`Analysis failed: ${status.current_step}`);
                    }
                    
                } catch (error) {
                    console.error('Error polling status:', error);
                }
            }, 2000); // Poll every 2 seconds
        }
        
        function updateStatusDisplay(status) {
            document.getElementById('statusText').textContent = status.current_step;
            document.getElementById('progressFill').style.width = `${status.progress_percentage}%`;
            
            if (status.estimated_remaining_seconds > 0) {
                document.getElementById('estimatedTime').textContent = 
                    `Estimated time remaining: ${status.estimated_remaining_seconds} seconds`;
            } else {
                document.getElementById('estimatedTime').textContent = '';
            }
        }
        
        async function loadResults(analysisId) {
            try {
                const response = await fetch(`/api/results/${analysisId}`);
                const results = await response.json();
                
                displayResults(results);
                document.getElementById('resultsSection').style.display = 'block';
                
            } catch (error) {
                showError(`Error loading results: ${error.message}`);
            }
        }
        
        function displayResults(results) {
            const content = document.getElementById('resultsContent');
            
            let html = '';
            
            // Channel Profile
            if (results.channel_profile) {
                html += createDNAComponent('🏢 Channel Profile', results.channel_profile);
            }
            
            // Content Patterns
            if (results.content_patterns) {
                html += createDNAComponent('📝 Content Patterns', results.content_patterns);
            }
            
            // Audience Psychology
            if (results.audience_psychology && !results.audience_psychology.error) {
                html += createDNAComponent('👥 Audience Psychology', results.audience_psychology);
            }
            
            // Performance Metrics
            if (results.performance_metrics) {
                html += createDNAComponent('📊 Performance Metrics', results.performance_metrics);
            }
            
            // Strategic Recommendations
            if (results.strategic_recommendations) {
                html += createDNAComponent('🎯 Strategic Recommendations', results.strategic_recommendations);
            }
            
            // AI Insights (Enhanced Analysis)
            if (results.ai_insights) {
                html += createAIInsights('🤖 AI-Powered Strategic Insights', results.ai_insights);
            }
            
            content.innerHTML = html;
        }
        
        function createDNAComponent(title, data) {
            let html = `<div class="dna-component"><h3>${title}</h3>`;
            
            function addMetrics(obj, prefix = '') {
                for (const [key, value] of Object.entries(obj)) {
                    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                        html += `<div class="metric"><span class="metric-label">${prefix}${formatKey(key)}</span><span class="metric-value">Object</span></div>`;
                        addMetrics(value, `${prefix}  `);
                    } else if (Array.isArray(value)) {
                        html += `<div class="metric"><span class="metric-label">${prefix}${formatKey(key)}</span><span class="metric-value">${value.length} items</span></div>`;
                    } else {
                        const displayValue = typeof value === 'number' ? 
                            (value < 1 ? (value * 100).toFixed(2) + '%' : value.toLocaleString()) : 
                            String(value);
                        html += `<div class="metric"><span class="metric-label">${prefix}${formatKey(key)}</span><span class="metric-value">${displayValue}</span></div>`;
                    }
                }
            }
            
            addMetrics(data);
            html += '</div>';
            return html;
        }
        
        function formatKey(key) {
            return key.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());
        }
        
        function createAIInsights(title, insights) {
            let html = `<div class="dna-component ai-insights"><h3>${title}</h3>`;
            
            for (const [key, value] of Object.entries(insights)) {
                if (key === 'error') {
                    html += `<div class="ai-insight error"><h4>⚠️ AI Analysis Error</h4><p>${value}</p></div>`;
                } else {
                    const sectionTitle = formatAIKey(key);
                    const content = typeof value === 'string' ? value : JSON.stringify(value, null, 2);
                    html += `
                        <div class="ai-insight">
                            <h4>${sectionTitle}</h4>
                            <div class="ai-content">${formatAIContent(content)}</div>
                        </div>
                    `;
                }
            }
            
            html += '</div>';
            return html;
        }
        
        function formatAIKey(key) {
            const keyMap = {
                'channel_strategy': '🏢 Channel Strategy Analysis',
                'content_narrative': '📝 Content DNA Deep Dive',
                'audience_psychology': '👥 Audience Psychology Insights',
                'competitive_analysis': '🎯 Competitive Positioning',
                'strategic_roadmap': '🗺️ Strategic Roadmap'
            };
            return keyMap[key] || formatKey(key);
        }
        
        function formatAIContent(content) {
            // Convert markdown-style formatting to HTML
            return content
                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')
                .replace(/\\n\\n/g, '</p><p>')
                .replace(/\\n/g, '<br>')
                .replace(/^/, '<p>')
                .replace(/$/, '</p>');
        }
        
        async function loadRecentAnalyses() {
            try {
                const response = await fetch('/api/recent');
                const analyses = await response.json();
                
                const container = document.getElementById('recentAnalyses');
                
                if (analyses.length === 0) {
                    container.innerHTML = '<p>No recent analyses found.</p>';
                    return;
                }
                
                let html = '';
                analyses.forEach(analysis => {
                    const date = new Date(analysis.created_at).toLocaleString();
                    html += `
                        <div class="recent-item">
                            <div>
                                <div class="recent-channel">${analysis.channel_name}</div>
                                <div style="font-size: 12px; color: #666;">${analysis.channel_id} • ${date}</div>
                            </div>
                            <div class="recent-status status-${analysis.status}">${analysis.status}</div>
                        </div>
                    `;
                });
                
                container.innerHTML = html;
                
            } catch (error) {
                document.getElementById('recentAnalyses').innerHTML = 
                    '<p style="color: #c33;">Error loading recent analyses</p>';
            }
        }
        
        function showError(message) {
            const existing = document.querySelector('.error');
            if (existing) existing.remove();
            
            const error = document.createElement('div');
            error.className = 'error';
            error.textContent = message;
            document.querySelector('.card').appendChild(error);
            
            setTimeout(() => error.remove(), 5000);
        }
        
        function showSuccess(message) {
            const existing = document.querySelector('.success');
            if (existing) existing.remove();
            
            const success = document.createElement('div');
            success.className = 'success';
            success.textContent = message;
            document.querySelector('.card').appendChild(success);
            
            setTimeout(() => success.remove(), 5000);
        }
    </script>
</body>
</html>
"""

if __name__ == "__main__":
    import uvicorn
    
    # Check environment variables
    required_vars = ['YOUTUBE_API_KEY']
    optional_vars = ['GEMINI_API_KEY']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these in your .env file")
        exit(1)
    
    print("🚀 Starting YouTube Research v2 Server...")
    print("📊 Channel DNA Analyzer Ready")
    
    # Check for optional Gemini API key
    if os.getenv('GEMINI_API_KEY'):
        print("🤖 AI Insights: Enabled (Gemini 2.0 Flash)")
    else:
        print("⚠️  AI Insights: Disabled (Add GEMINI_API_KEY for enhanced analysis)")
    
    print("🌐 Open http://localhost:8000 in your browser")
    print("-" * 50)
    
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")