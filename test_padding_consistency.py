#!/usr/bin/env python3
"""
Test Padding Consistency Across All Pages
"""

import requests
import time
import subprocess
import sys
import os
from datetime import datetime

class PaddingConsistencyTester:
    def __init__(self):
        self.base_url = "http://localhost:8003"
        self.server_process = None
        self.test_pages = [
            ("/", "Dashboard"),
            ("/video-analyzer", "Video Analyzer"),
            ("/channel-analyzer", "Channel Analyzer"),
            ("/market-research", "Market Research Hub"),
            ("/agent/comment", "Comment Intelligence")
        ]
    
    def start_server(self, timeout=30):
        """Start the main application server"""
        print("🚀 Starting server...")
        try:
            self.server_process = subprocess.Popen(
                [sys.executable, "working_crewai_app_tabbed.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=5)
                    if response.status_code == 200:
                        print("✅ Server started successfully")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)
                    continue
            
            print("❌ Server failed to start within timeout")
            return False
            
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return False
    
    def stop_server(self):
        """Stop the server"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                print("🛑 Server stopped")
            except subprocess.TimeoutExpired:
                self.server_process.kill()
                print("🛑 Server force killed")
            except Exception as e:
                print(f"⚠️ Error stopping server: {e}")
    
    def test_premium_content_class(self):
        """Test if all pages have the premium-content class"""
        print("\n📄 Testing Premium Content Class Across Pages...")
        
        all_pages_pass = True
        
        for url, name in self.test_pages:
            try:
                response = requests.get(f"{self.base_url}{url}", timeout=10)
                if response.status_code == 200:
                    html_content = response.text
                    
                    if 'class="premium-content"' in html_content:
                        print(f"   ✅ {name}: Has premium-content class")
                    else:
                        print(f"   ❌ {name}: Missing premium-content class")
                        all_pages_pass = False
                        
                else:
                    print(f"   ❌ {name}: Failed to load (status: {response.status_code})")
                    all_pages_pass = False
                    
            except Exception as e:
                print(f"   ❌ {name}: Error loading page: {e}")
                all_pages_pass = False
        
        return all_pages_pass
    
    def test_css_padding_styles(self):
        """Test if CSS contains proper premium-content padding"""
        print("\n🎨 Testing CSS Padding Styles...")
        try:
            response = requests.get(f"{self.base_url}/static/styles/youtube-research-v2-design-system.css", timeout=10)
            if response.status_code == 200:
                css_content = response.text
                
                # Check for premium-content padding
                required_styles = [
                    '.premium-content',  # Class exists
                    'padding: var(--space-2xl) var(--space-xl)',  # Proper padding
                    'flex: 1',  # Proper flex behavior
                    'background: var(--bg-secondary)',  # Background
                    'max-width: var(--content-max-width)',  # Max width
                    'margin: 0 auto'  # Centering
                ]
                
                missing_styles = []
                for style in required_styles:
                    if style not in css_content:
                        missing_styles.append(style)
                
                if missing_styles:
                    print(f"❌ Missing CSS styles: {missing_styles}")
                    return False
                else:
                    print("✅ All required CSS padding styles found")
                    
                    # Check responsive padding
                    if 'padding: var(--space-xl) var(--space-lg)' in css_content:
                        print("✅ Responsive padding for mobile devices found")
                    else:
                        print("⚠️ Responsive padding may be missing")
                    
                    return True
            else:
                print(f"❌ CSS file not accessible: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error accessing CSS file: {e}")
            return False
    
    def test_visual_consistency(self):
        """Test that all pages load and have consistent structure"""
        print("\n🔍 Testing Visual Consistency...")
        
        all_consistent = True
        
        for url, name in self.test_pages:
            try:
                response = requests.get(f"{self.base_url}{url}", timeout=10)
                if response.status_code == 200:
                    html_content = response.text
                    
                    # Check for consistent structure elements
                    structure_elements = [
                        'class="app-layout"',  # Main layout
                        'class="app-sidebar"',  # Sidebar
                        'class="app-main"',  # Main content area
                        'class="app-header"',  # Header
                        'class="premium-content"'  # Content wrapper
                    ]
                    
                    missing_elements = []
                    for element in structure_elements:
                        if element not in html_content:
                            missing_elements.append(element)
                    
                    if missing_elements:
                        print(f"   ❌ {name}: Missing structure elements: {missing_elements}")
                        all_consistent = False
                    else:
                        print(f"   ✅ {name}: All structure elements present")
                        
                else:
                    print(f"   ❌ {name}: Failed to load (status: {response.status_code})")
                    all_consistent = False
                    
            except Exception as e:
                print(f"   ❌ {name}: Error loading page: {e}")
                all_consistent = False
        
        return all_consistent
    
    def run_tests(self):
        """Run all tests"""
        print("📏 Padding Consistency Test")
        print("=" * 50)
        
        # Start server
        if not self.start_server():
            return False
        
        try:
            # Test premium-content class presence
            class_test = self.test_premium_content_class()
            
            # Test CSS padding styles
            css_test = self.test_css_padding_styles()
            
            # Test visual consistency
            consistency_test = self.test_visual_consistency()
            
            # Summary
            print(f"\n📊 Test Results:")
            print(f"   Premium Content Class: {'✅ PASS' if class_test else '❌ FAIL'}")
            print(f"   CSS Padding Styles: {'✅ PASS' if css_test else '❌ FAIL'}")
            print(f"   Visual Consistency: {'✅ PASS' if consistency_test else '❌ FAIL'}")
            
            if class_test and css_test and consistency_test:
                print(f"\n🎉 All tests passed! Padding is consistent across all pages.")
                print(f"   ✨ All pages use .premium-content class")
                print(f"   ✨ Consistent padding: var(--space-2xl) var(--space-xl)")
                print(f"   ✨ Responsive padding for mobile devices")
                print(f"   ✨ Professional spacing between sidebar and main content")
                return True
            else:
                print(f"\n⚠️ Some tests failed. Padding consistency needs attention.")
                return False
                
        finally:
            self.stop_server()

if __name__ == "__main__":
    tester = PaddingConsistencyTester()
    success = tester.run_tests()
    sys.exit(0 if success else 1)
