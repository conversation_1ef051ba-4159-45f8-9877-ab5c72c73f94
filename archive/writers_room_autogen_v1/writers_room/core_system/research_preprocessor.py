#!/usr/bin/env python3
"""
Research Preprocessor - Digests research ONCE before Writers' Room
Saves massive API costs by avoiding 6 agents reading same research 4 times
"""

import json
from typing import Dict, List, Any
from dataclasses import dataclass
import autogen
from autogen import AssistantAgent, UserProxyAgent


@dataclass
class ProcessedResearch:
    """Digested research for each agent role"""
    director_insights: str  # Structure patterns, successful formats
    hooksmith_patterns: str  # Viral hooks, opening strategies  
    narrative_elements: str  # Story beats, tension patterns
    emotional_triggers: str  # Psychological patterns, engagement peaks
    production_notes: str    # Visual/audio elements that work
    closing_strategies: str  # Successful ending patterns
    raw_summary: str        # General summary for all


class ResearchPreprocessor:
    """Processes research ONCE to create role-specific insights"""
    
    def __init__(self, model: str = "gpt-3.5-turbo", api_key: str = None):
        """Initialize with cheap model for preprocessing"""
        
        if api_key:
            import os
            os.environ["OPENAI_API_KEY"] = api_key
            
        self.llm_config = {
            "config_list": [{
                "model": model,
                "api_key": os.environ.get("OPENAI_API_KEY"),
            }],
            "temperature": 0.3,  # Low temp for accurate extraction
            "max_tokens": 1500,  # Controlled output size
        }
        
        # Single research analyzer agent
        self.analyzer = AssistantAgent(
            name="ResearchAnalyzer",
            system_message="""You are a RESEARCH PREPROCESSOR for video script creation.

Your job: Extract role-specific insights from research documents.

For each analysis, identify:
1. DIRECTOR: Successful video structures, pacing patterns, retention strategies
2. HOOKSMITH: Viral hooks, opening patterns, shock value elements
3. NARRATIVE: Story progression, tension patterns, transition techniques
4. EMOTIONAL: Psychological triggers, engagement peaks, memorable moments
5. PRODUCTION: Visual elements, audio cues, production techniques
6. CLOSING: Ending strategies, CTA patterns, engagement triggers

Extract SPECIFIC EXAMPLES with timestamps/quotes when available.
Focus on ACTIONABLE PATTERNS not general observations.""",
            llm_config=self.llm_config,
            human_input_mode="NEVER"
        )
        
        # User proxy for interaction
        self.user_proxy = UserProxyAgent(
            name="Researcher",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=0,
            code_execution_config=False
        )
    
    def process_research(self, research_text: str, video_analysis: Dict = None) -> ProcessedResearch:
        """Process research into role-specific insights"""
        
        print("📊 PREPROCESSING RESEARCH (Cost-Optimized)")
        print(f"📏 Research Length: {len(research_text.split())} words")
        
        # Construct analysis prompt
        prompt = f"""
ANALYZE THIS RESEARCH FOR VIDEO SCRIPT CREATION:

{research_text[:5000]}  # Limit to 5000 chars for cost control

{f"VIDEO ANALYSIS DATA: {json.dumps(video_analysis)[:2000]}" if video_analysis else ""}

EXTRACT ROLE-SPECIFIC INSIGHTS:

1. DIRECTOR INSIGHTS (Structure & Pacing):
- Successful video structures used
- Pacing patterns that maintain retention
- Beat timing and transitions

2. HOOKSMITH PATTERNS (Viral Openings):
- Successful hook examples with metrics
- Shock value elements that worked
- Opening formulas identified

3. NARRATIVE ELEMENTS (Story Patterns):
- Tension building techniques
- Story progression patterns
- Successful narrative devices

4. EMOTIONAL TRIGGERS (Psychology):
- Specific emotional peaks identified
- Psychological patterns that engage
- Memory formation moments

5. PRODUCTION NOTES (Visual/Audio):
- Visual elements mentioned
- Audio/music patterns
- Production techniques

6. CLOSING STRATEGIES (Endings):
- Successful ending patterns
- Engagement triggers used
- CTA effectiveness

Provide SPECIFIC EXAMPLES and ACTIONABLE INSIGHTS for each role.
"""
        
        # Single analysis pass
        self.user_proxy.initiate_chat(
            self.analyzer,
            message=prompt,
            clear_history=True
        )
        
        # Extract insights from response
        response = self.analyzer.last_message()["content"]
        
        # Parse response into sections
        sections = self._parse_insights(response)
        
        return ProcessedResearch(
            director_insights=sections.get("director", ""),
            hooksmith_patterns=sections.get("hooksmith", ""),
            narrative_elements=sections.get("narrative", ""),
            emotional_triggers=sections.get("emotional", ""),
            production_notes=sections.get("production", ""),
            closing_strategies=sections.get("closing", ""),
            raw_summary=response[:1000]  # First 1000 chars as summary
        )
    
    def _parse_insights(self, response: str) -> Dict[str, str]:
        """Parse response into role sections"""
        
        sections = {
            "director": "",
            "hooksmith": "",
            "narrative": "",
            "emotional": "",
            "production": "",
            "closing": ""
        }
        
        # Simple parsing by keywords
        current_section = None
        lines = response.split('\n')
        
        for line in lines:
            lower_line = line.lower()
            
            # Identify section changes
            if "director" in lower_line and "insight" in lower_line:
                current_section = "director"
            elif "hooksmith" in lower_line or "hook" in lower_line and "pattern" in lower_line:
                current_section = "hooksmith"
            elif "narrative" in lower_line and "element" in lower_line:
                current_section = "narrative"
            elif "emotional" in lower_line and "trigger" in lower_line:
                current_section = "emotional"
            elif "production" in lower_line and "note" in lower_line:
                current_section = "production"
            elif "closing" in lower_line and "strateg" in lower_line:
                current_section = "closing"
            
            # Add content to current section
            if current_section and line.strip():
                sections[current_section] += line + "\n"
        
        return sections
    
    def calculate_cost(self, research_length: int) -> Dict[str, float]:
        """Estimate preprocessing cost"""
        
        # Tokens estimation
        input_tokens = research_length * 1.3  # ~1.3 tokens per word
        output_tokens = 1500  # Controlled output
        
        # GPT-3.5 pricing
        input_cost = (input_tokens / 1000) * 0.0005
        output_cost = (output_tokens / 1000) * 0.0015
        
        return {
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "total_cost": input_cost + output_cost,
            "cost_per_agent_saved": (input_cost * 6 * 4)  # What we save by not having each agent read it
        }


def test_preprocessor():
    """Test the research preprocessor"""
    
    sample_research = """
    The Phoenix video analysis shows several key patterns:
    
    1. Hook Success: The video opens with "This city should not exist" - immediate 
    cognitive dissonance that achieved 15% higher CTR than channel average.
    
    2. Emotional Journey: Peaks every 45 seconds with revelations:
    - 0:45 - Water consumption shock (2x NYC)
    - 1:30 - Temperature records (113°F for 54 days)
    - 2:15 - Saudi water theft revelation
    - 3:00 - $60B semiconductor gamble
    
    3. Visual Elements: Heavy use of aerial shots, data visualizations, and 
    before/after comparisons. Map animations particularly effective.
    
    4. Ending Strategy: Unresolved question about Phoenix's future triggered 
    3x more comments than average, with 68% expressing concern about climate.
    """
    
    preprocessor = ResearchPreprocessor()
    
    # Calculate cost
    word_count = len(sample_research.split())
    cost_estimate = preprocessor.calculate_cost(word_count)
    
    print(f"\n💰 COST ESTIMATE:")
    print(f"Research preprocessing: ${cost_estimate['total_cost']:.4f}")
    print(f"Savings vs all agents reading: ${cost_estimate['cost_per_agent_saved']:.4f}")
    
    # Process research
    processed = preprocessor.process_research(sample_research)
    
    print(f"\n✅ PROCESSED RESEARCH:")
    print(f"Director Insights: {len(processed.director_insights)} chars")
    print(f"Hooksmith Patterns: {len(processed.hooksmith_patterns)} chars")
    print(f"Narrative Elements: {len(processed.narrative_elements)} chars")
    
    return processed


if __name__ == "__main__":
    test_preprocessor()