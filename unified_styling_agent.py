"""
UNIFIED VIDEO STYLING AGENT
Single master agent that transforms all 5 analysis reports into beautiful unified interface
STRICT RULE: NO CONTENT CREATION - ONLY VISUAL FORMATTING
"""

from crewai import Agent, Task, Crew
from crewai.llm import LLM
import os
import re
import time
from bs4 import BeautifulSoup
from difflib import SequenceMatcher
import logging

logger = logging.getLogger(__name__)

class UnifiedVideoStylingAgent:
    """Master styling agent for all analysis tabs - FORMATTING ONLY, NO CONTENT CREATION"""
    
    def __init__(self, llm):
        self.llm = llm
        self.design_tokens = self._load_essential_design_tokens()
        
    def _load_essential_design_tokens(self):
        """Load only essential design tokens (not full CSS) to reduce token usage"""
        
        # Extract only the essential classes and variables, not the full 2000+ line CSS
        essential_tokens = """
/* ESSENTIAL DESIGN TOKENS */
:root {
  --primary-gradient: linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%);
  --bg-primary: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
  --surface-glass: rgba(255, 255, 255, 0.08);
  --surface-card: rgba(30, 41, 59, 0.8);
  --text-primary: #FAFAFA;
  --text-secondary: #E2E8F0;
  --text-tertiary: #94A3B8;
  --space-xs: 0.5rem; --space-sm: 1rem; --space-md: 1.5rem; --space-lg: 2rem; --space-xl: 3rem;
  --radius-md: 0.75rem; --radius-lg: 1rem; --radius-xl: 1.5rem;
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* COMPONENT CLASSES */
.hero-grade-section { background: var(--surface-glass); padding: var(--space-lg); border-radius: var(--radius-xl); }
.grade-circle { width: 120px; height: 120px; border-radius: 50%; display: flex; align-items: center; justify-content: center; }
.grade-a { background: linear-gradient(135deg, #10B981 0%, #059669 100%); }
.grade-b { background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%); }
.grade-c { background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%); }
.grade-text { font-size: 2.5rem; font-weight: 800; color: white; }

.metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--space-md); }
.metric-card { background: var(--surface-card); padding: var(--space-md); border-radius: var(--radius-lg); text-align: center; }
.metric-value { font-size: 2rem; font-weight: 700; background: var(--primary-gradient); -webkit-background-clip: text; -webkit-text-fill-color: transparent; }
.metric-label { color: var(--text-tertiary); margin-top: var(--space-xs); }

.insight-card { background: var(--surface-glass); padding: var(--space-lg); border-radius: var(--radius-lg); margin: var(--space-md) 0; }
.insight-header { font-size: 1.25rem; font-weight: 600; color: var(--text-primary); margin-bottom: var(--space-sm); }
.insight-content { color: var(--text-secondary); line-height: 1.6; }

.recommendation-item { background: var(--surface-card); padding: var(--space-md); border-radius: var(--radius-md); margin: var(--space-sm) 0; }
.recommendation-number { background: var(--primary-gradient); color: white; width: 24px; height: 24px; border-radius: 50%; display: inline-flex; align-items: center; justify-content: center; font-weight: 600; margin-right: var(--space-sm); }

.progress-bar { background: rgba(255,255,255,0.1); border-radius: var(--radius-md); height: 8px; overflow: hidden; }
.progress-fill { background: var(--primary-gradient); height: 100%; transition: width var(--transition-fast); }

.keyword-tag { display: inline-block; background: var(--surface-card); padding: var(--space-xs) var(--space-sm); border-radius: var(--radius-md); margin: var(--space-xs); color: var(--text-secondary); }
.keyword-high-volume { border: 1px solid #10B981; color: #10B981; }
.keyword-medium-volume { border: 1px solid #F59E0B; color: #F59E0B; }
.keyword-low-volume { border: 1px solid #6B7280; color: #6B7280; }

.quote-block { background: var(--surface-glass); border-left: 4px solid var(--primary-gradient); padding: var(--space-md); margin: var(--space-md) 0; border-radius: var(--radius-md); }
.quote-text { font-style: italic; color: var(--text-secondary); }
.quote-author { color: var(--text-tertiary); font-size: var(--text-sm); margin-top: var(--space-xs); }
"""
        return essential_tokens.strip()
    
    def format_all_analyses(self, all_analysis_data, video_data):
        """Transform all 5 analysis reports into unified beautiful interface - FORMATTING ONLY"""
        
        start_time = time.time()
        
        # Extract raw analysis text from each agent
        performance_text = all_analysis_data.get('performance_analysis', {}).get('analysis', '')
        script_text = all_analysis_data.get('script_analysis', {}).get('analysis', '')
        seo_text = all_analysis_data.get('seo_analysis', {}).get('analysis', '')
        psychology_text = all_analysis_data.get('psychology_analysis', {}).get('analysis', '')
        comments_text = all_analysis_data.get('comment_analysis', {}).get('analysis', '')
        thumbnail_text = all_analysis_data.get('thumbnail_analysis', {}).get('analysis', '')
        
        logger.info("Creating unified styling agent...")
        
        agent = Agent(
            role="Master Visual Formatter",
            goal="Transform raw analysis text into beautiful HTML using design system - NO CONTENT CREATION ALLOWED",
            backstory="""You are a world-class visual formatter who takes existing analysis text and makes it beautiful using HTML and CSS classes. 
            You NEVER add new facts, insights, or analysis - you only format existing content for maximum visual impact and readability.
            You are like a graphic designer who takes a written report and makes it visually stunning without changing any words.""",
            llm=self.llm,
            verbose=True
        )
        
        task = Task(
            description=f"""
🚨 ABSOLUTE CRITICAL RULES - VIOLATION WILL BLOCK OUTPUT:
1. PRESERVE ALL TEXT - Include ALL words, sentences, and paragraphs from original analysis
2. FORMATTING ONLY - You are applying HTML/CSS styling to existing text, NOT writing new content
3. NO CONTENT REMOVAL - Do not shorten, summarize, or omit any analysis text
4. NO NEW INSIGHTS - Do not add analysis, conclusions, or interpretations
5. TEXT PRESERVATION - Wrap existing text in HTML tags but keep all original content

ANALYSIS DATA TO FORMAT (PRESERVE EVERY WORD):

## PERFORMANCE ANALYSIS (INCLUDE ALL TEXT BELOW):
{performance_text}

## SCRIPT ANALYSIS (INCLUDE ALL TEXT BELOW):
{script_text}

## SEO ANALYSIS (INCLUDE ALL TEXT BELOW):
{seo_text}

## PSYCHOLOGY ANALYSIS (INCLUDE ALL TEXT BELOW):
{psychology_text}

## COMMENTS ANALYSIS (INCLUDE ALL TEXT BELOW):
{comments_text}

## THUMBNAIL ANALYSIS (INCLUDE ALL TEXT BELOW):
{thumbnail_text}

## VIDEO METADATA:
Title: {video_data.get('title', 'N/A')}
Views: {video_data.get('statistics', {}).get('viewCount', '0')}
Likes: {video_data.get('statistics', {}).get('likeCount', '0')}
Comments: {video_data.get('statistics', {}).get('commentCount', '0')}

STRICT FORMATTING REQUIREMENTS:
1. INCLUDE ALL ORIGINAL TEXT - Wrap in HTML but preserve every word
2. Extract metrics/numbers into metric cards while KEEPING the original context text
3. Structure content using design system classes but PRESERVE all analysis content

EXAMPLE OF CORRECT FORMATTING:
Original: "Views: 100K, Likes: 5K, Engagement: 5%. Great performance!"

CORRECT Styling:
<div class="metrics-grid">
  <div class="metric-card"><div class="metric-value">100K</div><div class="metric-label">Views</div></div>
  <div class="metric-card"><div class="metric-value">5K</div><div class="metric-label">Likes</div></div>
  <div class="metric-card"><div class="metric-value">5%</div><div class="metric-label">Engagement</div></div>
</div>
<div class="insight-card">
  <div class="insight-content">Views: 100K, Likes: 5K, Engagement: 5%. Great performance!</div>
</div>

WRONG Styling (DO NOT DO THIS):
<div class="metrics-grid">
  <div class="metric-card"><div class="metric-value">100K</div><div class="metric-label">Views</div></div>
</div>

Return HTML structure for each tab using ALL existing analysis content:

<div class="performance-styled">
  <!-- Include ALL performance analysis text with beautiful formatting -->
</div>

<div class="script-styled">
  <!-- Include ALL script analysis text with structured sections -->
</div>

<div class="seo-styled">
  <!-- Include ALL SEO analysis text with keyword displays -->
</div>

<div class="psychology-styled">
  <!-- Include ALL psychology analysis text with insight cards -->
</div>

<div class="comments-styled">
  <!-- Include ALL comments analysis text with quote blocks -->
</div>

<div class="thumbnail-styled">
  <!-- Include ALL thumbnail analysis text with visual intelligence cards -->
</div>

DESIGN SYSTEM CLASSES:
{self.design_tokens}

🚨 CRITICAL: You MUST include ALL original analysis text. Your job is to make it beautiful, not to edit it!
            """,
            agent=agent,
            expected_output="HTML formatted versions of all 5 analysis sections using design system classes"
        )
        
        logger.info("Executing unified styling task...")
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()
        
        formatting_time = time.time() - start_time
        logger.info(f"Unified styling completed in {formatting_time:.2f} seconds")
        
        # Parse and validate output
        parsed_output = self._parse_unified_output(str(result))
        
        # Validate content preservation
        self._validate_content_preservation(
            {
                'performance': performance_text,
                'script': script_text,
                'seo': seo_text,
                'psychology': psychology_text,
                'comments': comments_text,
                'thumbnail': thumbnail_text
            },
            parsed_output
        )
        
        return {
            'styled_sections': parsed_output,
            'processing_time': formatting_time,
            'token_savings': self._calculate_token_savings()
        }
    
    def _parse_unified_output(self, html_output):
        """Parse unified output into individual tab sections"""
        
        sections = {}
        
        # The agent returns HTML in blocks, not wrapped in the expected divs
        # Parse based on the actual structure returned
        
        # Look for the start/end patterns that define each section
        section_patterns = {
            'performance': (r'<div class="performance-styled">(.*?)(?=<div class="script-styled"|$)', 'performance_html'),
            'script': (r'<div class="script-styled">(.*?)(?=<div class="seo-styled"|$)', 'script_html'),
            'seo': (r'<div class="seo-styled">(.*?)(?=<div class="psychology-styled"|$)', 'seo_html'),
            'psychology': (r'<div class="psychology-styled">(.*?)(?=<div class="comments-styled"|$)', 'psychology_html'),
            'comments': (r'<div class="comments-styled">(.*?)(?=<div class="thumbnail-styled"|$)', 'comments_html'),
            'thumbnail': (r'<div class="thumbnail-styled">(.*?)(?=$)', 'thumbnail_html')
        }
        
        for section_name, (pattern, key) in section_patterns.items():
            match = re.search(pattern, html_output, re.DOTALL | re.IGNORECASE)
            if match:
                sections[key] = match.group(1).strip()
                logger.info(f"✅ Extracted {section_name} section: {len(match.group(1))} chars")
            else:
                # Fallback: look for section content without wrapper div
                logger.warning(f"Could not extract {section_name} section with wrapper, trying fallback...")
                
                # If the agent didn't use the wrapper structure, try to extract by order
                # This is a fallback for when the agent doesn't follow the exact format
                sections[key] = f"<p>Formatting extraction failed for {section_name} section</p>"
        
        return sections
    
    def _validate_content_preservation(self, original_analyses, styled_outputs):
        """Ensure no content was removed during styling - CRITICAL VALIDATION"""
        
        for section_name, original_text in original_analyses.items():
            styled_html = styled_outputs.get(f"{section_name}_html", "")
            
            if not styled_html:
                continue
                
            # Extract text content from HTML
            soup = BeautifulSoup(styled_html, 'html.parser')
            styled_text = soup.get_text()
            
            # Normalize text for comparison (remove extra whitespace, make lowercase)
            original_clean = ' '.join(original_text.lower().split())
            styled_clean = ' '.join(styled_text.lower().split())
            
            # Check that most original words are preserved
            original_words = set(original_clean.split())
            styled_words = set(styled_clean.split())
            
            # Check for content removal (more critical than addition)
            missing_words = original_words - styled_words
            missing_significant = [w for w in missing_words if len(w) > 3 and w not in ['the', 'and', 'with', 'for', 'this', 'that']]
            
            # More lenient check - focus on whether core content is preserved
            preservation_ratio = len(original_words & styled_words) / len(original_words) if original_words else 1
            
            # Debug logging
            logger.info(f"VALIDATION DEBUG for {section_name}:")
            logger.info(f"  Original words: {original_words}")
            logger.info(f"  Styled words: {styled_words}")
            logger.info(f"  Preserved: {original_words & styled_words}")
            logger.info(f"  Missing: {missing_words}")
            logger.info(f"  Preservation ratio: {preservation_ratio:.2f}")
            
            if preservation_ratio < 0.7:  # Less than 70% of original words preserved
                logger.error(f"CONTENT VALIDATION FAILED for {section_name}: Too much content removed")
                logger.error(f"Preservation ratio: {preservation_ratio:.2f}")
                logger.error(f"Missing significant words: {missing_significant[:10]}")
                raise ValueError(f"Styling agent removed too much content from {section_name} section - BLOCKED")
            
            # Check for major structural changes (very lenient)
            original_length = len(original_text)
            styled_length = len(styled_text)
            length_ratio = min(styled_length, original_length) / max(styled_length, original_length)
            
            if length_ratio < 0.3:  # Very lenient - only catch massive changes
                logger.error(f"CONTENT VALIDATION FAILED for {section_name}: Content length changed drastically")
                logger.error(f"Original: {original_length} chars, Styled: {styled_length} chars")
                raise ValueError(f"Styling agent changed content length too much in {section_name} section - BLOCKED")
        
        logger.info("✅ Content preservation validation PASSED - Core content preserved")
        return True
    
    def _calculate_token_savings(self):
        """Calculate estimated token savings vs current 5-agent approach"""
        
        # Current approach: 5 agents × 2000 tokens design system each = 10,000 tokens
        current_design_tokens = 5 * 2000
        
        # Unified approach: 1 agent × 500 tokens essential design tokens = 500 tokens
        unified_design_tokens = 500
        
        savings = current_design_tokens - unified_design_tokens
        savings_percentage = (savings / current_design_tokens) * 100
        
        return {
            'token_savings': savings,
            'savings_percentage': savings_percentage,
            'current_tokens': current_design_tokens,
            'unified_tokens': unified_design_tokens
        }

class UnifiedStylingValidator:
    """Validation helper for unified styling agent"""
    
    @staticmethod
    def compare_with_original(original_styling_result, unified_styling_result):
        """Compare unified agent output with original 5-agent output"""
        
        comparison = {
            'content_preservation': True,
            'visual_quality': 'equivalent',
            'performance_improvement': 0,
            'errors': []
        }
        
        # Check each section
        sections = ['performance', 'script', 'seo', 'psychology', 'comments', 'thumbnail']
        
        for section in sections:
            original_html = original_styling_result.get(f'{section}_analysis', {}).get('styled_html', '')
            unified_html = unified_styling_result['styled_sections'].get(f'{section}_html', '')
            
            if not unified_html:
                comparison['errors'].append(f"Missing {section} section in unified output")
                comparison['content_preservation'] = False
                continue
            
            # Extract text and compare
            if original_html:
                original_soup = BeautifulSoup(original_html, 'html.parser')
                unified_soup = BeautifulSoup(unified_html, 'html.parser')
                
                original_text = original_soup.get_text()
                unified_text = unified_soup.get_text()
                
                similarity = SequenceMatcher(None, original_text, unified_text).ratio()
                if similarity < 0.7:
                    comparison['errors'].append(f"{section} content differs significantly (similarity: {similarity:.2f})")
        
        return comparison