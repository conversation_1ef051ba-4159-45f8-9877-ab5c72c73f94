"""
Simple Transcript Cache for Step 2
Just handles transcript storage and metadata
"""
import time
import logging

logger = logging.getLogger(__name__)

class SimpleTranscriptCache:
    """Simple cache for transcript data"""
    
    def __init__(self):
        self._cache = {}
        self._metadata = {}
    
    def store_transcript(self, video_id: str, transcript: str) -> str:
        """Store transcript and return cache key"""
        cache_key = f"transcript_{video_id}"
        
        # Store metadata for prompts
        word_count = len(transcript.split())
        self._metadata[cache_key] = {
            'word_count': word_count,
            'length': len(transcript),
            'preview': transcript[:200] + '...' if len(transcript) > 200 else transcript,
            'has_timestamps': '[' in transcript and ']' in transcript,
            'stored_at': time.time()
        }
        
        # Store full text
        self._cache[cache_key] = transcript
        
        logger.info(f"📝 Cached transcript for {video_id}: {word_count} words")
        return cache_key
    
    def get_transcript(self, cache_key: str) -> str:
        """Get full transcript from cache"""
        return self._cache.get(cache_key, '')
    
    def get_metadata(self, cache_key: str) -> dict:
        """Get transcript metadata without loading full text"""
        return self._metadata.get(cache_key, {})
    
    def get_prompt_summary(self, cache_key: str) -> str:
        """Get a summary for agent prompts instead of full text"""
        metadata = self.get_metadata(cache_key)
        if not metadata:
            return "No transcript available"
        
        return f"""TRANSCRIPT SUMMARY:
- Word Count: {metadata['word_count']} words  
- Length: {metadata['length']} characters
- Has Timestamps: {metadata.get('has_timestamps', False)}
- Preview: {metadata['preview']}

[Full transcript is cached and available for analysis - not included in prompt to save tokens]"""

# Global cache instance
transcript_cache = SimpleTranscriptCache()