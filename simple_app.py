"""
Simple Working YouTube Research v2 App
Simplified version to ensure it works
"""

import os
import uuid
import json
from datetime import datetime
from typing import Dict, Any
from fastapi import FastAP<PERSON>, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import logging

# Simple imports without complex dependencies
import sys
sys.path.append('.')

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="YouTube Research v2 - Simple")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple in-memory storage
analysis_store = {}

# Pydantic models
class AnalysisRequest(BaseModel):
    channel_id: str
    max_videos: int = 20
    max_comments: int = 50

class AnalysisResponse(BaseModel):
    analysis_id: str
    status: str
    message: str

# Routes
@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve simple web interface"""
    return """
<!DOCTYPE html>
<html>
<head>
    <title>YouTube Research v2 - Simple</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { background: #f5f5f5; padding: 20px; border-radius: 10px; margin: 20px 0; }
        input { padding: 10px; margin: 5px; width: 300px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { background: white; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #007bff; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .success { border-left-color: #28a745; background: #d4edda; }
    </style>
</head>
<body>
    <h1>🧬 YouTube Research v2 - Channel DNA Analyzer</h1>
    
    <div class="container">
        <h2>Analyze YouTube Channel</h2>
        <div>
            <input type="text" id="channelId" placeholder="Enter Channel ID (e.g. UCX6OQ3DkcsbYNE6H8uQQuVA)" />
            <br>
            <input type="number" id="maxVideos" value="20" min="5" max="50" placeholder="Max Videos" />
            <input type="number" id="maxComments" value="50" min="10" max="100" placeholder="Max Comments" />
            <br>
            <button onclick="startAnalysis()">🚀 Start Analysis</button>
        </div>
        <div id="status"></div>
        <div id="results"></div>
    </div>

    <div class="container">
        <h2>Recent Analyses</h2>
        <div id="recent"></div>
        <button onclick="loadRecent()">🔄 Refresh</button>
    </div>

    <script>
        let currentAnalysisId = null;

        async function startAnalysis() {
            const channelId = document.getElementById('channelId').value.trim();
            const maxVideos = document.getElementById('maxVideos').value;
            const maxComments = document.getElementById('maxComments').value;
            
            if (!channelId) {
                showMessage('Please enter a channel ID', 'error');
                return;
            }
            
            try {
                showMessage('Starting analysis...', 'success');
                
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        channel_id: channelId,
                        max_videos: parseInt(maxVideos),
                        max_comments: parseInt(maxComments)
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    currentAnalysisId = data.analysis_id;
                    showMessage(`Analysis started! ID: ${data.analysis_id}`, 'success');
                    pollStatus();
                } else {
                    showMessage(`Error: ${data.detail}`, 'error');
                }
                
            } catch (error) {
                showMessage(`Error: ${error.message}`, 'error');
            }
        }
        
        async function pollStatus() {
            if (!currentAnalysisId) return;
            
            try {
                const response = await fetch(`/api/status/${currentAnalysisId}`);
                const status = await response.json();
                
                document.getElementById('status').innerHTML = `
                    <div class="result">
                        <strong>Status:</strong> ${status.status}<br>
                        <strong>Progress:</strong> ${status.progress || 0}%<br>
                        <strong>Step:</strong> ${status.current_step || 'Processing...'}
                    </div>
                `;
                
                if (status.status === 'completed') {
                    loadResults(currentAnalysisId);
                    loadRecent();
                } else if (status.status === 'failed') {
                    showMessage('Analysis failed', 'error');
                } else {
                    setTimeout(pollStatus, 2000);
                }
                
            } catch (error) {
                showMessage(`Status check failed: ${error.message}`, 'error');
            }
        }
        
        async function loadResults(analysisId) {
            try {
                const response = await fetch(`/api/results/${analysisId}`);
                const results = await response.json();
                
                document.getElementById('results').innerHTML = `
                    <div class="result success">
                        <h3>✅ Analysis Complete!</h3>
                        <pre>${JSON.stringify(results, null, 2)}</pre>
                    </div>
                `;
                
            } catch (error) {
                showMessage(`Failed to load results: ${error.message}`, 'error');
            }
        }
        
        async function loadRecent() {
            try {
                const response = await fetch('/api/recent');
                const analyses = await response.json();
                
                let html = '';
                analyses.forEach(analysis => {
                    html += `
                        <div class="result">
                            <strong>${analysis.channel_name || analysis.channel_id}</strong><br>
                            Status: ${analysis.status}<br>
                            Created: ${new Date(analysis.created_at).toLocaleString()}
                        </div>
                    `;
                });
                
                document.getElementById('recent').innerHTML = html || '<p>No recent analyses</p>';
                
            } catch (error) {
                showMessage(`Failed to load recent analyses: ${error.message}`, 'error');
            }
        }
        
        function showMessage(message, type) {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            document.getElementById('status').appendChild(div);
        }
        
        // Load recent analyses on page load
        window.onload = loadRecent;
    </script>
</body>
</html>
"""

@app.get("/health")
async def health():
    """Health check"""
    return {"status": "healthy", "timestamp": datetime.utcnow().isoformat()}

@app.post("/api/analyze", response_model=AnalysisResponse)
async def analyze(request: AnalysisRequest, background_tasks: BackgroundTasks):
    """Start analysis"""
    analysis_id = str(uuid.uuid4())
    
    # Store analysis
    analysis_store[analysis_id] = {
        "analysis_id": analysis_id,
        "channel_id": request.channel_id,
        "status": "pending",
        "created_at": datetime.utcnow(),
        "progress": 0,
        "current_step": "Starting analysis...",
        "results": None
    }
    
    # Start background task
    background_tasks.add_task(run_analysis, analysis_id, request)
    
    return AnalysisResponse(
        analysis_id=analysis_id,
        status="pending",
        message=f"Analysis started for {request.channel_id}"
    )

@app.get("/api/status/{analysis_id}")
async def get_status(analysis_id: str):
    """Get analysis status"""
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    return analysis_store[analysis_id]

@app.get("/api/results/{analysis_id}")
async def get_results(analysis_id: str):
    """Get analysis results"""
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    analysis = analysis_store[analysis_id]
    if analysis["status"] != "completed":
        raise HTTPException(status_code=400, detail="Analysis not completed")
    
    return analysis["results"]

@app.get("/api/recent")
async def get_recent():
    """Get recent analyses"""
    recent = sorted(
        analysis_store.values(),
        key=lambda x: x["created_at"],
        reverse=True
    )[:10]
    
    return [{
        "analysis_id": r["analysis_id"],
        "channel_id": r["channel_id"],
        "channel_name": r.get("channel_name", "Unknown"),
        "status": r["status"],
        "created_at": r["created_at"]
    } for r in recent]

async def run_analysis(analysis_id: str, request: AnalysisRequest):
    """Run the actual analysis"""
    try:
        # Update status
        analysis_store[analysis_id]["status"] = "in_progress"
        analysis_store[analysis_id]["current_step"] = "Initializing..."
        analysis_store[analysis_id]["progress"] = 10
        
        # Check if we have YouTube API key
        youtube_key = os.getenv('YOUTUBE_API_KEY')
        if not youtube_key:
            raise Exception("YouTube API key not configured")
        
        # Simulate analysis steps
        import asyncio
        
        analysis_store[analysis_id]["current_step"] = "Fetching channel data..."
        analysis_store[analysis_id]["progress"] = 30
        await asyncio.sleep(2)
        
        analysis_store[analysis_id]["current_step"] = "Analyzing content patterns..."
        analysis_store[analysis_id]["progress"] = 60
        await asyncio.sleep(2)
        
        analysis_store[analysis_id]["current_step"] = "Generating insights..."
        analysis_store[analysis_id]["progress"] = 90
        await asyncio.sleep(2)
        
        # Try to run real analysis if possible
        try:
            # Import our analyzer
            from test_channel_analysis import ChannelAnalyzer
            analyzer = ChannelAnalyzer(youtube_key)
            results = analyzer.analyze_channel(request.channel_id, request.max_videos, request.max_comments)
            
            if results:
                # Extract channel name
                channel_name = "Unknown Channel"
                if "channel_profile" in results:
                    # Try to get channel name from original API call
                    try:
                        channel_info = analyzer._get_channel_info(request.channel_id)
                        if channel_info:
                            channel_name = channel_info['snippet']['title']
                    except:
                        pass
                
                analysis_store[analysis_id]["channel_name"] = channel_name
                analysis_store[analysis_id]["results"] = results
            else:
                raise Exception("Analysis returned no results")
                
        except Exception as e:
            logger.error(f"Real analysis failed: {e}")
            # Fallback to mock results
            analysis_store[analysis_id]["results"] = {
                "channel_profile": {
                    "growth_trajectory": "simulated",
                    "subscriber_tier": "unknown",
                    "note": f"Mock analysis for {request.channel_id} (API error: {str(e)})"
                },
                "content_patterns": {
                    "note": "This is simulated data - check your API keys for real analysis"
                },
                "analysis_metadata": {
                    "simulated": True,
                    "reason": str(e)
                }
            }
            analysis_store[analysis_id]["channel_name"] = f"Channel {request.channel_id}"
        
        # Complete
        analysis_store[analysis_id]["status"] = "completed"
        analysis_store[analysis_id]["progress"] = 100
        analysis_store[analysis_id]["current_step"] = "Analysis complete!"
        
        logger.info(f"Completed analysis {analysis_id}")
        
    except Exception as e:
        logger.error(f"Analysis {analysis_id} failed: {e}")
        analysis_store[analysis_id]["status"] = "failed"
        analysis_store[analysis_id]["current_step"] = f"Error: {str(e)}"
        analysis_store[analysis_id]["error"] = str(e)

if __name__ == "__main__":
    import uvicorn
    
    print("🧬 YouTube Research v2 - Simple Version")
    print("=" * 50)
    
    # Check API key
    if os.getenv('YOUTUBE_API_KEY'):
        print("✅ YouTube API Key found - Real analysis available")
    else:
        print("⚠️  YouTube API Key missing - Will use simulated data")
        print("   Add YOUTUBE_API_KEY to .env for real analysis")
    
    print("🚀 Starting server...")
    print("🌐 Open http://localhost:8000 in your browser")
    print("⌨️  Press Ctrl+C to stop")
    print("-" * 50)
    
    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")