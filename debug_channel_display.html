<!DOCTYPE html>
<html>
<head>
    <title>Debug Channel Display</title>
</head>
<body>
    <h1>Debug Channel Context Display</h1>
    
    <script>
        // Test data mimicking API response
        const testData = {
            video_data: {
                views: 10248,
                likes: 1221,
                comments: 90,
                duration_minutes: 11,
                duration_seconds: 20,
                // Missing channel_context!
            }
        };
        
        // Copy the generateChannelContextHTML function
        function generateChannelContextHTML(videoData) {
            if (!videoData) return '';
            
            const channelContext = videoData.channel_context;
            const performanceMetrics = videoData.performance_metrics;
            
            if (!channelContext) return '';
            
            // Generate performance indicator based on overperformance ratio
            let performanceClass = 'metric-item';
            let performanceIcon = '📊';
            let performanceLabel = 'Standard Performance';
            
            if (performanceMetrics) {
                const ratio = performanceMetrics.overperformance_ratio || 0;
                if (ratio >= 10) {
                    performanceClass += ' viral-performance';
                    performanceIcon = '🚀';
                    performanceLabel = 'Viral Performance';
                } else if (ratio >= 2) {
                    performanceClass += ' strong-performance';
                    performanceIcon = '📈';
                    performanceLabel = 'Strong Performance';
                } else if (ratio >= 0.5) {
                    performanceIcon = '📊';
                    performanceLabel = 'Standard Performance';
                } else {
                    performanceClass += ' weak-performance';
                    performanceIcon = '📉';
                    performanceLabel = 'Below Average';
                }
            }
            
            return `
                <div class="analysis-section" style="margin-top: var(--space-lg);">
                    <div class="section-header">
                        <div class="section-icon">🏆</div>
                        <div>
                            <div class="section-title">Channel Context Intelligence</div>
                            <div class="section-subtitle">Channel-relative performance analysis</div>
                        </div>
                    </div>
                    
                    <div class="metric-grid">
                        <div class="metric-item">
                            <div class="metric-value">📺 ${channelContext.channel_name || 'Unknown'}</div>
                            <div class="metric-label">Channel Name</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${channelContext.channel_tier || 'Unknown'}</div>
                            <div class="metric-label">Channel Tier</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${(channelContext.channel_subscribers || 0).toLocaleString()}</div>
                            <div class="metric-label">Subscribers</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${(channelContext.avg_views_per_video || 0).toLocaleString()}</div>
                            <div class="metric-label">Avg Views/Video</div>
                        </div>
                    </div>
                    
                    ${performanceMetrics ? `
                    <div class="metric-grid" style="margin-top: var(--space-md);">
                        <div class="${performanceClass}">
                            <div class="metric-value">${performanceIcon} ${performanceMetrics.overperformance_ratio?.toFixed(1) || '0'}x</div>
                            <div class="metric-label">Overperformance Ratio</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${performanceMetrics.subscriber_reach_rate?.toFixed(1) || '0'}%</div>
                            <div class="metric-label">Subscriber Reach</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${performanceMetrics.performance_note || 'Unknown'}</div>
                            <div class="metric-label">Performance Classification</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${channelContext.channel_country || 'Unknown'}</div>
                            <div class="metric-label">Country</div>
                        </div>
                    </div>
                    ` : ''}
                </div>
            `;
        }
        
        // Test the function
        console.log('Testing with data missing channel_context:');
        const result = generateChannelContextHTML(testData.video_data);
        console.log('Result:', result);
        console.log('Result is empty string:', result === '');
        
        // Test with proper data
        testData.video_data.channel_context = {
            channel_name: 'Test Channel',
            channel_tier: 'Medium (100K-1M)',
            channel_subscribers: 500000,
            avg_views_per_video: 50000
        };
        
        testData.video_data.performance_metrics = {
            overperformance_ratio: 2.5,
            subscriber_reach_rate: 125.0,
            performance_note: 'Strong Performance'
        };
        
        console.log('\nTesting with channel_context present:');
        const result2 = generateChannelContextHTML(testData.video_data);
        console.log('Result length:', result2.length);
        
        document.body.innerHTML += `
            <h2>Test Results:</h2>
            <p>Without channel_context: ${result === '' ? 'Returns empty string ❌' : 'Returns content ✅'}</p>
            <p>With channel_context: ${result2.length > 0 ? 'Returns content ✅' : 'Returns empty ❌'}</p>
            
            <h2>Rendered Output:</h2>
            ${result2}
        `;
    </script>
</body>
</html>