// yt-heatmap-tester/server.js – v3 (robust)
//→ Adds stronger waiting logic, broader bucket detection, verbose error output.
//   * Waits for networkidle, then an extra 1.5 s for late JS.
//   * No longer assumes bucket values ≤ 1 (some test vids reach 1.3‑1.7).
//   * Explicitly walks known path first before heuristic search.
//   * Optional ?debug=1 query prints full keys path to console.

import express from "express";
import { chromium } from "playwright";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = 3000;

app.use(express.static(path.join(__dirname, "public")));

// Utility: walk object by string path (array of keys)
function dig(obj, pathArr) {
  return pathArr.reduce((acc, k) => (acc && k in acc ? acc[k] : undefined), obj);
}

function looksLikeBuckets(arr) {
  return (
    Array.isArray(arr) &&
    arr.length >= 10 &&
    arr.length <= 400 &&
    arr.every(n => typeof n === "number" && isFinite(n))
  );
}

function bruteFindBuckets(obj, depth = 0) {
  if (depth > 8 || !obj) return null;
  if (looksLikeBuckets(obj)) return obj;
  if (typeof obj === "object") {
    for (const val of Object.values(obj)) {
      const found = bruteFindBuckets(val, depth + 1);
      if (found) return found;
    }
  }
  return null;
}

async function getPlayerResponse(page) {
  // Try global first
  const pr = await page.evaluate(() => window.ytInitialPlayerResponse ?? null);
  if (pr) return pr;

  // Fallback: intercept player endpoint
  const resp = await page.waitForResponse(r => /\/youtubei\/v1\/player/.test(r.url()), { timeout: 10000 }).catch(() => null);
  if (resp) {
    try {
      return await resp.json();
    } catch {}
  }
  return null;
}

async function scrapeHeatmapBuckets(videoId, debug = false) {
  const browser = await chromium.launch({ headless: true });
  try {
    const page = await browser.newPage();
    await page.goto(`https://www.youtube.com/watch?v=${videoId}`, { waitUntil: "networkidle" });
    await page.waitForTimeout(1500); // allow late JS to register globals

    const pr = await getPlayerResponse(page);
    if (!pr) throw new Error("ytInitialPlayerResponse not found – could be GEO/age‑gated");

    if (debug) {
      console.log("Available top-level keys:", Object.keys(pr));
      // Check for heatmap-related keys
      const heatmapKeys = Object.keys(pr).filter(key => key.toLowerCase().includes('heat'));
      console.log("Heatmap-related keys:", heatmapKeys);
    }

    // Try multiple known paths based on research
    const knownPaths = [
      ["annotations", "playerBar", "playerBarRenderer", "heatmap", "heatmapRenderer", "buckets"],
      ["heatmap", "heatmapRenderer", "heatmapData"],
      ["heatmap", "heatmapRenderer", "buckets"],
      ["videoDetails", "heatmap", "heatmapRenderer", "heatmapData"],
      ["playerConfig", "heatmap", "heatmapRenderer", "buckets"],
    ];

    let buckets = null;
    for (const path of knownPaths) {
      buckets = dig(pr, path);
      if (debug) console.log(`Trying path ${path.join(' -> ')}:`, buckets ? "found data" : "not found");
      if (looksLikeBuckets(buckets)) break;
    }

    // 2️⃣ heuristic brute‑force search if needed
    if (!looksLikeBuckets(buckets)) {
      if (debug) console.log("Starting brute-force search...");
      buckets = bruteFindBuckets(pr);
    }
    
    if (!buckets) throw new Error("Heat‑map buckets not located – feature may be disabled or script blocked");

    if (debug) console.log("Found", buckets.length, "buckets for", videoId);
    return buckets;
  } finally {
    await browser.close();
  }
}

app.get("/api/heatmap", async (req, res) => {
  const raw = (req.query.v || "").trim();
  const debug = req.query.debug === "1";
  if (!raw) return res.status(400).json({ error: "Missing v param" });
  const match = raw.match(/(?:v=|youtu\.be\/|[\\/])([A-Za-z0-9_-]{11})/);
  const videoId = match ? match[1] : raw.slice(-11);
  try {
    const buckets = await scrapeHeatmapBuckets(videoId, debug);
    res.json({ videoId, bucketCount: buckets.length, buckets });
  } catch (e) {
    res.status(404).json({ error: e.message });
  }
});

app.listen(PORT, () => console.log(`YT Heat‑map Tester running → http://localhost:${PORT}`));