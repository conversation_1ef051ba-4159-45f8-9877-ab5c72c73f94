"""
YouTube Research v2 - WebSocket Routes
Real-time communication for AI agent status updates
"""

from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, HTTPException
from typing import Optional
import logging
import json
import asyncio

from ai_agent_status import agent_status_manager, AgentStatus
from supabase_integration import get_current_user_optional, UserProfile

logger = logging.getLogger(__name__)

# Create WebSocket router
ws_router = APIRouter()

@ws_router.websocket("/ws/analysis/{session_id}")
async def websocket_analysis_status(websocket: WebSocket, session_id: str):
    """WebSocket endpoint for real-time analysis status updates"""
    try:
        # Connect WebSocket to session
        await agent_status_manager.connect_websocket(session_id, websocket)
        
        # Send welcome message
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "session_id": session_id,
            "message": "🚀 Connected to AI Agent Status Feed! Ready for real-time updates."
        }))
        
        # Keep connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client (like ping/pong)
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": message.get("timestamp")
                    }))
                
                elif message.get("type") == "request_status":
                    # Send current session status
                    session = agent_status_manager.get_session(session_id)
                    if session:
                        await websocket.send_text(json.dumps({
                            "type": "session_status",
                            "session": {
                                "session_id": session.session_id,
                                "analysis_type": session.analysis_type,
                                "target_url": session.target_url,
                                "overall_progress": session.overall_progress,
                                "status": session.status,
                                "current_agent": session.current_agent,
                                "started_at": session.started_at.isoformat()
                            }
                        }))
                
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                # Invalid JSON, ignore
                continue
            except Exception as e:
                logger.error(f"Error handling WebSocket message: {e}")
                continue
                
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected from session {session_id}")
    except Exception as e:
        logger.error(f"WebSocket error for session {session_id}: {e}")
    finally:
        # Clean up connection
        await agent_status_manager.disconnect_websocket(session_id, websocket)

@ws_router.websocket("/ws/global-status")
async def websocket_global_status(websocket: WebSocket):
    """WebSocket endpoint for global system status updates"""
    await websocket.accept()
    
    try:
        # Send welcome message
        await websocket.send_text(json.dumps({
            "type": "global_connection_established",
            "message": "🌐 Connected to Global Status Feed!"
        }))
        
        # Send periodic system status updates
        while True:
            try:
                # Send system health status every 30 seconds
                await asyncio.sleep(30)
                
                active_sessions = len(agent_status_manager.active_sessions)
                total_connections = sum(len(conns) for conns in agent_status_manager.websocket_connections.values())
                
                await websocket.send_text(json.dumps({
                    "type": "system_status",
                    "data": {
                        "active_sessions": active_sessions,
                        "total_connections": total_connections,
                        "status": "healthy",
                        "timestamp": asyncio.get_event_loop().time()
                    }
                }))
                
            except WebSocketDisconnect:
                break
            except Exception as e:
                logger.error(f"Error in global status WebSocket: {e}")
                break
                
    except WebSocketDisconnect:
        logger.info("Global status WebSocket disconnected")
    except Exception as e:
        logger.error(f"Global status WebSocket error: {e}")

# REST API endpoints for status management
status_router = APIRouter(prefix="/api/status", tags=["status"])

@status_router.post("/session")
async def create_analysis_session(
    analysis_type: str,
    target_url: str,
    current_user: Optional[UserProfile] = Depends(get_current_user_optional)
):
    """Create a new analysis session"""
    user_id = current_user.id if current_user else None
    session_id = await agent_status_manager.create_session(analysis_type, target_url, user_id)
    
    return {
        "session_id": session_id,
        "websocket_url": f"/ws/analysis/{session_id}",
        "message": "Analysis session created successfully"
    }

@status_router.get("/session/{session_id}")
async def get_session_status(session_id: str):
    """Get current session status"""
    session = agent_status_manager.get_session(session_id)
    
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return {
        "session_id": session.session_id,
        "analysis_type": session.analysis_type,
        "target_url": session.target_url,
        "overall_progress": session.overall_progress,
        "status": session.status,
        "current_agent": session.current_agent,
        "started_at": session.started_at.isoformat(),
        "agents": session.agents
    }

@status_router.post("/session/{session_id}/agent/{agent_id}")
async def update_agent_status(
    session_id: str,
    agent_id: str,
    status: str,
    progress: float = 0.0,
    message: Optional[str] = None
):
    """Update agent status (for internal use by analysis system)"""
    try:
        agent_status = AgentStatus(status)
    except ValueError:
        raise HTTPException(status_code=400, detail="Invalid agent status")
    
    if not (0.0 <= progress <= 1.0):
        raise HTTPException(status_code=400, detail="Progress must be between 0.0 and 1.0")
    
    await agent_status_manager.update_agent_status(
        session_id, 
        agent_id, 
        agent_status, 
        progress,
        {"custom_message": message} if message else None
    )
    
    return {"message": "Agent status updated successfully"}

@status_router.post("/session/{session_id}/complete")
async def complete_session(
    session_id: str,
    results: Optional[dict] = None
):
    """Mark session as complete"""
    await agent_status_manager.complete_session(session_id, results)
    return {"message": "Session completed successfully"}

@status_router.delete("/session/{session_id}")
async def cleanup_session(session_id: str):
    """Clean up session resources"""
    agent_status_manager.cleanup_session(session_id)
    return {"message": "Session cleaned up successfully"}

@status_router.get("/health")
async def status_system_health():
    """Get status system health information"""
    active_sessions = len(agent_status_manager.active_sessions)
    total_connections = sum(len(conns) for conns in agent_status_manager.websocket_connections.values())
    
    return {
        "status": "healthy",
        "active_sessions": active_sessions,
        "total_websocket_connections": total_connections,
        "agent_types": list(agent_status_manager.agent_configs.keys()),
        "features": {
            "real_time_updates": True,
            "personified_messages": True,
            "progress_tracking": True,
            "websocket_support": True
        }
    }

# Demo endpoint for testing agent status updates
@status_router.post("/demo/{session_id}")
async def demo_agent_sequence(session_id: str):
    """Demo endpoint to simulate agent status updates"""
    session = agent_status_manager.get_session(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    # Simulate agent sequence
    agents = ["performance", "script", "seo", "psychology", "comments", "synthesis"]
    
    async def simulate_agent_work(agent_id: str):
        # Starting
        await agent_status_manager.update_agent_status(session_id, agent_id, AgentStatus.STARTING)
        await asyncio.sleep(2)
        
        # Working with progress updates
        for progress in [0.2, 0.4, 0.6, 0.8]:
            await agent_status_manager.update_agent_status(session_id, agent_id, AgentStatus.WORKING, progress)
            await asyncio.sleep(1)
        
        # Thinking
        await agent_status_manager.update_agent_status(session_id, agent_id, AgentStatus.THINKING, 0.9)
        await asyncio.sleep(2)
        
        # Complete
        await agent_status_manager.update_agent_status(session_id, agent_id, AgentStatus.COMPLETE, 1.0)
        await asyncio.sleep(1)
    
    # Run simulation in background
    async def run_simulation():
        for agent_id in agents:
            await simulate_agent_work(agent_id)
        
        # Complete session
        await agent_status_manager.complete_session(session_id, {
            "demo": True,
            "message": "Demo analysis completed successfully!"
        })
    
    # Start simulation without blocking
    asyncio.create_task(run_simulation())
    
    return {
        "message": "Demo agent sequence started",
        "session_id": session_id,
        "estimated_duration": "60 seconds"
    }
