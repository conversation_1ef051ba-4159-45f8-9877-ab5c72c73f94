{% extends "base.html" %}

{% block title %}Metrics Dashboard - YouTube Research v2{% endblock %}
{% block page_title %}Metrics & KPIs Dashboard{% endblock %}
{% block page_subtitle %}AARRR Funnel Performance & Key Benchmarks{% endblock %}

{% block content %}
<div class="metrics-dashboard">
    <!-- KPI Overview Cards -->
    <div class="kpi-overview">
        <div class="kpi-card activation-card">
            <div class="kpi-header">
                <h3>Activation Rate</h3>
                <span class="kpi-target">Target: >60%</span>
            </div>
            <div class="kpi-value">
                <span class="metric-value" id="activationRate">--</span>
                <span class="metric-change positive" id="activationChange">--</span>
            </div>
            <div class="kpi-details">
                <span>7-day activation rate</span>
                <div class="progress-bar">
                    <div class="progress-fill" id="activationProgress"></div>
                </div>
            </div>
        </div>

        <div class="kpi-card churn-card">
            <div class="kpi-header">
                <h3>Logo Churn</h3>
                <span class="kpi-target">Target: <5%</span>
            </div>
            <div class="kpi-value">
                <span class="metric-value" id="churnRate">--</span>
                <span class="metric-change negative" id="churnChange">--</span>
            </div>
            <div class="kpi-details">
                <span>3-month logo churn</span>
                <div class="progress-bar">
                    <div class="progress-fill" id="churnProgress"></div>
                </div>
            </div>
        </div>

        <div class="kpi-card revenue-card">
            <div class="kpi-header">
                <h3>Net Revenue Retention</h3>
                <span class="kpi-target">Target: >120%</span>
            </div>
            <div class="kpi-value">
                <span class="metric-value" id="nrrRate">--</span>
                <span class="metric-change positive" id="nrrChange">--</span>
            </div>
            <div class="kpi-details">
                <span>Monthly NRR</span>
                <div class="progress-bar">
                    <div class="progress-fill" id="nrrProgress"></div>
                </div>
            </div>
        </div>

        <div class="kpi-card users-card">
            <div class="kpi-header">
                <h3>Active Users</h3>
                <span class="kpi-target">Growth</span>
            </div>
            <div class="kpi-value">
                <span class="metric-value" id="activeUsers">--</span>
                <span class="metric-change positive" id="usersChange">--</span>
            </div>
            <div class="kpi-details">
                <span>Monthly active users</span>
                <div class="progress-bar">
                    <div class="progress-fill" id="usersProgress"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- AARRR Funnel Visualization -->
    <div class="funnel-section">
        <h2>AARRR Funnel Performance</h2>
        <div class="funnel-chart">
            <div class="funnel-stage acquisition">
                <div class="stage-header">
                    <h3>Acquisition</h3>
                    <span class="stage-count" id="acquisitionCount">--</span>
                </div>
                <div class="stage-bar">
                    <div class="stage-fill" id="acquisitionFill"></div>
                </div>
                <div class="stage-details">
                    <span>New visitors</span>
                </div>
            </div>

            <div class="funnel-stage activation">
                <div class="stage-header">
                    <h3>Activation</h3>
                    <span class="stage-count" id="activationCount">--</span>
                </div>
                <div class="stage-bar">
                    <div class="stage-fill" id="activationFill"></div>
                </div>
                <div class="stage-details">
                    <span>First value experience</span>
                </div>
            </div>

            <div class="funnel-stage retention">
                <div class="stage-header">
                    <h3>Retention</h3>
                    <span class="stage-count" id="retentionCount">--</span>
                </div>
                <div class="stage-bar">
                    <div class="stage-fill" id="retentionFill"></div>
                </div>
                <div class="stage-details">
                    <span>Regular usage</span>
                </div>
            </div>

            <div class="funnel-stage referral">
                <div class="stage-header">
                    <h3>Referral</h3>
                    <span class="stage-count" id="referralCount">--</span>
                </div>
                <div class="stage-bar">
                    <div class="stage-fill" id="referralFill"></div>
                </div>
                <div class="stage-details">
                    <span>Word of mouth</span>
                </div>
            </div>

            <div class="funnel-stage revenue">
                <div class="stage-header">
                    <h3>Revenue</h3>
                    <span class="stage-count" id="revenueCount">--</span>
                </div>
                <div class="stage-bar">
                    <div class="stage-fill" id="revenueFill"></div>
                </div>
                <div class="stage-details">
                    <span>Paying customers</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="activity-section">
        <h2>Recent Activity</h2>
        <div class="activity-list" id="recentActivity">
            <!-- Activity items will be populated by JavaScript -->
        </div>
    </div>

    <!-- Feedback Collection -->
    <div class="feedback-section">
        <div class="feedback-card">
            <h3>Customer Feedback</h3>
            <p>Help us improve by sharing your experience</p>
            <button class="btn btn-primary" id="feedbackBtn" 
                    data-track='{"feature_name": "feedback", "action": "feedback_started"}'>
                <i data-lucide="message-circle"></i>
                Give Feedback
            </button>
        </div>
        
        <div class="quarterly-interviews">
            <h3>Quarterly Interviews</h3>
            <p>Join our quarterly customer interview program</p>
            <button class="btn btn-outline" id="interviewBtn"
                    data-track='{"feature_name": "interview", "action": "interview_signup"}'>
                <i data-lucide="calendar"></i>
                Schedule Interview
            </button>
        </div>
    </div>
</div>

<!-- Feedback Modal -->
<div class="modal" id="feedbackModal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Share Your Feedback</h3>
            <button class="modal-close" id="closeFeedback">×</button>
        </div>
        <div class="modal-body">
            <form id="feedbackForm">
                <div class="form-group">
                    <label for="satisfaction">How satisfied are you with our platform?</label>
                    <select id="satisfaction" name="satisfaction" required>
                        <option value="">Select rating</option>
                        <option value="5">Very Satisfied</option>
                        <option value="4">Satisfied</option>
                        <option value="3">Neutral</option>
                        <option value="2">Dissatisfied</option>
                        <option value="1">Very Dissatisfied</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="mostValuable">What features do you find most valuable?</label>
                    <textarea id="mostValuable" name="mostValuable" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="improvements">What improvements would you like to see?</label>
                    <textarea id="improvements" name="improvements" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="nps">How likely are you to recommend us? (0-10)</label>
                    <input type="number" id="nps" name="nps" min="0" max="10" />
                </div>
                
                <button type="submit" class="btn btn-primary"
                        data-track='{"feature_name": "feedback", "action": "feedback_submitted"}'>
                    Submit Feedback
                </button>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize metrics dashboard
    loadMetricsData();
    loadRecentActivity();
    setupFeedbackModal();
    
    // Update metrics every 5 minutes
    setInterval(loadMetricsData, 5 * 60 * 1000);
});

function loadMetricsData() {
    // Simulated data - replace with actual API calls
    const metricsData = {
        activation: { rate: 67, change: '+5%', target: 60 },
        churn: { rate: 3.2, change: '-1.1%', target: 5 },
        nrr: { rate: 128, change: '+8%', target: 120 },
        activeUsers: { count: 1247, change: '+12%' }
    };
    
    updateKPICards(metricsData);
    updateFunnelChart();
}

function updateKPICards(data) {
    // Update activation rate
    document.getElementById('activationRate').textContent = data.activation.rate + '%';
    document.getElementById('activationChange').textContent = data.activation.change;
    document.getElementById('activationProgress').style.width = 
        Math.min((data.activation.rate / data.activation.target) * 100, 100) + '%';
    
    // Update churn rate
    document.getElementById('churnRate').textContent = data.churn.rate + '%';
    document.getElementById('churnChange').textContent = data.churn.change;
    document.getElementById('churnProgress').style.width = 
        Math.min((data.churn.rate / data.churn.target) * 100, 100) + '%';
    
    // Update NRR
    document.getElementById('nrrRate').textContent = data.nrr.rate + '%';
    document.getElementById('nrrChange').textContent = data.nrr.change;
    document.getElementById('nrrProgress').style.width = 
        Math.min((data.nrr.rate / data.nrr.target) * 100, 100) + '%';
    
    // Update active users
    document.getElementById('activeUsers').textContent = data.activeUsers.count.toLocaleString();
    document.getElementById('usersChange').textContent = data.activeUsers.change;
}

function updateFunnelChart() {
    // Simulated funnel data
    const funnelData = [
        { stage: 'acquisition', count: 2150, percentage: 100 },
        { stage: 'activation', count: 1441, percentage: 67 },
        { stage: 'retention', count: 1009, percentage: 47 },
        { stage: 'referral', count: 323, percentage: 15 },
        { stage: 'revenue', count: 247, percentage: 11.5 }
    ];
    
    funnelData.forEach(stage => {
        document.getElementById(stage.stage + 'Count').textContent = stage.count.toLocaleString();
        document.getElementById(stage.stage + 'Fill').style.width = stage.percentage + '%';
    });
}

function loadRecentActivity() {
    const activities = [
        { type: 'activation', message: 'New user completed video analysis', time: '2 minutes ago' },
        { type: 'revenue', message: 'User upgraded to Pro plan', time: '5 minutes ago' },
        { type: 'retention', message: '15 users returned today', time: '1 hour ago' },
        { type: 'feedback', message: 'Received NPS score of 9', time: '2 hours ago' }
    ];
    
    const activityList = document.getElementById('recentActivity');
    activityList.innerHTML = activities.map(activity => `
        <div class="activity-item ${activity.type}">
            <div class="activity-message">${activity.message}</div>
            <div class="activity-time">${activity.time}</div>
        </div>
    `).join('');
}

function setupFeedbackModal() {
    const feedbackBtn = document.getElementById('feedbackBtn');
    const feedbackModal = document.getElementById('feedbackModal');
    const closeFeedback = document.getElementById('closeFeedback');
    const feedbackForm = document.getElementById('feedbackForm');
    
    feedbackBtn.addEventListener('click', () => {
        feedbackModal.style.display = 'flex';
        analytics.track('feedback_modal_opened');
    });
    
    closeFeedback.addEventListener('click', () => {
        feedbackModal.style.display = 'none';
    });
    
    feedbackForm.addEventListener('submit', (e) => {
        e.preventDefault();
        const formData = new FormData(feedbackForm);
        const feedback = Object.fromEntries(formData);
        
        // Submit feedback
        submitFeedback(feedback);
        
        feedbackModal.style.display = 'none';
        showToast('Thank you for your feedback!');
    });
}

function submitFeedback(feedback) {
    // Track feedback submission
    analytics.trackFeature('feedback', 'feedback_submitted', {
        satisfaction: feedback.satisfaction,
        nps_score: feedback.nps
    });
    
    // Send to server
    fetch('/api/feedback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(feedback)
    }).catch(error => {
        console.warn('Failed to submit feedback:', error);
    });
}

function showToast(message) {
    // Simple toast notification
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// Track dashboard view
analytics.trackActivation('dashboard_viewed', {
    dashboard_type: 'metrics'
});
</script>

<style>
.metrics-dashboard {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
}

.kpi-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 48px;
}

.kpi-card {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    transition: all 0.2s ease;
}

.kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.kpi-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.kpi-header h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.kpi-target {
    font-size: 14px;
    color: var(--text-secondary);
    background: var(--background-secondary);
    padding: 4px 8px;
    border-radius: 6px;
}

.kpi-value {
    display: flex;
    align-items: baseline;
    gap: 12px;
    margin-bottom: 16px;
}

.metric-value {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
}

.metric-change {
    font-size: 14px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: 4px;
}

.metric-change.positive {
    color: var(--success-color);
    background: var(--success-bg);
}

.metric-change.negative {
    color: var(--error-color);
    background: var(--error-bg);
}

.progress-bar {
    height: 6px;
    background: var(--background-secondary);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: 3px;
    transition: width 0.5s ease;
}

.funnel-section {
    margin-bottom: 48px;
}

.funnel-section h2 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 24px;
}

.funnel-chart {
    display: grid;
    gap: 16px;
}

.funnel-stage {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px;
}

.stage-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.stage-header h3 {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
}

.stage-count {
    font-size: 18px;
    font-weight: 700;
    color: var(--primary-color);
}

.stage-bar {
    height: 8px;
    background: var(--background-secondary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.stage-fill {
    height: 100%;
    background: var(--primary-color);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.activity-section {
    margin-bottom: 48px;
}

.activity-list {
    display: grid;
    gap: 12px;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.activity-time {
    font-size: 14px;
    color: var(--text-secondary);
}

.feedback-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.feedback-card, 
.quarterly-interviews {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    text-align: center;
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background: var(--surface-color);
    border-radius: 12px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: var(--text-secondary);
}

.modal-body {
    padding: 24px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--background-color);
    color: var(--text-primary);
}

.toast {
    position: fixed;
    bottom: 24px;
    right: 24px;
    background: var(--success-color);
    color: white;
    padding: 12px 24px;
    border-radius: 6px;
    z-index: 1001;
}
</style>
{% endblock %}
