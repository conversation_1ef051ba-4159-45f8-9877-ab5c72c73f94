<!-- YouTube Research v2 - Analysis Cards Component Library -->
<!-- Reusable analysis card components following the new design system -->

<!-- Analysis Result Card Template -->
<template id="analysis-result-card-template">
    <div class="analysis-card">
        <div class="analysis-card-header">
            <div class="analysis-card-icon">
                <i data-lucide="zap" class="card-icon"></i>
            </div>
            <div class="analysis-card-title-section">
                <h3 class="analysis-card-title">Analysis Title</h3>
                <p class="analysis-card-subtitle">Subtitle or description</p>
            </div>
            <div class="analysis-card-actions">
                <button class="btn btn-ghost btn-sm card-action-btn" data-action="expand">
                    <i data-lucide="maximize-2" class="btn-icon"></i>
                </button>
                <button class="btn btn-ghost btn-sm card-action-btn" data-action="export">
                    <i data-lucide="download" class="btn-icon"></i>
                </button>
            </div>
        </div>
        
        <div class="analysis-card-content">
            <div class="analysis-card-body">
                <!-- Content will be populated dynamically -->
            </div>
        </div>
        
        <div class="analysis-card-footer">
            <div class="analysis-card-meta">
                <span class="analysis-timestamp">2 minutes ago</span>
                <span class="analysis-confidence">95% confidence</span>
            </div>
            <div class="analysis-card-tags">
                <!-- Tags will be populated dynamically -->
            </div>
        </div>
    </div>
</template>

<!-- Agent Status Card Template -->
<template id="agent-status-card-template">
    <div class="agent-status-card">
        <div class="agent-avatar-section">
            <div class="agent-avatar agent-performance">
                <i data-lucide="zap" class="agent-icon"></i>
            </div>
            <div class="agent-status-indicator status-working"></div>
        </div>
        
        <div class="agent-info-section">
            <h4 class="agent-name">Performance Intelligence</h4>
            <p class="agent-message">Analyzing performance metrics and engagement patterns...</p>
            <div class="agent-progress">
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 65%;"></div>
                </div>
                <span class="progress-text">65%</span>
            </div>
        </div>
        
        <div class="agent-actions">
            <button class="btn btn-ghost btn-sm" data-action="details">
                <i data-lucide="info" class="btn-icon"></i>
            </button>
        </div>
    </div>
</template>

<!-- Insight Card Template -->
<template id="insight-card-template">
    <div class="insight-card">
        <div class="insight-header">
            <div class="insight-priority priority-high">
                <i data-lucide="trending-up" class="priority-icon"></i>
            </div>
            <div class="insight-category">Performance</div>
        </div>
        
        <div class="insight-content">
            <h4 class="insight-title">Key Insight Title</h4>
            <p class="insight-description">Detailed description of the insight and its implications.</p>
            
            <div class="insight-metrics">
                <div class="metric-item">
                    <span class="metric-label">Impact</span>
                    <span class="metric-value">+23%</span>
                </div>
                <div class="metric-item">
                    <span class="metric-label">Confidence</span>
                    <span class="metric-value">92%</span>
                </div>
            </div>
        </div>
        
        <div class="insight-actions">
            <button class="btn btn-primary btn-sm">
                <i data-lucide="play" class="btn-icon"></i>
                Apply
            </button>
            <button class="btn btn-ghost btn-sm">
                <i data-lucide="bookmark" class="btn-icon"></i>
                Save
            </button>
        </div>
    </div>
</template>

<!-- Progress Indicator Template -->
<template id="progress-indicator-template">
    <div class="progress-indicator">
        <div class="progress-header">
            <h4 class="progress-title">Analysis Progress</h4>
            <span class="progress-percentage">0%</span>
        </div>
        
        <div class="progress-bar-container">
            <div class="progress-bar">
                <div class="progress-fill" style="width: 0%;"></div>
            </div>
        </div>
        
        <div class="progress-steps">
            <div class="progress-step active">
                <div class="step-indicator"></div>
                <span class="step-label">Starting</span>
            </div>
            <div class="progress-step">
                <div class="step-indicator"></div>
                <span class="step-label">Analyzing</span>
            </div>
            <div class="progress-step">
                <div class="step-indicator"></div>
                <span class="step-label">Processing</span>
            </div>
            <div class="progress-step">
                <div class="step-indicator"></div>
                <span class="step-label">Complete</span>
            </div>
        </div>
        
        <div class="progress-message">
            <p class="current-message">Initializing analysis...</p>
        </div>
    </div>
</template>

<!-- Metric Card Template -->
<template id="metric-card-template">
    <div class="metric-card">
        <div class="metric-header">
            <div class="metric-icon">
                <i data-lucide="bar-chart" class="metric-icon-svg"></i>
            </div>
            <div class="metric-trend trend-up">
                <i data-lucide="trending-up" class="trend-icon"></i>
            </div>
        </div>
        
        <div class="metric-content">
            <h3 class="metric-value">1.2M</h3>
            <p class="metric-label">Total Views</p>
            <div class="metric-change">
                <span class="change-value">+12.5%</span>
                <span class="change-period">vs last month</span>
            </div>
        </div>
    </div>
</template>

<!-- Status Feed Template -->
<template id="status-feed-template">
    <div class="status-feed">
        <div class="status-feed-header">
            <h4 class="feed-title">
                <i data-lucide="activity" class="feed-icon"></i>
                Live Analysis Feed
            </h4>
            <button class="btn btn-ghost btn-sm" data-action="clear">
                <i data-lucide="x" class="btn-icon"></i>
            </button>
        </div>
        
        <div class="status-feed-content" id="statusFeedContent">
            <!-- Status items will be populated dynamically -->
        </div>
        
        <div class="status-feed-footer">
            <div class="connection-status connected">
                <div class="status-dot"></div>
                <span>Connected</span>
            </div>
        </div>
    </div>
</template>

<!-- Status Feed Item Template -->
<template id="status-feed-item-template">
    <div class="status-feed-item">
        <div class="status-item-avatar">
            <i data-lucide="zap" class="status-icon"></i>
        </div>
        <div class="status-item-content">
            <div class="status-item-header">
                <span class="status-agent-name">Agent Name</span>
                <span class="status-timestamp">2:34 PM</span>
            </div>
            <p class="status-message">Status message content goes here...</p>
        </div>
    </div>
</template>

<!-- Loading Skeleton Template -->
<template id="loading-skeleton-template">
    <div class="loading-skeleton">
        <div class="skeleton-header">
            <div class="skeleton-avatar"></div>
            <div class="skeleton-text skeleton-title"></div>
        </div>
        <div class="skeleton-content">
            <div class="skeleton-text skeleton-line"></div>
            <div class="skeleton-text skeleton-line short"></div>
            <div class="skeleton-text skeleton-line"></div>
        </div>
        <div class="skeleton-footer">
            <div class="skeleton-button"></div>
            <div class="skeleton-button small"></div>
        </div>
    </div>
</template>

<!-- Error State Template -->
<template id="error-state-template">
    <div class="error-state">
        <div class="error-icon">
            <i data-lucide="alert-triangle" class="error-icon-svg"></i>
        </div>
        <div class="error-content">
            <h3 class="error-title">Something went wrong</h3>
            <p class="error-message">We encountered an error while processing your request.</p>
            <div class="error-actions">
                <button class="btn btn-primary" data-action="retry">
                    <i data-lucide="refresh-cw" class="btn-icon"></i>
                    Try Again
                </button>
                <button class="btn btn-ghost" data-action="support">
                    <i data-lucide="help-circle" class="btn-icon"></i>
                    Get Help
                </button>
            </div>
        </div>
    </div>
</template>

<!-- Empty State Template -->
<template id="empty-state-template">
    <div class="empty-state">
        <div class="empty-icon">
            <i data-lucide="inbox" class="empty-icon-svg"></i>
        </div>
        <div class="empty-content">
            <h3 class="empty-title">No data available</h3>
            <p class="empty-message">Start by running an analysis to see results here.</p>
            <div class="empty-actions">
                <button class="btn btn-primary" data-action="start">
                    <i data-lucide="play" class="btn-icon"></i>
                    Start Analysis
                </button>
            </div>
        </div>
    </div>
</template>

<!-- Component Library JavaScript -->
<script>
class ComponentLibrary {
    constructor() {
        this.templates = new Map();
        this.loadTemplates();
    }
    
    loadTemplates() {
        // Load all templates into memory for quick access
        const templates = document.querySelectorAll('template[id$="-template"]');
        templates.forEach(template => {
            const id = template.id.replace('-template', '');
            this.templates.set(id, template);
        });
    }
    
    create(componentType, data = {}) {
        const template = this.templates.get(componentType);
        if (!template) {
            console.error(`Template not found: ${componentType}`);
            return null;
        }
        
        const clone = template.content.cloneNode(true);
        
        // Populate with data based on component type
        switch (componentType) {
            case 'analysis-result-card':
                return this.populateAnalysisCard(clone, data);
            case 'agent-status-card':
                return this.populateAgentCard(clone, data);
            case 'insight-card':
                return this.populateInsightCard(clone, data);
            case 'metric-card':
                return this.populateMetricCard(clone, data);
            case 'progress-indicator':
                return this.populateProgressIndicator(clone, data);
            case 'status-feed-item':
                return this.populateStatusFeedItem(clone, data);
            default:
                return clone;
        }
    }
    
    populateAnalysisCard(clone, data) {
        if (data.title) {
            clone.querySelector('.analysis-card-title').textContent = data.title;
        }
        if (data.subtitle) {
            clone.querySelector('.analysis-card-subtitle').textContent = data.subtitle;
        }
        if (data.icon) {
            clone.querySelector('.card-icon').setAttribute('data-lucide', data.icon);
        }
        if (data.content) {
            clone.querySelector('.analysis-card-body').innerHTML = data.content;
        }
        if (data.timestamp) {
            clone.querySelector('.analysis-timestamp').textContent = data.timestamp;
        }
        if (data.confidence) {
            clone.querySelector('.analysis-confidence').textContent = `${data.confidence}% confidence`;
        }
        return clone;
    }
    
    populateAgentCard(clone, data) {
        if (data.name) {
            clone.querySelector('.agent-name').textContent = data.name;
        }
        if (data.message) {
            clone.querySelector('.agent-message').textContent = data.message;
        }
        if (data.progress !== undefined) {
            const progressFill = clone.querySelector('.progress-fill');
            const progressText = clone.querySelector('.progress-text');
            progressFill.style.width = `${data.progress}%`;
            progressText.textContent = `${Math.round(data.progress)}%`;
        }
        if (data.status) {
            const indicator = clone.querySelector('.agent-status-indicator');
            indicator.className = `agent-status-indicator status-${data.status}`;
        }
        if (data.agentType) {
            const avatar = clone.querySelector('.agent-avatar');
            avatar.className = `agent-avatar agent-${data.agentType}`;
        }
        return clone;
    }
    
    populateInsightCard(clone, data) {
        if (data.title) {
            clone.querySelector('.insight-title').textContent = data.title;
        }
        if (data.description) {
            clone.querySelector('.insight-description').textContent = data.description;
        }
        if (data.category) {
            clone.querySelector('.insight-category').textContent = data.category;
        }
        if (data.priority) {
            const priorityEl = clone.querySelector('.insight-priority');
            priorityEl.className = `insight-priority priority-${data.priority}`;
        }
        return clone;
    }
    
    populateMetricCard(clone, data) {
        if (data.value) {
            clone.querySelector('.metric-value').textContent = data.value;
        }
        if (data.label) {
            clone.querySelector('.metric-label').textContent = data.label;
        }
        if (data.change) {
            clone.querySelector('.change-value').textContent = data.change;
        }
        if (data.trend) {
            const trendEl = clone.querySelector('.metric-trend');
            trendEl.className = `metric-trend trend-${data.trend}`;
        }
        return clone;
    }
    
    populateProgressIndicator(clone, data) {
        if (data.title) {
            clone.querySelector('.progress-title').textContent = data.title;
        }
        if (data.percentage !== undefined) {
            const progressFill = clone.querySelector('.progress-fill');
            const progressPercentage = clone.querySelector('.progress-percentage');
            progressFill.style.width = `${data.percentage}%`;
            progressPercentage.textContent = `${Math.round(data.percentage)}%`;
        }
        if (data.message) {
            clone.querySelector('.current-message').textContent = data.message;
        }
        return clone;
    }
    
    populateStatusFeedItem(clone, data) {
        if (data.agentName) {
            clone.querySelector('.status-agent-name').textContent = data.agentName;
        }
        if (data.message) {
            clone.querySelector('.status-message').textContent = data.message;
        }
        if (data.timestamp) {
            clone.querySelector('.status-timestamp').textContent = data.timestamp;
        }
        if (data.icon) {
            clone.querySelector('.status-icon').setAttribute('data-lucide', data.icon);
        }
        return clone;
    }
}

// Initialize component library
window.componentLibrary = new ComponentLibrary();

// Utility functions for common component operations
window.ComponentUtils = {
    showLoading: function(container) {
        const loading = window.componentLibrary.create('loading-skeleton');
        container.innerHTML = '';
        container.appendChild(loading);
    },
    
    showError: function(container, message = null) {
        const error = window.componentLibrary.create('error-state');
        if (message) {
            error.querySelector('.error-message').textContent = message;
        }
        container.innerHTML = '';
        container.appendChild(error);
    },
    
    showEmpty: function(container, message = null) {
        const empty = window.componentLibrary.create('empty-state');
        if (message) {
            empty.querySelector('.empty-message').textContent = message;
        }
        container.innerHTML = '';
        container.appendChild(empty);
    }
};
</script>
