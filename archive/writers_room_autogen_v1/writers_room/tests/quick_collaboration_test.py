#!/usr/bin/env python3
"""
Quick test to show real AutoGen collaboration in action
Shorter version to see actual agent debates
"""

import os
import sys
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'core_system'))

from real_autogen_writers_room import RealAutoGenWritersRoom, WritersRoomInput


def quick_collaboration_test():
    """Quick test to see real agent collaboration"""
    
    input_data = WritersRoomInput(
        topic="The $20 Uber Ride That Cost a Man His Marriage",
        golden_nuggets=[
            "<PERSON> took a $20 Uber ride to a bar instead of going home",
            "His wife tracked his location and found out he lied",
            "She filed for divorce the next day",
            "The divorce settlement cost him $500,000",
            "The Uber driver later said <PERSON> seemed nervous the whole ride",
            "This became a viral story about modern relationships and technology"
        ],
        persona={
            "channel": "Modern Drama",
            "voice": "Shocked storyteller",
            "focus": "How small decisions create life-changing consequences"
        },
        quality_threshold=7.0,  # Lower threshold for faster test
        max_rounds=15  # Fewer rounds for quicker completion
    )
    
    print("🚀 QUICK COLLABORATION TEST")
    print("=" * 60)
    print(f"📍 Topic: {input_data.topic}")
    print(f"🎯 Quality Threshold: {input_data.quality_threshold}/10")
    print(f"🔄 Max Rounds: {input_data.max_rounds}")
    print("=" * 60)
    
    if not os.environ.get("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY not found")
        return
    
    try:
        writers_room = RealAutoGenWritersRoom(model="gpt-4")
        
        # Just run Phase 1 to see collaboration
        print("\n🥊 RUNNING STRUCTURE COLLABORATION ONLY...")
        result = writers_room._phase_1_structure_collaboration(input_data)
        
        print(f"\n✅ COLLABORATION SAMPLE COMPLETE")
        print(f"🔄 Rounds: {result['rounds']}")
        print(f"💬 Messages: {len(result['conversation'])}")
        
        # Show conversation sample
        print(f"\n📜 CONVERSATION SAMPLE:")
        print("-" * 60)
        
        for i, msg in enumerate(result['conversation'][-6:]):  # Last 6 messages
            name = msg.get('name', 'Unknown')
            content = msg.get('content', '')[:200] + "..." if len(msg.get('content', '')) > 200 else msg.get('content', '')
            
            print(f"\n{name}:")
            print(f"{content}")
            print("-" * 40)
        
        # Save sample output
        output_dir = "/Users/<USER>/Documents/CrewAI/youtube-research-v2/writers_room/outputs/collaboration_samples"
        os.makedirs(output_dir, exist_ok=True)
        
        import json
        with open(f"{output_dir}/uber_marriage_sample.json", 'w') as f:
            json.dump(result, f, indent=2)
        
        print(f"\n💾 Sample saved to: {output_dir}/uber_marriage_sample.json")
        
        return result
        
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    quick_collaboration_test()