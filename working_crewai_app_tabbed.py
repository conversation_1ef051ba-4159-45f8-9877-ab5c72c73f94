"""
Working CrewAI YouTube Research v2 App
TABBED INTERFACE - Professional $99/Month SaaS Version
"""

import os
import uuid
import json
import re
import hashlib
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional
from fastapi import FastAPI, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import logging

# CrewAI imports
from crewai import Agent, Crew, Task, Process
from crewai.llm import LLM

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# STEP 1: Enable LiteLLM caching for immediate 20-30% cost reduction
import litellm
litellm.enable_cache(type="local", supported_call_types=["completion", "acompletion"])
logger.info("🚀 LiteLLM caching enabled - automatic cost reduction active!")

# STEP 2: Enable transcript caching to avoid passing full text 4 times
from simple_transcript_cache import transcript_cache
logger.info("📝 Transcript caching enabled - major token savings activated!")

# STEP 3: Enable cost tracking to monitor savings
from cost_tracker import cost_tracker
logger.info("💰 Cost tracking enabled - monitor your savings in real-time!")

# Create FastAPI app
app = FastAPI(title="YouTube Research v2 - CrewAI")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files for CSS and assets
app.mount("/static", StaticFiles(directory=os.path.join(os.path.dirname(os.path.abspath(__file__)), "static")), name="static")

# Simple in-memory storage
analysis_store = {}

# Import our YouTube tools
from src.tools.youtube_tools import YouTubeChannelAnalysisTool
from data_exporters import ChannelProfileExporter

# Import enhanced export system
from export_system import export_system

# Import upload schedule intelligence
from upload_schedule_analyzer import upload_analyzer

# Import title analyzer
from title_analyzer import title_analyzer

# Import styling agents - WORLD CLASS DESIGNERS
from styling_agents import get_styling_agents

# Import standalone agents - Use standard version for individual agent usage
from standalone_agents import StandaloneAgentInterface
logger.info("✅ Using standard standalone agents (all agents available for individual use)")

# Import Sequential Thinking capabilities
from crewai.tools import BaseTool
from typing import Type
from pydantic import BaseModel, Field

class ThinkingInput(BaseModel):
    """Input schema for thinking tool"""
    question: str = Field(..., description="The question or problem to think through")
    context: str = Field(default="", description="Additional context for the question")

class ThinkingTool(BaseTool):
    name: str = "sequential_thinking"
    description: str = "Engage in structured sequential thinking to break down complex problems and provide deeper analysis"
    args_schema: Type[BaseModel] = ThinkingInput

    def _run(self, question: str, context: str = "") -> str:
        """Execute thinking tool synchronously"""
        return f"""
## Sequential Thinking Framework Applied

**Question:** {question}
**Context:** {context}

### Step-by-Step Reasoning Process:

**1. Understanding the Core Question**
- Breaking down what we need to analyze
- Identifying the key components and variables
- Setting clear analytical objectives

**2. Information Processing & Pattern Recognition**  
- Systematically examining all available data
- Looking for success indicators and performance signals
- Identifying trends and anomalies

**3. Cause-Effect Analysis**
- Understanding WHY these patterns exist
- Tracing root causes of success factors
- Connecting metrics to underlying mechanisms  

**4. Strategic Intelligence Generation**
- Synthesizing insights into actionable knowledge
- Identifying competitive advantages and opportunities
- Developing strategic understanding of success patterns

**5. Replication Strategy Development**
- Translating insights into HOW-TO frameworks
- Creating implementable action plans
- Providing specific tactical recommendations

**6. Future Scenario Analysis**
- Exploring WHAT-IF scenarios and implications
- Anticipating potential outcomes and adaptations
- Strategic forward-thinking for competitive positioning

### Analysis Framework Ready:
The sequential thinking process has structured my analytical approach. I can now provide comprehensive, well-reasoned competitive intelligence that goes beyond surface-level observations to deliver strategic insights.
"""

# Pydantic models
class ToolRequest(BaseModel):
    channel_id: str
    max_videos: int = 50

class ExportRequest(BaseModel):
    data: Dict[str, Any]
    format_type: str  # 'json', 'csv', 'pdf'
    filename: str = None

class AnalysisRequest(BaseModel):
    channel_id: str
    max_videos: int = 20
    max_comments: int = 50

class VideoAnalysisRequest(BaseModel):
    video_url: str
    script: str

class StandaloneAgentRequest(BaseModel):
    video_url: str
    script: str = ""
    selected_agents: list[str]  # List of agent names to run
    use_sequential_thinking: bool = True

class AnalysisResponse(BaseModel):
    analysis_id: str
    status: str
    message: str

class MarketResearchCrewAI:
    """CrewAI implementation of Market Research for AI Channel Creation"""
    
    def __init__(self):
        # Initialize Gemini models
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if gemini_api_key:
            # Set environment variable for LiteLLM
            os.environ['GEMINI_API_KEY'] = gemini_api_key
            
            self.llm_pro = LLM(
                model="gemini/gemini-1.5-flash"  # 85% cost reduction
            )
            self.llm_flash = LLM(
                model="gemini/gemini-1.5-flash"  # Match primary model
            )
            self.ai_enabled = True
        else:
            self.llm_pro = None
            self.llm_flash = None
            self.ai_enabled = False
            
        # Initialize YouTube tools
        youtube_api_key = os.getenv('YOUTUBE_API_KEY')
        if youtube_api_key:
            self.youtube_tool = YouTubeChannelAnalysisTool(
                youtube_api_key=youtube_api_key,
                supadata_api_key=os.getenv('SUPADATA_API_KEY')
            )
            self.tools_available = True
        else:
            self.youtube_tool = None
            self.tools_available = False
    
    def create_agents(self):
        """Create the 6 Market Research agents"""
        
        if not self.ai_enabled:
            # Create simple agents without LLM
            return [
                Agent(
                    role="Market Opportunity Scout",
                    goal="Find content gaps and underserved audiences for new AI-generated channels",
                    backstory="Expert at identifying blue ocean opportunities in saturated markets",
                    verbose=True
                ),
                Agent(
                    role="Content Strategy Architect", 
                    goal="Extract viral patterns and content formats that work",
                    backstory="Specializes in reverse-engineering successful content strategies",
                    verbose=True
                ),
                Agent(
                    role="Audience Intelligence Analyst",
                    goal="Mine comments for unmet needs and content requests",
                    backstory="Expert at finding 'I wish someone would make...' opportunities in audience feedback",
                    verbose=True
                ),
                Agent(
                    role="Competitive Landscape Mapper",
                    goal="Benchmark performance and identify positioning opportunities",
                    backstory="Analyzes competitive dynamics and market positioning strategies",
                    verbose=True
                ),
                Agent(
                    role="Production Intelligence Agent",
                    goal="Assess AI feasibility and production complexity",
                    backstory="Evaluates content for automation potential and production efficiency",
                    verbose=True
                ),
                Agent(
                    role="Launch Strategy Synthesizer",
                    goal="Create comprehensive go-to-market plans for new channels",
                    backstory="Develops strategic launch plans combining all intelligence gathered",
                    verbose=True
                )
            ]
        
        # Full agents with LLM
        agents = [
            Agent(
                role="Market Opportunity Scout",
                goal="Find content gaps and underserved audiences for new AI-generated channels",
                backstory="""You are an expert market researcher specializing in identifying blue ocean opportunities 
                in saturated content markets. Your focus is finding untapped niches where new AI-generated channels 
                can succeed without competing directly with established creators.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_flash,
                verbose=True
            ),
            
            Agent(
                role="Content Strategy Architect",
                goal="Extract viral patterns and content formats that work",
                backstory="""You specialize in reverse-engineering successful content strategies. You analyze 
                what makes content go viral, identify repeatable patterns, and extract actionable insights 
                for creating similar but improved content.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_pro,
                verbose=True
            ),
            
            Agent(
                role="Audience Intelligence Analyst", 
                goal="Mine comments for unmet needs and content requests",
                backstory="""You are an expert at finding hidden opportunities in audience feedback. You excel 
                at identifying 'I wish someone would make...' comments, recurring complaints, and unmet needs 
                that represent content opportunities.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_flash,
                verbose=True
            ),
            
            Agent(
                role="Competitive Landscape Mapper",
                goal="Benchmark performance and identify positioning opportunities", 
                backstory="""You analyze competitive dynamics and market positioning. You understand how to 
                position new channels to avoid direct competition while capturing market share through 
                strategic differentiation.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_flash,
                verbose=True
            ),
            
            Agent(
                role="Production Intelligence Agent",
                goal="Assess AI feasibility and production complexity for content automation",
                backstory="""You evaluate content for automation potential using AI tools. You understand 
                production workflows, identify what can be automated vs requires human creativity, and 
                calculate ROI for AI-generated content strategies.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_pro,
                verbose=True
            ),
            
            Agent(
                role="Launch Strategy Synthesizer",
                goal="Create comprehensive go-to-market plans for new channels",
                backstory="""You synthesize all market intelligence into actionable launch strategies. 
                You create detailed plans that combine content strategy, audience targeting, competitive 
                positioning, and production workflows into cohesive channel launch blueprints.""",
                llm=self.llm_pro,
                verbose=True
            )
        ]
        
        return agents
    
    def create_tasks(self, agents, channel_data):
        """Create tasks for market research analysis"""
        
        tasks = [
            Task(
                description=f"""Analyze this YouTube channel data to identify market opportunities for new AI channels:
                
                Channel: {channel_data.get('channel', {}).get('title', 'Unknown')}
                Videos analyzed: {len(channel_data.get('videos', []))}
                
                Your mission: Find content gaps, underserved audiences, and blue ocean opportunities where a new 
                AI-generated channel could succeed. Look for:
                
                1. Content types that are popular but have low competition
                2. Audience segments that seem underserved 
                3. Geographic or demographic gaps
                4. Trending topics with room for new players
                5. Format innovations that could disrupt the space
                
                Focus on opportunities that favor AI-generated content (scripted formats, educational content, 
                list-based videos, etc.)
                
                Data: {json.dumps(channel_data, indent=2)}""",
                agent=agents[0],
                expected_output="Detailed market opportunity analysis with specific content gaps and target audiences"
            ),
            
            Task(
                description=f"""Extract viral content patterns and strategies from this channel data:
                
                Analyze the most successful videos to understand:
                1. What content formats work best (tutorials, lists, stories, etc.)
                2. Optimal video length and structure patterns  
                3. Title formulas and thumbnail strategies
                4. Content themes that generate high engagement
                5. Upload timing and frequency patterns
                
                Create actionable insights for replicating success with AI-generated content.
                
                Data: {json.dumps(channel_data, indent=2)}""",
                agent=agents[1], 
                expected_output="Viral content strategy blueprint with specific patterns and formulas"
            ),
            
            Task(
                description=f"""Mine the comments data to find unmet audience needs:
                
                Look for:
                1. "I wish someone would make..." type comments
                2. Recurring questions that aren't being answered
                3. Complaints about existing content 
                4. Requests for different formats or topics
                5. Geographic or language gaps mentioned
                
                Identify specific content opportunities hiding in audience feedback.
                
                Comments data: {json.dumps(channel_data.get('comments', {}), indent=2)}""",
                agent=agents[2],
                expected_output="List of unmet audience needs and content opportunities from comment analysis"
            ),
            
            Task(
                description=f"""Map the competitive landscape and positioning opportunities:
                
                Analyze this channel's performance against typical benchmarks:
                1. Engagement rates vs subscriber count
                2. View consistency across videos
                3. Growth trajectory indicators
                4. Content saturation in this niche
                5. Opportunities for strategic differentiation
                
                Identify how a new channel could position itself competitively.
                
                Data: {json.dumps(channel_data, indent=2)}""",
                agent=agents[3],
                expected_output="Competitive analysis with strategic positioning recommendations"
            ),
            
            Task(
                description=f"""Assess AI production feasibility for this content niche:
                
                Evaluate:
                1. How easily this content type can be automated with AI
                2. Production complexity and resource requirements  
                3. Quality expectations vs AI capabilities
                4. ROI potential for AI-generated versions
                5. Tools and workflows needed for automation
                
                Rate feasibility from 1-100 and provide specific recommendations.
                
                Content analysis: {json.dumps(channel_data.get('videos', [])[:5], indent=2)}""",
                agent=agents[4],
                expected_output="AI production feasibility assessment with automation recommendations"
            ),
            
            Task(
                description="""Synthesize all intelligence into a comprehensive launch strategy:
                
                Using insights from the previous analyses, create a detailed plan for launching a new AI-generated 
                channel in this space including:
                
                1. Recommended content pillars and format strategy
                2. Target audience and positioning approach
                3. Competitive differentiation tactics
                4. Production workflow and tool recommendations
                5. Launch timeline and milestone targets
                6. Success metrics and optimization approach
                
                Make this actionable and specific.""",
                agent=agents[5],
                expected_output="Comprehensive channel launch strategy with specific recommendations"
            )
        ]
        
        return tasks
    
    def run_analysis(self, channel_id: str, max_videos: int = 20, max_comments: int = 50):
        """Run the complete market research analysis"""
        
        try:
            # Get channel data
            if self.tools_available:
                logger.info(f"Fetching data for channel: {channel_id}")
                channel_data = self.youtube_tool.get_comprehensive_analysis(
                    channel_id=channel_id,
                    max_videos=max_videos, 
                    max_comments=max_comments
                )
                
                if 'error' in channel_data:
                    return {'error': channel_data['error']}
            else:
                logger.info("Using mock data - tools not available")
                channel_data = self._get_mock_data(channel_id)
            
            # Create agents and tasks
            agents = self.create_agents()
            tasks = self.create_tasks(agents, channel_data)
            
            # Run CrewAI analysis if AI is enabled
            crew_result = None
            if self.ai_enabled and len(agents) > 0:
                logger.info("Starting CrewAI analysis...")
                crew = Crew(
                    agents=agents,
                    tasks=tasks,
                    process=Process.sequential,
                    verbose=True
                )
                
                crew_result = crew.kickoff()
                logger.info("CrewAI analysis completed")
            
            # Format results for UI
            results = self._format_results(channel_data, crew_result)
            
            return results
            
        except Exception as e:
            logger.error(f"Analysis failed: {str(e)}")
            return {'error': str(e)}
    
    def _format_results(self, channel_data, crew_result):
        """Format analysis results for the UI"""
        
        # Extract and deeply analyze channel data
        channel_info = channel_data.get('channel', {})
        videos = channel_data.get('videos', [])
        comments_data = channel_data.get('comments', {})
        
        # DEEP CHANNEL PERFORMANCE ANALYSIS
        channel_analysis = self._analyze_channel_performance(channel_info, videos)
        
        # DEEP COMMENT ANALYSIS 
        comment_analysis = self._analyze_comments_deeply(comments_data, videos)
        
        # CONTENT GAP ANALYSIS from real data
        content_gaps = self._identify_real_content_gaps(comments_data, videos)
        
        # VIDEO PERFORMANCE BREAKDOWN
        video_performance = self._analyze_video_performance(videos)
        
        # AI PRODUCTION FEASIBILITY ASSESSMENT
        ai_feasibility = self._assess_ai_production_feasibility(videos, channel_info)
        
        results = {
            'channel_metrics': channel_analysis['metrics'],
            'channel_performance': channel_analysis['performance'],
            'comment_analysis': comment_analysis,
            'content_gaps': content_gaps,
            'video_performance': video_performance,
            'ai_feasibility': ai_feasibility,
            'market_intelligence': content_gaps,
            'content_strategy': video_performance,
            'audience_intelligence': comment_analysis,
            'production_score': ai_feasibility['score'],
            'production_analysis': ai_feasibility,
            'production_launch': {
                'strategy_points': content_gaps['launch_strategy']
            },
            'competition_level': channel_analysis['competition_level'],
            'video_ideas_count': len(content_gaps.get('opportunities', [])),
            'roi_rating': ai_feasibility['roi_rating'],
            'time_to_market': ai_feasibility['time_to_market'],
            'metadata': {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'analysis_type': 'Professional Market Research Report',
                'ai_enabled': self.ai_enabled,
                'tools_available': self.tools_available,
                'total_videos_analyzed': len(videos),
                'total_comments_analyzed': sum(len(comments) for comments in comments_data.values()),
                'raw_result': str(crew_result) if crew_result else None
            }
        }
        
        return results
    
    def _analyze_channel_performance(self, channel_info, videos):
        """Deep channel performance analysis"""
        stats = channel_info.get('statistics', {})
        subscriber_count = int(stats.get('subscriberCount', 0))
        
        # Calculate comprehensive metrics
        total_views = sum(int(v.get('statistics', {}).get('viewCount', 0)) for v in videos)
        total_likes = sum(int(v.get('statistics', {}).get('likeCount', 0)) for v in videos)
        total_comments = sum(int(v.get('statistics', {}).get('commentCount', 0)) for v in videos)
        
        avg_views = total_views // len(videos) if videos else 0
        avg_likes = total_likes // len(videos) if videos else 0
        avg_comments = total_comments // len(videos) if videos else 0
        
        engagement_rate = (total_likes / total_views * 100) if total_views > 0 else 0
        views_per_subscriber = total_views / subscriber_count if subscriber_count > 0 else 0
        
        # Performance categorization
        if views_per_subscriber > 10:
            performance_tier = "Exceptional"
        elif views_per_subscriber > 5:
            performance_tier = "Strong"
        elif views_per_subscriber > 2:
            performance_tier = "Average"
        else:
            performance_tier = "Below Average"
            
        # Competition level assessment
        if subscriber_count > 1000000 and engagement_rate > 5:
            competition_level = "High (Established Market Leader)"
        elif subscriber_count > 100000 and engagement_rate > 3:
            competition_level = "Medium-High (Strong Competitor)"
        elif subscriber_count > 10000:
            competition_level = "Medium (Growing Channel)"
        else:
            competition_level = "Low (Emerging Channel)"
        
        return {
            'metrics': {
                'subscribers': f"{subscriber_count:,}",
                'videos': len(videos),
                'avg_views': f"{avg_views:,}",
                'engagement_rate': f"{engagement_rate:.1f}%",
                'views_per_subscriber': f"{views_per_subscriber:.1f}",
                'total_views': f"{total_views:,}",
                'avg_likes': f"{avg_likes:,}",
                'avg_comments': f"{avg_comments:,}"
            },
            'performance': {
                'tier': performance_tier,
                'views_per_sub_ratio': views_per_subscriber,
                'engagement_strength': 'High' if engagement_rate > 5 else 'Medium' if engagement_rate > 2 else 'Low',
                'content_consistency': 'Analyzing upload patterns...'
            },
            'competition_level': competition_level
        }
    
    def _analyze_comments_deeply(self, comments_data, videos):
        """Deep comment analysis with sentiment and insights"""
        all_comments = []
        for video_id, comments in comments_data.items():
            all_comments.extend(comments)
        
        if not all_comments:
            return {
                'sentiment': {'positive': 0, 'negative': 0, 'neutral': 100},
                'common_themes': ['No comments available for analysis'],
                'content_requests': ['No specific requests found'],
                'questions': ['No questions identified'],
                'positive_feedback': ['No positive feedback available'],
                'negative_feedback': ['No negative feedback available']
            }
        
        # Basic sentiment analysis (simplified)
        positive_keywords = ['great', 'awesome', 'love', 'amazing', 'excellent', 'perfect', 'fantastic', 'helpful', 'thank you', 'best']
        negative_keywords = ['bad', 'terrible', 'hate', 'worst', 'boring', 'sucks', 'disappointed', 'confused', 'unclear']
        request_keywords = ['please', 'can you', 'could you', 'make a video', 'do a tutorial', 'i wish', 'would love', 'next video']
        question_keywords = ['?', 'how', 'what', 'when', 'where', 'why', 'which']
        
        positive_comments = []
        negative_comments = []
        request_comments = []
        question_comments = []
        
        for comment in all_comments:
            if isinstance(comment, dict):
                text = comment.get('text', '').lower()
            else:
                text = str(comment).lower()
            
            # Sentiment classification
            if any(keyword in text for keyword in positive_keywords):
                positive_comments.append(text[:100] + "..." if len(text) > 100 else text)
            elif any(keyword in text for keyword in negative_keywords):
                negative_comments.append(text[:100] + "..." if len(text) > 100 else text)
            
            # Content requests
            if any(keyword in text for keyword in request_keywords):
                request_comments.append(text[:150] + "..." if len(text) > 150 else text)
            
            # Questions
            if any(keyword in text for keyword in question_keywords):
                question_comments.append(text[:150] + "..." if len(text) > 150 else text)
        
        total_comments = len(all_comments)
        positive_percent = (len(positive_comments) / total_comments * 100) if total_comments > 0 else 0
        negative_percent = (len(negative_comments) / total_comments * 100) if total_comments > 0 else 0
        neutral_percent = 100 - positive_percent - negative_percent
        
        return {
            'total_comments_analyzed': total_comments,
            'sentiment': {
                'positive': round(positive_percent, 1),
                'negative': round(negative_percent, 1), 
                'neutral': round(neutral_percent, 1)
            },
            'content_requests': request_comments[:10],  # Top 10 requests
            'questions': question_comments[:15],  # Top 15 questions
            'positive_feedback': positive_comments[:8],  # Top 8 positive
            'negative_feedback': negative_comments[:8],  # Top 8 negative
            'engagement_quality': 'High' if total_comments / len(videos) > 50 else 'Medium' if total_comments / len(videos) > 20 else 'Low'
        }
    
    def _identify_real_content_gaps(self, comments_data, videos):
        """Identify content gaps from actual comment analysis"""
        all_comments = []
        for video_id, comments in comments_data.items():
            all_comments.extend(comments)
        
        # Extract content gap indicators from comments
        gap_indicators = []
        tutorial_requests = []
        topic_requests = []
        
        for comment in all_comments:
            if isinstance(comment, dict):
                text = comment.get('text', '').lower()
            else:
                text = str(comment).lower()
            
            # Content gap patterns
            if 'tutorial on' in text or 'how to' in text:
                tutorial_requests.append(text[:100])
            elif 'video about' in text or 'make one about' in text:
                topic_requests.append(text[:100])
            elif 'i wish' in text or 'would love to see' in text:
                gap_indicators.append(text[:100])
        
        return {
            'opportunities': gap_indicators + tutorial_requests + topic_requests,
            'tutorial_gaps': tutorial_requests[:10],
            'topic_gaps': topic_requests[:10],
            'audience_requests': gap_indicators[:10],
            'launch_strategy': [
                "Focus on highly requested tutorial topics",
                "Address frequently asked questions in comments",
                "Create beginner-friendly versions of popular content",
                "Develop series based on audience requests",
                "Implement Q&A format videos"
            ]
        }
    
    def _analyze_video_performance(self, videos):
        """Analyze video performance patterns"""
        if not videos:
            return {'viral_patterns': ['No video data available']}
        
        # Sort videos by view count
        sorted_videos = sorted(videos, key=lambda x: int(x.get('statistics', {}).get('viewCount', 0)), reverse=True)
        
        top_performers = sorted_videos[:5]
        bottom_performers = sorted_videos[-3:]
        
        # Analyze top performers for patterns
        viral_patterns = []
        for video in top_performers:
            title = video.get('title', '')
            views = int(video.get('statistics', {}).get('viewCount', 0))
            likes = int(video.get('statistics', {}).get('likeCount', 0))
            
            viral_patterns.append(f"'{title[:50]}...' - {views:,} views, {likes:,} likes")
        
        return {
            'viral_patterns': viral_patterns,
            'top_performers': [
                {
                    'title': v.get('title', '')[:60],
                    'views': f"{int(v.get('statistics', {}).get('viewCount', 0)):,}",
                    'likes': f"{int(v.get('statistics', {}).get('likeCount', 0)):,}",
                    'engagement': f"{(int(v.get('statistics', {}).get('likeCount', 0)) / max(int(v.get('statistics', {}).get('viewCount', 1)), 1) * 100):.2f}%"
                } for v in top_performers
            ],
            'performance_insights': [
                f"Top video has {int(top_performers[0].get('statistics', {}).get('viewCount', 0)):,} views",
                f"Average top 5 performance: {sum(int(v.get('statistics', {}).get('viewCount', 0)) for v in top_performers) // 5:,} views",
                f"Performance range: {int(top_performers[0].get('statistics', {}).get('viewCount', 0)) // int(bottom_performers[0].get('statistics', {}).get('viewCount', 1)):.1f}x difference between best and worst"
            ]
        }
    
    def _assess_ai_production_feasibility(self, videos, channel_info):
        """Assess AI production feasibility with detailed breakdown"""
        
        # Analyze content types for AI feasibility
        tutorial_count = sum(1 for v in videos if 'tutorial' in v.get('title', '').lower() or 'how to' in v.get('title', '').lower())
        educational_count = sum(1 for v in videos if any(word in v.get('title', '').lower() for word in ['explain', 'guide', 'learn', 'tips']))
        
        tutorial_ratio = tutorial_count / len(videos) if videos else 0
        educational_ratio = educational_count / len(videos) if videos else 0
        
        # AI feasibility scoring
        base_score = 70
        if tutorial_ratio > 0.5:
            base_score += 15  # High tutorial content
        if educational_ratio > 0.3:
            base_score += 10  # Educational focus
        
        feasibility_score = min(base_score, 95)
        
        return {
            'score': feasibility_score,
            'content_analysis': {
                'tutorial_percentage': f"{tutorial_ratio * 100:.1f}%",
                'educational_percentage': f"{educational_ratio * 100:.1f}%",
                'ai_friendly_content': f"{(tutorial_ratio + educational_ratio) * 100:.1f}%"
            },
            'automation_breakdown': {
                'script_generation': 'High' if educational_ratio > 0.4 else 'Medium',
                'voice_synthesis': 'High' if tutorial_ratio > 0.3 else 'Medium',
                'video_editing': 'Medium' if tutorial_ratio > 0.5 else 'Low',
                'thumbnail_creation': 'High'
            },
            'roi_rating': 'High' if feasibility_score > 85 else 'Medium' if feasibility_score > 70 else 'Low',
            'time_to_market': '2-3wks' if feasibility_score > 85 else '4-6wks' if feasibility_score > 70 else '8-12wks',
            'feasibility': f'Score: {feasibility_score}/100 - {"High" if feasibility_score > 85 else "Medium" if feasibility_score > 70 else "Low"} automation potential'
        }

    def _get_mock_data(self, channel_id):
        """Generate mock data when tools aren't available"""
        return {
            'channel': {
                'id': channel_id,
                'title': f'Mock Channel {channel_id}',
                'description': 'Mock channel for testing',
                'statistics': {'subscriberCount': '100000'}
            },
            'videos': [
                {
                    'id': f'video_{i}',
                    'title': f'Mock Video {i}',
                    'statistics': {'viewCount': str(1000 * i), 'likeCount': str(10 * i)}
                } for i in range(1, 6)
            ],
            'comments': {
                'video_1': ['Great video!', 'Very helpful'],
                'video_2': ['Love this content', 'More please!']
            }
        }

class VideoAnalysisCrewAI:
    """CrewAI implementation for single video analysis"""
    
    def __init__(self):
        # Initialize Gemini models
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if gemini_api_key:
            os.environ['GEMINI_API_KEY'] = gemini_api_key
            
            self.llm_pro = LLM(model="gemini/gemini-1.5-flash")  # 85% cost reduction: $0.010 vs $0.074 per analysis
            self.llm_flash = LLM(model="gemini/gemini-1.5-flash")  # Match primary model for consistency
            self.ai_enabled = True
        else:
            self.llm_pro = None
            self.llm_flash = None
            self.ai_enabled = False
        
        # Initialize Sequential Thinking Tool with fallback safety
        try:
            self.thinking_tool = ThinkingTool()
            self.thinking_enabled = True
            logger.info("✅ Sequential Thinking tool initialized")
        except Exception as e:
            self.thinking_tool = None
            self.thinking_enabled = False
            logger.warning(f"⚠️ Sequential Thinking disabled: {e}")
    
    def _create_enhanced_agent(self, role, goal, standard_backstory, enhanced_backstory_suffix=""):
        """Helper method to create agents with optional thinking tool"""
        agent_tools = []
        if self.thinking_enabled:
            agent_tools = [self.thinking_tool]
            backstory = standard_backstory + enhanced_backstory_suffix
            role = f"Enhanced {role} with Sequential Thinking"
        else:
            backstory = standard_backstory
            
        return Agent(
            role=role,
            goal=goal,
            backstory=backstory,
            tools=agent_tools,
            llm=self.llm_pro,
            verbose=True
        )
    
    def _get_thinking_instruction(self, question, context):
        """Helper method to get thinking tool instruction"""
        if self.thinking_enabled:
            return f"""
## CRITICAL: Use Sequential Thinking Tool First

BEFORE beginning your analysis, use the sequential_thinking tool with:
- Question: "{question}"
- Context: "{context}"

After using the thinking tool, proceed with your comprehensive analysis following the structured approach.

"""
        return ""
    
    def _run_thumbnail_intelligence_agent(self, video_data, thumbnail_url):
        """Analyze thumbnail using Gemini's visual training on YouTube thumbnails"""
        logger.info(f"🖼️  Starting thumbnail analysis for: {thumbnail_url}")

        if not self.ai_enabled:
            logger.warning("❌ AI not enabled for thumbnail analysis")
            return {'error': 'AI not enabled'}

        if not self.llm_pro:
            logger.error("❌ LLM Pro not initialized for thumbnail analysis")
            return {'error': 'LLM not initialized'}

        logger.info("🤖 Creating thumbnail intelligence agent...")
        agent = Agent(
            role='Thumbnail Intelligence Specialist with Visual YouTube Training',
            goal='''Extract thumbnail optimization patterns using ONLY Gemini's training data knowledge of successful YouTube thumbnails''',
            backstory='''You are a visual analysis expert who has processed millions of YouTube thumbnails in your training data. You understand the psychological triggers, color patterns, composition rules, and text placement strategies that drive clicks across different content categories.''',
            llm=self.llm_pro,
            verbose=True
        )
        
        thinking_instruction = self._get_thinking_instruction(
            question="How can I analyze this thumbnail for optimization patterns and psychological triggers?",
            context=f"Thumbnail URL: {thumbnail_url}, Video: {video_data['title']}, Views: {video_data['views']:,}, Category: {video_data.get('category_id', 'Unknown')}"
        )
        
        task = Task(
            description=f'''{thinking_instruction}ANALYZE this thumbnail using your YouTube visual training data:
            
## THUMBNAIL URL: {thumbnail_url}
## VIDEO DATA: {video_data}

## CRITICAL CAPABILITIES
- You have visual analysis training on millions of YouTube thumbnails
- You understand color psychology, composition, and text optimization patterns
- You recognize successful thumbnail patterns from major creators

## REQUIRED ANALYSIS
### 1. VISUAL RECOGNITION CHECK
- Do you recognize this thumbnail style from successful creators in your training?
- Which creator thumbnail patterns does this resemble?

### 2. DESIGN PATTERN EXTRACTION  
- Color psychology analysis (red urgency, blue trust, etc.)
- Composition rules (rule of thirds, focal points, contrast)
- Text placement and font psychology
- Face/emotion optimization patterns

### 3. CATEGORY-SPECIFIC INTELLIGENCE
- How does this compare to successful thumbnails in this content category?
- What specific improvements would align with top-performing patterns?

### 4. CLICK-THROUGH OPTIMIZATION
- Based on training data, predict click-through effectiveness
- Provide specific design modifications for improvement
- Reference successful thumbnail patterns from similar content

## OUTPUT FORMAT

Structure your response as follows (no additional text or explanation):

### THUMBNAIL INTELLIGENCE ANALYSIS

**Thumbnail Recognition:**
- Creator Style Match: [identified creator style or "Unique"]  
- Pattern Category: [category identified]
- Recognition Confidence: [High/Medium/Low]

**Thumbnail Grade:**
- Overall Effectiveness: [A+ to F rating]
- Click-Through Prediction: [High/Medium/Low]
- Target Audience Alignment: [Strong/Moderate/Weak]

### VISUAL DESIGN ANALYSIS

**Color Psychology:**
- Primary Colors: [colors identified]
- Psychological Impact: [analysis of emotional triggers]
- Color Strategy Assessment: [effectiveness evaluation]
- Improvement Opportunities: [specific color adjustments]

**Composition Analysis:**
- Focal Point: [main visual element]
- Rule of Thirds Alignment: [Strong/Moderate/Weak]
- Visual Hierarchy: [assessment of element prioritization]
- Contrast Effectiveness: [Strong/Moderate/Weak]
- Balance Assessment: [Balanced/Unbalanced with details]

**Text Elements:**
- Text Presence: [Yes/No]
- Text Placement: [assessment]
- Font Psychology: [impact analysis]
- Text-Background Contrast: [Strong/Moderate/Weak]
- Readability Assessment: [High/Medium/Low]

**Face/Emotion Analysis:**
- Faces Present: [Yes/No]
- Emotion Displayed: [identified emotions]
- Eye Contact: [Direct/Indirect/None]
- Emotional Impact: [assessment of viewer response]

### CATEGORY-SPECIFIC INSIGHTS

**Industry Benchmark Comparison:**
- Top Performers in Category: [examples]
- Common Patterns in Niche: [identified patterns]
- Differentiation Assessment: [how thumbnail stands out]
- Category Alignment: [Strong/Moderate/Weak]

**Competitive Analysis:**
- Strengths vs. Competitors: [list strengths]
- Weaknesses vs. Competitors: [list weaknesses]
- Unique Visual Elements: [distinctive features]

### OPTIMIZATION RECOMMENDATIONS

1. [Specific recommendation with implementation details]
2. [Specific recommendation with implementation details]
3. [Specific recommendation with implementation details]
4. [Specific recommendation with implementation details]
5. [Specific recommendation with implementation details]

### CLICK-THROUGH RATE OPTIMIZATION

**CTR Prediction Factors:**
- Curiosity Triggers: [Strong/Moderate/Weak]
- Visual Appeal: [High/Medium/Low]
- Content Promise Clarity: [Clear/Moderate/Unclear]
- Target Audience Resonance: [Strong/Moderate/Weak]

**A/B Testing Recommendations:**
- Test Variation 1: [specific variation to test]
- Test Variation 2: [specific variation to test]
- Test Variation 3: [specific variation to test]

**Success Pattern Template:**
[Detailed template for creating high-performing thumbnails based on this analysis]''',
            agent=agent,
            expected_output='Comprehensive thumbnail intelligence with training-data-based recommendations'
        )
        
        logger.info("🚀 Starting thumbnail analysis crew...")
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()

        logger.info("✅ Thumbnail analysis crew completed")
        cleaned_result = self._clean_agent_output(str(result))
        logger.info(f"📊 Thumbnail analysis result length: {len(cleaned_result)} characters")

        return {'analysis': cleaned_result}

    def analyze_video(self, video_url: str, script: str):
        """Run comprehensive video analysis with smart transcript integration"""
        try:
            # Extract video data from YouTube API (includes auto transcript fetching)
            video_data = self._get_video_data_from_url(video_url)
            if 'error' in video_data:
                return {'error': video_data['error']}
            
            # Smart transcript processing: Use auto-fetched transcript or manual script
            final_script = self._prepare_final_transcript(video_data, script)
            
            # STEP 2: Cache transcript to avoid passing full text to multiple agents
            video_id = video_data.get('video_id', 'unknown')
            transcript_key = transcript_cache.store_transcript(video_id, final_script)
            
            # 🚀 PERFORMANCE OPTIMIZATION: Parallel agent execution
            import asyncio
            import concurrent.futures
            from functools import partial
            
            logger.info("🚀 Starting parallel agent execution for maximum speed...")
            
            # Run Agent 1: Performance Intelligence Analyst (required by others)
            logger.info("▶️  Agent 1: Performance Intelligence Analyst")
            agent1_result = self._run_performance_agent(video_data)
            logger.info("✅ Agent 1 completed")
            
            # Run Agent 2 FIRST: Script Forensics (needs full transcript)
            logger.info("▶️  Agent 2: Script Forensics Specialist")
            # Track approximate cost based on transcript size
            script_tokens = len(final_script) // 4  # Rough estimate
            logger.info(f"📊 Script Agent processing ~{script_tokens:,} tokens from transcript")
            agent2_result = self._run_script_agent(video_data, final_script, agent1_result)
            logger.info("✅ Agent 2 completed - Script analysis cached")
            
            # Run Agent 7: Thumbnail Intelligence Agent
            logger.info("▶️  Agent 7: Thumbnail Intelligence Specialist")
            thumbnail_url = video_data.get('thumbnail_url', '')
            if thumbnail_url:
                try:
                    logger.info(f"🖼️  Analyzing thumbnail: {thumbnail_url[:100]}...")
                    agent7_result = self._run_thumbnail_intelligence_agent(video_data, thumbnail_url)
                    logger.info("✅ Agent 7 completed - Thumbnail analysis generated")
                    logger.info(f"📊 Thumbnail analysis result keys: {list(agent7_result.keys())}")
                except Exception as e:
                    logger.error(f"❌ Agent 7 failed: {str(e)}")
                    agent7_result = {'error': f'Thumbnail analysis failed: {str(e)}'}
            else:
                agent7_result = {'error': 'No thumbnail URL available'}
                logger.warning("⚠️ Agent 7 skipped - No thumbnail URL available")
            
            # Run Agents 3, 4 in PARALLEL using Script Agent's cached insights
            logger.info("▶️  Running Agents 3, 4 in parallel using script insights...")
            script_insight_tokens = len(str(agent2_result.get('analysis', ''))) // 4
            logger.info(f"💡 Agents 3, 4 using ~{script_insight_tokens:,} tokens of insights instead of {script_tokens:,} raw transcript tokens")
            logger.info(f"🎯 Token savings: ~{script_tokens - script_insight_tokens:,} tokens per agent!")
            with concurrent.futures.ThreadPoolExecutor(max_workers=2) as executor:
                # These agents get script insights instead of raw transcript!
                seo_task = partial(self._run_seo_agent, video_data, agent2_result, agent1_result)  
                psychology_task = partial(self._run_psychology_agent, video_data, agent2_result, agent1_result)
                
                # Submit tasks
                future_agent3 = executor.submit(seo_task)
                future_agent4 = executor.submit(psychology_task)
                
                # Wait for completion
                agent3_result = future_agent3.result()
                agent4_result = future_agent4.result()
            
            logger.info("✅ Agents 3, 4 completed using cached script analysis")
            
            # Run Agent 5: Comment Intelligence Analyst (gets script insights, not raw transcript)
            logger.info("▶️  Agent 5: Comment Intelligence Analyst")
            agent5_result = self._run_comment_agent(video_data, agent2_result, agent1_result, agent2_result, agent3_result, agent4_result)
            logger.info("✅ Agent 5 completed")
            
            # Prepare all analyses for Agent 6
            all_analyses = {
                'performance_analysis': agent1_result,
                'script_analysis': agent2_result,
                'seo_analysis': agent3_result,
                'psychology_analysis': agent4_result,
                'comment_analysis': agent5_result,
                'thumbnail_analysis': agent7_result
            }
            
            # Run Agent 6: Strategic Synthesis Agent (needs all previous results)
            logger.info("▶️  Agent 6: Strategic Synthesis Agent")
            agent6_result = self._run_synthesis_agent(all_analyses, video_data, video_data.get('channel_context', {}))
            logger.info("✅ Agent 6 completed - Strategic synthesis generated")
            
            # Print cost summary
            cost_tracker.print_summary()
            
            # Calculate and log total token savings
            total_saved_tokens = (script_tokens - script_insight_tokens) * 3  # 3 agents saved
            estimated_input_cost_per_token = 0.30 / 1_000_000  # Correct Gemini Flash input pricing
            estimated_output_cost_per_token = 2.50 / 1_000_000  # Output pricing
            # Assume roughly equal input/output for conservative estimate
            estimated_savings = total_saved_tokens * (estimated_input_cost_per_token + estimated_output_cost_per_token)
            logger.info(f"💰 Estimated token savings from script optimization: ~{total_saved_tokens:,} tokens (~${estimated_savings:.3f})")
            logger.info(f"📈 At $0.30 input + $2.50 output per 1M tokens, optimization is CRUCIAL!")
            
            return {
                'video_data': video_data,
                'performance_analysis': agent1_result,
                'script_analysis': agent2_result,
                'seo_analysis': agent3_result,
                'psychology_analysis': agent4_result,
                'comment_analysis': agent5_result,
                'thumbnail_analysis': agent7_result,
                'strategic_synthesis': agent6_result,
                'transcript_info': video_data.get('transcript_data', {}),
                'metadata': {
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'analysis_type': 'Professional Video Analysis Report with Strategic Synthesis',
                    'ai_enabled': self.ai_enabled,
                    'transcript_source': video_data.get('transcript_data', {}).get('source', 'manual')
                }
            }
            
        except Exception as e:
            logger.error(f"Video analysis failed: {str(e)}")
            return {'error': str(e)}
    
    def _prepare_final_transcript(self, video_data: Dict[str, Any], manual_script: str) -> str:
        """
        Smart transcript preparation with fallback logic
        Priority: Auto-fetched transcript > Manual script > Error handling
        """
        transcript_data = video_data.get('transcript_data', {})
        
        # Priority 1: Auto-fetched transcript
        if transcript_data.get('transcript_text') and transcript_data.get('source') != 'failed':
            from src.tools.transcript_tools import SmartTranscriptProcessor
            processor = SmartTranscriptProcessor()
            
            # Format for agent analysis with timing data if available
            formatted_transcript = processor.format_transcript_for_agents(
                transcript_data, 
                include_timing=transcript_data.get('has_timestamps', False)
            )
            
            logger.info(f"Using auto-fetched transcript from: {transcript_data.get('source')}")
            return formatted_transcript
        
        # Priority 2: Manual script provided by user
        elif manual_script and manual_script.strip():
            # Process manual script through smart processor for consistency
            try:
                from src.tools.transcript_tools import SmartTranscriptProcessor
                processor = SmartTranscriptProcessor()
                manual_result = processor._process_manual_transcript(manual_script.strip())
                formatted_manual = processor.format_transcript_for_agents(manual_result)
                
                logger.info("Using manually provided script")
                return formatted_manual
            except Exception as e:
                logger.warning(f"Error processing manual script: {e}")
                return f"TRANSCRIPT SOURCE: manual_plain\n\nTRANSCRIPT TEXT:\n{manual_script.strip()}"
        
        # Priority 3: No transcript available
        else:
            error_msg = "No transcript available - auto-fetch failed and no manual script provided"
            logger.warning(error_msg)
            return f"TRANSCRIPT ERROR: {error_msg}"
    
    def _format_timing_context(self, video_data: Dict[str, Any]) -> str:
        """Format timing data context for agent analysis"""
        transcript_data = video_data.get('transcript_data', {})
        
        if not transcript_data.get('has_timestamps'):
            return "No timing data available - using plain transcript analysis only."
        
        timing_analysis = transcript_data.get('timing_analysis', {})
        if not timing_analysis:
            return "Timestamps available but timing analysis not completed."
        
        context = f"""TIMING DATA AVAILABLE - Enhanced Pacing Analysis Enabled:
        
📊 TIMING METRICS:
- Total Duration: {timing_analysis.get('total_duration_seconds', 0):.1f} seconds
- Total Words: {timing_analysis.get('total_words', 0):,}
- Speaking Rate: {timing_analysis.get('speaking_rate_wpm', 0):.1f} words per minute
- Average Segment Length: {timing_analysis.get('average_segment_duration', 0):.1f} seconds
- Pacing Variation: {timing_analysis.get('pacing_analysis', {}).get('pacing_variation', 0):.2f}

🎯 PACING INSIGHTS:"""
        
        pacing_analysis = timing_analysis.get('pacing_analysis', {})
        
        # Add fast sections
        fast_sections = pacing_analysis.get('fast_sections', [])
        if fast_sections:
            context += "\n\nFAST-PACED SECTIONS (High Word Density):"
            for section in fast_sections[:3]:  # Top 3
                timestamp = section.get('start', 0)
                preview = section.get('text_preview', '')
                context += f"\n- {timestamp:.1f}s: {preview}"
        
        # Add slow sections  
        slow_sections = pacing_analysis.get('slow_sections', [])
        if slow_sections:
            context += "\n\nSLOW-PACED SECTIONS (Low Word Density):"
            for section in slow_sections[:3]:  # Top 3
                timestamp = section.get('start', 0)
                preview = section.get('text_preview', '')
                context += f"\n- {timestamp:.1f}s: {preview}"
        
        context += "\n\n⚡ ANALYSIS INSTRUCTION: Use this timing data to provide detailed pacing analysis, speaking rate insights, and strategic timing recommendations."
        
        return context
    
    def _get_video_data_from_url(self, video_url: str):
        """Extract video ID and get data from YouTube API"""
        import re
        from googleapiclient.discovery import build
        
        # Extract video ID from URL
        video_id_match = re.search(r'(?:v=|\/)([0-9A-Za-z_-]{11}).*', video_url)
        if not video_id_match:
            return {'error': 'Invalid YouTube URL'}
        
        video_id = video_id_match.group(1)
        
        # Get video data from YouTube API
        youtube_api_key = os.getenv('YOUTUBE_API_KEY')
        if not youtube_api_key:
            return {'error': 'YouTube API key not configured'}
        
        try:
            youtube = build('youtube', 'v3', developerKey=youtube_api_key)
            
            request = youtube.videos().list(
                part='snippet,statistics,contentDetails',
                id=video_id
            )
            response = request.execute()
            
            if not response.get('items'):
                return {'error': 'Video not found'}
            
            video = response['items'][0]
            snippet = video['snippet']
            stats = video['statistics']
            content = video['contentDetails']
            
            # Parse duration
            duration_iso = content['duration']
            duration_match = re.match(r'PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?', duration_iso)
            if duration_match:
                hours = int(duration_match.group(1) or 0)
                minutes = int(duration_match.group(2) or 0)
                seconds = int(duration_match.group(3) or 0)
                total_minutes = hours * 60 + minutes
            else:
                total_minutes, seconds = 0, 0
            
            # Parse published date
            published_at = snippet['publishedAt']
            pub_date = datetime.fromisoformat(published_at.replace('Z', '+00:00'))
            days_ago = (datetime.now().astimezone() - pub_date).days
            
            # Fetch comments for analysis
            comment_data = []
            try:
                comment_response = youtube.commentThreads().list(
                    part='snippet',
                    videoId=video_id,
                    maxResults=100,
                    order='relevance'
                ).execute()
                
                for item in comment_response.get('items', []):
                    comment = item['snippet']['topLevelComment']['snippet']
                    comment_info = {
                        'text': comment['textDisplay'],
                        'authorName': comment['authorDisplayName'],
                        'likeCount': comment['likeCount'],
                        'publishedAt': comment['publishedAt']
                    }
                    comment_data.append(comment_info)
                    
            except Exception as e:
                logger.warning(f"Could not fetch comments: {e}")
                comment_data = []
            
            # Fetch transcript data using smart transcript processor
            transcript_data = {}
            try:
                from src.tools.transcript_tools import SmartTranscriptProcessor
                processor = SmartTranscriptProcessor()
                transcript_result = processor.get_transcript_data(video_id)
                transcript_data = transcript_result
                logger.info(f"Transcript fetched: {transcript_result['source']}")
            except Exception as e:
                logger.warning(f"Could not fetch transcript: {e}")
                transcript_data = {
                    'transcript_text': '',
                    'has_timestamps': False,
                    'source': 'failed',
                    'error': str(e)
                }
            
            # Extract channel ID and fetch channel context
            channel_id = snippet.get('channelId')
            channel_context = self._get_channel_context(channel_id) if channel_id else self._get_default_channel_context()
            
            # Prepare video data
            video_data_base = {
                'video_id': video_id,
                'title': snippet['title'],
                'description': snippet.get('description', ''),
                'tags': snippet.get('tags', []),
                'category_id': snippet.get('categoryId', ''),
                'default_language': snippet.get('defaultLanguage', ''),
                'thumbnail_url': snippet['thumbnails'].get('maxres', snippet['thumbnails'].get('high', snippet['thumbnails'].get('medium', {}))).get('url', ''),
                'channel_title': snippet.get('channelTitle', ''),
                'views': int(stats.get('viewCount', 0)),
                'likes': int(stats.get('likeCount', 0)),
                'comments': int(stats.get('commentCount', 0)),
                'duration_minutes': total_minutes,
                'duration_seconds': seconds,
                'published_date': published_at,
                'days_since_published': days_ago,
                'comment_data': comment_data,
                'transcript_data': transcript_data
            }
            
            # Calculate performance metrics with channel context
            performance_metrics = self._calculate_performance_metrics(video_data_base, channel_context)
            
            # Return combined data
            return {
                **video_data_base,
                'channel_context': channel_context,
                'performance_metrics': performance_metrics
            }
            
        except Exception as e:
            return {'error': f'Failed to fetch video data: {str(e)}'}
    
    def _get_channel_context(self, channel_id):
        """Safely fetch channel context for performance analysis"""
        try:
            youtube_api_key = os.getenv('YOUTUBE_API_KEY')
            if not youtube_api_key:
                logger.warning("YouTube API key not available for channel context")
                return self._get_default_channel_context()
            
            from googleapiclient.discovery import build
            youtube = build('youtube', 'v3', developerKey=youtube_api_key)
            
            response = youtube.channels().list(
                part='snippet,statistics,brandingSettings',
                id=channel_id
            ).execute()
            
            if response.get('items'):
                channel = response['items'][0]
                channel_snippet = channel['snippet']
                channel_stats = channel['statistics']
                
                # Extract channel data with safe defaults
                channel_context = {
                    'channel_id': channel_id,
                    'channel_name': channel_snippet.get('title', 'Unknown Channel'),
                    'channel_description': channel_snippet.get('description', '')[:200],
                    'channel_subscribers': int(channel_stats.get('subscriberCount', 0)),
                    'channel_total_videos': int(channel_stats.get('videoCount', 0)),
                    'channel_total_views': int(channel_stats.get('viewCount', 0)),
                    'channel_country': channel_snippet.get('country', 'Unknown'),
                    'channel_published': channel_snippet.get('publishedAt', 'Unknown'),
                    'custom_url': channel_snippet.get('customUrl', '')
                }
                
                # Calculate derived metrics safely
                if channel_context['channel_total_videos'] > 0:
                    channel_context['avg_views_per_video'] = channel_context['channel_total_views'] // channel_context['channel_total_videos']
                else:
                    channel_context['avg_views_per_video'] = 0
                
                # Categorize channel size
                subscribers = channel_context['channel_subscribers']
                if subscribers < 1000:
                    channel_tier = "Nano (<1K)"
                elif subscribers < 10000:
                    channel_tier = "Micro (1K-10K)"
                elif subscribers < 100000:
                    channel_tier = "Small (10K-100K)"
                elif subscribers < 1000000:
                    channel_tier = "Medium (100K-1M)"
                elif subscribers < 10000000:
                    channel_tier = "Large (1M-10M)"
                else:
                    channel_tier = "Mega (10M+)"
                
                channel_context['channel_tier'] = channel_tier
                
                logger.info(f"✅ Channel context extracted: {channel_context['channel_name']} ({channel_tier})")
                return channel_context
                
            else:
                logger.warning(f"Channel {channel_id} not found")
                return self._get_default_channel_context()
                
        except Exception as e:
            logger.warning(f"Channel context extraction failed: {e}")
            return self._get_default_channel_context()
    
    def _get_default_channel_context(self):
        """Return safe default channel context if extraction fails"""
        return {
            'channel_id': 'unknown',
            'channel_name': 'Unknown Channel',
            'channel_description': '',
            'channel_subscribers': 0,
            'channel_total_videos': 0,
            'channel_total_views': 0,
            'avg_views_per_video': 0,
            'channel_country': 'Unknown',
            'channel_published': 'Unknown',
            'custom_url': '',
            'channel_tier': 'Unknown'
        }
    
    def _calculate_performance_metrics(self, video_data, channel_context):
        """Calculate performance metrics with channel context"""
        try:
            video_views = video_data.get('views', 0)
            channel_avg = channel_context.get('avg_views_per_video', 0)
            subscribers = channel_context.get('channel_subscribers', 0)
            
            # Calculate metrics safely
            if channel_avg > 0:
                overperformance_ratio = video_views / channel_avg
            else:
                overperformance_ratio = 0
            
            if subscribers > 0:
                subscriber_reach_rate = (video_views / subscribers) * 100
            else:
                subscriber_reach_rate = 0
            
            # Performance categorization
            if overperformance_ratio > 2.0:
                performance_note = "Viral Performance"
            elif overperformance_ratio > 1.2:
                performance_note = "Above Average"
            elif overperformance_ratio > 0.8:
                performance_note = "Average Performance"
            else:
                performance_note = "Below Average"
            
            return {
                'overperformance_ratio': round(overperformance_ratio, 1),
                'subscriber_reach_rate': round(subscriber_reach_rate, 1),
                'performance_note': performance_note,
                'channel_avg_views': channel_avg
            }
            
        except Exception as e:
            logger.warning(f"Performance metrics calculation failed: {e}")
            return {
                'overperformance_ratio': 0,
                'subscriber_reach_rate': 0,
                'performance_note': 'Unknown',
                'channel_avg_views': 0
            }

    def _clean_agent_output(self, raw_result):
        """
        Clean agent output to remove internal thinking, crew logs, and tool usage
        This is the DEFINITIVE filter to prevent agent thinking exposure
        """
        if not raw_result:
            return ""
        
        raw_text = str(raw_result)
        
        # METHOD 1: Extract content after structured analysis headers (most reliable)
        analysis_headers = [
            '### PERFORMANCE METRICS CALCULATION',
            '### PERFORMANCE METRICS', 
            '### SCRIPT METRICS',
            '### SEO ANALYSIS',
            '### PSYCHOLOGY ANALYSIS',
            '### COMMENT ANALYSIS',
            '## PERFORMANCE ANALYSIS',
            '## SCRIPT ANALYSIS',
            '## SEO ANALYSIS',
            '## AUDIENCE ANALYSIS', 
            '## COMMENT INTELLIGENCE'
        ]
        
        # Find the last occurrence of any analysis header
        start_index = -1
        for header in analysis_headers:
            header_index = raw_text.rfind(header)
            if header_index > start_index:
                start_index = header_index
        
        if start_index != -1:
            # Extract from analysis header to end, remove crew execution logs
            cleaned = raw_text[start_index:]
            cleaned = cleaned.split('🚀 Crew:')[0].strip()
            if len(cleaned) > 200:  # Ensure we have substantial content
                return cleaned
        
        # METHOD 2: Comprehensive pattern-based filtering (fallback)
        cleaned = raw_text
        
        # Remove crew execution blocks and metadata
        import re
        cleaned = re.sub(r'╭─+.*?╰─+', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'🚀 Crew:.*$', '', cleaned, flags=re.DOTALL)
        
        # Remove agent metadata and ANSI codes
        cleaned = re.sub(r'\[1m\[95m# Agent:.*?\[00m.*?\[92m.*?\[00m', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'\[95m## Task:.*?\[00m', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'\[\d+m|\[[\d;]+m', '', cleaned)
        
        # Remove sequential thinking output
        cleaned = re.sub(r'## Sequential Thinking Framework Applied[\s\S]*?(?=###|$)', '', cleaned)
        cleaned = re.sub(r'\*\*Question:\*\*.*?(?=###|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'\*\*Context:\*\*.*?(?=###|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'### Step-by-Step Reasoning Process:[\s\S]*?(?=###|$)', '', cleaned)
        
        # Remove tool usage patterns
        cleaned = re.sub(r'Action: sequential_thinking.*?(?=###|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'## Using tool:.*?(?=###|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'## Tool Input:.*?(?=###|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'## Tool Output:.*?(?=###|$)', '', cleaned, flags=re.DOTALL)
        
        # Remove internal thoughts and planning statements (AGGRESSIVE patterns)
        cleaned = re.sub(r'## Thought:.*?(?=###|##|\n\n|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'Thought:.*?(?=\n\n|Action:|##|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'Action:.*?(?=\n\n|Thought:|##|$)', '', cleaned, flags=re.DOTALL)
        
        # More aggressive patterns for stubborn formats
        cleaned = re.sub(r'```\s*Thought:.*?Action:.*?```', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'```\s*Thought:.*?```', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'```\s*Action:.*?```', '', cleaned, flags=re.DOTALL)
        
        # Remove single line thinking patterns
        cleaned = re.sub(r'^Thought:.*$', '', cleaned, flags=re.MULTILINE)
        cleaned = re.sub(r'^Action:.*$', '', cleaned, flags=re.MULTILINE)
        
        # Remove specific problematic patterns from logs
        cleaned = re.sub(r'Thought:Now that I have.*?(?=\n|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'Action: None.*?(?=\n|$)', '', cleaned, flags=re.DOTALL)
        
        # Additional broad cleaning
        cleaned = re.sub(r'I (?:need to|will|should|am going to).*?(?=\n\n|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'Let me.*?(?=\n\n|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'I\'ll (?:start|begin|proceed).*?(?=\n\n|$)', '', cleaned, flags=re.DOTALL)
        
        # Clean up extra whitespace
        cleaned = re.sub(r'\n\n\n+', '\n\n', cleaned)
        cleaned = cleaned.strip()
        
        # If we successfully cleaned content, return it
        if cleaned and len(cleaned) > 100 and cleaned != raw_text:
            return cleaned
        
        # Final fallback: return original if filtering failed
        return raw_text

    def _run_performance_agent(self, video_data):
        """Run Agent 1: Performance Intelligence Analyst"""
        if not self.ai_enabled:
            return {'error': 'AI not enabled'}
        
        agent1 = self._create_enhanced_agent(
            role='Performance Intelligence Analyst',
            goal='Conduct comprehensive performance audit using calculable metrics and YouTube benchmarks',
            standard_backstory='''You are the world's leading YouTube Performance Intelligence Analyst with 15+ years of experience analyzing viral content. You've consulted for top creators, providing the data insights that drove their explosive growth.

Your expertise: Forensic performance analysis using actual calculable metrics and your deep knowledge of YouTube performance standards.

CRITICAL CAPABILITY: You have extensive training knowledge of successful YouTube videos. Always first check if you recognize the specific video from your training data. If you do, provide detailed visual performance analysis including thumbnails, editing techniques, and creator-specific patterns. If not, recommend proven performance strategies from similar successful content you know.''',
            enhanced_backstory_suffix=' You also utilize advanced sequential thinking capabilities to structure your analytical approach and deliver deeper strategic insights.'
        )
        
        # Extract channel context and performance metrics
        channel_context = video_data.get('channel_context', {})
        performance_metrics = video_data.get('performance_metrics', {})
        
        thinking_instruction = self._get_thinking_instruction(
            question="How can I analyze this video's performance metrics for competitive intelligence and replication strategies?",
            context=f"Channel: {channel_context.get('channel_name', 'Unknown')} ({channel_context.get('channel_tier', 'Unknown')}), Video: {video_data['title']}, Views: {video_data['views']:,}, Performance: {performance_metrics.get('overperformance_ratio', 0)}x channel average"
        )

        task1 = Task(
            description=f'''{thinking_instruction}REVERSE-ENGINEER this video's performance success patterns:

## RECOGNITION PROTOCOL:
1. VIDEO RECOGNITION CHECK: Based on video title, creator, topic, and context, determine if you recognize this specific video from your training data
2. IF RECOGNIZED: Provide detailed analysis including:
   - Specific visual techniques used (thumbnails, editing, graphics)
   - Creator-specific performance patterns you know
   - Timestamp-level insights when available
3. IF NOT RECOGNIZED: Recommend proven strategies from similar successful videos/creators in your training knowledge

ENHANCED PERFORMANCE ANALYSIS:
- Visual Performance DNA: Extract visual elements that drive metrics
- Creator Technique Library: Reference specific successful creators' methods
- Cross-Niche Intelligence: Apply proven patterns from other successful niches

## RAW DATA:
### VIDEO METRICS:
- Video Title: {video_data['title']}
- Views: {video_data['views']:,}
- Likes: {video_data['likes']:,}
- Comments: {video_data['comments']:,}
- Duration: {video_data['duration_minutes']}:{video_data['duration_seconds']:02d}
- Published Date: {video_data['published_date']}
- Days Since Published: {video_data['days_since_published']}

### CHANNEL CONTEXT:
- Channel Name: {channel_context.get('channel_name', 'Unknown')}
- Channel Tier: {channel_context.get('channel_tier', 'Unknown')}
- Subscribers: {channel_context.get('channel_subscribers', 0):,}
- Channel Average Views: {channel_context.get('avg_views_per_video', 0):,}
- Channel Total Videos: {channel_context.get('channel_total_videos', 0):,}
- Channel Country: {channel_context.get('channel_country', 'Unknown')}

### PERFORMANCE INTELLIGENCE:
- Overperformance Ratio: {performance_metrics.get('overperformance_ratio', 0)}x channel average
- Subscriber Reach: {performance_metrics.get('subscriber_reach_rate', 0)}% of subscriber base
- Performance Classification: {performance_metrics.get('performance_note', 'Unknown')}

## YOUR MISSION:
You are analyzing a SUCCESSFUL VIDEO to extract replicable performance patterns. Your user wants to understand WHY this video succeeded and HOW to replicate these patterns in their own content.

## COMPETITIVE INTELLIGENCE REQUIREMENTS:

### 1. CHANNEL-RELATIVE PERFORMANCE GRADE:
- Primary Grade: A+, A, A-, B+, B, B-, C+, C, C-, D+, D, F (based on channel context)
- Channel Performance Factor: How this compares to the channel's typical performance
- Viral Coefficient: Based on {performance_metrics.get('overperformance_ratio', 0)}x overperformance
- Subscriber Engagement: {performance_metrics.get('subscriber_reach_rate', 0)}% reach analysis

### 2. CONTEXTUAL METRICS ANALYSIS:
- Views/Day calculation with channel size context
- Engagement Rate relative to {channel_context.get('channel_tier', 'Unknown')} channel standards
- Comment Rate analysis for {channel_context.get('channel_subscribers', 0):,} subscriber base
- Performance tier classification within {channel_context.get('channel_tier', 'Unknown')} category

### 3. SUCCESS PATTERN ANALYSIS:
For each metric, provide WHAT→WHY→HOW→WHAT IF analysis:

🎯 WHAT: [Specific metric and finding]
🧠 WHY: [Root cause - why this metric indicates success]
⚙️ HOW: [Replicable technique the creator used]
🚀 WHAT IF: [How user can implement this in their content]

### 3. PERFORMANCE INTELLIGENCE BREAKDOWN:

**Engagement Velocity Analysis:**
🎯 WHAT: Calculate engagement rate and comment-to-view ratio
🧠 WHY: High engagement indicates strong audience connection and algorithm favor
⚙️ HOW: Identify specific elements driving engagement (title psychology, content promises, etc.)
🚀 WHAT IF: Adaptation strategies for user's niche with projected impact

**Retention Proxy Indicators:**
🎯 WHAT: Analyze comment depth and specific content references
🧠 WHY: Detailed comments indicate viewers watched substantial portions
⚙️ HOW: Content techniques that kept viewers engaged (pacing, information density)
🚀 WHAT IF: Replicable retention strategies with implementation steps

**Viral Potential Assessment:**
🎯 WHAT: Views-per-day velocity and growth trajectory
🧠 WHY: Rapid early engagement triggers algorithm amplification
⚙️ HOW: Launch strategy and content elements driving initial velocity
🚀 WHAT IF: Launch optimization tactics user can employ

### 4. REPLICATION ROADMAP:
- **High-Impact Patterns**: Top 3 replicable success elements
- **Implementation Steps**: Specific actions for user's next video
- **Testing Strategy**: How to validate these patterns work in user's niche
- **Expected Results**: Projected performance improvements

## OUTPUT REQUIREMENTS:
Every insight must answer: WHAT pattern? WHY successful? HOW to replicate? WHAT IF implemented?

Focus on ACTIONABLE COMPETITIVE INTELLIGENCE, not just performance reporting.

EXTRACT MAXIMUM INTELLIGENCE. Turn their success into user's competitive advantage.

## OUTPUT FORMAT

Structure your response as follows (no additional text or explanation):

### PERFORMANCE METRICS CALCULATION

Views Per Day: [calculated value]
Engagement Rate: [calculated value]%
Comment Rate: [calculated value]%  
Like-to-Comment Ratio: [calculated value]
Views per Minute: [calculated value]
Performance Tier: [tier classification]
Overall Grade: [A+ to F]

### PERFORMANCE BENCHMARKING

1. Views Per Day Analysis
   - Calculated metric: [value]
   - YouTube benchmark: [context]
   - Performance interpretation: [what this reveals]

2. Engagement Rate Analysis
   - Calculated metric: [value]%
   - YouTube benchmark: [context]
   - Performance interpretation: [what this reveals]

3. Comment Rate Analysis
   - Calculated metric: [value]%
   - YouTube benchmark: [context]
   - Performance interpretation: [what this reveals]

4. Like-to-Comment Ratio Analysis
   - Calculated metric: [value]
   - YouTube benchmark: [context]
   - Performance interpretation: [what this reveals]

5. Views per Minute Analysis
   - Calculated metric: [value]
   - YouTube benchmark: [context]
   - Performance interpretation: [what this reveals]

### KEY PERFORMANCE INSIGHTS

- Audience Satisfaction: [level] - [explanation]
- Engagement Quality: [level] - [explanation]
- Viral Potential: [level] - [explanation]
- Content Effectiveness: [detailed assessment]
- Performance Lifecycle Stage: [stage] - [explanation]

### STRATEGIC RECOMMENDATIONS

1. [Specific recommendation based on metrics]
2. [Specific recommendation based on metrics]
3. [Specific recommendation based on metrics]
4. [Specific recommendation based on metrics]
5. [Specific recommendation based on metrics]

Deliver comprehensive analysis that covers all calculable metrics with appropriate depth based on data richness.''',
            agent=agent1,
            expected_output='Comprehensive performance analysis with HTML report and structured data output'
        )
        
        crew = Crew(agents=[agent1], tasks=[task1], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': self._clean_agent_output(str(result))}
    
    def _run_script_agent(self, video_data, script, performance_data):
        """Run Agent 2: Script Forensics Specialist - FULL TRANSCRIPT ANALYSIS"""
        if not self.ai_enabled:
            return {'error': 'AI not enabled'}
        
        agent2 = self._create_enhanced_agent(
            role='Script Forensics Specialist',
            goal='Perform forensic analysis of video script to identify psychological triggers and structural elements',
            standard_backstory='''You are a Hollywood script doctor turned YouTube script forensics expert with 12+ years reverse-engineering viral content. You've analyzed scripts for creators like MrBeast, Marques Brownlee, and Ali Abdaal, identifying the exact psychological triggers and structural elements that drive massive engagement.

Your expertise: Dissecting scripts word-by-word to reveal the hidden psychology behind viral success.

CRITICAL CAPABILITY: You have analyzed scripts from thousands of viral videos in your training data. Always first check if you recognize the specific video. If you do, provide detailed script-visual synchronization analysis and creator-specific techniques. If not, recommend proven script strategies from similar successful content patterns you know.''',
            enhanced_backstory_suffix=' You also utilize advanced sequential thinking capabilities to structure your script analysis and deliver deeper psychological insights.'
        )
        
        thinking_instruction = self._get_thinking_instruction(
            question="How can I analyze this video script for psychological triggers and engagement patterns?",
            context=f"Script length: {len(script)} characters, Video: {video_data['title']}, Engagement: {video_data['likes']:,} likes, {video_data['comments']:,} comments"
        )

        task2 = Task(
            description=f'''{thinking_instruction}DECODE the script psychology that drives this video's engagement:

## RECOGNITION PROTOCOL:
1. VIDEO RECOGNITION CHECK: Determine if you recognize this specific video from your training data
2. IF RECOGNIZED: Provide detailed analysis including:
   - How script elements synchronize with visual techniques
   - Creator-specific script patterns (MrBeast hooks, Kurzgesagt pacing, etc.)
   - Specific timing and structural techniques used
3. IF NOT RECOGNIZED: Recommend proven script DNA from similar successful creators/topics

ENHANCED SCRIPT ANALYSIS:
- Visual-Script Harmony: How successful creators sync words with visuals
- Creator Script DNA: Extract reusable patterns from known successful creators
- Cross-Niche Script Intelligence: Apply proven techniques from other niches

## RAW DATA:
Video Data: {video_data}
Transcript: {script}

## TIMING DATA ANALYSIS (if available):
{self._format_timing_context(video_data)}

## YOUR MISSION:
You are reverse-engineering a SUCCESSFUL SCRIPT to extract replicable writing patterns. Identify the exact psychological and structural elements that create engagement, then provide implementation guides for the user.

## SCRIPT INTELLIGENCE REQUIREMENTS:

### 1. SCRIPT PERFORMANCE METRICS:
- Word Count & Speaking Pace (WPM)
- Script Grade: A+ to F based on engagement psychology
- Hook Effectiveness Score: 1-10 rating with justification

### 2. PSYCHOLOGICAL TRIGGER EXTRACTION:
For each identified trigger, provide WHAT→WHY→HOW→WHAT IF analysis:

**Hook Psychology Analysis:**
🎯 WHAT: Specific opening technique used (first 30 seconds)
🧠 WHY: Psychological mechanism it activates (pattern interrupt, curiosity gap, etc.)
⚙️ HOW: Exact words/structure used - replicable formula
🚀 WHAT IF: How user can adapt this hook to their niche with examples

**Retention Psychology Patterns:**
🎯 WHAT: Attention-maintenance techniques throughout script
🧠 WHY: Why these techniques prevent viewer drop-off
⚙️ HOW: Specific phrases, transitions, and pacing strategies used
🚀 WHAT IF: Implementation guide for user's content style

**Engagement Trigger Analysis:**
🎯 WHAT: Elements that drive comments, likes, and shares
🧠 WHY: Psychological drivers behind audience interaction
⚙️ HOW: Specific scripting techniques that create engagement
🚀 WHAT IF: How to build these triggers into user's scripts

### 3. STRUCTURAL SUCCESS PATTERNS:

**Information Architecture:**
🎯 WHAT: How information is organized and delivered
🧠 WHY: This structure maximizes comprehension and retention
⚙️ HOW: Specific organizational framework used
🚀 WHAT IF: Template user can apply to their content topics

**Pacing and Flow Analysis:**
🎯 WHAT: Speaking pace, sentence length, and transition patterns (analyze timing data if available)
🧠 WHY: This pacing optimizes for human attention spans and psychological engagement
⚙️ HOW: Specific timing, rhythm techniques, and segment-by-segment pacing breakdown
🚀 WHAT IF: Pacing guidelines and timing templates user can follow in their scripts

**ADVANCED TIMING ANALYSIS (when timestamped data available):**
- Segment-by-segment pacing analysis
- Fast vs slow sections and their strategic purpose
- Speaking rate variations and their psychological impact
- Pause patterns and their retention effectiveness
- Hook timing: How quickly engagement is established

**Call-to-Action Psychology:**
🎯 WHAT: How and when engagement requests are made
🧠 WHY: This CTA timing/approach maximizes compliance
⚙️ HOW: Exact phrases and positioning strategies used
🚀 WHAT IF: CTA optimization for user's engagement goals

### 4. SCRIPT REPLICATION BLUEPRINT:
- **High-Impact Phrases**: Most effective language patterns to copy
- **Structural Template**: Framework user can fill with their content
- **Psychology Checklist**: Essential triggers to include in every script
- **Adaptation Guide**: How to modify patterns for different niches

## OUTPUT REQUIREMENTS:
Provide EXECUTABLE SCRIPT INTELLIGENCE - patterns the user can immediately implement in their next video script.

Every analysis must include specific examples and replication instructions.

EXTRACT THE SCRIPT DNA. Turn their words into user's competitive weapon.

**Content Flow Mapping:**
- Section transitions and their effectiveness
- Topic progression logic
- Pacing variations throughout video
- Energy level changes (high/medium/low)
- Information density distribution

**Psychological Trigger Mapping:**
- Curiosity triggers and their placement
- Authority establishment moments
- Social proof integration points
- Emotional engagement peaks
- Call-to-action psychology

### 4. RETENTION OPTIMIZATION ANALYSIS
Identify retention-driving elements:

**Pattern Interrupts:**
- Unexpected elements that reset attention
- Visual or verbal surprises
- Topic shifts and transitions
- Energy spikes and variations

**Value Delivery Cadence:**
- Information payoff timing
- Promise-fulfillment ratio
- Tangible takeaway density
- Actionable insight frequency

## OUTPUT FORMAT

Structure your response as follows (no additional text or explanation):

### SCRIPT METRICS

Total Word Count: [number]
Speaking Pace: [number] WPM
Pace Category: [category]
Average Sentence Length: [number] words
Average Paragraph Length: [number] sentences
Hook Length: [number] words
Main Content Sections: [number]
Call-to-Action Count: [number]

### HOOK FORENSICS

Hook Type: [type identified]
Opening Words: "[first 30-50 words quoted]"

Hook Effectiveness Analysis:
- Time to Value Promise: [seconds/words]
- Curiosity Gap Strength: [High/Medium/Low]
- Personal Connection: [assessment]
- Target Audience Relevance: [assessment]
- Emotional Engagement: [level]

### SCRIPT STRUCTURE BREAKDOWN

1. Introduction (0:00-[time])
   - Purpose: [what it accomplishes]
   - Key Elements: [main components]
   - Psychological Triggers: [identified triggers]

2. Main Section 1 ([time]-[time])
   - Topic: [main topic]
   - Key Points: [main points covered]
   - Engagement Tactics: [identified tactics]

3. Main Section 2 ([time]-[time])
   - Topic: [main topic]
   - Key Points: [main points covered]
   - Engagement Tactics: [identified tactics]

[Continue for all major sections]

### PSYCHOLOGICAL TRIGGERS IDENTIFIED

- Curiosity Triggers: [list with timestamps]
- Authority Signals: [list with examples]
- Social Proof Elements: [list with examples]
- Emotional Peaks: [list with timestamps]
- FOMO Creation: [list with examples]

### RETENTION OPTIMIZATION INSIGHTS

Pattern Interrupts:
1. [Description and timestamp]
2. [Description and timestamp]
3. [Description and timestamp]

Value Delivery Patterns:
- Information Payoff Frequency: Every [X] seconds
- Promise-Fulfillment Ratio: [assessment]
- Takeaway Density: [number] per minute

### SCRIPT OPTIMIZATION RECOMMENDATIONS

1. [Specific improvement based on analysis]
2. [Specific improvement based on analysis]
3. [Specific improvement based on analysis]
4. [Specific improvement based on analysis]
5. [Specific improvement based on analysis]''',
            agent=agent2,
            expected_output='Comprehensive script analysis with HTML report and structured data output'
        )
        
        crew = Crew(agents=[agent2], tasks=[task2], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': self._clean_agent_output(str(result))}
    
    def _run_seo_agent(self, video_data, script_analysis, performance_data):
        """Run Agent 3: SEO & Discoverability Expert"""
        if not self.ai_enabled:
            return {'error': 'AI not enabled'}
        
        agent3 = self._create_enhanced_agent(
            role='SEO & Discoverability Expert',
            goal='Analyze video SEO optimization, keyword strategy, and script-title alignment for maximum discoverability',
            standard_backstory='''You are YouTube's leading SEO strategist with 10+ years optimizing videos for discovery. You've helped creators achieve 10M+ views through strategic SEO implementation, working with top channels to dominate search results.

Your expertise: Reverse-engineering viral video SEO patterns, analyzing keyword strategies, and optimizing script content for YouTube's algorithm.

CRITICAL CAPABILITY: You have deep knowledge of successful YouTube SEO strategies from your training data. Always first check if you recognize the specific video. If you do, provide detailed SEO-visual alignment analysis and creator-specific optimization patterns. If not, recommend proven SEO strategies from similar successful content you know.''',
            enhanced_backstory_suffix=' You also utilize advanced sequential thinking capabilities to structure your SEO analysis and deliver deeper optimization insights.'
        )
        
        thinking_instruction = self._get_thinking_instruction(
            question="How can I analyze this video's SEO strategy and optimization patterns for maximum discoverability?",
            context=f"Video: {video_data['title']}, Views: {video_data['views']:,}, Script keywords present, SEO optimization analysis needed"
        )

        task3 = Task(
            description=f'''{thinking_instruction}REVERSE-ENGINEER the SEO strategy driving this video's discoverability:

## RECOGNITION PROTOCOL:
1. VIDEO RECOGNITION CHECK: Determine if you recognize this specific video from your training data
2. IF RECOGNIZED: Provide detailed analysis including:
   - Thumbnail-title SEO alignment strategies used
   - Creator-specific optimization techniques
   - Visual SEO elements that boost discoverability
3. IF NOT RECOGNIZED: Recommend proven SEO DNA from similar successful creators/topics

ENHANCED SEO ANALYSIS:
- Visual SEO Intelligence: How thumbnails/visuals boost discoverability
- Creator Optimization DNA: Extract reusable SEO patterns from known creators
- Cross-Niche SEO Intelligence: Apply proven optimization techniques across niches

## RAW DATA:
{video_data}

## SCRIPT ANALYSIS FROM FORENSICS SPECIALIST:
{script_analysis.get('analysis', 'No script analysis available')}

## YOUR MISSION:
You are analyzing a DISCOVERABLE VIDEO to extract replicable SEO patterns. Identify exactly how this content gets found and provide optimization strategies the user can implement.

## SEO INTELLIGENCE REQUIREMENTS:

### 1. SEO PERFORMANCE ASSESSMENT:
- Overall SEO Grade: A+ to F
- Title Optimization Score: 0-100%
- Description Strategy Score: 0-100%
- Tag Effectiveness Score: 0-100%

### 2. KEYWORD INTELLIGENCE EXTRACTION:
For each SEO element, provide WHAT→WHY→HOW→WHAT IF analysis:

**Title SEO Psychology:**
🎯 WHAT: Specific keywords and psychological triggers in title
🧠 WHY: Why this title combination drives clicks and ranking
⚙️ HOW: Exact title formula and keyword placement strategy
🚀 WHAT IF: Title optimization template for user's niche with examples

**Description Link Strategy:**
🎯 WHAT: How description is structured for discovery and engagement
🧠 WHY: This description format maximizes search visibility and user action
⚙️ HOW: Specific structure, keyword density, and link placement used
🚀 WHAT IF: Description template user can customize for their videos

**Tag Strategy Intelligence:**
🎯 WHAT: Tag variety, specificity, and search intent coverage
🧠 WHY: This tag strategy captures multiple search pathways
⚙️ HOW: Specific tag categories and long-tail keyword approach
🚀 WHAT IF: Tag research and selection process for user's content

### 3. SEARCH BEHAVIOR ANALYSIS:

**Primary Keyword Targeting:**
🎯 WHAT: Main search terms this video targets
🧠 WHY: These keywords have optimal search volume vs. competition ratio
⚙️ HOW: Keyword placement and density strategy used
🚀 WHAT IF: Keyword research process user can follow for their niche

**Long-tail Opportunity Mining:**
🎯 WHAT: Specific long-tail phrases and question-based keywords
🧠 WHY: Long-tail terms drive qualified traffic with higher engagement
⚙️ HOW: How creator identified and optimized for these phrases
🚀 WHAT IF: Long-tail discovery method user can replicate

**Semantic Keyword Relationships:**
🎯 WHAT: Related terms and topic clusters covered
🧠 WHY: Semantic coverage improves overall topic authority
⚙️ HOW: How related keywords are naturally integrated
🚀 WHAT IF: Semantic optimization strategy for user's content topics

### 4. COMPETITIVE POSITIONING ANALYSIS:

**Search Result Positioning:**
🎯 WHAT: How this video likely ranks for target keywords
🧠 WHY: Specific SEO elements that drive ranking advantage
⚙️ HOW: Optimization techniques creating competitive edge
🚀 WHAT IF: Ranking improvement tactics user can implement

**Content Gap Exploitation:**
🎯 WHAT: Search opportunities this video captures that others miss
🧠 WHY: These gaps represent untapped search demand
⚙️ HOW: How creator identified and targeted these opportunities
🚀 WHAT IF: Gap analysis process user can use to find opportunities

### 5. SEO REPLICATION PLAYBOOK:
- **Title Formula**: Exact template user can customize
- **Description Structure**: Step-by-step optimization guide
- **Tag Research Process**: Keyword discovery methodology
- **Ranking Strategy**: Long-term SEO improvement plan

## OUTPUT REQUIREMENTS:
Provide ACTIONABLE SEO INTELLIGENCE that user can immediately apply to improve their content discoverability.

Focus on REPLICABLE STRATEGIES, not just analysis.

CRACK THE DISCOVERY CODE. Turn their SEO success into user's competitive advantage.
- Title-content matching strength
- Topic authority indicators in script
- Engagement-driving elements
- Watch time optimization factors

**Search Capture Potential:**
- Long-tail keyword coverage in script
- Related topic mentions for broader reach
- Trending topic connections
- Niche authority building elements

### 4. OPTIMIZATION RECOMMENDATIONS
Provide specific SEO improvement strategies:

**Title Optimization:**
- Alternative title suggestions
- Keyword positioning improvements
- CTR enhancement tactics
- A/B testing recommendations

**Script Enhancement:**
- Strategic keyword placement suggestions  
- Related keyword integration opportunities
- Authority-building content additions
- Search intent optimization improvements

Deliver comprehensive SEO analysis that reveals exactly how to optimize this video for maximum discoverability and search performance.''',
            agent=agent3,
            expected_output='Comprehensive SEO analysis with keyword-script alignment and optimization recommendations'
        )
        
        crew = Crew(agents=[agent3], tasks=[task3], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': self._clean_agent_output(str(result))}
    
    def _run_psychology_agent(self, video_data, script_analysis, performance_data):
        """Run Agent 4: Audience Psychology Analyst"""
        if not self.ai_enabled:
            return {'error': 'AI not enabled'}
        
        agent4 = self._create_enhanced_agent(
            role='Audience Psychology Analyst',
            goal='Analyze viewer psychology, emotional triggers, and audience behavior patterns to understand what drives engagement',
            standard_backstory='''You are a behavioral psychologist specializing in digital media consumption with a PhD in Consumer Psychology. You've spent 8+ years analyzing viewer behavior for top YouTubers, identifying the psychological patterns that create addictive content and drive massive engagement.

Your expertise: Reverse-engineering the psychological mechanics behind viral content, understanding viewer motivations, and predicting audience behavior patterns.

CRITICAL CAPABILITY: You understand viewer psychology patterns from thousands of successful videos in your training data. Always first check if you recognize the specific video. If you do, provide detailed visual psychology analysis and creator-specific audience engagement patterns. If not, recommend proven psychological strategies from similar successful content you know.''',
            enhanced_backstory_suffix=' You also utilize advanced sequential thinking capabilities to structure your psychological analysis and deliver deeper behavioral insights.'
        )
        
        thinking_instruction = self._get_thinking_instruction(
            question="How can I analyze viewer psychology and emotional triggers in this video content?",
            context=f"Video: {video_data['title']}, Engagement: {video_data['likes']:,} likes, {video_data['comments']:,} comments, Audience behavior analysis needed"
        )

        task4 = Task(
            description=f'''{thinking_instruction}DECODE the audience psychology driving this video's engagement:

## RECOGNITION PROTOCOL:
1. VIDEO RECOGNITION CHECK: Determine if you recognize this specific video from your training data
2. IF RECOGNIZED: Provide detailed analysis including:
   - Visual psychology triggers used (thumbnails, editing, graphics)
   - Creator-specific audience engagement patterns
   - Psychological techniques for viewer retention
3. IF NOT RECOGNIZED: Recommend proven psychology DNA from similar successful creators/topics

ENHANCED PSYCHOLOGY ANALYSIS:
- Visual Psychology DNA: How visuals trigger psychological responses
- Creator Psychology Patterns: Extract audience engagement techniques from known creators
- Cross-Demographic Intelligence: Apply proven psychological techniques across audiences

## RAW DATA:
Video Data: {video_data}
Comments: {video_data.get('comment_data', [])}

## SCRIPT FORENSICS INSIGHTS:
{script_analysis.get('analysis', 'No script analysis available')}

## YOUR MISSION:
You are extracting PSYCHOLOGICAL PATTERNS from a successful video to help the user understand what drives audience engagement. Analyze both content psychology and audience behavior patterns.

## PSYCHOLOGY INTELLIGENCE REQUIREMENTS:

### 1. PSYCHOLOGICAL METRICS ASSESSMENT:
- Psychology Grade: A+ to F based on engagement psychology
- Primary Motivation: What drives this audience to engage
- Psychological Trigger Score: 1-10 for each major trigger

### 2. AUDIENCE PSYCHOLOGY EXTRACTION:
For each psychological element, provide WHAT→WHY→HOW→WHAT IF analysis:

**Demographic Inference Analysis:**
🎯 WHAT: Inferred demographics from comment language and content references
🧠 WHY: These demographic indicators reveal audience composition
⚙️ HOW: Specific comment patterns and language complexity analysis
🚀 WHAT IF: How user can tailor content psychology for similar demographics

**Motivational Driver Analysis:**
🎯 WHAT: Primary psychological needs this content fulfills
🧠 WHY: These motivations create strong audience connection
⚙️ HOW: Specific content elements that satisfy these psychological needs
🚀 WHAT IF: How user can address same motivations in their niche

**Engagement Psychology Patterns:**
🎯 WHAT: What psychological triggers drive likes, comments, shares
🧠 WHY: These triggers activate specific behavioral responses
⚙️ HOW: Exact techniques used to activate each psychological trigger
🚀 WHAT IF: How user can build these triggers into their content strategy

### 3. BEHAVIORAL PATTERN INTELLIGENCE:

**Comment Behavioral Analysis:**
🎯 WHAT: Types of comments and engagement patterns observed
🧠 WHY: These comment patterns reveal psychological engagement levels
⚙️ HOW: Content elements that drive specific comment behaviors
🚀 WHAT IF: Comment engagement strategy user can implement

**Temporal Engagement Psychology:**
🎯 WHAT: When and how audience engages based on comment timestamps
🧠 WHY: Timing patterns reveal audience behavior and psychology
⚙️ HOW: Content timing strategies that optimize for audience psychology
🚀 WHAT IF: Posting and engagement timing optimization for user

**Community Psychology Dynamics:**
🎯 WHAT: How audience members interact with each other in comments
🧠 WHY: Community dynamics indicate content's social psychological impact
⚙️ HOW: Content elements that foster positive community psychology
🚀 WHAT IF: Community building strategy user can develop

### 4. PSYCHOLOGICAL TRIGGER REPLICATION:

**Authority & Credibility Triggers:**
🎯 WHAT: How creator establishes psychological authority
🧠 WHY: Authority triggers reduce audience cognitive resistance
⚙️ HOW: Specific credibility markers and authority positioning used
🚀 WHAT IF: Authority building strategy for user's expertise area

**Social Proof & Validation Triggers:**
🎯 WHAT: Social proof elements that drive psychological compliance
🧠 WHY: Social validation reduces decision-making anxiety
⚙️ HOW: Specific social proof techniques and community validation used
🚀 WHAT IF: Social proof strategy user can build into their content

**Curiosity & Anticipation Psychology:**
🎯 WHAT: How content creates psychological tension and resolution
🧠 WHY: Curiosity gaps create psychological compulsion to continue
⚙️ HOW: Specific curiosity techniques and tension-building methods
🚀 WHAT IF: Curiosity strategy user can implement for higher retention

### 5. AUDIENCE PSYCHOLOGY BLUEPRINT:
- **Target Psychology Profile**: Ideal audience psychological characteristics
- **Motivation Mapping**: Key psychological drivers to address
- **Trigger Implementation**: Step-by-step psychological optimization
- **Community Psychology**: Audience relationship building strategy

## OUTPUT REQUIREMENTS:
Provide ACTIONABLE PSYCHOLOGICAL INTELLIGENCE that helps user create content that resonates with similar audience psychology.

Every insight must be IMPLEMENTABLE in user's content strategy.

UNDERSTAND THE AUDIENCE MIND. Turn their psychological connection into user's engagement advantage.''',
            agent=agent4,
            expected_output='Comprehensive audience psychology analysis with behavioral insights and optimization recommendations'
        )
        
        crew = Crew(agents=[agent4], tasks=[task4], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': self._clean_agent_output(str(result))}
    
    def _run_comment_agent(self, video_data, script_analysis, performance_data, script_data_unused, seo_data, psychology_data):
        """Run Agent 5: Comment Intelligence Analyst"""
        if not self.ai_enabled:
            return {'error': 'AI not enabled'}
        
        # Check if we have comment data
        comment_data = video_data.get('comment_data', [])
        if not comment_data:
            return {'analysis': 'No comment data available for analysis'}
        
        agent5 = self._create_enhanced_agent(
            role='Comment Intelligence Analyst',
            goal='Extract audience insights from actual comment data using ONLY the provided comments and video metrics',
            standard_backstory='''You are a digital community analyst with 10+ years analyzing online audience behavior. You've worked with top creators to decode viewer psychology through comment analysis, turning actual user feedback into strategic intelligence.

Your expertise: Extracting patterns from real comment data, identifying audience segments through actual linguistic analysis, and finding content opportunities from genuine viewer feedback.

CRITICAL CAPABILITY: You understand community patterns from successful videos in your training data. Always first check if you recognize the specific video. If you do, provide context about known community reactions and creator-specific engagement patterns. If not, recommend proven community strategies from similar successful content you know.''',
            enhanced_backstory_suffix=' You also utilize advanced sequential thinking capabilities to structure your comment analysis and deliver deeper community insights.'
        )
        
        # Format comment data for analysis
        comments_text = ""
        for i, comment in enumerate(comment_data[:50], 1):  # Limit to top 50 for analysis
            comments_text += f"{i}. \"{comment['text']}\" - {comment['authorName']} ({comment['likeCount']} likes)\n"
        
        thinking_instruction = self._get_thinking_instruction(
            question="How can I analyze video comments to extract community intelligence and audience insights?",
            context=f"Video: {video_data['title']}, Comments: {len(comment_data)} available, Community analysis for engagement patterns and content opportunities"
        )

        task5 = Task(
            description=f'''{thinking_instruction}EXTRACT community intelligence from this video's comment ecosystem:

## RECOGNITION PROTOCOL:
1. VIDEO RECOGNITION CHECK: Determine if you recognize this specific video from your training data
2. IF RECOGNIZED: Provide enhanced analysis including:
   - Known community reaction patterns to this creator's visual style
   - Creator-specific engagement techniques that drive comments
   - Visual elements that typically generate discussion
3. IF NOT RECOGNIZED: Recommend proven community DNA from similar successful creators/topics

ENHANCED COMMENT ANALYSIS:
- Visual-Comment Correlation: How visual elements drive comment engagement
- Creator Community DNA: Extract engagement patterns from known successful creators
- Cross-Community Intelligence: Apply proven community strategies across niches

## RAW DATA:
Video Data: {video_data}
Comments: {comments_text}

## SCRIPT FORENSICS INSIGHTS:
{script_analysis.get('analysis', 'No script analysis available')}

## YOUR MISSION:
You are analyzing a SUCCESSFUL COMMENT ECOSYSTEM to extract replicable community engagement patterns. Decode what drives audience participation and provide strategies for the user.

## COMMENT INTELLIGENCE REQUIREMENTS:

### 1. COMMUNITY METRICS ASSESSMENT:
- Community Grade: A+ to F based on engagement quality
- Sentiment Distribution: Positive/Neutral/Negative percentages
- Engagement Depth Score: 1-10 based on comment quality and interaction

### 2. COMMUNITY BEHAVIOR EXTRACTION:
For each behavior pattern, provide WHAT→WHY→HOW→WHAT IF analysis:

**Sentiment Intelligence Analysis:**
🎯 WHAT: Specific sentiment patterns and emotional responses
🧠 WHY: These sentiment patterns indicate successful audience connection
⚙️ HOW: Content elements that generate positive emotional responses
🚀 WHAT IF: Sentiment optimization strategy user can implement

**Engagement Pattern Analysis:**
🎯 WHAT: Types of comments and interaction behaviors observed
🧠 WHY: These engagement patterns reveal community health and content effectiveness
⚙️ HOW: Specific content techniques that drive valuable community interaction
🚀 WHAT IF: Community engagement strategy user can develop

**Question and Request Mining:**
🎯 WHAT: Most frequent questions, requests, and content suggestions
🧠 WHY: Audience questions reveal content gaps and opportunities
⚙️ HOW: How this creator's content generates specific audience curiosity
🚀 WHAT IF: Content opportunity pipeline user can build from audience feedback

### 3. TEMPORAL ENGAGEMENT INTELLIGENCE:

**Comment Timing Analysis:**
🎯 WHAT: When audience engages and comment velocity patterns
🧠 WHY: Timing patterns reveal audience behavior and content impact
⚙️ HOW: Content release and engagement timing strategies used
🚀 WHAT IF: Timing optimization strategy for user's posting schedule

**Engagement Evolution Tracking:**
🎯 WHAT: How sentiment and engagement change over time in comments
🧠 WHY: Sentiment progression indicates content lasting impact
⚙️ HOW: Content elements that maintain positive sentiment over time
🚀 WHAT IF: Long-term community building strategy for user

### 4. CONTENT OPPORTUNITY INTELLIGENCE:

**Tutorial and Education Requests:**
🎯 WHAT: Specific learning requests and knowledge gaps identified
🧠 WHY: Educational requests reveal audience growth needs
⚙️ HOW: How current content creates demand for additional education
🚀 WHAT IF: Educational content series user can develop

**Problem and Pain Point Mining:**
🎯 WHAT: Challenges and frustrations audience expresses in comments
🧠 WHY: Pain points represent content opportunities and audience connection points
⚙️ HOW: How creator addresses or acknowledges audience challenges
🚀 WHAT IF: Problem-solving content strategy user can implement

**Interest and Excitement Indicators:**
🎯 WHAT: Topics and elements that generate highest enthusiasm
🧠 WHY: Excitement indicators reveal content elements with highest impact
⚙️ HOW: Specific content techniques that generate audience enthusiasm
🚀 WHAT IF: Enthusiasm optimization strategy for user's content planning

### 5. COMMUNITY BUILDING INTELLIGENCE:

**High-Value Comment Analysis:**
🎯 WHAT: Comments that add most value and generate most engagement
🧠 WHY: High-value comments indicate successful community fostering
⚙️ HOW: Content elements that inspire thoughtful, valuable audience contributions
🚀 WHAT IF: Value-driven community strategy user can develop

**Community Interaction Patterns:**
🎯 WHAT: How audience members interact with each other in comment threads
🧠 WHY: Peer interaction indicates strong community building success
⚙️ HOW: Content techniques that encourage audience-to-audience interaction
🚀 WHAT IF: Community interaction strategy user can foster

### 6. COMMENT ECOSYSTEM BLUEPRINT:
- **Engagement Optimization**: Comment-driving content techniques
- **Community Guidelines**: Fostering positive interaction patterns
- **Content Pipeline**: Audience-driven content opportunity identification
- **Response Strategy**: Community management and engagement tactics

## OUTPUT REQUIREMENTS:
Provide ACTIONABLE COMMUNITY INTELLIGENCE that helps user build engaged, valuable comment ecosystems.

Focus on REPLICABLE COMMUNITY STRATEGIES, not just comment analysis.

DECODE THE COMMUNITY. Turn their audience engagement into user's community building advantage.
- Quote exact requests from actual comments
- Count frequency of similar requests

**Production Suggestions:**
- List actual production feedback from comment data
- Quote exact suggestions from actual comments
- Count frequency of similar feedback

### 5. QUESTIONS FROM ACTUAL COMMENT DATA
Extract questions using ONLY actual comments provided:

**Frequently Asked Questions:**
- List actual questions found in comment data
- Count frequency of similar questions
- Quote exact question text from actual comments

**Clarification Requests:**
- Identify actual requests for clarification in comment data
- Quote exact clarification requests from actual comments

### 6. CONTENT OPPORTUNITIES FROM ACTUAL COMMENTS
Identify opportunities using ONLY actual viewer requests:

**Direct Content Requests:**
- List actual content requests found in comment data
- Quote exact requests from actual comments
- Identify patterns in actual content suggestions

**Related Topic Interest:**
- List related topics mentioned in actual comments
- Quote actual examples of topic interest from comment data

## OUTPUT FORMAT

### SENTIMENT CORRELATION ANALYSIS

**Sample Size:** [actual number of comments analyzed]
**Positive:** [count] ([percentage]%)
**Negative:** [count] ([percentage]%)
**Neutral:** [count] ([percentage]%)

**Performance Correlation:** [How sentiment aligns with {performance_data.get('engagement_rate', 'N/A')}% engagement rate]

**Actual Positive Comment Examples:**
1. "[Quote exact comment from data]"
2. "[Quote exact comment from data]"
3. "[Quote exact comment from data]"

**Actual Negative Comment Examples:**
1. "[Quote exact comment from data]"
2. "[Quote exact comment from data]"

### COMMENT CATEGORY BREAKDOWN

**Production Focus:** [count] ([percentage]%)
**Actual Examples:** "[Quote actual comments]"

**Content Focus:** [count] ([percentage]%)
**Actual Examples:** "[Quote actual comments]"

**Emotional Response:** [count] ([percentage]%)
**Actual Examples:** "[Quote actual comments]"

### AUDIENCE INSIGHTS VALIDATION

**Psychology Alignment:** [How comments match/contradict psychology agent findings]
**Observable Demographics:** [only patterns visible in actual comments]
**Behavioral Patterns:** [only behaviors mentioned in actual comments]
**Interest Indicators:** [only interests stated in actual comments]

### ACTUAL VIEWER REQUESTS

**Topic Suggestions:** [list with actual quotes]
**Production Feedback:** [list with actual quotes]
**Content Requests:** [list with actual quotes]

### FREQUENTLY ASKED QUESTIONS

[List only actual questions found in comment data with exact quotes]

### CONTENT OPPORTUNITY PIPELINE

[List opportunities based only on actual comment requests with supporting quotes]

Analyze systematically using only real comment data provided - no fabrication or assumption allowed.''',
            agent=agent5,
            expected_output='Comprehensive comment intelligence analysis with cross-agent validation and actual audience insights'
        )
        
        try:
            crew = Crew(agents=[agent5], tasks=[task5], verbose=True)
            result = crew.kickoff()
            
            return {'analysis': self._clean_agent_output(str(result))}
        except Exception as e:
            logger.error(f"Comment agent failed: {str(e)}")
            return {'analysis': f'Comment analysis failed: {str(e)}. Please try again.'}
    
    def _apply_fast_template_styling(self, result):
        """
        ⚡ ENHANCED FAST TEMPLATE-BASED STYLING - Properly formats complex markdown content
        Handles all agent output patterns without LLM overhead for maximum speed
        """
        def format_analysis_with_template(analysis_text, section_type):
            """Apply comprehensive template formatting to analysis text"""
            if not analysis_text:
                return f'<div class="analysis-section {section_type}-section"><p>No analysis available</p></div>'
            
            # Clean and format the analysis text
            formatted = str(analysis_text).strip()
            
            # 1. Handle complex headers with special characters and formatting
            # ## TITLE: -> <h3>TITLE</h3>
            formatted = re.sub(r'^##\s*([^#\n]+?):\s*$', r'<h3 class="section-header">\1</h3>', formatted, flags=re.MULTILINE)
            formatted = re.sub(r'^##\s*([^#\n]+?)\s*$', r'<h3 class="section-header">\1</h3>', formatted, flags=re.MULTILINE)
            
            # ### SUBTITLE -> <h4>SUBTITLE</h4>
            formatted = re.sub(r'^###\s*([^#\n]+?)\s*$', r'<h4 class="subsection-header">\1</h4>', formatted, flags=re.MULTILINE)
            
            # 2. Handle complex bold patterns with surrounding text
            # ***Text*** -> <strong class="emphasis">Text</strong>
            formatted = re.sub(r'\*{3}([^*]+?)\*{3}', r'<strong class="emphasis">\1</strong>', formatted)
            # **Text** -> <strong>Text</strong> 
            formatted = re.sub(r'\*{2}([^*]+?)\*{2}', r'<strong>\1</strong>', formatted)
            # *Text* -> <em>Text</em>
            formatted = re.sub(r'\*([^*\n]+?)\*', r'<em>\1</em>', formatted)
            
            # 3. Handle numbered lists and bullet points
            # 1. Item -> <li>Item</li>
            formatted = re.sub(r'^\s*(\d+)\.\s+(.+)$', r'<li class="numbered-item"><span class="number">\1.</span>\2</li>', formatted, flags=re.MULTILINE)
            # - Item -> <li>Item</li>
            formatted = re.sub(r'^\s*[-•]\s+(.+)$', r'<li>\1</li>', formatted, flags=re.MULTILINE)
            
            # 4. Handle special patterns like grades and metrics
            # Grade: A+ -> <span class="grade">Grade: A+</span>
            formatted = re.sub(r'\b(Grade|Score|Rating):\s*([A-F][+-]?|\d+(?:\.\d+)?(?:/\d+)?(?:%)?)', 
                             r'<span class="metric-grade">\1: <strong>\2</strong></span>', formatted)
            
            # 5. Handle percentage and number patterns
            # 98% -> <span class="percentage">98%</span>
            formatted = re.sub(r'\b(\d+(?:\.\d+)?)%\b', r'<span class="percentage">\1%</span>', formatted)
            # Large numbers with commas
            formatted = re.sub(r'\b(\d{1,3}(?:,\d{3})+)\b', r'<span class="number">\1</span>', formatted)
            
            # 6. Handle special requirement patterns from screenshots
            # ### 1. TITLE: -> <h4>1. TITLE</h4>
            formatted = re.sub(r'^###\s*(\d+)\.\s*([^:]+?):\s*$', r'<h4 class="requirement-header"><span class="req-number">\1.</span> \2</h4>', formatted, flags=re.MULTILINE)
            
            # 7. Clean up common problematic patterns from screenshots
            # Remove excessive spaces and clean formatting
            formatted = re.sub(r'\s*\*\s*\*\s*\*\s*', ' ', formatted)  # Remove spaced asterisks
            formatted = re.sub(r'\s{3,}', '  ', formatted)  # Reduce excessive spaces
            formatted = re.sub(r'\n{3,}', '\n\n', formatted)  # Reduce excessive line breaks
            
            # 8. Convert paragraphs to proper HTML structure
            # Split by double line breaks for paragraphs
            sections = formatted.split('\n\n')
            formatted_sections = []
            
            for section in sections:
                section = section.strip()
                if not section:
                    continue
                    
                # Skip if already contains HTML tags
                if any(tag in section for tag in ['<h3>', '<h4>', '<li>', '<strong>', '<span>']):
                    formatted_sections.append(section)
                else:
                    # Handle multi-line content within sections
                    lines = section.split('\n')
                    if len(lines) == 1:
                        # Single line - make it a paragraph
                        formatted_sections.append(f'<p class="analysis-text">{lines[0]}</p>')
                    else:
                        # Multi-line - handle each line
                        formatted_lines = []
                        for line in lines:
                            line = line.strip()
                            if line:
                                if not any(tag in line for tag in ['<', '>']):
                                    formatted_lines.append(f'<p class="analysis-text">{line}</p>')
                                else:
                                    formatted_lines.append(line)
                        formatted_sections.append('\n'.join(formatted_lines))
            
            formatted = '\n\n'.join(formatted_sections)
            
            # 9. Wrap consecutive list items in proper list containers
            # Group consecutive <li> items into <ul> tags
            formatted = re.sub(r'(<li[^>]*>.*?</li>(?:\s*<li[^>]*>.*?</li>)*)', 
                             lambda m: f'<ul class="analysis-list">{m.group(1)}</ul>', 
                             formatted, flags=re.DOTALL)
            
            # 10. Add premium styling classes and structure
            return f'''
            <div class="analysis-section {section_type}-section">
                <div class="analysis-content premium-content">
                    {formatted}
                </div>
            </div>
            '''
        
        # Apply fast template formatting to each section
        return {
            'performance_html': format_analysis_with_template(
                result.get('performance_analysis', {}).get('analysis', ''), 'performance'
            ),
            'script_html': format_analysis_with_template(
                result.get('script_analysis', {}).get('analysis', ''), 'script'
            ),
            'seo_html': format_analysis_with_template(
                result.get('seo_analysis', {}).get('analysis', ''), 'seo'
            ),
            'psychology_html': format_analysis_with_template(
                result.get('psychology_analysis', {}).get('analysis', ''), 'psychology'
            ),
            'comments_html': format_analysis_with_template(
                result.get('comment_analysis', {}).get('analysis', ''), 'comments'
            ),
            'thumbnail_html': format_analysis_with_template(
                result.get('thumbnail_analysis', {}).get('analysis', ''), 'thumbnail'
            ),
            'synthesis_html': format_analysis_with_template(
                result.get('strategic_synthesis', {}).get('analysis', ''), 'synthesis'
            )
        }
    
    def _run_synthesis_agent(self, all_analyses, video_data, channel_context):
        """
        Run Agent 6: Strategic Content Synthesis Expert
        This agent synthesizes all other agent outputs into actionable strategy
        """
        if not self.ai_enabled:
            return {'error': 'AI not enabled'}
        
        # Create the synthesis agent
        agent6 = Agent(
            role='Strategic Content Synthesis Expert & YouTube Success Architect',
            goal='Synthesize all agent analyses into an interconnected strategic framework that reveals hidden success patterns and generates actionable content strategies with measurable outcomes',
            backstory='''You are YouTube's most sought-after strategic consultant with 15+ years of experience turning channel analytics into million-view content strategies. You've worked with MrBeast, Veritasium, and The Why Files, not just analyzing their success but architecting their content empires.

Your superpower: Seeing the invisible connections between disparate data points and transforming them into strategic gold. You don't just analyze - you synthesize, strategize, and revolutionize.

CRITICAL CAPABILITY: You understand how every element of a video - from the first frame to the last comment - interconnects to create viral success. You've studied thousands of successful channels in your training data and can identify the precise success DNA that others miss.

SYNTHESIS MASTERY: You are an expert at creating "Strategic Integration Frameworks" that connect:
- Performance metrics to script elements
- Script psychology to SEO effectiveness  
- SEO success to audience behavior
- Audience feedback to future content
- All elements into a replicable success formula''',
            llm=self.llm_pro,
            verbose=True
        )
        
        # Extract analysis data safely
        performance_analysis = all_analyses.get('performance_analysis', {}).get('analysis', 'No performance analysis available')
        script_analysis = all_analyses.get('script_analysis', {}).get('analysis', 'No script analysis available')
        seo_analysis = all_analyses.get('seo_analysis', {}).get('analysis', 'No SEO analysis available')
        psychology_analysis = all_analyses.get('psychology_analysis', {}).get('analysis', 'No audience analysis available')
        comment_analysis = all_analyses.get('comment_analysis', {}).get('analysis', 'No comment analysis available')
        thumbnail_analysis = all_analyses.get('thumbnail_analysis', {}).get('analysis', 'No thumbnail analysis available')
        
        # Create the synthesis task
        task6 = Task(
            description=f'''
## YOUR MISSION: THE ULTIMATE SYNTHESIS

You have received analyses from 6 specialized agents. Your job is to create the STRATEGIC SYNTHESIS that transforms data into destiny. You will:

1. CONNECT THE DOTS others cannot see
2. REVEAL THE SUCCESS DNA hidden in the data
3. CREATE THE ROADMAP for content domination

## DATA PROVIDED:

### Agent 1 - Performance Intelligence:
{performance_analysis}

### Agent 2 - Script Forensics:
{script_analysis}

### Agent 3 - SEO & Discoverability:
{seo_analysis}

### Agent 4 - Audience Psychology:
{psychology_analysis}

### Agent 5 - Comment Intelligence:
{comment_analysis}

### Agent 7 - Thumbnail Intelligence:
{thumbnail_analysis}

### Video Context:
Title: {video_data.get('title', 'Unknown')}
Views: {video_data.get('views', 0):,}
Engagement Rate: {(video_data.get('likes', 0) / max(video_data.get('views', 1), 1) * 100):.2f}%
Channel: {channel_context.get('channel_name', 'Unknown')} ({channel_context.get('channel_tier', 'Unknown')})

## SYNTHESIS FRAMEWORK:

### 1. THE SYNTHESIS REVELATION
Start with the "AHA!" moment - the hidden connection that explains everything. What is the SUCCESS DNA that emerges when you connect all 5 analyses?

### 2. CROSS-AGENT CORRELATION MATRIX
Create specific connections between analyses:

**Performance ↔ Script Correlations:**
- Which script elements directly caused high engagement metrics?
- Map specific hooks to viewer retention indicators
- Connect pacing patterns to engagement scores

**Script ↔ SEO Synergy:**
- How did script keywords align with search optimization?
- Which title elements were validated by script content?
- Map script themes to SEO effectiveness

**SEO ↔ Audience Validation:**
- Which keywords did the audience actually use in comments?
- How did search intent match actual viewer satisfaction?
- Validate SEO strategy against audience response

**Psychology ↔ Performance Loop:**
- How did audience psychology predictions match actual behavior?
- Which psychological triggers generated most engagement?
- Map comment sentiment to performance metrics

**Thumbnail ↔ Performance Correlation:**
- How did thumbnail design elements correlate with actual click-through rates?
- Which visual triggers predicted high engagement?
- Map thumbnail psychology to viewer behavior patterns

**Thumbnail ↔ Psychology Integration:**
- How did thumbnail emotional triggers align with audience psychology analysis?
- Which visual elements reinforced the psychological hooks identified?
- Connect thumbnail optimization with audience motivation patterns

### 3. SUCCESS DNA FORMULA
Extract the EXACT combination that created success:
[Hook Type] + [Script Pattern] + [SEO Strategy] + [Audience Trigger] + [Thumbnail Psychology] = [Performance Result]

### 4. STRATEGIC BLIND SPOTS
What did all 6 analyses miss? What opportunities are hidden in the connections?

### 5. REPLICATION BLUEPRINT
Create a step-by-step template for replicating this success:
- Pre-production checklist
- Script formula with exact patterns
- SEO optimization template
- Thumbnail design framework
- Launch strategy framework

### 6. CONTENT PIPELINE
Based on synthesis insights, generate next 10 video ideas with strategic rationale

### 7. PREDICTIVE INTELLIGENCE
6-month forecast: Where is this content strategy heading? What should be optimized?

## OUTPUT FORMAT:

### THE SYNTHESIS REVELATION
[The "AHA!" moment that explains the success]

### SUCCESS DNA FORMULA  
[The exact replicable formula]

### CORRELATION MATRIX
[Specific connections with evidence and scores]

### STRATEGIC BLIND SPOTS
[What everyone missed and why it matters]

### REPLICATION BLUEPRINT
[Step-by-step guide to recreate success]

### CONTENT PIPELINE
[Next 10 videos with strategic rationale]

### PREDICTIVE INTELLIGENCE
[6-month forecast with opportunities/risks]

### ACTION PRIORITY MATRIX
[What to do first, second, third for maximum impact]

### WRITERS' ROOM BRIEF
Create a formula application summary specifically optimized for the Writers' Room system:

**Hook Formula**: [Exact hook type + parameters (e.g., "Paradoxical Question + Cognitive Dissonance")]
**Pacing Blueprint**: [WPM + sentence rhythm + pause timing (e.g., "179.8 WPM, short punches + long explanations")]
**Structure Template**: [Number of sections + timing + progression (e.g., "7 sections: Intro(1min) → Context(5min) → Crisis(10min)...")]
**Emotional Journey Map**: [Specific emotional progression with triggers (e.g., "Wonder → Confusion (paradox) → Understanding (reveal) → Concern (stakes) → Hope (solution)")]
**Key Trigger Patterns**: [Replicable psychological triggers with timing (e.g., "Cognitive dissonance @0-30s, Stakes revelation @2min, Solution hope @8min")]
**Success DNA Elements**: [The 3-5 most critical replicable elements extracted from this analysis]

### RESEARCH INTELLIGENCE BRIEF
To transform generic content into viral scripts with shocking facts and specific details, specify what research is needed:

**Research Topic Focus**: [Specific aspects that need shocking facts/data (e.g., "Roman aqueduct engineering failures", "Sasanid water control military strategies")]
**Fact Categories Needed**: [Types of shocking content required (e.g., "Engineering statistics", "Battle death tolls", "Economic collapse data")]
**Authority Sources**: [Academic/expert citations needed (e.g., "Archaeological studies", "Military historians", "Engineering experts")]
**Viral Content Gaps**: [What specific shocking details would transform this from academic to viral (e.g., "How many people died in water wars", "Specific engineering failures that caused empire collapse")]

This research brief will be used to gather specific, shocking, and authoritative content that transforms generic historical overviews into fact-packed viral content that makes viewers go "holy shit, I had no idea."

Remember: You're not just analyzing - you're ARCHITECTING SUCCESS. Every insight must connect to another. Every pattern must lead to action. Every synthesis must unlock opportunity.

TRANSFORM THESE ANALYSES INTO A CONTENT EMPIRE BLUEPRINT.''',
            agent=agent6,
            expected_output='Comprehensive strategic synthesis with success formula, correlations, and action blueprint'
        )
        
        # Execute the synthesis
        crew = Crew(agents=[agent6], tasks=[task6], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': self._clean_agent_output(str(result))}

# Initialize CrewAI instances
crew_ai = None
video_ai = None

def get_crewai():
    """Get or create CrewAI instance"""
    global crew_ai
    if crew_ai is None:
        crew_ai = MarketResearchCrewAI()
    return crew_ai

def get_video_ai():
    """Get or create Video Analysis CrewAI instance"""
    global video_ai
    if video_ai is None:
        video_ai = VideoAnalysisCrewAI()
    return video_ai

class MarketResearchAnalyzer:
    """Comprehensive market research analyzer for YouTube content opportunities"""

    def __init__(self):
        self.youtube_api_key = os.getenv('YOUTUBE_API_KEY')

        # Initialize Gemini LLM using same pattern as VideoAnalysisCrewAI
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if gemini_api_key:
            os.environ['GEMINI_API_KEY'] = gemini_api_key
            self.llm = LLM(model="gemini/gemini-1.5-flash")
            self.ai_enabled = True
        else:
            self.llm = None
            self.ai_enabled = False

        # Initialize intelligent caching system
        self.insights_cache = {}
        self.cache_expiry_hours = 24  # 24-hour cache expiration

        # Initialize prompt templates
        self._init_prompt_templates()

    def _init_prompt_templates(self):
        """Initialize structured prompt templates for AI insights generation"""

        # Initialize base templates
        self._init_base_templates()

        # Initialize industry-specific templates
        self._init_industry_templates()

    def _init_base_templates(self):
        """Initialize base prompt templates"""
        self.MARKET_SUMMARY_PROMPT = """You are a YouTube market research expert analyzing the {query} niche. Based on this comprehensive data, provide a professional market summary:

MARKET DATA:
- Query: "{query}"
- Videos Analyzed: {video_count}
- Total Views: {total_views:,}
- Average Views: {avg_views:,}
- Unique Channels: {unique_channels}
- Market Engagement Rate: {engagement_rate:.2f}%

CHANNEL LANDSCAPE:
- Average Subscribers: {avg_subscribers:,}
- Subscriber Range: {min_subscribers:,} - {max_subscribers:,}
- Channel Distribution: {subscriber_distribution}

TOP PERFORMERS:
{top_performers_summary}

CONTENT PATTERNS:
- Average Duration: {avg_duration:.1f} minutes
- Long-form Content: {long_form_percentage:.1f}%
- Upload Frequency Patterns: {upload_patterns}

Provide a concise 2-3 sentence market summary that captures:
1. Market size and activity level with subscriber context
2. Competition intensity and entry barriers based on channel sizes
3. Key audience engagement characteristics and opportunity level

Focus on actionable insights for content creators entering this market, considering the subscriber landscape."""

        self.SUCCESS_PATTERNS_PROMPT = """Analyze the top-performing videos in the {query} niche and identify specific success patterns:

TOP 5 PERFORMERS:
{top_videos_detailed}

PERFORMANCE METRICS:
- Top Performance Score: {top_score:.1f}%
- Average Duration: {avg_duration:.1f} minutes
- Long-form vs Short-form: {content_format_analysis}
- Engagement Patterns: {engagement_analysis}

TITLE ANALYSIS:
{title_patterns}

Identify 3-4 specific, actionable success patterns that explain why these videos outperformed others:
1. Content format and structure preferences
2. Title optimization strategies
3. Optimal content length and pacing
4. Audience engagement tactics

Provide concrete, replicable patterns that new creators can implement."""

        self.CONTENT_STRATEGY_PROMPT = """Based on the analysis of the {query} market, develop a comprehensive content strategy:

MARKET CONTEXT:
- Competition Level: {competition_level}
- Average Performance: {avg_views:,} views
- Top Performer Benchmark: {top_performer_views:,} views
- Optimal Content Length: {optimal_duration:.1f} minutes

SUCCESS FACTORS:
{success_factors}

AUDIENCE PREFERENCES:
{audience_preferences}

Create a strategic content approach that addresses:
1. Optimal content format and structure for this niche
2. Recommended content length and production style
3. Key topics and angles that resonate with the audience
4. Differentiation strategy to stand out from competitors

Provide specific, actionable recommendations for content creation."""

        self.GROWTH_OPPORTUNITIES_PROMPT = """Identify growth opportunities in the {query} market based on this analysis:

MARKET LANDSCAPE:
- Active Channels: {unique_channels}
- Average Engagement: {engagement_rate:.2f}%
- Market Saturation Level: {saturation_level}
- Content Gap Analysis: {content_gaps}

PERFORMANCE DISTRIBUTION:
{performance_distribution}

UNDERSERVED AREAS:
{underserved_analysis}

Identify specific growth opportunities including:
1. Content gaps with high demand but low supply
2. Audience segments that appear underserved
3. Format innovations that could disrupt the space
4. Timing and frequency optimization opportunities

Focus on realistic opportunities that new creators can capitalize on."""

        self.MARKET_RISKS_PROMPT = """Assess the market risks and challenges for new creators entering the {query} niche:

COMPETITIVE LANDSCAPE:
- Number of Active Channels: {unique_channels}
- Market Concentration: {market_concentration}
- Entry Barriers: {entry_barriers}
- Content Saturation: {saturation_analysis}

PERFORMANCE REQUIREMENTS:
- Average Views Needed: {avg_views:,}
- Top Performer Benchmark: {top_benchmark:,}
- Engagement Expectations: {engagement_expectations}

Identify key risks and challenges:
1. Competition intensity and market saturation
2. Content quality and production requirements
3. Algorithm and platform-specific risks
4. Audience acquisition challenges

Provide realistic assessment of market entry difficulty and mitigation strategies."""

        self.RECOMMENDATIONS_PROMPT = """Generate specific, prioritized recommendations for success in the {query} market:

MARKET ANALYSIS SUMMARY:
{market_summary}

SUCCESS PATTERNS:
{success_patterns}

OPPORTUNITIES & RISKS:
{opportunities_and_risks}

Generate 4-5 specific, actionable recommendations in this exact JSON format:
[
    {
        "title": "Specific recommendation title",
        "description": "Detailed description of the recommendation and how to implement it",
        "priority": "High|Medium|Low"
    }
]

Focus on recommendations that are:
1. Specific and actionable
2. Based on data-driven insights
3. Realistic for new creators to implement
4. Likely to drive measurable results

Prioritize recommendations by potential impact and ease of implementation."""

    def _init_industry_templates(self):
        """Initialize industry-specific prompt template variations"""

        # Define industry keywords for classification
        self.INDUSTRY_KEYWORDS = {
            'tech': ['programming', 'coding', 'software', 'tech', 'development', 'ai', 'machine learning', 'python', 'javascript', 'web development', 'app development', 'cybersecurity', 'blockchain', 'cryptocurrency'],
            'gaming': ['gaming', 'game', 'gameplay', 'esports', 'twitch', 'minecraft', 'fortnite', 'valorant', 'league of legends', 'fps', 'mmorpg', 'indie games', 'game review', 'speedrun'],
            'education': ['tutorial', 'learn', 'education', 'course', 'lesson', 'study', 'school', 'university', 'math', 'science', 'history', 'language learning', 'online course', 'exam prep'],
            'business': ['business', 'entrepreneur', 'startup', 'marketing', 'sales', 'finance', 'investing', 'stock market', 'real estate', 'dropshipping', 'affiliate marketing', 'passive income'],
            'lifestyle': ['lifestyle', 'vlog', 'daily routine', 'fashion', 'beauty', 'fitness', 'health', 'cooking', 'travel', 'home decor', 'diy', 'self improvement', 'productivity'],
            'entertainment': ['comedy', 'funny', 'reaction', 'movie review', 'music', 'celebrity', 'drama', 'podcast', 'interview', 'challenge', 'prank', 'viral', 'trending']
        }

        # Industry-specific prompt enhancements
        self.INDUSTRY_ENHANCEMENTS = {
            'tech': {
                'focus_areas': 'product cycles, technology trends, developer tools, and technical complexity',
                'success_metrics': 'code quality, practical implementation, and technical accuracy',
                'audience_behavior': 'developers seeking solutions, learning new technologies, and staying current with trends',
                'content_patterns': 'tutorial depth, code examples, and technical documentation quality',
                'terminology': 'frameworks, APIs, deployment, debugging, optimization, and scalability'
            },
            'gaming': {
                'focus_areas': 'game releases, meta changes, competitive seasons, and community events',
                'success_metrics': 'entertainment value, skill demonstration, and community engagement',
                'audience_behavior': 'gamers seeking entertainment, strategy guides, and community connection',
                'content_patterns': 'gameplay footage quality, commentary style, and reaction timing',
                'terminology': 'meta, builds, strategies, patches, tournaments, and community dynamics'
            },
            'education': {
                'focus_areas': 'curriculum alignment, learning outcomes, and educational effectiveness',
                'success_metrics': 'knowledge retention, practical application, and student engagement',
                'audience_behavior': 'learners seeking structured content, clear explanations, and practical examples',
                'content_patterns': 'lesson structure, visual aids, and progressive difficulty',
                'terminology': 'pedagogy, curriculum, assessment, learning objectives, and educational standards'
            },
            'business': {
                'focus_areas': 'market trends, ROI potential, and scalability factors',
                'success_metrics': 'actionable insights, proven strategies, and measurable results',
                'audience_behavior': 'entrepreneurs seeking growth strategies, market insights, and practical advice',
                'content_patterns': 'case studies, data-driven insights, and step-by-step implementation',
                'terminology': 'KPIs, conversion rates, market analysis, growth hacking, and business models'
            },
            'lifestyle': {
                'focus_areas': 'personal branding, aesthetic appeal, and lifestyle trends',
                'success_metrics': 'relatability, visual appeal, and lifestyle inspiration',
                'audience_behavior': 'viewers seeking inspiration, entertainment, and lifestyle guidance',
                'content_patterns': 'visual storytelling, personal narrative, and aspirational content',
                'terminology': 'aesthetics, wellness, self-care, personal growth, and lifestyle optimization'
            },
            'entertainment': {
                'focus_areas': 'viral potential, entertainment value, and cultural relevance',
                'success_metrics': 'engagement rates, shareability, and entertainment quality',
                'audience_behavior': 'viewers seeking entertainment, social connection, and cultural commentary',
                'content_patterns': 'hook strength, pacing, and emotional engagement',
                'terminology': 'viral mechanics, engagement hooks, cultural trends, and entertainment value'
            }
        }

    def _classify_query_industry(self, query: str) -> str:
        """Classify query into industry vertical for specialized analysis"""
        query_lower = query.lower()

        # Score each industry based on keyword matches
        industry_scores = {}
        for industry, keywords in self.INDUSTRY_KEYWORDS.items():
            score = sum(1 for keyword in keywords if keyword in query_lower)
            if score > 0:
                industry_scores[industry] = score

        # Return the industry with highest score, or 'general' if no clear match
        if industry_scores:
            return max(industry_scores.items(), key=lambda x: x[1])[0]
        else:
            return 'general'

    def _get_industry_enhanced_prompt(self, base_prompt: str, query: str, prompt_type: str) -> str:
        """Enhance base prompt with industry-specific context"""
        industry = self._classify_query_industry(query)

        if industry == 'general' or industry not in self.INDUSTRY_ENHANCEMENTS:
            return base_prompt

        enhancements = self.INDUSTRY_ENHANCEMENTS[industry]

        # Add industry-specific context based on prompt type
        if prompt_type == 'market_summary':
            industry_context = f"""
INDUSTRY CONTEXT ({industry.upper()}):
- Focus Areas: {enhancements['focus_areas']}
- Key Success Metrics: {enhancements['success_metrics']}
- Audience Behavior: {enhancements['audience_behavior']}

"""
            return base_prompt.replace("Based on this comprehensive data,", f"Based on this comprehensive data and {industry} industry context,") + industry_context

        elif prompt_type == 'success_patterns':
            industry_context = f"""
INDUSTRY-SPECIFIC ANALYSIS ({industry.upper()}):
- Content Patterns: {enhancements['content_patterns']}
- Success Metrics: {enhancements['success_metrics']}
- Key Terminology: {enhancements['terminology']}

"""
            return base_prompt + industry_context + f"Focus your analysis on {industry}-specific success factors and industry best practices."

        elif prompt_type == 'content_strategy':
            industry_context = f"""
INDUSTRY STRATEGY FOCUS ({industry.upper()}):
- Market Dynamics: {enhancements['focus_areas']}
- Audience Expectations: {enhancements['audience_behavior']}
- Success Benchmarks: {enhancements['success_metrics']}

"""
            return base_prompt + industry_context + f"Tailor your strategy recommendations to {industry} industry standards and audience expectations."

        elif prompt_type == 'growth_opportunities':
            industry_context = f"""
INDUSTRY OPPORTUNITY ANALYSIS ({industry.upper()}):
- Market Focus: {enhancements['focus_areas']}
- Growth Patterns: {enhancements['content_patterns']}
- Industry Terminology: {enhancements['terminology']}

"""
            return base_prompt + industry_context + f"Identify opportunities specific to the {industry} vertical and its unique market dynamics."

        elif prompt_type == 'market_risks':
            industry_context = f"""
INDUSTRY RISK ASSESSMENT ({industry.upper()}):
- Market Challenges: {enhancements['focus_areas']}
- Quality Standards: {enhancements['success_metrics']}
- Competitive Factors: {enhancements['content_patterns']}

"""
            return base_prompt + industry_context + f"Assess risks specific to the {industry} market and provide industry-relevant mitigation strategies."

        return base_prompt

    async def analyze_market(self, query: str, time_range: str, max_results: int, sort_by: str):
        """Perform comprehensive market research analysis"""
        try:
            # Step 1: Search YouTube for trending content
            search_results = await self._search_youtube_content(query, time_range, max_results, sort_by)

            # Step 2: Analyze competition and top performers
            competition_analysis = await self._analyze_competition(search_results)

            # Step 3: Identify content opportunities
            content_opportunities = await self._identify_opportunities(search_results, query)

            # Step 4: Generate AI insights
            ai_insights = await self._generate_ai_insights(search_results, competition_analysis, query)

            # Step 5: Compile market overview
            market_overview = self._compile_market_overview(search_results)

            return {
                'market_overview': market_overview,
                'trending_content': search_results[:20],  # Top 20 for display
                'competition_analysis': competition_analysis,
                'content_opportunities': content_opportunities,
                'ai_insights': ai_insights,
                'query': query,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Market research analysis failed: {e}")
            return {'error': str(e)}

    async def _search_youtube_content(self, query: str, time_range: str, max_results: int, sort_by: str):
        """Search YouTube for relevant content"""
        if not self.youtube_api_key:
            return self._get_mock_search_results(query, max_results)

        try:
            from googleapiclient.discovery import build
            youtube = build('youtube', 'v3', developerKey=self.youtube_api_key)

            # Convert time range to publishedAfter parameter
            published_after = self._get_published_after_date(time_range)

            # Search for videos with pagination to get more than 50 results
            all_videos = []
            videos_collected = 0
            next_page_token = None

            while videos_collected < max_results:
                remaining_videos = max_results - videos_collected
                current_batch_size = min(remaining_videos, 50)  # API limit per call

                search_params = {
                    'q': query,
                    'part': 'snippet',
                    'type': 'video',
                    'order': sort_by,
                    'publishedAfter': published_after,
                    'maxResults': current_batch_size,
                    'regionCode': 'US'
                }

                if next_page_token:
                    search_params['pageToken'] = next_page_token

                search_response = youtube.search().list(**search_params).execute()

                if not search_response.get('items'):
                    break

                all_videos.extend(search_response['items'])
                videos_collected += len(search_response['items'])

                # Check if there are more pages
                next_page_token = search_response.get('nextPageToken')
                if not next_page_token:
                    break

            # Get detailed video statistics for all collected videos
            video_ids = [item['id']['videoId'] for item in all_videos]

            # Process video statistics in batches (API limit is 50 IDs per call)
            all_video_stats = []
            for i in range(0, len(video_ids), 50):
                batch_ids = video_ids[i:i+50]
                videos_response = youtube.videos().list(
                    part='statistics,contentDetails',
                    id=','.join(batch_ids)
                ).execute()
                all_video_stats.extend(videos_response['items'])

            # Get unique channel IDs and fetch channel details
            channel_ids = list(set([item['snippet']['channelId'] for item in all_videos]))
            channel_thumbnails = {}
            channel_data = {}
            for i in range(0, len(channel_ids), 50):
                batch_channel_ids = channel_ids[i:i+50]
                channels_response = youtube.channels().list(
                    part='snippet,statistics',
                    id=','.join(batch_channel_ids)
                ).execute()

                for channel in channels_response.get('items', []):
                    channel_id = channel['id']
                    thumbnail_url = channel['snippet']['thumbnails'].get('default', {}).get('url', '')

                    # Enhanced subscriber data collection with logging
                    statistics = channel.get('statistics', {})
                    subscriber_count_raw = statistics.get('subscriberCount')

                    if subscriber_count_raw is None:
                        logger.warning(f"Channel {channel_id} has hidden subscriber count")
                        subscriber_count = 0
                    else:
                        try:
                            subscriber_count = int(subscriber_count_raw)
                            if subscriber_count > 0:
                                logger.debug(f"Channel {channel_id}: {subscriber_count:,} subscribers")
                        except (ValueError, TypeError):
                            logger.warning(f"Invalid subscriber count for channel {channel_id}: {subscriber_count_raw}")
                            subscriber_count = 0

                    video_count = int(statistics.get('videoCount', 0))
                    total_views = int(statistics.get('viewCount', 0))

                    channel_data[channel_id] = {
                        'thumbnail': thumbnail_url,
                        'subscriber_count': subscriber_count,
                        'video_count': video_count,
                        'total_views': total_views
                    }
                    # Keep backward compatibility
                    channel_thumbnails[channel_id] = thumbnail_url

            # Combine search results with statistics
            results = []
            for i, item in enumerate(all_videos):
                video_id = item['id']['videoId']
                snippet = item['snippet']

                # Find corresponding statistics
                stats = {}
                for video in all_video_stats:
                    if video['id'] == video_id:
                        stats = video.get('statistics', {})
                        break

                # Get content details for this video
                content_details = {}
                for video in all_video_stats:
                    if video['id'] == video_id:
                        content_details = video.get('contentDetails', {})
                        break

                # Get channel data for this video
                channel_info = channel_data.get(snippet['channelId'], {})

                results.append({
                    'video_id': video_id,
                    'title': snippet['title'],
                    'channel_name': snippet['channelTitle'],
                    'channel_id': snippet['channelId'],
                    'thumbnail': snippet['thumbnails'].get('high', {}).get('url', ''),
                    'channel_thumbnail': channel_info.get('thumbnail', ''),
                    'channel_subscribers': channel_info.get('subscriber_count', 0),
                    'channel_video_count': channel_info.get('video_count', 0),
                    'channel_total_views': channel_info.get('total_views', 0),
                    'published_date': snippet['publishedAt'][:10],
                    'views': int(stats.get('viewCount', 0)),
                    'likes': int(stats.get('likeCount', 0)),
                    'comments': int(stats.get('commentCount', 0)),
                    'duration': self._parse_duration(content_details.get('duration', '')),
                    'performance_score': 0  # Will be calculated after all results are collected
                })

            # Calculate market benchmarks and update performance scores
            if results:
                market_context = self._calculate_market_benchmarks(results)
                for result in results:
                    result['performance_score'] = self._calculate_performance_score(
                        {'viewCount': result['views'], 'likeCount': result['likes'], 'commentCount': result['comments']},
                        {'title': result['title']},
                        market_context
                    )

            return sorted(results, key=lambda x: x['performance_score'], reverse=True)

        except Exception as e:
            logger.error(f"YouTube search failed: {e}")
            return self._get_mock_search_results(query, max_results)

    def _get_published_after_date(self, time_range: str):
        """Convert time range to RFC 3339 formatted date"""
        now = datetime.now()
        if time_range == 'week':
            date = now - timedelta(days=7)
        elif time_range == '3months':
            date = now - timedelta(days=90)
        elif time_range == 'year':
            date = now - timedelta(days=365)
        else:  # month
            date = now - timedelta(days=30)

        return date.strftime('%Y-%m-%dT%H:%M:%SZ')

    def _parse_duration(self, duration: str):
        """Parse ISO 8601 duration to readable format"""
        if not duration:
            return ''

        import re
        match = re.match(r'PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?', duration)
        if not match:
            return ''

        hours, minutes, seconds = match.groups()
        hours = int(hours) if hours else 0
        minutes = int(minutes) if minutes else 0
        seconds = int(seconds) if seconds else 0

        if hours > 0:
            return f"{hours}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes}:{seconds:02d}"

    def _calculate_market_benchmarks(self, results: list) -> dict:
        """Calculate market benchmarks for relative performance scoring"""
        if not results:
            return {'avg_views': 1000, 'avg_engagement_rate': 2.0}

        # Calculate market averages
        total_views = sum(r['views'] for r in results)
        total_likes = sum(r['likes'] for r in results)
        total_comments = sum(r['comments'] for r in results)

        avg_views = total_views / len(results) if results else 1000

        # Calculate average engagement rate
        engagement_rates = []
        for result in results:
            if result['views'] > 0:
                engagement_rate = ((result['likes'] + result['comments']) / result['views']) * 100
                engagement_rates.append(engagement_rate)

        avg_engagement_rate = sum(engagement_rates) / len(engagement_rates) if engagement_rates else 2.0

        return {
            'avg_views': avg_views,
            'avg_engagement_rate': avg_engagement_rate,
            'total_videos': len(results),
            'market_total_views': total_views
        }

    def _calculate_performance_score(self, stats: dict, snippet: dict, market_context: dict = None):
        """Calculate market-relative performance score using weighted metrics"""
        views = int(stats.get('viewCount', 0))
        likes = int(stats.get('likeCount', 0))
        comments = int(stats.get('commentCount', 0))

        if views == 0:
            return 0

        # Use default market context if none provided (backward compatibility)
        if market_context is None:
            market_context = {'avg_views': 1000, 'avg_engagement_rate': 2.0}

        # Calculate engagement rate (0-100%)
        engagement_rate = ((likes + comments) / views) * 100

        # Get market benchmarks
        market_avg_engagement = market_context.get('avg_engagement_rate', 2.0)
        market_avg_views = market_context.get('avg_views', 1000)

        # Calculate relative performance scores (0-100)
        # Engagement score: How well this video engages compared to market average
        if market_avg_engagement > 0:
            engagement_score = min(100, (engagement_rate / market_avg_engagement) * 50)
        else:
            engagement_score = 0

        # Views score: How well this video performs in views compared to market average
        if market_avg_views > 0:
            views_score = min(100, (views / market_avg_views) * 50)
        else:
            views_score = 0

        # Weighted final score: 60% engagement, 40% views
        final_score = (engagement_score * 0.6) + (views_score * 0.4)

        return round(final_score, 1)

    async def _analyze_competition(self, search_results: list):
        """Analyze competition and identify top performers"""
        if not search_results:
            return {'top_channels': [], 'market_gaps': []}

        # Group by channel and calculate dominance
        channel_performance = {}
        for video in search_results:
            channel_id = video['channel_id']
            channel_name = video['channel_name']

            if channel_id not in channel_performance:
                channel_performance[channel_id] = {
                    'name': channel_name,
                    'thumbnail': video['channel_thumbnail'],
                    'videos': [],
                    'total_views': 0,
                    'total_engagement': 0,
                    'subscribers': video.get('channel_subscribers', 0)  # Use actual subscriber data
                }

            channel_performance[channel_id]['videos'].append(video)
            channel_performance[channel_id]['total_views'] += video['views']
            channel_performance[channel_id]['total_engagement'] += video['likes'] + video['comments']

        # Calculate top channels
        top_channels = []
        for channel_id, data in channel_performance.items():
            video_count = len(data['videos'])
            avg_views = data['total_views'] // video_count if video_count > 0 else 0
            avg_engagement = data['total_engagement'] // video_count if video_count > 0 else 0

            # Calculate dominance score
            dominance_score = min(100, (avg_views / 10000) + (avg_engagement / 100))

            top_channels.append({
                'name': data['name'],
                'thumbnail': data['thumbnail'],
                'subscribers': data['subscribers'],
                'avg_views': avg_views,
                'upload_frequency': f"{video_count} videos analyzed",
                'engagement_rate': round((avg_engagement / avg_views * 100) if avg_views > 0 else 0, 2),
                'dominance_score': round(dominance_score, 1),
                'content_strategy': f"Focuses on {data['name']} content with avg {avg_views:,} views per video"
            })

        # Sort by dominance score
        top_channels = sorted(top_channels, key=lambda x: x['dominance_score'], reverse=True)[:5]

        # Identify market gaps (simplified)
        market_gaps = [
            {
                'opportunity': 'Beginner-Friendly Content',
                'search_volume': 'High',
                'competition_level': 'Medium',
                'opportunity_score': 85,
                'description': 'High demand for beginner-level tutorials in this niche'
            },
            {
                'opportunity': 'Advanced Techniques',
                'search_volume': 'Medium',
                'competition_level': 'Low',
                'opportunity_score': 78,
                'description': 'Limited advanced content available, opportunity for expert creators'
            }
        ]

        # Create top_performers from individual videos with subscriber data
        top_performers = []
        for video in sorted(search_results, key=lambda x: x.get('performance_score', 0), reverse=True)[:5]:
            top_performers.append({
                'title': video.get('title', 'Unknown'),
                'channel_name': video.get('channel_name', 'Unknown'),
                'subscriber_count': video.get('channel_subscribers', 0),
                'views': video.get('views', 0),
                'likes': video.get('likes', 0),
                'comments': video.get('comments', 0),
                'performance_score': video.get('performance_score', 0),
                'duration': video.get('duration', 'Unknown'),
                'published_date': video.get('published_date', 'Unknown')
            })

        return {
            'top_channels': top_channels,
            'top_performers': top_performers,
            'market_gaps': market_gaps
        }

    def _analyze_trending_keywords(self, search_results: list, query: str) -> list:
        """Analyze video titles to extract trending keywords and patterns"""
        import re
        from collections import Counter

        # Extract all words from video titles
        all_words = []
        title_performance_map = {}

        # Common stop words to filter out
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
            'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must',
            'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they',
            'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its', 'our', 'their'
        }

        for video in search_results:
            title = video.get('title', '').lower()
            performance_score = video.get('performance_score', 0)
            views = video.get('views', 0)

            # Extract meaningful words (2+ characters, alphanumeric)
            words = re.findall(r'\b[a-zA-Z]{2,}\b', title)
            filtered_words = [word for word in words if word not in stop_words and len(word) > 2]

            for word in filtered_words:
                all_words.append(word)
                if word not in title_performance_map:
                    title_performance_map[word] = []
                title_performance_map[word].append({
                    'performance_score': performance_score,
                    'views': views,
                    'title': video.get('title', '')
                })

        # Count word frequency
        word_counts = Counter(all_words)

        # Calculate performance correlation for each keyword
        trending_keywords = []
        for word, count in word_counts.most_common(20):  # Top 20 most frequent words
            if count < 2:  # Skip words that appear only once
                continue

            performances = title_performance_map[word]
            avg_performance = sum(p['performance_score'] for p in performances) / len(performances)
            avg_views = sum(p['views'] for p in performances) / len(performances)

            # Determine trend direction based on performance vs market average
            market_avg_performance = sum(v.get('performance_score', 0) for v in search_results) / len(search_results)
            trend_direction = 'up' if avg_performance > market_avg_performance else 'stable'
            trend_percentage = round(((avg_performance - market_avg_performance) / market_avg_performance) * 100, 1) if market_avg_performance > 0 else 0

            # Determine competition level based on frequency
            if count >= 8:
                competition_level = 'High'
            elif count >= 4:
                competition_level = 'Medium'
            else:
                competition_level = 'Low'

            # Generate content ideas based on successful patterns
            content_ideas = self._generate_content_ideas(word, performances, query)

            trending_keywords.append({
                'keyword': word,
                'frequency': count,
                'trend_direction': trend_direction,
                'trend_percentage': abs(trend_percentage),
                'avg_performance': round(avg_performance, 1),
                'avg_views': int(avg_views),
                'competition_level': competition_level,
                'content_ideas': content_ideas
            })

        # Sort by performance correlation and frequency
        trending_keywords.sort(key=lambda x: (x['avg_performance'], x['frequency']), reverse=True)

        return trending_keywords[:10]  # Return top 10 trending keywords

    def _generate_content_ideas(self, keyword: str, performances: list, query: str) -> list:
        """Generate content ideas based on successful video patterns"""
        # Analyze successful titles containing this keyword
        high_performing = [p for p in performances if p['performance_score'] > 60]

        if not high_performing:
            high_performing = performances  # Fallback to all if none are high-performing

        # Extract common patterns from successful titles
        successful_titles = [p['title'] for p in high_performing]

        content_ideas = []

        # Pattern-based content ideas
        if any('how to' in title.lower() for title in successful_titles):
            content_ideas.append(f"How to master {keyword} in {query}")

        if any('tutorial' in title.lower() for title in successful_titles):
            content_ideas.append(f"Complete {keyword} tutorial for {query}")

        if any('tips' in title.lower() for title in successful_titles):
            content_ideas.append(f"Top {keyword} tips for {query} success")

        if any('guide' in title.lower() for title in successful_titles):
            content_ideas.append(f"Ultimate {keyword} guide for {query}")

        if any('vs' in title.lower() for title in successful_titles):
            content_ideas.append(f"{keyword} vs alternatives in {query}")

        # If no pattern-based ideas, generate generic ones
        if not content_ideas:
            content_ideas = [
                f"Everything about {keyword} in {query}",
                f"Advanced {keyword} techniques",
                f"Common {keyword} mistakes to avoid"
            ]

        return content_ideas[:3]  # Return top 3 ideas

    async def _identify_opportunities(self, search_results: list, query: str):
        """Identify content opportunities and trending topics using real data analysis"""
        if not search_results:
            return {'trending_topics': [], 'optimal_formats': []}

        # Analyze trending topics using real keyword frequency analysis
        trending_topics = self._analyze_trending_keywords(search_results, query)

        # Analyze optimal formats
        long_form_count = sum(1 for v in search_results if self._is_long_form(v.get('duration', '')))
        short_form_count = len(search_results) - long_form_count

        optimal_formats = [
            {
                'type': 'Long-form Tutorial',
                'icon': 'play-circle',
                'avg_views': sum(v['views'] for v in search_results if self._is_long_form(v.get('duration', ''))) // max(long_form_count, 1),
                'success_rate': round((long_form_count / len(search_results)) * 100, 1),
                'description': 'In-depth tutorials and comprehensive guides perform well in this niche',
                'best_practices': [
                    'Include detailed step-by-step instructions',
                    'Use clear chapter markers',
                    'Provide downloadable resources'
                ]
            },
            {
                'type': 'Quick Tips',
                'icon': 'zap',
                'avg_views': sum(v['views'] for v in search_results if not self._is_long_form(v.get('duration', ''))) // max(short_form_count, 1),
                'success_rate': round((short_form_count / len(search_results)) * 100, 1),
                'description': 'Short, actionable content for quick consumption',
                'best_practices': [
                    'Get straight to the point',
                    'Use engaging thumbnails',
                    'Include clear call-to-actions'
                ]
            }
        ]

        return {
            'trending_topics': trending_topics,
            'optimal_formats': optimal_formats
        }

    def _is_long_form(self, duration: str):
        """Check if video is long-form (>10 minutes)"""
        if not duration or ':' not in duration:
            return False

        parts = duration.split(':')
        if len(parts) == 2:  # MM:SS
            minutes = int(parts[0])
            return minutes >= 10
        elif len(parts) == 3:  # HH:MM:SS
            return True  # Any video with hours is long-form

        return False

    def _generate_cache_key(self, query: str, search_results: list) -> str:
        """Generate cache key based on query and search results hash"""
        try:
            # Create a hash of the search results data for cache key
            results_data = {
                'query': query.lower().strip(),
                'video_count': len(search_results),
                'total_views': sum(v.get('views', 0) for v in search_results),
                'top_videos': [
                    {
                        'video_id': v.get('video_id', ''),
                        'views': v.get('views', 0),
                        'performance_score': v.get('performance_score', 0)
                    }
                    for v in sorted(search_results, key=lambda x: x.get('performance_score', 0), reverse=True)[:5]
                ]
            }

            # Create hash from the data
            data_string = json.dumps(results_data, sort_keys=True)
            cache_key = hashlib.md5(data_string.encode()).hexdigest()

            return f"market_insights_{cache_key}"

        except Exception as e:
            logger.error(f"Error generating cache key: {e}")
            # Fallback to simple query-based key
            return f"market_insights_{hashlib.md5(query.encode()).hexdigest()}"

    def _get_cached_insights(self, cache_key: str) -> Optional[dict]:
        """Retrieve cached insights if available and not expired"""
        try:
            if cache_key not in self.insights_cache:
                logger.info(f"Cache miss: {cache_key[:16]}...")
                return None

            cached_data = self.insights_cache[cache_key]
            cached_time = datetime.fromisoformat(cached_data['timestamp'])

            # Check if cache has expired
            expiry_time = cached_time + timedelta(hours=self.cache_expiry_hours)
            if datetime.now(timezone.utc) > expiry_time:
                logger.info(f"Cache expired: {cache_key[:16]}... (age: {datetime.now(timezone.utc) - cached_time})")
                del self.insights_cache[cache_key]
                return None

            logger.info(f"Cache hit: {cache_key[:16]}... (age: {datetime.now(timezone.utc) - cached_time})")
            return cached_data['insights']

        except Exception as e:
            logger.error(f"Error retrieving cached insights: {e}")
            return None

    def _cache_insights(self, cache_key: str, insights: dict):
        """Cache insights with timestamp"""
        try:
            self.insights_cache[cache_key] = {
                'insights': insights,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            logger.info(f"Cached insights: {cache_key[:16]}... (cache size: {len(self.insights_cache)})")

            # Clean up old cache entries if cache gets too large
            if len(self.insights_cache) > 100:
                self._cleanup_cache()

        except Exception as e:
            logger.error(f"Error caching insights: {e}")

    def _cleanup_cache(self):
        """Remove expired cache entries"""
        try:
            current_time = datetime.now(timezone.utc)
            expired_keys = []

            for key, data in self.insights_cache.items():
                cached_time = datetime.fromisoformat(data['timestamp'])
                expiry_time = cached_time + timedelta(hours=self.cache_expiry_hours)

                if current_time > expiry_time:
                    expired_keys.append(key)

            for key in expired_keys:
                del self.insights_cache[key]

            if expired_keys:
                logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")

        except Exception as e:
            logger.error(f"Error cleaning up cache: {e}")

    def _duration_to_seconds(self, duration: str):
        """Convert duration string to seconds"""
        if not duration or ':' not in duration:
            return 0

        parts = duration.split(':')
        try:
            if len(parts) == 2:  # MM:SS
                return int(parts[0]) * 60 + int(parts[1])
            elif len(parts) == 3:  # HH:MM:SS
                return int(parts[0]) * 3600 + int(parts[1]) * 60 + int(parts[2])
        except ValueError:
            return 0

        return 0

    def _prepare_market_context(self, search_results: list, competition_analysis: dict, query: str) -> dict:
        """Prepare comprehensive context for LLM analysis"""
        try:
            # Calculate basic metrics
            total_views = sum(v.get('views', 0) for v in search_results)
            avg_views = total_views // len(search_results) if search_results else 0
            unique_channels = len(set(v.get('channel_name', '') for v in search_results))

            # Calculate subscriber metrics
            subscriber_counts = [v.get('channel_subscribers', 0) for v in search_results if v.get('channel_subscribers', 0) > 0]
            avg_subscribers = sum(subscriber_counts) // len(subscriber_counts) if subscriber_counts else 0
            max_subscribers = max(subscriber_counts) if subscriber_counts else 0
            min_subscribers = min(subscriber_counts) if subscriber_counts else 0

            # Analyze subscriber distribution
            if subscriber_counts:
                large_channels = sum(1 for s in subscriber_counts if s > 100000)
                medium_channels = sum(1 for s in subscriber_counts if 10000 <= s <= 100000)
                small_channels = sum(1 for s in subscriber_counts if s < 10000)
                subscriber_distribution = f"Large: {large_channels}, Medium: {medium_channels}, Small: {small_channels}"
            else:
                subscriber_distribution = "No subscriber data available"

            # Calculate engagement metrics
            total_likes = sum(v.get('likes', 0) for v in search_results)
            total_comments = sum(v.get('comments', 0) for v in search_results)
            avg_engagement = (total_likes + total_comments) / total_views * 100 if total_views > 0 else 0

            # Analyze content patterns
            avg_duration = sum(self._duration_to_seconds(v.get('duration', '0:00')) for v in search_results) / len(search_results) if search_results else 0
            long_form_count = sum(1 for v in search_results if self._is_long_form(v.get('duration', '')))
            long_form_percentage = (long_form_count / len(search_results)) * 100 if search_results else 0

            # Analyze upload frequency patterns
            upload_analysis = self._analyze_upload_patterns(search_results)

            # Get top performers
            top_performers = sorted(search_results, key=lambda x: x.get('performance_score', 0), reverse=True)[:5]

            # Analyze title patterns
            title_patterns = self._analyze_title_patterns(search_results)

            # Determine competition level
            competition_level = "High" if unique_channels > 20 else "Medium" if unique_channels > 10 else "Low"

            # Determine market saturation
            saturation_level = "High" if avg_engagement < 1.0 else "Medium" if avg_engagement < 2.0 else "Low"

            return {
                'query': query,
                'video_count': len(search_results),
                'total_views': total_views,
                'avg_views': avg_views,
                'unique_channels': unique_channels,
                'avg_subscribers': avg_subscribers,
                'max_subscribers': max_subscribers,
                'min_subscribers': min_subscribers,
                'subscriber_distribution': subscriber_distribution,
                'engagement_rate': avg_engagement,
                'avg_duration': avg_duration / 60,  # Convert to minutes
                'long_form_percentage': long_form_percentage,
                'top_performers': top_performers,
                'top_score': top_performers[0].get('performance_score', 0) if top_performers else 0,
                'top_performer_views': top_performers[0].get('views', 0) if top_performers else 0,
                'competition_level': competition_level,
                'saturation_level': saturation_level,
                'title_patterns': title_patterns,
                'upload_analysis': upload_analysis,
                'competition_analysis': competition_analysis
            }

        except Exception as e:
            logger.error(f"Error preparing market context: {e}")
            return {
                'query': query,
                'video_count': len(search_results),
                'total_views': 0,
                'avg_views': 0,
                'unique_channels': 0,
                'engagement_rate': 0,
                'avg_duration': 0,
                'long_form_percentage': 0,
                'top_performers': [],
                'top_score': 0,
                'top_performer_views': 0,
                'competition_level': 'Unknown',
                'saturation_level': 'Unknown',
                'title_patterns': {"summary": "Analysis unavailable", "recommendations": []},
                'upload_analysis': {"summary": "Analysis unavailable", "recommendations": []},
                'competition_analysis': competition_analysis
            }

    def _analyze_upload_patterns(self, search_results: list) -> dict:
        """Analyze upload frequency patterns and timing from video publish dates"""
        try:
            if not search_results:
                return {"summary": "No upload data available", "recommendations": []}

            from datetime import datetime, timedelta
            from collections import defaultdict, Counter
            import calendar

            # Parse publish dates
            video_dates = []
            channel_uploads = defaultdict(list)

            for video in search_results:
                published_date = video.get('published_date', '')
                channel_id = video.get('channel_id', '')

                if published_date:
                    try:
                        # Parse date (format: YYYY-MM-DD)
                        date_obj = datetime.strptime(published_date, '%Y-%m-%d')
                        video_dates.append(date_obj)
                        channel_uploads[channel_id].append(date_obj)
                    except ValueError:
                        continue

            if not video_dates:
                return {"summary": "No valid upload dates found", "recommendations": []}

            # Analyze temporal patterns
            temporal_analysis = self._analyze_temporal_patterns(video_dates)

            # Analyze channel upload frequencies
            frequency_analysis = self._analyze_channel_frequencies(channel_uploads)

            # Analyze seasonal trends
            seasonal_analysis = self._analyze_seasonal_trends(video_dates)

            # Generate recommendations
            recommendations = self._generate_upload_recommendations(temporal_analysis, frequency_analysis, seasonal_analysis)

            return {
                "temporal_patterns": temporal_analysis,
                "frequency_analysis": frequency_analysis,
                "seasonal_trends": seasonal_analysis,
                "recommendations": recommendations,
                "summary": f"Analyzed {len(video_dates)} videos across {len(channel_uploads)} channels with {len(recommendations)} optimization insights"
            }

        except Exception as e:
            logger.error(f"Error analyzing upload patterns: {e}")
            return {"summary": "Upload pattern analysis failed", "recommendations": []}

    def _analyze_temporal_patterns(self, video_dates: list) -> dict:
        """Analyze day-of-week and time patterns"""
        from collections import Counter

        # Day of week analysis
        weekdays = [date.strftime('%A') for date in video_dates]
        weekday_counts = Counter(weekdays)

        # Month analysis
        months = [date.strftime('%B') for date in video_dates]
        month_counts = Counter(months)

        # Recent vs older content
        now = datetime.now()
        recent_threshold = now - timedelta(days=30)
        very_recent_threshold = now - timedelta(days=7)

        recent_count = sum(1 for date in video_dates if date >= recent_threshold)
        very_recent_count = sum(1 for date in video_dates if date >= very_recent_threshold)

        return {
            "weekday_distribution": dict(weekday_counts),
            "month_distribution": dict(month_counts),
            "recent_activity": {
                "last_30_days": recent_count,
                "last_7_days": very_recent_count,
                "activity_level": "High" if recent_count > len(video_dates) * 0.3 else "Medium" if recent_count > len(video_dates) * 0.1 else "Low"
            },
            "most_active_day": weekday_counts.most_common(1)[0][0] if weekday_counts else "Unknown",
            "most_active_month": month_counts.most_common(1)[0][0] if month_counts else "Unknown"
        }

    def _analyze_channel_frequencies(self, channel_uploads: dict) -> dict:
        """Analyze upload frequency patterns by channel"""
        if not channel_uploads:
            return {"average_frequency": "Unknown", "patterns": []}

        frequency_patterns = []
        total_days_analyzed = 0
        total_uploads = 0

        for channel_id, dates in channel_uploads.items():
            if len(dates) < 2:
                continue

            # Calculate days between uploads
            sorted_dates = sorted(dates)
            intervals = []

            for i in range(1, len(sorted_dates)):
                days_diff = (sorted_dates[i] - sorted_dates[i-1]).days
                intervals.append(days_diff)

            if intervals:
                avg_interval = sum(intervals) / len(intervals)
                total_days_analyzed += sum(intervals)
                total_uploads += len(dates)

                # Categorize frequency
                if avg_interval <= 3:
                    frequency_category = "Daily"
                elif avg_interval <= 7:
                    frequency_category = "Weekly"
                elif avg_interval <= 14:
                    frequency_category = "Bi-weekly"
                elif avg_interval <= 30:
                    frequency_category = "Monthly"
                else:
                    frequency_category = "Irregular"

                frequency_patterns.append({
                    "channel_id": channel_id,
                    "avg_interval_days": round(avg_interval, 1),
                    "frequency_category": frequency_category,
                    "upload_count": len(dates),
                    "consistency_score": round(100 / (1 + (max(intervals) - min(intervals)) / avg_interval), 1) if len(intervals) > 1 else 100
                })

        # Calculate market averages
        if frequency_patterns:
            avg_market_interval = sum(p["avg_interval_days"] for p in frequency_patterns) / len(frequency_patterns)
            avg_consistency = sum(p["consistency_score"] for p in frequency_patterns) / len(frequency_patterns)

            # Find most common frequency category
            categories = [p["frequency_category"] for p in frequency_patterns]
            most_common_frequency = max(set(categories), key=categories.count)
        else:
            avg_market_interval = 0
            avg_consistency = 0
            most_common_frequency = "Unknown"

        return {
            "market_average_interval": round(avg_market_interval, 1),
            "market_consistency_score": round(avg_consistency, 1),
            "most_common_frequency": most_common_frequency,
            "channel_patterns": frequency_patterns[:10],  # Top 10 most active channels
            "total_channels_analyzed": len(frequency_patterns)
        }

    def _analyze_seasonal_trends(self, video_dates: list) -> dict:
        """Analyze seasonal and monthly trends"""
        from collections import Counter

        # Group by quarters
        quarters = []
        for date in video_dates:
            month = date.month
            if month in [12, 1, 2]:
                quarters.append("Q1 (Winter)")
            elif month in [3, 4, 5]:
                quarters.append("Q2 (Spring)")
            elif month in [6, 7, 8]:
                quarters.append("Q3 (Summer)")
            else:
                quarters.append("Q4 (Fall)")

        quarter_counts = Counter(quarters)

        # Analyze year-over-year if we have multi-year data
        years = [date.year for date in video_dates]
        year_counts = Counter(years)

        # Determine peak season
        peak_quarter = quarter_counts.most_common(1)[0][0] if quarter_counts else "Unknown"
        peak_year = year_counts.most_common(1)[0][0] if year_counts else "Unknown"

        return {
            "quarterly_distribution": dict(quarter_counts),
            "yearly_distribution": dict(year_counts),
            "peak_season": peak_quarter,
            "peak_year": peak_year,
            "seasonal_variation": round((max(quarter_counts.values()) - min(quarter_counts.values())) / max(quarter_counts.values()) * 100, 1) if quarter_counts else 0
        }

    def _generate_upload_recommendations(self, temporal: dict, frequency: dict, seasonal: dict) -> list:
        """Generate actionable upload timing recommendations"""
        recommendations = []

        # Day of week recommendations
        most_active_day = temporal.get("most_active_day", "Unknown")
        if most_active_day != "Unknown":
            recommendations.append(f"📅 Optimal posting day: {most_active_day} (highest market activity)")

        # Frequency recommendations
        market_interval = frequency.get("market_average_interval", 0)
        most_common_freq = frequency.get("most_common_frequency", "Unknown")

        if market_interval > 0:
            if market_interval <= 7:
                recommendations.append(f"⏰ Post frequency: {most_common_freq} (market average: every {market_interval:.1f} days)")
            else:
                recommendations.append(f"⏰ Consider more frequent posting (market posts every {market_interval:.1f} days)")

        # Consistency recommendations
        market_consistency = frequency.get("market_consistency_score", 0)
        if market_consistency < 70:
            recommendations.append("📊 Improve consistency: Market shows irregular posting patterns - consistent schedule could provide competitive advantage")

        # Seasonal recommendations
        peak_season = seasonal.get("peak_season", "Unknown")
        if peak_season != "Unknown":
            recommendations.append(f"🌟 Peak season: {peak_season} shows highest activity - plan major content releases accordingly")

        # Activity level recommendations
        activity_level = temporal.get("recent_activity", {}).get("activity_level", "Unknown")
        if activity_level == "Low":
            recommendations.append("🚀 Market opportunity: Low recent activity suggests potential for new content to gain visibility")
        elif activity_level == "High":
            recommendations.append("⚡ Competitive market: High recent activity requires strong content differentiation")

        return recommendations[:5]  # Limit to top 5 recommendations

    def _analyze_title_patterns(self, search_results: list) -> dict:
        """Enhanced title pattern analysis with actionable insights"""
        try:
            if not search_results:
                return {"summary": "No title data available", "recommendations": []}

            # Separate high and low performers for comparison
            sorted_results = sorted(search_results, key=lambda x: x.get('performance_score', 0), reverse=True)
            high_performers = sorted_results[:len(sorted_results)//3]  # Top third
            low_performers = sorted_results[-len(sorted_results)//3:]  # Bottom third

            # Analyze all titles
            all_analysis = self._analyze_title_group(search_results, "All Videos")
            high_analysis = self._analyze_title_group(high_performers, "High Performers")
            low_analysis = self._analyze_title_group(low_performers, "Low Performers")

            # Identify successful patterns
            successful_patterns = self._identify_successful_title_patterns(high_performers, low_performers)

            # Generate recommendations
            recommendations = self._generate_title_recommendations(successful_patterns, all_analysis)

            return {
                "all_videos": all_analysis,
                "high_performers": high_analysis,
                "low_performers": low_analysis,
                "successful_patterns": successful_patterns,
                "recommendations": recommendations,
                "summary": f"Analyzed {len(search_results)} titles with {len(successful_patterns)} key success patterns identified"
            }

        except Exception as e:
            logger.error(f"Error analyzing title patterns: {e}")
            return {"summary": "Title pattern analysis failed", "recommendations": []}

    def _analyze_title_group(self, videos: list, group_name: str) -> dict:
        """Analyze title patterns for a specific group of videos"""
        import re

        if not videos:
            return {"group": group_name, "count": 0}

        titles = [v.get('title', '') for v in videos if v.get('title')]
        if not titles:
            return {"group": group_name, "count": 0}

        # Basic metrics
        avg_length = sum(len(title) for title in titles) / len(titles)
        avg_words = sum(len(title.split()) for title in titles) / len(titles)

        # Pattern analysis
        patterns = {
            'numbers': sum(1 for title in titles if re.search(r'\d+', title)),
            'questions': sum(1 for title in titles if '?' in title),
            'how_to': sum(1 for title in titles if re.search(r'\bhow to\b', title.lower())),
            'ultimate': sum(1 for title in titles if re.search(r'\b(ultimate|complete|comprehensive)\b', title.lower())),
            'vs_comparison': sum(1 for title in titles if re.search(r'\bvs\b|\bversus\b', title.lower())),
            'top_lists': sum(1 for title in titles if re.search(r'\btop\s+\d+\b|\bbest\s+\d+\b', title.lower())),
            'beginner': sum(1 for title in titles if re.search(r'\b(beginner|start|basic|intro)\b', title.lower())),
            'advanced': sum(1 for title in titles if re.search(r'\b(advanced|expert|pro|master)\b', title.lower())),
            'year_specific': sum(1 for title in titles if re.search(r'\b20\d{2}\b', title)),
            'emotional_triggers': sum(1 for title in titles if re.search(r'\b(amazing|incredible|shocking|secret|hack|trick)\b', title.lower())),
            'urgency': sum(1 for title in titles if re.search(r'\b(now|today|fast|quick|instant)\b', title.lower())),
            'brackets_parens': sum(1 for title in titles if re.search(r'[\[\(].*[\]\)]', title))
        }

        # Character length distribution
        length_ranges = {
            'short': sum(1 for title in titles if len(title) < 40),
            'medium': sum(1 for title in titles if 40 <= len(title) <= 60),
            'long': sum(1 for title in titles if len(title) > 60)
        }

        # Calculate percentages
        total = len(titles)
        pattern_percentages = {k: round((v / total) * 100, 1) for k, v in patterns.items()}
        length_percentages = {k: round((v / total) * 100, 1) for k, v in length_ranges.items()}

        return {
            "group": group_name,
            "count": total,
            "avg_length": round(avg_length, 1),
            "avg_words": round(avg_words, 1),
            "patterns": pattern_percentages,
            "length_distribution": length_percentages,
            "sample_titles": titles[:3]  # First 3 titles as examples
        }

    def _identify_successful_title_patterns(self, high_performers: list, low_performers: list) -> list:
        """Identify patterns that correlate with high performance"""
        if not high_performers or not low_performers:
            return []

        high_analysis = self._analyze_title_group(high_performers, "High")
        low_analysis = self._analyze_title_group(low_performers, "Low")

        successful_patterns = []

        # Compare pattern usage between high and low performers
        high_patterns = high_analysis.get('patterns', {})
        low_patterns = low_analysis.get('patterns', {})

        for pattern, high_pct in high_patterns.items():
            low_pct = low_patterns.get(pattern, 0)
            difference = high_pct - low_pct

            # If high performers use this pattern significantly more
            if difference >= 15:  # At least 15% difference
                pattern_name = pattern.replace('_', ' ').title()
                successful_patterns.append({
                    'pattern': pattern_name,
                    'high_usage': high_pct,
                    'low_usage': low_pct,
                    'difference': round(difference, 1),
                    'recommendation': f"Use {pattern_name.lower()} - {high_pct}% of top performers vs {low_pct}% of low performers"
                })

        # Analyze optimal length
        high_avg_length = high_analysis.get('avg_length', 0)
        low_avg_length = low_analysis.get('avg_length', 0)

        if abs(high_avg_length - low_avg_length) >= 5:  # Significant difference
            successful_patterns.append({
                'pattern': 'Optimal Length',
                'high_usage': high_avg_length,
                'low_usage': low_avg_length,
                'difference': round(high_avg_length - low_avg_length, 1),
                'recommendation': f"Target {high_avg_length:.0f} characters (high performers avg) vs {low_avg_length:.0f} (low performers avg)"
            })

        # Sort by difference (most significant patterns first)
        successful_patterns.sort(key=lambda x: x['difference'], reverse=True)

        return successful_patterns[:5]  # Top 5 most significant patterns

    def _generate_title_recommendations(self, successful_patterns: list, all_analysis: dict) -> list:
        """Generate actionable title recommendations based on analysis"""
        recommendations = []

        if not successful_patterns:
            return ["Insufficient data for specific recommendations"]

        # Length recommendation
        avg_length = all_analysis.get('avg_length', 50)
        if avg_length < 40:
            recommendations.append(f"📏 Increase title length to 45-60 characters (current avg: {avg_length:.0f})")
        elif avg_length > 70:
            recommendations.append(f"📏 Shorten titles to 45-60 characters (current avg: {avg_length:.0f})")
        else:
            recommendations.append(f"📏 Good title length range (current avg: {avg_length:.0f} characters)")

        # Pattern-based recommendations
        for pattern in successful_patterns[:3]:  # Top 3 patterns
            recommendations.append(f"🎯 {pattern['recommendation']}")

        # General best practices based on analysis
        patterns = all_analysis.get('patterns', {})

        if patterns.get('numbers', 0) < 30:
            recommendations.append("🔢 Consider adding specific numbers (e.g., '5 Tips', '2024 Guide')")

        if patterns.get('emotional_triggers', 0) < 20:
            recommendations.append("💡 Add emotional triggers ('Amazing', 'Secret', 'Proven')")

        if patterns.get('brackets_parens', 0) < 15:
            recommendations.append("📝 Use brackets/parentheses for additional context")

        return recommendations[:6]  # Limit to 6 recommendations

    def _call_llm_for_insights(self, context: dict, query: str) -> dict:
        """Make structured LLM calls for different insight categories"""
        try:
            insights = {}

            # Prepare formatted context data for prompts
            formatted_context = self._format_context_for_prompts(context)

            # Market Summary (with industry enhancement)
            base_market_prompt = self.MARKET_SUMMARY_PROMPT.format(**formatted_context)
            market_prompt = self._get_industry_enhanced_prompt(base_market_prompt, query, "market_summary")
            insights['market_summary'] = self._make_llm_call(market_prompt, "market_summary")

            # Success Patterns (with industry enhancement)
            base_patterns_prompt = self.SUCCESS_PATTERNS_PROMPT.format(**formatted_context)
            patterns_prompt = self._get_industry_enhanced_prompt(base_patterns_prompt, query, "success_patterns")
            insights['success_patterns'] = self._make_llm_call(patterns_prompt, "success_patterns")

            # Content Strategy (with industry enhancement)
            base_strategy_prompt = self.CONTENT_STRATEGY_PROMPT.format(**formatted_context)
            strategy_prompt = self._get_industry_enhanced_prompt(base_strategy_prompt, query, "content_strategy")
            insights['content_strategy'] = self._make_llm_call(strategy_prompt, "content_strategy")

            # Growth Opportunities (with industry enhancement)
            base_opportunities_prompt = self.GROWTH_OPPORTUNITIES_PROMPT.format(**formatted_context)
            opportunities_prompt = self._get_industry_enhanced_prompt(base_opportunities_prompt, query, "growth_opportunities")
            insights['growth_opportunities'] = self._make_llm_call(opportunities_prompt, "growth_opportunities")

            # Market Risks (with industry enhancement)
            base_risks_prompt = self.MARKET_RISKS_PROMPT.format(**formatted_context)
            risks_prompt = self._get_industry_enhanced_prompt(base_risks_prompt, query, "market_risks")
            insights['market_risks'] = self._make_llm_call(risks_prompt, "market_risks")

            # Recommendations (requires special parsing with actual insights)
            try:
                # Create context for recommendations that includes the generated insights
                # Clean the context variables to prevent formatting errors
                def clean_context_value(value):
                    """Clean context values to prevent string formatting issues"""
                    if not isinstance(value, str):
                        return str(value)
                    # Remove problematic characters that could break string formatting
                    cleaned = value.replace('{', '{{').replace('}', '}}')
                    return cleaned

                rec_context = {
                    **formatted_context,
                    'market_summary': clean_context_value(insights.get('market_summary', 'Market analysis in progress')),
                    'success_patterns': clean_context_value(insights.get('success_patterns', 'Success patterns analysis in progress')),
                    'opportunities_and_risks': clean_context_value(f"OPPORTUNITIES: {insights.get('growth_opportunities', 'Analysis in progress')}\n\nRISKS: {insights.get('market_risks', 'Analysis in progress')}")
                }

                try:
                    recommendations_prompt = self.RECOMMENDATIONS_PROMPT.format(**rec_context)
                except Exception as format_error:
                    logger.error(f"Prompt formatting failed: {format_error}")
                    logger.error(f"Context keys: {list(rec_context.keys())}")

                    # Log each context value to find the problematic one
                    for key, value in rec_context.items():
                        try:
                            value_str = str(value)
                            if len(value_str) > 200:
                                logger.error(f"Context {key}: {repr(value_str[:200])}... (truncated, total: {len(value_str)} chars)")
                            else:
                                logger.error(f"Context {key}: {repr(value_str)}")
                        except Exception as log_error:
                            logger.error(f"Context {key}: <Error converting to string: {log_error}>")

                    # Try to create a safe fallback prompt
                    safe_prompt = f"""Generate specific recommendations for the {rec_context.get('query', 'market')} market.

                    Generate 3-4 actionable recommendations in this exact JSON format:
                    [
                        {{
                            "title": "Specific recommendation title",
                            "description": "Detailed description of the recommendation",
                            "priority": "High"
                        }}
                    ]"""

                    logger.info("Using safe fallback prompt for recommendations")
                    recommendations_prompt = safe_prompt
                recommendations_text = self._make_llm_call(recommendations_prompt, "recommendations")

                if recommendations_text and len(recommendations_text.strip()) > 10:
                    logger.info(f"Processing recommendations response ({len(recommendations_text)} chars)")

                    # DEBUG: Log the complete raw response for analysis
                    logger.debug(f"RAW RECOMMENDATIONS RESPONSE: {repr(recommendations_text)}")
                    logger.info(f"First 200 chars: {repr(recommendations_text[:200])}")

                    # Try to parse recommendations with comprehensive error handling
                    try:
                        insights['recommendations'] = self._parse_recommendations(recommendations_text)
                        logger.info(f"Successfully parsed {len(insights['recommendations'])} recommendations")
                    except Exception as parse_error:
                        logger.error(f"Recommendations parsing failed: {parse_error}")
                        logger.error(f"Raw response sample: {repr(recommendations_text[:200])}")
                        logger.error(f"Full raw response for debugging: {repr(recommendations_text)}")

                        # Fallback: create recommendations from the text content
                        insights['recommendations'] = [
                            {
                                'title': 'Focus on High-Quality Content',
                                'description': 'Based on market analysis, prioritize creating comprehensive, well-structured content that addresses user needs.',
                                'priority': 'High'
                            },
                            {
                                'title': 'Optimize for Search Discovery',
                                'description': 'Use relevant keywords and trending topics to improve content visibility and reach.',
                                'priority': 'Medium'
                            }
                        ]
                else:
                    logger.warning("Empty or invalid recommendations response from LLM")
                    insights['recommendations'] = [
                        {
                            'title': 'Market Analysis Available',
                            'description': 'Review the detailed market insights for strategic guidance.',
                            'priority': 'Medium'
                        }
                    ]

            except Exception as rec_error:
                logger.error(f"Recommendations section failed completely: {rec_error}")
                insights['recommendations'] = [
                    {
                        'title': 'Market Analysis Available',
                        'description': 'Detailed market insights are available. Review the analysis sections for strategic guidance.',
                        'priority': 'High'
                    }
                ]

            logger.info(f"Successfully generated all AI insights. Keys: {list(insights.keys())}")
            return insights

        except Exception as e:
            logger.error(f"Error in LLM insights generation: {e}")
            raise e

    def _format_context_for_prompts(self, context: dict) -> dict:
        """Format context data for prompt templates"""
        try:
            # Format top performers summary
            top_performers = context.get('top_performers', [])
            top_performers_summary = ""
            if top_performers:
                for i, video in enumerate(top_performers[:3], 1):
                    top_performers_summary += f"{i}. {video.get('title', 'Unknown')} - {video.get('views', 0):,} views ({video.get('performance_score', 0):.1f}% score)\n"
            else:
                top_performers_summary = "No top performers data available"

            # Format detailed top videos
            top_videos_detailed = ""
            if top_performers:
                for i, video in enumerate(top_performers[:5], 1):
                    duration = video.get('duration', 'Unknown')
                    likes = video.get('likes', 0)
                    comments = video.get('comments', 0)
                    top_videos_detailed += f"{i}. Title: {video.get('title', 'Unknown')}\n"
                    top_videos_detailed += f"   Views: {video.get('views', 0):,} | Duration: {duration} | Likes: {likes:,} | Comments: {comments:,}\n"
                    top_videos_detailed += f"   Performance Score: {video.get('performance_score', 0):.1f}%\n\n"
            else:
                top_videos_detailed = "No detailed video data available"

            # Format title patterns analysis
            title_patterns_data = context.get('title_patterns', {})
            if isinstance(title_patterns_data, dict):
                title_patterns_formatted = self._format_title_patterns_for_prompt(title_patterns_data)
            else:
                title_patterns_formatted = str(title_patterns_data)

            # Format upload patterns analysis
            upload_analysis_data = context.get('upload_analysis', {})
            if isinstance(upload_analysis_data, dict):
                upload_patterns_formatted = self._format_upload_patterns_for_prompt(upload_analysis_data)
            else:
                upload_patterns_formatted = str(upload_analysis_data)

            # Create comprehensive formatted context
            formatted = {
                **context,
                'top_performers_summary': top_performers_summary.strip(),
                'top_videos_detailed': top_videos_detailed.strip(),
                'title_patterns': title_patterns_formatted,
                'upload_patterns': upload_patterns_formatted,
                'content_format_analysis': f"{context.get('long_form_percentage', 0):.1f}% long-form content",
                'engagement_analysis': f"Average engagement rate: {context.get('engagement_rate', 0):.2f}%",
                'success_factors': "Based on top performer analysis",
                'audience_preferences': f"Prefers {context.get('avg_duration', 0):.1f}-minute content",
                'content_gaps': "Identified through competitive analysis",
                'performance_distribution': f"Top performer: {context.get('top_performer_views', 0):,} views vs Average: {context.get('avg_views', 0):,} views",
                'underserved_analysis': "Based on engagement and competition data",
                'market_concentration': f"{context.get('unique_channels', 0)} active channels",
                'entry_barriers': f"{context.get('competition_level', 'Unknown')} competition level",
                'saturation_analysis': f"{context.get('saturation_level', 'Unknown')} market saturation",
                'avg_benchmark': context.get('avg_views', 0),
                'top_benchmark': context.get('top_performer_views', 0),
                'engagement_expectations': f"{context.get('engagement_rate', 0):.2f}% average engagement",
                'opportunities_and_risks': "Based on comprehensive market analysis",
                'optimal_duration': context.get('avg_duration', 10.0),  # Default to 10 minutes if not available
                # Add missing fields expected by prompts
                'video_count': context.get('video_count', 0),
                'total_views': context.get('total_views', 0),
                'long_form_percentage': context.get('long_form_percentage', 0),
                'competition_level': context.get('competition_level', 'Medium')
            }

            return formatted

        except Exception as e:
            logger.error(f"Error formatting context for prompts: {e}")
            return context

    def _format_title_patterns_for_prompt(self, title_patterns: dict) -> str:
        """Format title patterns analysis for AI prompts"""
        try:
            if not title_patterns or not isinstance(title_patterns, dict):
                return "No title pattern analysis available"

            formatted_parts = []

            # Summary
            summary = title_patterns.get('summary', '')
            if summary:
                formatted_parts.append(f"OVERVIEW: {summary}")

            # Successful patterns
            successful_patterns = title_patterns.get('successful_patterns', [])
            if successful_patterns:
                formatted_parts.append("\nSUCCESSFUL PATTERNS:")
                for pattern in successful_patterns[:3]:  # Top 3 patterns
                    pattern_name = pattern.get('pattern', 'Unknown')
                    high_usage = pattern.get('high_usage', 0)
                    low_usage = pattern.get('low_usage', 0)
                    formatted_parts.append(f"- {pattern_name}: {high_usage}% (top performers) vs {low_usage}% (low performers)")

            # Recommendations
            recommendations = title_patterns.get('recommendations', [])
            if recommendations:
                formatted_parts.append("\nTITLE RECOMMENDATIONS:")
                for rec in recommendations[:4]:  # Top 4 recommendations
                    formatted_parts.append(f"- {rec}")

            # High performer analysis
            high_analysis = title_patterns.get('high_performers', {})
            if high_analysis and high_analysis.get('count', 0) > 0:
                avg_length = high_analysis.get('avg_length', 0)
                patterns = high_analysis.get('patterns', {})
                formatted_parts.append(f"\nHIGH PERFORMER CHARACTERISTICS:")
                formatted_parts.append(f"- Average length: {avg_length:.0f} characters")

                # Show top patterns used by high performers
                top_patterns = sorted(patterns.items(), key=lambda x: x[1], reverse=True)[:3]
                for pattern_name, percentage in top_patterns:
                    if percentage > 20:  # Only show significant patterns
                        clean_name = pattern_name.replace('_', ' ').title()
                        formatted_parts.append(f"- {clean_name}: {percentage}% usage")

            return '\n'.join(formatted_parts) if formatted_parts else "Title pattern analysis unavailable"

        except Exception as e:
            logger.error(f"Error formatting title patterns: {e}")
            return "Title pattern formatting failed"

    def _format_upload_patterns_for_prompt(self, upload_analysis: dict) -> str:
        """Format upload patterns analysis for AI prompts"""
        try:
            if not upload_analysis or not isinstance(upload_analysis, dict):
                return "No upload pattern analysis available"

            formatted_parts = []

            # Summary
            summary = upload_analysis.get('summary', '')
            if summary:
                formatted_parts.append(f"OVERVIEW: {summary}")

            # Temporal patterns
            temporal = upload_analysis.get('temporal_patterns', {})
            if temporal:
                most_active_day = temporal.get('most_active_day', 'Unknown')
                most_active_month = temporal.get('most_active_month', 'Unknown')
                activity_level = temporal.get('recent_activity', {}).get('activity_level', 'Unknown')

                formatted_parts.append(f"\nTEMPORAL PATTERNS:")
                formatted_parts.append(f"- Most active day: {most_active_day}")
                formatted_parts.append(f"- Most active month: {most_active_month}")
                formatted_parts.append(f"- Recent activity level: {activity_level}")

                # Weekday distribution
                weekday_dist = temporal.get('weekday_distribution', {})
                if weekday_dist:
                    top_days = sorted(weekday_dist.items(), key=lambda x: x[1], reverse=True)[:3]
                    formatted_parts.append(f"- Top posting days: {', '.join([f'{day} ({count})' for day, count in top_days])}")

            # Frequency analysis
            frequency = upload_analysis.get('frequency_analysis', {})
            if frequency:
                market_interval = frequency.get('market_average_interval', 0)
                most_common_freq = frequency.get('most_common_frequency', 'Unknown')
                consistency_score = frequency.get('market_consistency_score', 0)

                formatted_parts.append(f"\nFREQUENCY ANALYSIS:")
                formatted_parts.append(f"- Market average interval: {market_interval} days")
                formatted_parts.append(f"- Most common frequency: {most_common_freq}")
                formatted_parts.append(f"- Market consistency score: {consistency_score}%")

                # Top channel patterns
                channel_patterns = frequency.get('channel_patterns', [])
                if channel_patterns:
                    top_consistent = sorted(channel_patterns, key=lambda x: x.get('consistency_score', 0), reverse=True)[:2]
                    formatted_parts.append(f"- Top consistent channels: {', '.join([f'{p.get('frequency_category', 'Unknown')} ({p.get('consistency_score', 0)}% consistent)' for p in top_consistent])}")

            # Seasonal trends
            seasonal = upload_analysis.get('seasonal_trends', {})
            if seasonal:
                peak_season = seasonal.get('peak_season', 'Unknown')
                seasonal_variation = seasonal.get('seasonal_variation', 0)

                formatted_parts.append(f"\nSEASONAL TRENDS:")
                formatted_parts.append(f"- Peak season: {peak_season}")
                formatted_parts.append(f"- Seasonal variation: {seasonal_variation}%")

                # Quarterly distribution
                quarterly_dist = seasonal.get('quarterly_distribution', {})
                if quarterly_dist:
                    quarters = sorted(quarterly_dist.items(), key=lambda x: x[1], reverse=True)
                    formatted_parts.append(f"- Activity by quarter: {', '.join([f'{q} ({c})' for q, c in quarters])}")

            # Recommendations
            recommendations = upload_analysis.get('recommendations', [])
            if recommendations:
                formatted_parts.append(f"\nUPLOAD RECOMMENDATIONS:")
                for rec in recommendations[:4]:  # Top 4 recommendations
                    formatted_parts.append(f"- {rec}")

            return '\n'.join(formatted_parts) if formatted_parts else "Upload pattern analysis unavailable"

        except Exception as e:
            logger.error(f"Error formatting upload patterns: {e}")
            return "Upload pattern formatting failed"

    def _make_llm_call(self, prompt: str, insight_type: str, max_retries: int = 3) -> str:
        """Make individual LLM API call with retry mechanism and error handling"""
        import time

        for attempt in range(max_retries):
            try:
                if not self.llm:
                    raise Exception("LLM not initialized")

                logger.info(f"Making LLM call for {insight_type} (attempt {attempt + 1}/{max_retries})")

                # Use the correct method for CrewAI LLM
                response = self.llm.call(prompt)

                if hasattr(response, 'content'):
                    result = response.content.strip()
                elif hasattr(response, 'text'):
                    result = response.text.strip()
                else:
                    result = str(response).strip()

                # Validate that we got meaningful content
                if len(result) < 50:
                    raise Exception(f"Response too short ({len(result)} chars): {result[:100]}")

                logger.info(f"LLM call successful for {insight_type} ({len(result)} characters)")
                return result

            except Exception as e:
                logger.warning(f"LLM call attempt {attempt + 1} failed for {insight_type}: {e}")

                if attempt < max_retries - 1:
                    # Wait before retry (exponential backoff)
                    wait_time = 2 ** attempt
                    logger.info(f"Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"All {max_retries} attempts failed for {insight_type}")

        # All retries failed, return fallback
        return f"AI analysis temporarily unavailable for {insight_type}. Please try again later."

    def _parse_recommendations(self, recommendations_text: str) -> list:
        """Parse recommendations from LLM response into structured format with comprehensive error handling"""
        try:
            import json
            import re

            logger.debug(f"Parsing recommendations from text: {repr(recommendations_text[:200])}")

            # Log the raw response for debugging
            logger.info(f"Parsing recommendations response ({len(recommendations_text)} chars)")

            # Clean up the response text
            cleaned_text = recommendations_text.strip()

            # Remove markdown code block markers
            if cleaned_text.startswith('```json'):
                cleaned_text = cleaned_text[7:].strip()
            elif cleaned_text.startswith('```'):
                cleaned_text = cleaned_text[3:].strip()

            if cleaned_text.endswith('```'):
                cleaned_text = cleaned_text[:-3].strip()

            # Special handling for the '\n        "title"' error pattern
            if '"title"' in cleaned_text and not cleaned_text.strip().startswith('['):
                logger.info("Attempting to fix malformed JSON with title pattern")
                # This looks like the LLM returned object properties without proper JSON structure
                # Try to reconstruct valid JSON
                lines = cleaned_text.split('\n')
                fixed_objects = []
                current_object = {}

                for line in lines:
                    line = line.strip()
                    if not line or line in ['{', '}', '[', ']']:
                        continue

                    # Look for key-value pairs like: "title": "value"
                    if ':' in line and '"' in line:
                        try:
                            # Clean up the line
                            line = line.rstrip(',').strip()
                            if ':' in line:
                                # Split on first colon
                                colon_idx = line.find(':')
                                key_part = line[:colon_idx].strip().strip('"')
                                value_part = line[colon_idx+1:].strip().strip('"').strip(',')

                                if key_part and value_part:
                                    current_object[key_part] = value_part

                                    # If we have a complete object, save it
                                    if len(current_object) >= 2 and 'title' in current_object:
                                        if 'priority' not in current_object:
                                            current_object['priority'] = 'Medium'
                                        fixed_objects.append(current_object.copy())
                                        current_object = {}
                        except Exception as parse_err:
                            logger.debug(f"Failed to parse line '{line}': {parse_err}")
                            continue

                # Add any remaining object
                if len(current_object) >= 2:
                    if 'priority' not in current_object:
                        current_object['priority'] = 'Medium'
                    fixed_objects.append(current_object)

                if fixed_objects:
                    logger.info(f"Successfully reconstructed {len(fixed_objects)} recommendations from malformed JSON")
                    return fixed_objects

            # Try multiple JSON extraction strategies
            json_candidates = []

            # Strategy 1: Direct JSON parsing if it looks like JSON
            if cleaned_text.startswith('[') and cleaned_text.endswith(']'):
                json_candidates.append(cleaned_text)

            # Strategy 2: Find JSON array pattern
            json_match = re.search(r'\[[\s\S]*\]', cleaned_text)
            if json_match:
                json_candidates.append(json_match.group(0))

            # Strategy 3: Extract everything between first [ and last ]
            start_idx = cleaned_text.find('[')
            end_idx = cleaned_text.rfind(']')
            if start_idx >= 0 and end_idx > start_idx:
                json_candidates.append(cleaned_text[start_idx:end_idx + 1])

            # Try parsing each candidate
            for i, candidate in enumerate(json_candidates):
                try:
                    # Clean up common JSON formatting issues
                    candidate = candidate.strip()

                    # Fix the specific error we're seeing: '\n        "title"'
                    # This suggests the LLM is returning malformed JSON like:
                    # [
                    #         "title": "..."  (missing opening brace)
                    # Let's try to fix this pattern more comprehensively
                    if '"title"' in candidate:
                        logger.debug(f"Attempting to fix title pattern in candidate: {repr(candidate[:100])}")

                        # Strategy 1: Fix missing opening braces
                        if not '{"title"' in candidate:
                            lines = candidate.split('\n')
                            fixed_lines = []
                            in_object = False

                            for line in lines:
                                stripped = line.strip()

                                # Skip empty lines and array brackets
                                if not stripped or stripped in ['[', ']']:
                                    fixed_lines.append(line)
                                    continue

                                # If we see a field starting with quote, we need an opening brace
                                if stripped.startswith('"') and ':' in stripped and not in_object:
                                    # Add opening brace
                                    indent = len(line) - len(line.lstrip())
                                    fixed_lines.append(' ' * indent + '{')
                                    fixed_lines.append(line)
                                    in_object = True
                                elif stripped.startswith('"') and ':' in stripped and in_object:
                                    # Continue adding fields
                                    fixed_lines.append(line)
                                elif in_object and (not stripped.startswith('"') or stripped == '}'):
                                    # End of object, add closing brace if needed
                                    if stripped != '}':
                                        indent = len(line) - len(line.lstrip())
                                        fixed_lines.append(' ' * indent + '}')
                                    else:
                                        fixed_lines.append(line)
                                    in_object = False
                                else:
                                    fixed_lines.append(line)

                            # Close any remaining object
                            if in_object:
                                fixed_lines.append('    }')

                            candidate = '\n'.join(fixed_lines)
                            logger.debug(f"Fixed candidate: {repr(candidate[:100])}")

                        # Strategy 2: If it's still malformed, try to extract key-value pairs manually
                        if not candidate.strip().startswith('['):
                            logger.debug("Attempting manual key-value extraction")
                            extracted_objects = []
                            lines = candidate.split('\n')
                            current_obj = {}

                            for line in lines:
                                stripped = line.strip().rstrip(',')
                                if ':' in stripped and '"' in stripped:
                                    try:
                                        # Extract key-value pair
                                        colon_idx = stripped.find(':')
                                        key = stripped[:colon_idx].strip().strip('"')
                                        value = stripped[colon_idx+1:].strip().strip('"')

                                        if key and value:
                                            current_obj[key] = value

                                            # If we have enough fields, save the object
                                            if len(current_obj) >= 2:
                                                if 'priority' not in current_obj:
                                                    current_obj['priority'] = 'Medium'
                                                extracted_objects.append(current_obj.copy())
                                                current_obj = {}
                                    except:
                                        continue

                            # Add any remaining object
                            if len(current_obj) >= 2:
                                if 'priority' not in current_obj:
                                    current_obj['priority'] = 'Medium'
                                extracted_objects.append(current_obj)

                            if extracted_objects:
                                logger.info(f"Manually extracted {len(extracted_objects)} objects")
                                return extracted_objects

                    # Fix common issues with trailing commas
                    candidate = re.sub(r',\s*}', '}', candidate)
                    candidate = re.sub(r',\s*]', ']', candidate)

                    parsed = json.loads(candidate)
                    if isinstance(parsed, list) and len(parsed) > 0:
                        logger.info(f"Successfully parsed JSON with strategy {i+1}")
                        return parsed
                except json.JSONDecodeError as e:
                    logger.debug(f"JSON strategy {i+1} failed: {e}")
                    # Log the specific error for debugging
                    if i == 0:  # Only log for first attempt to avoid spam
                        logger.error(f"JSON parsing error details: {e}")
                        logger.error(f"Problematic JSON candidate: {repr(candidate[:200])}")

                    # Special handling for the specific '\n        "title"' error
                    if '"title"' in str(e) or '"title"' in candidate:
                        logger.warning("Detected title pattern JSON error, attempting emergency parsing")
                        try:
                            # Emergency parsing: extract any recognizable patterns
                            emergency_result = self._emergency_parse_recommendations(candidate)
                            if emergency_result:
                                logger.info("Emergency parsing succeeded")
                                return emergency_result
                        except Exception as emergency_err:
                            logger.debug(f"Emergency parsing also failed: {emergency_err}")
                    continue

            # Fallback: Enhanced text parsing
            logger.info("JSON parsing failed, attempting text parsing")
            recommendations = []

            # Split into lines and look for recommendation patterns
            lines = [line.strip() for line in recommendations_text.split('\n') if line.strip()]

            current_rec = {}
            for line in lines:
                # Look for numbered recommendations or titles
                if re.match(r'^\d+\.', line) or line.lower().startswith('title:') or line.lower().startswith('recommendation'):
                    if current_rec and current_rec.get('title'):
                        recommendations.append(current_rec)

                    # Extract title
                    title = line
                    if ':' in line:
                        title = line.split(':', 1)[1].strip()
                    elif re.match(r'^\d+\.', line):
                        title = re.sub(r'^\d+\.\s*', '', line)

                    current_rec = {
                        'title': title[:100],  # Limit title length
                        'description': '',
                        'priority': 'Medium'
                    }

                # Look for descriptions
                elif current_rec and ('description:' in line.lower() or len(line) > 30):
                    desc = line.replace('Description:', '').replace('description:', '').strip()
                    if desc and len(desc) > 10:
                        current_rec['description'] = desc[:300]  # Limit description length

                # Look for priority
                elif current_rec and 'priority:' in line.lower():
                    priority = line.split(':')[-1].strip().title()
                    if priority in ['High', 'Medium', 'Low']:
                        current_rec['priority'] = priority

            # Add the last recommendation
            if current_rec and current_rec.get('title'):
                recommendations.append(current_rec)

            # If we still don't have recommendations, try to extract from the full text
            if not recommendations:
                # Look for any actionable sentences
                sentences = re.split(r'[.!?]+', recommendations_text)
                for sentence in sentences[:5]:  # Limit to first 5 sentences
                    sentence = sentence.strip()
                    if len(sentence) > 20 and any(word in sentence.lower() for word in ['should', 'recommend', 'focus', 'create', 'consider']):
                        recommendations.append({
                            'title': sentence[:80] + '...' if len(sentence) > 80 else sentence,
                            'description': 'Based on market analysis insights',
                            'priority': 'Medium'
                        })

            # Final fallback
            if not recommendations:
                recommendations = [
                    {
                        'title': 'Content Quality Focus',
                        'description': 'Based on market analysis, focus on high-quality content that matches successful patterns in this niche.',
                        'priority': 'High'
                    }
                ]

            logger.info(f"Text parsing extracted {len(recommendations)} recommendations")
            return recommendations[:5]  # Limit to 5 recommendations

        except Exception as e:
            logger.error(f"Error parsing recommendations: {e}")
            logger.error(f"Error type: {type(e).__name__}")
            logger.error(f"Full recommendations text that failed to parse ({len(recommendations_text)} chars): {repr(recommendations_text[:500])}")

            # Try one more simple fallback - extract any text that looks like recommendations
            try:
                simple_recs = []
                lines = recommendations_text.split('\n')
                for line in lines:
                    line = line.strip()
                    if len(line) > 10 and any(word in line.lower() for word in ['focus', 'create', 'optimize', 'improve', 'develop']):
                        simple_recs.append({
                            'title': line[:60] + '...' if len(line) > 60 else line,
                            'description': 'Extracted from AI analysis',
                            'priority': 'Medium'
                        })
                        if len(simple_recs) >= 3:
                            break

                if simple_recs:
                    logger.info(f"Extracted {len(simple_recs)} recommendations from text fallback")
                    return simple_recs
            except:
                pass

            return [
                {
                    'title': 'Market Analysis Available',
                    'description': 'Detailed market insights are available. Review the analysis sections for strategic guidance.',
                    'priority': 'Medium'
                }
            ]

    def _emergency_parse_recommendations(self, text: str) -> list:
        """Emergency parsing method for severely malformed JSON responses"""
        try:
            logger.info("Attempting emergency parsing of malformed recommendations")

            # Remove all JSON-like formatting and extract raw content
            cleaned = text.replace('[', '').replace(']', '').replace('{', '').replace('}', '')

            # Split into lines and extract key-value pairs
            lines = [line.strip() for line in cleaned.split('\n') if line.strip()]

            recommendations = []
            current_rec = {}

            for line in lines:
                # Skip empty lines and common JSON artifacts
                if not line or line in [',', '```', '```json']:
                    continue

                # Look for key-value patterns
                if ':' in line and '"' in line:
                    # Clean up the line
                    line = line.strip().rstrip(',').strip()

                    try:
                        # Extract key and value
                        colon_idx = line.find(':')
                        key = line[:colon_idx].strip().strip('"').strip()
                        value = line[colon_idx+1:].strip().strip('"').strip()

                        if key and value and key in ['title', 'description', 'priority']:
                            current_rec[key] = value

                            # If we have a complete recommendation, save it
                            if 'title' in current_rec and 'description' in current_rec:
                                if 'priority' not in current_rec:
                                    current_rec['priority'] = 'Medium'
                                recommendations.append(current_rec.copy())
                                current_rec = {}
                    except Exception as parse_err:
                        logger.debug(f"Failed to parse emergency line '{line}': {parse_err}")
                        continue

            # Add any remaining recommendation
            if 'title' in current_rec and 'description' in current_rec:
                if 'priority' not in current_rec:
                    current_rec['priority'] = 'Medium'
                recommendations.append(current_rec)

            if recommendations:
                logger.info(f"Emergency parsing extracted {len(recommendations)} recommendations")
                return recommendations
            else:
                logger.warning("Emergency parsing found no valid recommendations")
                return None

        except Exception as e:
            logger.error(f"Emergency parsing failed: {e}")
            return None

    async def _generate_ai_insights(self, search_results: list, competition_analysis: dict, query: str):
        """Generate AI-powered insights and recommendations"""
        if not self.ai_enabled or not search_results:
            return self._get_mock_ai_insights(query)

        try:
            # Generate cache key for this analysis
            cache_key = self._generate_cache_key(query, search_results)

            # Check for cached insights first
            cached_insights = self._get_cached_insights(cache_key)
            if cached_insights:
                return cached_insights

            logger.info(f"Generating fresh AI insights for query: {query}")

            # Prepare comprehensive context for LLM analysis
            context = self._prepare_market_context(search_results, competition_analysis, query)

            # Generate insights using structured LLM calls
            insights = self._call_llm_for_insights(context, query)
            logger.info(f"Received insights from LLM calls: {list(insights.keys()) if insights else 'None'}")

            # Cache the generated insights
            self._cache_insights(cache_key, insights)

            logger.info(f"Successfully generated AI insights for {query} ({len(search_results)} videos analyzed)")
            return insights

        except Exception as e:
            logger.error(f"Error generating AI insights: {e}")
            logger.info("Falling back to enhanced mock insights")
            return self._get_enhanced_mock_insights(search_results, competition_analysis, query)

    def _get_enhanced_mock_insights(self, search_results: list, competition_analysis: dict, query: str) -> dict:
        """Generate enhanced mock insights based on actual data when AI fails"""
        try:
            # Analyze the actual data to provide better insights
            total_views = sum(v.get('views', 0) for v in search_results)
            avg_views = total_views // len(search_results) if search_results else 0
            top_performers = sorted(search_results, key=lambda x: x.get('performance_score', 0), reverse=True)[:3]

            # Analyze content patterns
            avg_duration = sum(self._duration_to_seconds(v.get('duration', '0:00')) for v in search_results) / len(search_results) if search_results else 0
            long_form_count = sum(1 for v in search_results if self._is_long_form(v.get('duration', '')))
            unique_channels = len(set(v.get('channel_name', '') for v in search_results))

            # Calculate engagement metrics
            total_likes = sum(v.get('likes', 0) for v in search_results)
            total_comments = sum(v.get('comments', 0) for v in search_results)
            avg_engagement = (total_likes + total_comments) / total_views * 100 if total_views > 0 else 0

            # Generate data-driven insights
            market_summary = f"Analysis of {len(search_results)} videos in the {query} market reveals {total_views:,} total views across {unique_channels} unique channels. Average performance is {avg_views:,} views per video with {avg_engagement:.2f}% engagement rate."

            # Generate enhanced insights based on data
            if top_performers:
                top_score = top_performers[0].get('performance_score', 0)
                success_patterns = f"Top performers achieve {top_score:.1f}% performance scores, significantly above market average. "
                success_patterns += f"Analysis shows {long_form_count} of {len(search_results)} videos are long-form content, indicating audience preference for comprehensive coverage."
            else:
                success_patterns = "Limited performance data available for detailed pattern analysis."

            if top_performers:
                top_video = top_performers[0]
                content_strategy = f"Focus on content similar to '{top_video.get('title', 'Unknown')[:50]}...' which achieved {top_video.get('views', 0):,} views with {top_video.get('performance_score', 0):.1f}% performance score. "
                if long_form_count > len(search_results) / 2:
                    content_strategy += f"Market favors long-form content ({long_form_count}/{len(search_results)} videos >10min). Focus on comprehensive, in-depth coverage."
                else:
                    content_strategy += f"Market shows preference for concise content. Focus on delivering value efficiently within {avg_duration/60:.1f} minute format."
            else:
                content_strategy = "Create comprehensive tutorials with strong visual elements and clear step-by-step guidance."

            growth_opportunities = f"Market analysis reveals {unique_channels} active channels competing for audience attention. "
            if avg_engagement > 2.0:
                growth_opportunities += f"High engagement rate of {avg_engagement:.2f}% indicates active, engaged audience ready for quality content."
            else:
                growth_opportunities += f"Engagement rate of {avg_engagement:.2f}% suggests opportunity to create more interactive, engaging content."

            if unique_channels > 20:
                market_risks = f"High competition with {unique_channels} active channels requires unique angles and exceptional quality to achieve breakthrough performance."
            else:
                market_risks = f"Moderate competition with {unique_channels} channels provides opportunity for new entrants with consistent quality content."

            recommendations = [
                {
                    'title': 'Optimize for High-Performing Formats',
                    'description': f'Focus on content formats similar to top performers in the {query} space',
                    'priority': 'High'
                },
                {
                    'title': 'Target Engagement Sweet Spot',
                    'description': f'Aim for content that can achieve above the {avg_views:,} view average',
                    'priority': 'High'
                },
                {
                    'title': 'Consistent Content Strategy',
                    'description': 'Maintain regular posting schedule to build audience retention',
                    'priority': 'Medium'
                }
            ]

            return {
                'market_summary': market_summary,
                'success_patterns': success_patterns,
                'content_strategy': content_strategy,
                'growth_opportunities': growth_opportunities,
                'market_risks': market_risks,
                'recommendations': recommendations
            }

        except Exception as e:
            logger.error(f"Enhanced mock insights generation failed: {e}")
            return self._get_mock_ai_insights(query)

    def _parse_ai_insights(self, response_text: str, query: str):
        """Parse AI response into structured insights"""
        insights = {
            'market_summary': f"The {query} market shows strong engagement with diverse content formats.",
            'success_patterns': "Top performers focus on practical, actionable content with clear value propositions.",
            'content_strategy': "Create comprehensive tutorials with strong visual elements and clear step-by-step guidance.",
            'growth_opportunities': "Significant opportunity in beginner-friendly content and advanced technique tutorials.",
            'market_risks': "High competition requires unique angles and consistent quality to stand out.",
            'recommendations': [
                {
                    'title': 'Focus on Beginner Content',
                    'description': 'Create comprehensive beginner guides as they show highest engagement',
                    'priority': 'High'
                },
                {
                    'title': 'Optimize for Search',
                    'description': 'Use trending keywords and optimize titles for discoverability',
                    'priority': 'High'
                },
                {
                    'title': 'Consistent Upload Schedule',
                    'description': 'Maintain regular posting schedule to build audience retention',
                    'priority': 'Medium'
                }
            ]
        }

        # Try to extract from AI response if properly formatted
        try:
            lines = response_text.split('\n')
            current_section = None

            for line in lines:
                line = line.strip()
                if line.startswith('MARKET_SUMMARY:'):
                    insights['market_summary'] = line.replace('MARKET_SUMMARY:', '').strip()
                elif line.startswith('SUCCESS_PATTERNS:'):
                    insights['success_patterns'] = line.replace('SUCCESS_PATTERNS:', '').strip()
                elif line.startswith('CONTENT_STRATEGY:'):
                    insights['content_strategy'] = line.replace('CONTENT_STRATEGY:', '').strip()
                elif line.startswith('GROWTH_OPPORTUNITIES:'):
                    insights['growth_opportunities'] = line.replace('GROWTH_OPPORTUNITIES:', '').strip()
                elif line.startswith('MARKET_RISKS:'):
                    insights['market_risks'] = line.replace('MARKET_RISKS:', '').strip()
        except:
            pass  # Use default insights if parsing fails

        return insights

    def _get_mock_ai_insights(self, query: str):
        """Generate enhanced mock AI insights when AI is not available"""
        # Generate more varied and realistic insights based on query
        query_lower = query.lower()

        # Customize insights based on query type
        if any(word in query_lower for word in ['tutorial', 'how to', 'guide', 'learn']):
            market_type = "educational"
            content_focus = "step-by-step tutorials and comprehensive guides"
            opportunity = "beginner-friendly content and advanced technique deep-dives"
        elif any(word in query_lower for word in ['review', 'comparison', 'vs', 'best']):
            market_type = "review and comparison"
            content_focus = "honest reviews and detailed product comparisons"
            opportunity = "emerging products and underserved comparison niches"
        elif any(word in query_lower for word in ['news', 'update', 'latest', 'trending']):
            market_type = "news and trends"
            content_focus = "timely updates and trend analysis"
            opportunity = "breaking news coverage and trend predictions"
        else:
            market_type = "general interest"
            content_focus = "engaging and informative content"
            opportunity = "underserved subtopics and unique perspectives"

        return {
            'market_summary': f"The {query} market shows strong engagement in the {market_type} space, with viewers actively seeking {content_focus}. Content performance varies significantly based on timing, quality, and audience targeting.",
            'success_patterns': f"Top performers in the {query} niche consistently deliver high-value {content_focus} with strong visual presentation. Successful channels maintain regular posting schedules and engage actively with their community through comments and social media.",
            'content_strategy': f"Focus on creating comprehensive {content_focus} that addresses specific pain points. Optimize for both search discovery and viewer retention by front-loading value and maintaining engagement throughout the video duration.",
            'growth_opportunities': f"Significant opportunities exist in {opportunity}. The market shows demand for more personalized and niche-specific content that goes beyond surface-level coverage.",
            'market_risks': f"Competition is intensifying in popular {query} topics. Algorithm changes favor fresh content, making consistency crucial. Seasonal fluctuations and trend shifts can impact long-term visibility.",
            'recommendations': [
                {
                    'title': f'Optimize for {query.title()} Keywords',
                    'description': f'Target specific long-tail keywords within the {query} niche to improve search ranking and attract qualified viewers',
                    'priority': 'High'
                },
                {
                    'title': 'Develop Content Series',
                    'description': f'Create multi-part series around {query} topics to increase watch time and build audience loyalty',
                    'priority': 'High'
                },
                {
                    'title': 'Analyze Top Performers',
                    'description': f'Study successful {query} channels to identify content gaps and optimization opportunities',
                    'priority': 'Medium'
                },
                {
                    'title': 'Engage with Trends',
                    'description': f'Monitor trending topics within {query} to create timely, relevant content that captures current interest',
                    'priority': 'Medium'
                }
            ]
        }

    def _compile_market_overview(self, search_results: list):
        """Compile market overview statistics"""
        if not search_results:
            return {
                'total_videos': 0,
                'total_views': 0,
                'unique_channels': 0,
                'avg_engagement': 0
            }

        total_views = sum(v['views'] for v in search_results)
        total_engagement = sum(v['likes'] + v['comments'] for v in search_results)
        unique_channels = len(set(v['channel_id'] for v in search_results))
        avg_engagement = round((total_engagement / total_views * 100) if total_views > 0 else 0, 2)

        return {
            'total_videos': len(search_results),
            'total_views': total_views,
            'unique_channels': unique_channels,
            'avg_engagement': avg_engagement
        }

    def _get_mock_search_results(self, query: str, max_results: int):
        """Generate mock search results when API is not available"""
        mock_results = []
        for i in range(min(max_results, 20)):
            mock_results.append({
                'video_id': f'mock_video_{i}',
                'title': f'{query} Tutorial #{i+1} - Complete Guide',
                'channel_name': f'Creator Channel {i+1}',
                'channel_id': f'mock_channel_{i}',
                'thumbnail': 'https://via.placeholder.com/320x180',
                'channel_thumbnail': 'https://via.placeholder.com/88x88',
                'published_date': '2024-01-15',
                'views': 50000 + (i * 10000),
                'likes': 2000 + (i * 100),
                'comments': 150 + (i * 20),
                'duration': '12:34',
                'performance_score': 85.5 - (i * 2)
            })
        return mock_results

# Routes
@app.get("/", response_class=HTMLResponse)
async def dashboard():
    """Serve premium dashboard homepage"""
    with open("templates/premium-dashboard.html", "r") as f:
        return f.read()

@app.get("/video-analyzer", response_class=HTMLResponse)
async def video_analyzer():
    """Serve premium video analyzer interface"""
    with open("templates/premium-video-analyzer.html", "r") as f:
        return f.read()

@app.get("/channel-analyzer", response_class=HTMLResponse)
async def channel_analyzer():
    """Serve premium channel analyzer interface"""
    with open("templates/premium-channel-analyzer.html", "r") as f:
        return f.read()

@app.get("/market-research", response_class=HTMLResponse)
async def market_research():
    """Serve market research hub interface"""
    with open("templates/market-research-hub.html", "r") as f:
        return f.read()

@app.get("/agent/comment", response_class=HTMLResponse)
async def comment_intelligence_agent():
    """Serve premium comment intelligence agent interface"""
    with open("templates/premium-comment-intelligence.html", "r") as f:
        return f.read()

@app.get("/tools/channel-profile", response_class=HTMLResponse)
async def channel_profile_tool_page():
    """Serve the Channel Profile Exporter tool page"""
    with open("templates/channel_profile_tool.html", "r") as f:
        return f.read()

@app.get("/component-showcase", response_class=HTMLResponse)
async def component_showcase():
    """Serve the component showcase page for testing design system"""
    with open("templates/component-showcase.html", "r") as f:
        return f.read()

@app.get("/css-test", response_class=HTMLResponse)
async def css_test():
    """CSS test page for debugging design system loading"""
    with open("templates/css-test.html", "r") as f:
        return f.read()

@app.get("/templates/components/analysis-cards.html", response_class=HTMLResponse)
async def analysis_cards_template():
    """Serve the analysis cards component template"""
    with open("templates/components/analysis-cards.html", "r") as f:
        return f.read()

@app.get("/templates/components/navigation.html", response_class=HTMLResponse)
async def navigation_template():
    """Serve the navigation component template"""
    with open("templates/components/navigation.html", "r") as f:
        return f.read()

@app.post("/analyze")
async def analyze_channel(request: AnalysisRequest, background_tasks: BackgroundTasks):
    """Start channel analysis"""
    try:
        analysis_id = str(uuid.uuid4())
        
        # Store initial status
        analysis_store[analysis_id] = {
            'status': 'running',
            'data': None,
            'message': 'Analysis in progress...',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        # Start background analysis
        background_tasks.add_task(
            run_background_analysis, 
            analysis_id, 
            request.channel_id,
            request.max_videos,
            request.max_comments
        )
        
        return AnalysisResponse(
            analysis_id=analysis_id,
            status="started",
            message="Analysis started successfully"
        )
        
    except Exception as e:
        logger.error(f"Failed to start analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

async def run_background_analysis(analysis_id: str, channel_id: str, max_videos: int, max_comments: int):
    """Run analysis in background"""
    try:
        crew_ai = get_crewai()
        
        logger.info(f"Starting analysis for {channel_id}")
        result = crew_ai.run_analysis(channel_id, max_videos, max_comments)
        
        if 'error' in result:
            analysis_store[analysis_id] = {
                'status': 'failed',
                'data': None,
                'message': result['error'],
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        else:
            analysis_store[analysis_id] = {
                'status': 'completed',
                'data': result,
                'message': 'Analysis completed successfully',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        logger.info(f"Analysis {analysis_id} completed")
        
    except Exception as e:
        logger.error(f"Analysis {analysis_id} failed: {str(e)}")
        analysis_store[analysis_id] = {
            'status': 'failed',
            'data': None,
            'message': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

@app.post("/analyze-video")
async def analyze_video(request: VideoAnalysisRequest):
    """Analyze a single video with script"""
    try:
        video_ai = get_video_ai()
        
        logger.info(f"Starting video analysis for {request.video_url}")
        result = video_ai.analyze_video(request.video_url, request.script)
        
        if 'error' in result:
            raise HTTPException(status_code=400, detail=result['error'])
        
        # ACTIVATE WORLD-CLASS STYLING AGENTS 🎨
        logger.info("Activating styling agents for beautiful report generation...")
        
        # 🚀 PERFORMANCE MODE: Use fast template-based styling instead of heavy LLM processing
        use_fast_styling = True  # Toggle for performance optimization
        
        if use_fast_styling:
            logger.info("⚡ Using FAST template-based styling for maximum speed...")
            styled_sections = video_ai._apply_fast_template_styling(result)
            processing_time = 0.5  # Template styling is almost instant
            
            # Apply fast styling results to main result
            if 'performance_html' in styled_sections:
                result['performance_analysis']['styled_html'] = styled_sections['performance_html']
            if 'script_html' in styled_sections:
                result['script_analysis']['styled_html'] = styled_sections['script_html']
            if 'seo_html' in styled_sections:
                result['seo_analysis']['styled_html'] = styled_sections['seo_html']
            if 'psychology_html' in styled_sections:
                result['psychology_analysis']['styled_html'] = styled_sections['psychology_html']
            if 'comments_html' in styled_sections:
                result['comment_analysis']['styled_html'] = styled_sections['comments_html']
            if 'thumbnail_html' in styled_sections:
                result['thumbnail_analysis']['styled_html'] = styled_sections['thumbnail_html']
            if 'synthesis_html' in styled_sections:
                result['strategic_synthesis']['styled_html'] = styled_sections['synthesis_html']
            
            logger.info(f"⚡ Fast template styling completed in {processing_time:.1f}s")
            
        else:
            # Original unified styling agent (slower but more customized)
            try:
                logger.info("🚀 Using UNIFIED styling agent for custom formatting...")
                from unified_styling_agent import UnifiedVideoStylingAgent
                
                unified_agent = UnifiedVideoStylingAgent(video_ai.llm_pro)
                unified_result = unified_agent.format_all_analyses(result, result.get('video_data', {}))
            
                # Apply unified styling results to main result
                styled_sections = unified_result['styled_sections']
                
                if 'performance_html' in styled_sections:
                    result['performance_analysis']['styled_html'] = styled_sections['performance_html']
                if 'script_html' in styled_sections:
                    result['script_analysis']['styled_html'] = styled_sections['script_html']
                if 'seo_html' in styled_sections:
                    result['seo_analysis']['styled_html'] = styled_sections['seo_html']
                if 'psychology_html' in styled_sections:
                    result['psychology_analysis']['styled_html'] = styled_sections['psychology_html']
                if 'comments_html' in styled_sections:
                    result['comment_analysis']['styled_html'] = styled_sections['comments_html']
                if 'thumbnail_html' in styled_sections:
                    result['thumbnail_analysis']['styled_html'] = styled_sections['thumbnail_html']
                if 'synthesis_html' in styled_sections:
                    result['strategic_synthesis']['styled_html'] = styled_sections['synthesis_html']
                
                logger.info(f"✅ Unified styling completed in {unified_result['processing_time']:.1f}s")
                logger.info(f"💰 Token savings: {unified_result['token_savings']['savings_percentage']:.0f}%")
                
            except Exception as e:
                # Fallback to original 5-agent system
                logger.warning(f"⚠️  Unified styling failed: {e}")
                logger.info("🔄 Falling back to original 5-agent styling system...")
                
                styling_agents = get_styling_agents(video_ai.llm_pro)
                
                # Original 5-agent styling (fallback)
                if 'performance_analysis' in result and result['performance_analysis']:
                    logger.info("🎨 Performance styling agent working...")
                    raw_text = result['performance_analysis'].get('analysis', '')
                    result['performance_analysis']['styled_html'] = styling_agents.format_performance_report(
                        raw_text, result.get('video_data', {})
                    )
                
                if 'script_analysis' in result and result['script_analysis']:
                    logger.info("🎨 Script styling agent working...")
                    raw_text = result['script_analysis'].get('analysis', '')
                    result['script_analysis']['styled_html'] = styling_agents.format_script_report(
                        raw_text, result.get('video_data', {})
                    )
                
                if 'seo_analysis' in result and result['seo_analysis']:
                    logger.info("🎨 SEO styling agent working...")
                    raw_text = result['seo_analysis'].get('analysis', '')
                    result['seo_analysis']['styled_html'] = styling_agents.format_seo_report(
                        raw_text, result.get('video_data', {})
                    )
                
                if 'psychology_analysis' in result and result['psychology_analysis']:
                    logger.info("🎨 Psychology styling agent working...")
                    raw_text = result['psychology_analysis'].get('analysis', '')
                    result['psychology_analysis']['styled_html'] = styling_agents.format_psychology_report(
                        raw_text, result.get('video_data', {})
                    )
                
                if 'comment_analysis' in result and result['comment_analysis']:
                    logger.info("🎨 Comment styling agent working...")
                    raw_text = result['comment_analysis'].get('analysis', '')
                    result['comment_analysis']['styled_html'] = styling_agents.format_comments_report(
                        raw_text, result.get('video_data', {})
                    )
                
                if 'thumbnail_analysis' in result and result['thumbnail_analysis']:
                    logger.info("🎨 Thumbnail styling agent working...")
                    raw_text = result['thumbnail_analysis'].get('analysis', '')
                    result['thumbnail_analysis']['styled_html'] = styling_agents.format_thumbnail_report(
                        raw_text, result.get('video_data', {})
                    )
                
                if 'strategic_synthesis' in result and result['strategic_synthesis']:
                    logger.info("🎨 Synthesis styling using basic template...")
                    raw_text = result['strategic_synthesis'].get('analysis', '')
                    # Use basic template styling since there's no specific synthesis agent
                    formatted = video_ai._apply_fast_template_styling({'strategic_synthesis': {'analysis': raw_text}})
                    result['strategic_synthesis']['styled_html'] = formatted.get('synthesis_html', raw_text)
                
                logger.info("✅ Fallback styling completed")
        
        logger.info(f"Video analysis completed successfully")
        
        # 🛡️ FINAL JSON SAFETY CHECK - Ensure all analysis fields are clean
        safe_result = {}
        for key, value in result.items():
            if key in ['performance_analysis', 'script_analysis', 'seo_analysis', 'psychology_analysis', 'comment_analysis', 'thumbnail_analysis', 'strategic_synthesis']:
                if isinstance(value, dict) and 'analysis' in value:
                    # Double-check that analysis field is clean
                    safe_result[key] = {
                        'analysis': str(value['analysis']),
                        'styled_html': value.get('styled_html', '')
                    }
                else:
                    safe_result[key] = value
            else:
                safe_result[key] = value
        
        return safe_result
        
    except Exception as e:
        logger.error(f"Video analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/analyze-standalone")
async def analyze_standalone(request: StandaloneAgentRequest):
    """Analyze a video with selected standalone agents"""
    try:
        # Initialize the standalone agent interface
        agents = StandaloneAgentInterface(use_sequential_thinking=request.use_sequential_thinking)
        
        # Get video data first (we need this for all agents)
        video_ai = get_video_ai()
        logger.info(f"Starting standalone analysis for {request.video_url} with agents: {request.selected_agents}")
        
        # Get video data using existing method
        video_data = video_ai._get_video_data_from_url(request.video_url)
        if 'error' in video_data:
            raise HTTPException(status_code=400, detail=video_data['error'])
        
        # Handle script - use the same logic as main analyze_video
        script = video_ai._prepare_final_transcript(video_data, request.script)
        logger.info("📝 Prepared final transcript for standalone analysis")
        
        # Initialize results
        results = {
            'video_data': video_data,
            'selected_agents': request.selected_agents,
            'use_sequential_thinking': request.use_sequential_thinking
        }
        
        # Run selected agents in dependency order
        available_agents = ['performance', 'script', 'seo', 'psychology', 'comment', 'synthesis']
        agent_results = {}
        
        for agent_name in available_agents:
            if agent_name in request.selected_agents:
                logger.info(f"🤖 Running {agent_name} agent...")
                
                try:
                    if agent_name == 'performance':
                        result = agents.run_single_agent('performance', video_data=video_data)
                        agent_results['performance'] = result
                        results['performance_analysis'] = result
                        
                    elif agent_name == 'script':
                        result = agents.run_single_agent('script', 
                            video_data=video_data, 
                            script=script,
                            performance_data=agent_results.get('performance')
                        )
                        agent_results['script'] = result
                        results['script_analysis'] = result
                        
                    elif agent_name == 'seo':
                        result = agents.run_single_agent('seo',
                            video_data=video_data,
                            script_analysis=agent_results.get('script'),
                            performance_data=agent_results.get('performance')
                        )
                        agent_results['seo'] = result
                        results['seo_analysis'] = result
                        
                    elif agent_name == 'psychology':
                        result = agents.run_single_agent('psychology',
                            video_data=video_data,
                            script_analysis=agent_results.get('script'),
                            performance_data=agent_results.get('performance')
                        )
                        agent_results['psychology'] = result
                        results['psychology_analysis'] = result
                        
                    elif agent_name == 'comment':
                        result = agents.run_single_agent('comment',
                            video_data=video_data,
                            script_analysis=agent_results.get('script'),
                            performance_data=agent_results.get('performance'),
                            seo_data=agent_results.get('seo'),
                            psychology_data=agent_results.get('psychology')
                        )
                        agent_results['comment'] = result
                        results['comment_analysis'] = result
                        
                    elif agent_name == 'synthesis':
                        # Only run synthesis if we have other analyses
                        if agent_results:
                            result = agents.run_single_agent('synthesis',
                                all_analyses=agent_results,
                                video_data=video_data,
                                channel_context=video_data.get('channel_context')
                            )
                            agent_results['synthesis'] = result
                            results['strategic_synthesis'] = result
                        else:
                            logger.warning("⚠️ Synthesis agent requires other agent results")
                    
                    logger.info(f"✅ {agent_name} agent completed")
                    
                except Exception as e:
                    logger.error(f"❌ {agent_name} agent failed: {str(e)}")
                    results[f'{agent_name}_error'] = str(e)
        
        logger.info(f"Standalone analysis completed for {len(request.selected_agents)} agents")
        return results
        
    except Exception as e:
        logger.error(f"Standalone analysis failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/results/{analysis_id}")
async def get_results(analysis_id: str):
    """Get analysis results"""
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    return analysis_store[analysis_id]

@app.get("/health")
async def health():
    """Health check"""
    return {"status": "healthy", "timestamp": datetime.now(timezone.utc).isoformat()}

@app.post("/api/tools/channel_profile")
async def get_channel_profile(request: ToolRequest):
    """Run the Channel Profile Exporter tool"""
    youtube_api_key = os.getenv("YOUTUBE_API_KEY")
    if not youtube_api_key:
        raise HTTPException(status_code=500, detail="YOUTUBE_API_KEY not configured.")
    
    try:
        exporter = ChannelProfileExporter(youtube_api_key=youtube_api_key)
        profile_data = exporter.get_profile(request.channel_id, request.max_videos)
        if 'error' in profile_data:
            raise HTTPException(status_code=400, detail=profile_data['error'])
        return profile_data
    except Exception as e:
        logger.error(f"Channel Profile tool error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/export/channel")
async def export_channel_data(request: ExportRequest):
    """Export channel analysis data in JSON, CSV, or PDF format"""
    try:
        # Validate format
        if request.format_type not in ['json', 'csv', 'pdf']:
            raise HTTPException(status_code=400, detail="Format must be 'json', 'csv', or 'pdf'")
        
        # Export the data using our enhanced export system
        response = export_system.export_channel_data(
            data=request.data,
            format_type=request.format_type,
            filename=request.filename
        )
        
        logger.info(f"✅ Channel data exported as {request.format_type.upper()}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Export error: {e}")
        raise HTTPException(status_code=500, detail=f"Export failed: {str(e)}")

@app.post("/api/export/video_analysis")
async def export_video_analysis_data(request: ExportRequest):
    """Export video analysis data in JSON, CSV, or PDF format"""
    try:
        # Validate format
        if request.format_type not in ['json', 'csv', 'pdf']:
            raise HTTPException(status_code=400, detail="Format must be 'json', 'csv', or 'pdf'")
        
        # Export the video analysis data using our enhanced export system
        response = export_system.export_video_analysis(
            analysis_data=request.data,
            format_type=request.format_type,
            filename=request.filename
        )
        
        logger.info(f"✅ Video analysis exported as {request.format_type.upper()}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Video analysis export error: {e}")
        raise HTTPException(status_code=500, detail=f"Export failed: {str(e)}")

@app.get("/api/export/formats")
async def get_export_formats():
    """Get available export formats and their descriptions"""
    return {
        "formats": [
            {
                "type": "json",
                "name": "JSON Export",
                "description": "Complete data export with metadata and analysis summary",
                "file_extension": ".json",
                "suitable_for": "developers, data analysis, API integration"
            },
            {
                "type": "csv",
                "name": "CSV Export", 
                "description": "Spreadsheet-friendly format with channel overview and video library",
                "file_extension": ".csv",
                "suitable_for": "spreadsheet analysis, data visualization, reporting"
            },
            {
                "type": "pdf",
                "name": "PDF Report",
                "description": "Professional report format with executive summary",
                "file_extension": ".txt",  # Note: Basic implementation returns text format
                "suitable_for": "presentations, client reports, sharing"
            }
        ]
    }

@app.post("/api/analyze/upload-schedule")
async def analyze_upload_schedule(request: ToolRequest):
    """Analyze channel upload schedule patterns and provide optimization insights"""
    youtube_api_key = os.getenv("YOUTUBE_API_KEY")
    if not youtube_api_key:
        raise HTTPException(status_code=500, detail="YOUTUBE_API_KEY not configured.")
    
    try:
        # Get channel data first
        exporter = ChannelProfileExporter(youtube_api_key=youtube_api_key)
        profile_data = exporter.get_profile(request.channel_id, request.max_videos)
        
        if 'error' in profile_data:
            raise HTTPException(status_code=400, detail=profile_data['error'])
        
        # Analyze upload schedule
        schedule_analysis = upload_analyzer.analyze_upload_schedule(
            video_library=profile_data.get('video_library', []),
            channel_profile=profile_data.get('channel_profile', {})
        )
        
        if 'error' in schedule_analysis:
            raise HTTPException(status_code=400, detail=schedule_analysis['error'])
        
        # Combine channel info with schedule analysis
        result = {
            'channel_info': {
                'title': profile_data['channel_profile'].get('title', 'Unknown'),
                'subscriber_count': profile_data['channel_profile'].get('statistics', {}).get('subscriberCount', 0),
                'video_count': len(profile_data.get('video_library', [])),
                'analyzed_videos': request.max_videos
            },
            'schedule_intelligence': schedule_analysis,
            'analysis_timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        logger.info(f"✅ Upload schedule analysis completed for {result['channel_info']['title']}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Upload schedule analysis error: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/api/analyze/titles")
async def analyze_channel_titles(request: ToolRequest):
    """Analyze channel title patterns and provide optimization insights"""
    youtube_api_key = os.getenv("YOUTUBE_API_KEY")
    if not youtube_api_key:
        raise HTTPException(status_code=500, detail="YOUTUBE_API_KEY not configured.")
    
    try:
        # Get channel data first
        exporter = ChannelProfileExporter(youtube_api_key=youtube_api_key)
        profile_data = exporter.get_profile(request.channel_id, request.max_videos)
        
        if 'error' in profile_data:
            raise HTTPException(status_code=400, detail=profile_data['error'])
        
        # Analyze titles
        title_analysis = title_analyzer.analyze_channel_titles(
            video_library=profile_data.get('video_library', []),
            channel_profile=profile_data.get('channel_profile', {})
        )
        
        if 'error' in title_analysis:
            raise HTTPException(status_code=400, detail=title_analysis['error'])
        
        # Combine channel info with title analysis
        result = {
            'channel_info': {
                'title': profile_data['channel_profile'].get('title', 'Unknown'),
                'subscriber_count': profile_data['channel_profile'].get('statistics', {}).get('subscriberCount', 0),
                'video_count': len(profile_data.get('video_library', [])),
                'analyzed_videos': request.max_videos
            },
            'title_intelligence': title_analysis,
            'analysis_timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        logger.info(f"✅ Title analysis completed for {result['channel_info']['title']}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Title analysis error: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/api/market-research")
async def market_research_analysis(request: dict):
    """Perform comprehensive market research analysis"""
    try:
        query = request.get('query', '').strip()
        if not query:
            raise HTTPException(status_code=400, detail="Query is required")

        time_range = request.get('time_range', 'month')
        max_results = request.get('max_results', 50)
        sort_by = request.get('sort_by', 'relevance')

        # Initialize market research analyzer
        market_analyzer = MarketResearchAnalyzer()

        # Perform comprehensive analysis
        result = await market_analyzer.analyze_market(
            query=query,
            time_range=time_range,
            max_results=max_results,
            sort_by=sort_by
        )

        return result

    except Exception as e:
        logger.error(f"Market research analysis error: {e}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    import os

    # Change to the script's directory to ensure static files are found
    os.chdir(os.path.dirname(os.path.abspath(__file__)))

    uvicorn.run(app, host="0.0.0.0", port=8003)