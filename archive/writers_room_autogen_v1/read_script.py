#!/usr/bin/env python3
"""
Extract and display just the script content from Writers' Room results
"""

import json
import os

def extract_script_content():
    """Extract and display the complete script in readable format"""
    
    script_file = "writers_room/outputs/complete_workflow/rome_sasanid_phoenix_script.json"
    
    if not os.path.exists(script_file):
        print(f"❌ Script file not found: {script_file}")
        return
    
    with open(script_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("🎬 COMPLETE VIDEO SCRIPT")
    print("=" * 80)
    print(f"📺 Title: {data.get('topic', 'Unknown')}")
    print(f"⏱️  Execution Time: {data['execution_time']:.1f} seconds")
    print(f"🎯 Quality Score: {data['collaboration_metrics']['final_quality_score']}")
    print("=" * 80)
    
    # Extract the narrative script from StoryWeaver section
    final_script = data['final_script']
    
    # Find the StoryWeaver section with the full narrative
    if 'INTRO' in final_script:
        # Extract the detailed narrative
        start = final_script.find('INTRO')
        end = final_script.find('[End credits roll')
        if end == -1:
            end = len(final_script)
        
        narrative = final_script[start:end]
        
        print("\n📝 COMPLETE NARRATIVE SCRIPT:")
        print("-" * 80)
        print(narrative)
        print("-" * 80)
    
    # Extract conversation transcript for the complete script
    if 'conversation_transcript' in data:
        for message in data['conversation_transcript']:
            if message['name'] == 'StoryWeaver' and 'INTRO' in message['content']:
                print("\n📖 STORYWEAVER'S COMPLETE SCRIPT:")
                print("-" * 80)
                print(message['content'])
                print("-" * 80)
                break
    
    print(f"\n📄 Full file location: {script_file}")

if __name__ == "__main__":
    extract_script_content()