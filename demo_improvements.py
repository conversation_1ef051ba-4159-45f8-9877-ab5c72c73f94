#!/usr/bin/env python3
"""
Visual Demo of Channel Analyzer Improvements
"""
import requests
import json

def demo_improvements():
    """Demonstrate the improvements with real data"""
    
    print("🎬 CHANNEL ANALYZER IMPROVEMENTS DEMO")
    print("=" * 60)
    
    # Get MrBeast data
    response = requests.post("http://localhost:8003/api/tools/channel_profile", 
                           json={"channel_id": "@MrBeast", "max_videos": 10})
    
    if response.status_code != 200:
        print("❌ Failed to get data")
        return
    
    data = response.json()
    channel = data['channel_profile']
    videos = data['video_library']
    
    print(f"📺 Channel: {channel['title']}")
    print(f"👥 Subscribers: {int(channel['statistics']['subscriberCount']):,}")
    
    # IMPROVEMENT 1: Full Description Display
    print(f"\n✨ IMPROVEMENT 1: Full Channel Description")
    print("-" * 40)
    description = channel.get('description', '')
    print(f"BEFORE: Only first 200 characters shown with '...'")
    print(f"AFTER:  Full description ({len(description)} characters)")
    print(f"")
    print(f"Full Description:")
    print(f'"{description}"')
    
    # IMPROVEMENT 2: Content Type Analysis
    print(f"\n✨ IMPROVEMENT 2: Shorts vs Longform Intelligence")
    print("-" * 40)
    
    shorts = []
    longform = []
    
    for video in videos:
        duration = video.get('duration', '')
        views = int(video.get('statistics', {}).get('viewCount', 0))
        title = video.get('title', '')
        
        if duration:
            import re
            match = re.match(r'PT(?:(\d+)M)?(?:(\d+)S)?', duration)
            if match:
                minutes = int(match.group(1) or 0)
                seconds = int(match.group(2) or 0)
                total_seconds = minutes * 60 + seconds
                
                video_data = {
                    'title': title,
                    'duration': duration,
                    'views': views,
                    'seconds': total_seconds
                }
                
                if total_seconds <= 60:
                    shorts.append(video_data)
                else:
                    longform.append(video_data)
    
    print(f"📊 CONTENT BREAKDOWN:")
    print(f"   🎬 Longform Videos: {len(longform)}")
    print(f"   📱 Shorts: {len(shorts)}")
    print(f"")
    
    # Show top performing of each type
    if shorts:
        top_short = max(shorts, key=lambda x: x['views'])
        print(f"🔥 TOP PERFORMING SHORT:")
        print(f"   Title: {top_short['title'][:50]}...")
        print(f"   Duration: {top_short['duration']} ({top_short['seconds']}s)")
        print(f"   Views: {top_short['views']:,}")
    
    if longform:
        top_longform = max(longform, key=lambda x: x['views'])
        print(f"")
        print(f"🔥 TOP PERFORMING LONGFORM:")
        print(f"   Title: {top_longform['title'][:50]}...")
        print(f"   Duration: {top_longform['duration']}")
        print(f"   Views: {top_longform['views']:,}")
    
    # Performance comparison
    if shorts and longform:
        avg_short_views = sum(s['views'] for s in shorts) / len(shorts)
        avg_longform_views = sum(l['views'] for l in longform) / len(longform)
        
        print(f"")
        print(f"📈 PERFORMANCE COMPARISON:")
        print(f"   📱 Shorts avg: {avg_short_views:,.0f} views")
        print(f"   🎬 Longform avg: {avg_longform_views:,.0f} views")
        
        if avg_short_views > avg_longform_views:
            ratio = avg_short_views / avg_longform_views
            print(f"   🎯 Shorts outperform by {ratio:.1f}x")
        else:
            ratio = avg_longform_views / avg_short_views
            print(f"   🎯 Longform outperform by {ratio:.1f}x")
    
    print(f"\n🎨 UI ENHANCEMENTS:")
    print(f"   ✅ Visual 'Shorts' badges on thumbnails")
    print(f"   ✅ Orange duration indicators for shorts")
    print(f"   ✅ Content filter dropdown (All/Longform/Shorts)")
    print(f"   ✅ Real-time content type statistics")
    print(f"   ✅ Smart filtering and sorting")
    
    print(f"\n💡 STRATEGIC INSIGHTS:")
    print(f"   • MrBeast uses {len(shorts)}/{len(videos)} shorts strategy")
    print(f"   • Can analyze performance patterns by content type")
    print(f"   • Helps optimize content mix for maximum reach")
    print(f"   • Enables platform-specific strategy development")
    
    print(f"\n🏆 BUSINESS VALUE:")
    print(f"   💰 Saves hours of manual content categorization")
    print(f"   📊 Enables data-driven content strategy decisions")
    print(f"   🎯 Professional creator tool differentiation")
    print(f"   🚀 Justifies premium $99/month pricing")

if __name__ == "__main__":
    demo_improvements()