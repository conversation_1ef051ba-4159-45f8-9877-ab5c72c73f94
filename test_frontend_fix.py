#!/usr/bin/env python3
"""
Test script to verify the frontend JavaScript fix for market research
"""

import requests
import json
import time

def test_frontend_integration():
    """Test the complete frontend-backend integration"""
    print("🔍 Frontend Integration Test")
    print("=" * 50)
    
    try:
        # Test the market research API endpoint
        print("📡 Testing API endpoint...")
        response = requests.post(
            "http://localhost:8003/api/market-research",
            json={
                "query": "cooking tips",
                "time_range": "week", 
                "max_results": 5,
                "sort_by": "relevance"
            },
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API Response: SUCCESS")
            
            # Check the data structure that the frontend expects
            print("\n📊 Data Structure Analysis:")
            
            # Check basic stats
            if 'basic_stats' in data:
                stats = data['basic_stats']
                print(f"   📈 Total Videos: {stats.get('total_videos', 'N/A')}")
                print(f"   👁️  Total Views: {stats.get('total_views', 'N/A')}")
                print(f"   📺 Unique Channels: {stats.get('unique_channels', 'N/A')}")
            
            # Check trending content (this is where the error was occurring)
            if 'trending_content' in data:
                trending = data['trending_content']
                print(f"   🔥 Trending Videos: {len(trending.get('videos', []))}")
                
                # Check if video data has the required fields
                videos = trending.get('videos', [])
                if videos:
                    first_video = videos[0]
                    print(f"   📹 Sample Video Data:")
                    print(f"      - Views: {first_video.get('views', 'N/A')} (type: {type(first_video.get('views'))})")
                    print(f"      - Likes: {first_video.get('likes', 'N/A')} (type: {type(first_video.get('likes'))})")
                    print(f"      - Comments: {first_video.get('comments', 'N/A')} (type: {type(first_video.get('comments'))})")
            
            # Check content opportunities (where the formatNumber error occurred)
            if 'content_opportunities' in data:
                opportunities = data['content_opportunities']
                print(f"   🎯 Content Opportunities Available: {bool(opportunities)}")
                
                if opportunities and 'trending_topics' in opportunities:
                    topics = opportunities['trending_topics']
                    print(f"   📊 Trending Topics: {len(topics)}")
                    
                    if topics:
                        first_topic = topics[0]
                        print(f"   📈 Sample Topic Data:")
                        print(f"      - Search Volume: {first_topic.get('search_volume', 'N/A')} (type: {type(first_topic.get('search_volume'))})")
                        print(f"      - Trend Percentage: {first_topic.get('trend_percentage', 'N/A')}")
            
            # Check competition analysis
            if 'competition_analysis' in data:
                competition = data['competition_analysis']
                if 'top_channels' in competition:
                    channels = competition['top_channels']
                    print(f"   🏆 Top Channels: {len(channels)}")
                    
                    if channels:
                        first_channel = channels[0]
                        print(f"   📺 Sample Channel Data:")
                        print(f"      - Subscribers: {first_channel.get('subscribers', 'N/A')} (type: {type(first_channel.get('subscribers'))})")
                        print(f"      - Avg Views: {first_channel.get('avg_views', 'N/A')} (type: {type(first_channel.get('avg_views'))})")
            
            # Check AI insights (our recent fix)
            if 'ai_insights' in data:
                ai_insights = data['ai_insights']
                recommendations = ai_insights.get('recommendations', [])
                print(f"   🤖 AI Recommendations: {len(recommendations)} items")
                
                if recommendations:
                    print(f"   ✅ Recommendations working: {recommendations[0].get('title', 'No title')[:50]}...")
            
            print(f"\n🎉 Frontend should now display data correctly!")
            print(f"🌐 Visit: http://localhost:8003/market-research")
            print(f"🔍 Search for 'cooking tips' to test the fix")
            
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Test Failed: {e}")

if __name__ == "__main__":
    test_frontend_integration()
