"""
STYLING AGENTS - WORLD-CLASS REPORT DESIGNERS
Master agents that transform raw analysis into stunning visual reports
"""

from crewai import Agent, Task, Crew
from crewai.llm import LLM
import os
import re


class VideoAnalyzerStylingAgents:
    """Master styling agents for each analysis tab"""
    
    def __init__(self, llm):
        self.llm = llm
        
        # Load the complete design system
        with open('static/styles/video-analyzer-design-system.css', 'r') as f:
            self.design_system = f.read()
    
    def format_performance_report(self, raw_analysis, video_data):
        """Performance Intelligence - Metrics & Benchmarks Master"""
        
        agent = Agent(
            role="Performance Report Design Master",
            goal="Transform performance analysis into stunning visual dashboards with metrics, charts, and benchmarks",
            backstory="""You are a world-class data visualization designer specializing in performance analytics. 
            You create dashboards that executives and data analysts dream about - clean, scannable, and instantly 
            revealing insights. Your designs have been featured in top tech companies and SaaS products.""",
            llm=self.llm,
            verbose=True
        )
        
        task = Task(
            description=f"""
Transform performance analysis into a COMPETITIVE INTELLIGENCE DASHBOARD:

## RAW ANALYSIS DATA:
{raw_analysis}

## VIDEO METADATA:
Title: {video_data.get('title', 'N/A')}
Views: {video_data.get('statistics', {}).get('viewCount', '0')}
Likes: {video_data.get('statistics', {}).get('likeCount', '0')}
Comments: {video_data.get('statistics', {}).get('commentCount', '0')}
Duration: {video_data.get('contentDetails', {}).get('duration', 'N/A')}

## YOUR MISSION:
Create a professional performance dashboard that shows the user exactly HOW this video succeeded and WHAT they can replicate. Focus on actionable competitive intelligence, not just metrics display.

## DESIGN REQUIREMENTS:

### 1. HERO COMPETITIVE INTELLIGENCE SECTION:
```html
<div class="hero-grade-section">
    <div class="grade-display">[Letter Grade]</div>
    <div class="competitive-insight">
        <h3>SUCCESS PATTERN IDENTIFIED</h3>
        <p class="pattern-description">[Key success pattern explanation]</p>
    </div>
    <div class="performance-tier">Top [X]% Performance Tier</div>
</div>
```

### 2. METRICS INTELLIGENCE GRID:
Create cards showing WHAT→WHY→HOW→WHAT IF for each metric:
```html
<div class="metrics-intelligence-grid">
    <div class="metric-card">
        <div class="metric-header">
            <span class="metric-value">[Value]</span>
            <span class="metric-name">[Metric Name]</span>
        </div>
        <div class="intelligence-breakdown">
            <div class="what-section">🎯 WHAT: [Finding]</div>
            <div class="why-section">🧠 WHY: [Mechanism]</div>
            <div class="how-section">⚙️ HOW: [Technique]</div>
            <div class="what-if-section">🚀 WHAT IF: [Implementation]</div>
        </div>
    </div>
</div>
```

### 3. COMPETITIVE ADVANTAGE SECTION:
```html
<div class="competitive-advantages">
    <h3>🏆 REPLICABLE SUCCESS PATTERNS</h3>
    <div class="advantage-cards">
        [Pattern cards with implementation guides]
    </div>
</div>
```

### 4. IMPLEMENTATION ROADMAP:
```html
<div class="implementation-roadmap">
    <h3>📋 YOUR IMPLEMENTATION PLAN</h3>
    <div class="roadmap-steps">
        <div class="step">
            <div class="step-number">1</div>
            <div class="step-content">[Specific action]</div>
            <div class="expected-impact">[Projected result]</div>
        </div>
    </div>
</div>
```

## STYLING REQUIREMENTS:
- Competitive intelligence focus (not just analytics)
- Action-oriented language throughout
- Clear implementation guidance
- Professional SaaS dashboard aesthetic
- Mobile-responsive design

Transform performance data into COMPETITIVE STRATEGY INTELLIGENCE.

## AVAILABLE DESIGN SYSTEM:
{self.design_system[:2000]}...

## OUTPUT REQUIREMENTS:
- PURE HTML using ONLY the design system classes above
- NO custom CSS - only the provided classes
- RESPONSIVE and BEAUTIFUL on all screen sizes
- ANIMATIONS and MICRO-INTERACTIONS included
- PROFESSIONAL SaaS-grade visual quality

EXTRACT every metric, grade, and insight from the raw analysis. Make it SCANNABLE and STUNNING.
""",
            agent=agent,
            expected_output="Complete HTML performance dashboard using the design system"
        )
        
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()
        
        return self._clean_html_output(str(result))
    
    def format_script_report(self, raw_analysis, video_data):
        """Script Forensics - Structure & Psychology Master"""
        
        agent = Agent(
            role="Script Analysis Design Master", 
            goal="Transform script analysis into beautiful visual breakdowns showing structure, triggers, and psychology",
            backstory="""You are an expert in designing educational content and script breakdowns. Your visualizations 
            help content creators understand the psychology and structure of successful scripts. You've designed 
            interfaces for major content creation platforms and online learning companies.""",
            llm=self.llm,
            verbose=True
        )
        
        task = Task(
            description=f"""
Transform script analysis into an EDUCATIONAL BREAKDOWN DASHBOARD:

## RAW ANALYSIS DATA:
{raw_analysis}

## VIDEO METADATA:
{video_data}

## YOUR MISSION:
Create a script forensics dashboard that teaches the user exactly HOW successful scripts work and HOW to replicate these patterns.

## DESIGN REQUIREMENTS:

### 1. SCRIPT METRICS OVERVIEW (MANDATORY):
```html
<div class="metrics-grid stagger-children">
    <div class="metric-card fade-in">
        <div class="metric-header">
            <i class="fas fa-file-word metric-icon"></i>
            <span class="metric-trend trend-excellent">EXCELLENT</span>
        </div>
        <div class="metric-value">[WORD COUNT]</div>
        <div class="metric-label">Word Count</div>
        <div class="metric-description">Optimal length</div>
    </div>
    <!-- SPEAKING PACE, DURATION, ENGAGEMENT SCORE -->
</div>
```

### 2. PSYCHOLOGICAL TRIGGERS ANALYSIS (MANDATORY):
```html
<div class="grid-2">
    <div class="insight-card status-high">
        <div class="insight-header">
            <i class="fas fa-eye insight-icon"></i>
            <h3 class="insight-title">Curiosity & Anticipation</h3>
        </div>
        <div class="insight-content">
            <div class="progress-container">
                <div class="progress-header">
                    <span class="progress-label">Mystery Building</span>
                    <span class="progress-value">STRONG</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 85%"></div>
                </div>
            </div>
            [Additional trigger analysis]
        </div>
    </div>
    <!-- REPEAT FOR AUTHORITY, SOCIAL PROOF, etc. -->
</div>
```

### 3. SCRIPT STRUCTURE BREAKDOWN (MANDATORY):
```html
<div class="grid-3">
    <div class="card card-gradient">
        <div class="p-lg">
            <h3 class="heading-4 text-primary">Opening Hook</h3>
            <div class="progress-container mt-md">
                <div class="progress-header">
                    <span class="progress-label">Effectiveness</span>
                    <span class="progress-value">95%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 95%"></div>
                </div>
            </div>
            <div class="text-caption mt-sm">
                [Structure details]
            </div>
        </div>
    </div>
    <!-- CONTENT FLOW, RETENTION ELEMENTS -->
</div>
```

### 4. FINAL SCRIPT GRADE (MANDATORY):
```html
<div class="grade-hero mt-xl">
    <div class="grade-circle grade-a">
        <span class="grade-text">A+</span>
    </div>
    <h2 class="grade-title">Script Performance Grade</h2>
    <p class="grade-subtitle">[Grade explanation]</p>
</div>
```

## SCRIPT-SPECIFIC ICONS:
- fa-file-word (word count)
- fa-tachometer-alt (pace)
- fa-clock (duration)  
- fa-brain (engagement)
- fa-eye (curiosity)
- fa-users (social proof)
- fa-sitemap (structure)

## CONTENT ADAPTATION:
- **Extract** word count, speaking pace, duration from analysis
- **Identify** psychological triggers and show progress bars
- **Visualize** script structure with effectiveness percentages
- **Highlight** strengths vs optimization opportunities

MAKE SCRIPT ANALYSIS VISUAL AND EDUCATIONAL. Help creators understand WHY it works.

## AVAILABLE DESIGN SYSTEM:
{self.design_system[:2000]}...

OUTPUT: Pure HTML using design system classes. Make it EDUCATIONAL and BEAUTIFUL.
""",
            agent=agent,
            expected_output="Complete HTML script analysis dashboard"
        )
        
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()
        
        return self._clean_html_output(str(result))
    
    def format_seo_report(self, raw_analysis, video_data):
        """SEO & Discoverability - Search Optimization Master"""
        
        agent = Agent(
            role="SEO Analysis Design Master",
            goal="Transform SEO analysis into keyword-rich visual dashboards with optimization insights",
            backstory="""You are an SEO visualization expert who creates dashboards for digital marketing agencies. 
            Your designs help marketers quickly identify opportunities and track search performance. You understand 
            keyword research, search intent, and competitive analysis visualization.""",
            llm=self.llm,
            verbose=True
        )
        
        task = Task(
            description=f"""
TRANSFORM this SEO analysis into a PROFESSIONAL search optimization dashboard:

## RAW ANALYSIS DATA:
{raw_analysis}

## VIDEO METADATA:
{video_data}

## YOUR MISSION:
Create an SEO DASHBOARD that digital marketers would pay premium for.

## DESIGN REQUIREMENTS:

### 1. SEO SCORE OVERVIEW (MANDATORY):
```html
<div class="grade-hero">
    <div class="grade-circle grade-b">
        <span class="grade-text">B+</span>
    </div>
    <h2 class="grade-title">Overall SEO Score</h2>
    <p class="grade-subtitle">Strong optimization with room for improvement</p>
    
    <div class="metrics-grid mt-lg">
        <div class="metric-card">
            <div class="metric-value text-excellent">85%</div>
            <div class="metric-label">Title Optimization</div>
            <div class="metric-description">Excellent</div>
        </div>
        <!-- DESCRIPTION, KEYWORDS, DISCOVERABILITY -->
    </div>
</div>
```

### 2. KEYWORD ANALYSIS (MANDATORY):
```html
<div class="grid-2">
    <div class="card p-lg">
        <h3 class="heading-3 mb-md">
            <i class="fas fa-key text-primary mr-sm"></i>
            Primary Keywords
        </h3>
        <div class="keywords-container">
            <span class="keyword-tag high-volume">[KEYWORD]</span>
            <span class="keyword-tag medium-volume">[KEYWORD]</span>
            <span class="keyword-tag low-competition">[KEYWORD]</span>
        </div>
        [Volume and competition data]
    </div>
    
    <div class="card p-lg">
        <h3 class="heading-3 mb-md">
            <i class="fas fa-tags text-primary mr-sm"></i>
            Long-tail Keywords
        </h3>
        [Optimized vs needs work status]
    </div>
</div>
```

### 3. OPTIMIZATION ANALYSIS (MANDATORY):
```html
<div class="card p-lg">
    <h3 class="heading-3 mb-md">
        <i class="fas fa-edit text-primary mr-sm"></i>
        Content Optimization Analysis
    </h3>
    <div class="grid-2">
        <div>
            <h4 class="heading-4 mb-sm">Title Analysis</h4>
            [Character count, keyword placement, emotional triggers]
        </div>
        <div>
            <h4 class="heading-4 mb-sm">Description Analysis</h4>
            [Character count, keyword density, call-to-action]
        </div>
    </div>
</div>
```

### 4. PRIORITY RECOMMENDATIONS (MANDATORY):
```html
<div class="grid-2">
    <div class="card p-lg">
        <h3 class="heading-4 text-excellent mb-md">
            <i class="fas fa-arrow-up mr-sm"></i>
            High Impact
        </h3>
        <div class="space-y-sm">
            <div class="insight-card status-high">
                [High impact recommendation]
            </div>
        </div>
    </div>
    
    <div class="card p-lg">
        <h3 class="heading-4 text-warning mb-md">
            <i class="fas fa-arrow-right mr-sm"></i>
            Medium Impact
        </h3>
        [Medium impact recommendations]
    </div>
</div>
```

## SEO-SPECIFIC FEATURES:
- **EXTRACT** keywords from analysis and create keyword-tag elements
- **SHOW** search volumes (high-volume, medium-volume, low-competition classes)
- **VISUALIZE** optimization scores with progress bars
- **CATEGORIZE** recommendations by impact level

## SEO ICONS:
- fa-search (main SEO)
- fa-key (primary keywords)
- fa-tags (long-tail keywords)
- fa-edit (optimization)
- fa-chart-bar (analytics)
- fa-target (targeting)

MAKE SEO DATA SCANNABLE. Help marketers spot opportunities instantly.

## AVAILABLE DESIGN SYSTEM:
{self.design_system[:2000]}...

OUTPUT: Pure HTML using design system classes. Make it ACTIONABLE and PROFESSIONAL.
""",
            agent=agent,
            expected_output="Complete HTML SEO analysis dashboard"
        )
        
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()
        
        return self._clean_html_output(str(result))
    
    def format_psychology_report(self, raw_analysis, video_data):
        """Audience Psychology - Behavior & Motivation Master"""
        
        agent = Agent(
            role="Psychology Analysis Design Master",
            goal="Transform psychology analysis into insightful behavioral dashboards with motivation mapping",
            backstory="""You are a UX psychology expert who creates dashboards for user research teams. Your 
            visualizations reveal hidden audience motivations and behavioral patterns. You've worked with major 
            social media platforms and content creators to understand audience psychology.""",
            llm=self.llm,
            verbose=True
        )
        
        task = Task(
            description=f"""
TRANSFORM this psychology analysis into an INSIGHTFUL behavioral dashboard:

## RAW ANALYSIS DATA:
{raw_analysis}

## VIDEO METADATA:
{video_data}

## YOUR MISSION:
Create a PSYCHOLOGY DASHBOARD that reveals the hidden motivations of the audience.

## DESIGN REQUIREMENTS:

### 1. PSYCHOLOGY METRICS HEADER (MANDATORY):
```html
<div class="metrics-grid stagger-children">
    <div class="metric-card fade-in">
        <div class="metric-value text-primary">8.5/10</div>
        <div class="metric-label">Curiosity Gap</div>
        <div class="metric-description">Drives viewer interest</div>
    </div>
    <!-- AUTHORITY SCORE, SOCIAL PROOF, PERSONAL RELEVANCE -->
</div>
```

### 2. AUDIENCE MOTIVATIONS (MANDATORY):
```html
<div class="card p-lg">
    <h2 class="section-title">
        <i class="fas fa-users section-icon"></i>
        Primary Audience Motivations
    </h2>
    <div class="grid-2">
        <div class="insight-card status-high">
            <div class="insight-header">
                <i class="fas fa-lightbulb insight-icon"></i>
                <span class="insight-title">Knowledge Acquisition</span>
                <span class="metric-trend trend-excellent">95%</span>
            </div>
            <p class="insight-content">Learn new AI video generation techniques</p>
        </div>
        <!-- ACHIEVEMENT, ENTERTAINMENT, SOCIAL CONNECTION -->
    </div>
</div>
```

### 3. PSYCHOLOGICAL TRIGGERS (MANDATORY):
```html
<div class="grid-2">
    <div class="card p-lg">
        <h3 class="heading-3 mb-md">
            <i class="fas fa-bullseye text-primary mr-sm"></i>
            Active Psychological Triggers
        </h3>
        <div class="space-y-md">
            <div class="progress-container">
                <div class="progress-header">
                    <span class="progress-label">Curiosity Gaps</span>
                    <span class="progress-value text-primary">85%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 85%"></div>
                </div>
            </div>
            <!-- AUTHORITY, SOCIAL PROOF, FOMO -->
        </div>
    </div>
    
    <div class="card p-lg">
        <h3 class="heading-3 mb-md">
            <i class="fas fa-brain text-primary mr-sm"></i>
            Engagement Psychology
        </h3>
        [Psychology insights]
    </div>
</div>
```

### 4. DEMOGRAPHICS & BEHAVIORAL PATTERNS (MANDATORY):
```html
<div class="card p-lg">
    <h2 class="section-title">
        <i class="fas fa-user-friends section-icon"></i>
        Demographics & Behavioral Patterns
    </h2>
    <div class="grid-3 text-center">
        <div>
            <div class="metric-value text-primary">25-35</div>
            <div class="metric-label">Primary Age Range</div>
            <div class="metric-description">Tech-savvy creators</div>
        </div>
        <!-- GENDER, MOBILE VIEWING -->
    </div>
</div>
```

### 5. OPTIMIZATION RECOMMENDATIONS (MANDATORY):
```html
<div class="recommendations-grid">
    <div class="recommendation-item">
        <div class="recommendation-number">1</div>
        <div class="recommendation-content">
            <h4 class="recommendation-title">[Psychology Optimization]</h4>
            <p class="recommendation-description">[How to enhance psychological appeal]</p>
        </div>
    </div>
</div>
```

## PSYCHOLOGY-SPECIFIC FEATURES:
- **SCORE** metrics out of 10 for psychological factors
- **VISUALIZE** motivation percentages with status colors
- **SHOW** trigger effectiveness with progress bars
- **MAP** demographic patterns and behaviors

## PSYCHOLOGY ICONS:
- fa-brain (psychology)
- fa-users (audience)
- fa-lightbulb (knowledge)
- fa-trophy (achievement)
- fa-star (entertainment)
- fa-handshake (social)
- fa-bullseye (triggers)

MAKE PSYCHOLOGY VISUAL. Help creators understand what drives their audience.

## AVAILABLE DESIGN SYSTEM:
{self.design_system[:2000]}...

OUTPUT: Pure HTML using design system classes. Make it INSIGHTFUL and REVEALING.
""",
            agent=agent,
            expected_output="Complete HTML psychology analysis dashboard"
        )
        
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()
        
        return self._clean_html_output(str(result))
    
    def format_thumbnail_report(self, raw_analysis, video_data):
        """Thumbnail Intelligence - Visual Analysis Master"""
        
        agent = Agent(
            role="Thumbnail Analysis Design Master", 
            goal="Transform thumbnail analysis into visual intelligence dashboard with design patterns and optimization recommendations",
            backstory="""You are a visual design intelligence specialist who transforms thumbnail analysis into actionable design insights. 
            Your designs help creators understand visual psychology, composition rules, and click-through optimization. 
            You've designed visual intelligence dashboards for major content platforms and creative agencies.""",
            llm=self.llm,
            verbose=True
        )
        
        task = Task(
            description=f"""
Transform thumbnail analysis into a VISUAL INTELLIGENCE DASHBOARD:

## RAW ANALYSIS DATA:
{raw_analysis}

## VIDEO METADATA:
{video_data}

## YOUR MISSION:
Create a thumbnail intelligence dashboard that helps the user understand HOW their thumbnail performs, WHAT visual patterns work, and HOW to optimize for higher click-through rates.

## DESIGN REQUIREMENTS:

### 1. THUMBNAIL INTELLIGENCE OVERVIEW:
```html
<div class="thumbnail-intelligence-overview">
    <div class="thumbnail-grade-section">
        <div class="grade-display">[Letter Grade]</div>
        <div class="thumbnail-insight">
            <h3>THUMBNAIL EFFECTIVENESS</h3>
            <p class="thumbnail-description">[Key thumbnail assessment]</p>
        </div>
    </div>
    <div class="thumbnail-preview">
        <img src="{video_data.get('thumbnail_url', '')}" alt="Thumbnail" class="thumbnail-image" />
    </div>
    <div class="thumbnail-metrics">
        <div class="metric-card">
            <div class="metric-value">[Value]</div>
            <div class="metric-name">Click-Through Prediction</div>
        </div>
        <!-- More metrics -->
    </div>
</div>
```

### 2. VISUAL DESIGN ANALYSIS:
```html
<div class="visual-design-analysis">
    <h3>🎨 VISUAL DESIGN BREAKDOWN</h3>
    <div class="design-grid">
        <div class="design-card color-psychology">
            <div class="design-header">
                <i class="fas fa-palette"></i>
                <span class="design-label">Color Psychology</span>
            </div>
            <div class="design-content">
                <div class="design-assessment">[Color assessment]</div>
                <div class="design-recommendation">[Color recommendation]</div>
            </div>
        </div>
        <!-- Composition, Text Elements, Face/Emotion cards -->
    </div>
</div>
```

### 3. CATEGORY-SPECIFIC INSIGHTS:
```html
<div class="category-insights">
    <h3>🏆 CATEGORY BENCHMARK COMPARISON</h3>
    <div class="category-grid">
        <div class="category-card">
            <div class="category-header">
                <span class="category-name">[Category Name]</span>
                <span class="category-alignment">Strong/Moderate/Weak</span>
            </div>
            <div class="category-patterns">
                <div class="pattern">[Key pattern in category]</div>
            </div>
            <div class="category-recommendation">
                <h4>Optimization Strategy:</h4>
                <p>[Specific strategy for this category]</p>
            </div>
        </div>
    </div>
</div>
```

### 4. OPTIMIZATION RECOMMENDATIONS:
```html
<div class="optimization-recommendations">
    <h3>🚀 THUMBNAIL OPTIMIZATION RECOMMENDATIONS</h3>
    <div class="recommendations-grid">
        <div class="recommendation-card">
            <div class="recommendation-header">
                <i class="fas fa-lightbulb"></i>
                <h4>[Recommendation Title]</h4>
            </div>
            <div class="recommendation-description">
                <p>[Detailed description]</p>
            </div>
            <div class="recommendation-impact">
                <div class="impact-label">Expected Impact:</div>
                <div class="impact-value">High/Medium/Low</div>
            </div>
        </div>
    </div>
</div>
```

### 5. A/B TESTING RECOMMENDATIONS:
```html
<div class="ab-testing">
    <h3>🧪 A/B TESTING STRATEGY</h3>
    <div class="testing-variations">
        <div class="variation">
            <div class="variation-number">A</div>
            <div class="variation-content">
                <h4>[Variation Name]</h4>
                <p>[Implementation details]</p>
            </div>
        </div>
    </div>
</div>
```

## STYLING REQUIREMENTS:
- Visual intelligence focus
- Design pattern emphasis
- Actionable optimization strategies
- Professional visual dashboard aesthetic

## AVAILABLE DESIGN SYSTEM:
{self.design_system[:2000]}...

## OUTPUT REQUIREMENTS:
- PURE HTML using ONLY the design system classes above
- NO custom CSS - only the provided classes
- RESPONSIVE and BEAUTIFUL on all screen sizes
- ANIMATIONS and MICRO-INTERACTIONS included
- PROFESSIONAL visual intelligence dashboard quality

EXTRACT every visual insight, design pattern, and optimization opportunity from the raw analysis. Make it SCANNABLE and ACTIONABLE.
""",
            agent=agent,
            expected_output="Complete HTML thumbnail analysis dashboard using the design system"
        )
        
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()
        
        return self._clean_html_output(str(result))
        
    def format_comments_report(self, raw_analysis, video_data):
        """Comment Intelligence - Sentiment & Community Master"""
        
        agent = Agent(
            role="Comment Analysis Design Master",
            goal="Transform comment analysis into engaging community dashboards with sentiment visualization",
            backstory="""You are a community management expert who creates dashboards for social media teams. 
            Your designs help community managers understand sentiment, identify top contributors, and spot 
            content opportunities. You've built interfaces for major social platforms.""",
            llm=self.llm,
            verbose=True
        )
        
        task = Task(
            description=f"""
TRANSFORM this comment analysis into a COMPREHENSIVE community dashboard:

## RAW ANALYSIS DATA:
{raw_analysis}

## VIDEO METADATA:
{video_data}

## YOUR MISSION:
Create a COMMENT INTELLIGENCE DASHBOARD that reveals community insights and sentiment.

## DESIGN REQUIREMENTS:

### 1. COMMENT METRICS OVERVIEW (MANDATORY):
```html
<div class="metrics-grid stagger-children">
    <div class="metric-card fade-in">
        <div class="metric-value text-primary">534</div>
        <div class="metric-label">Total Comments</div>
        <div class="metric-description">↑ High Engagement</div>
    </div>
    <div class="metric-card fade-in">
        <div class="metric-value text-excellent">81%</div>
        <div class="metric-label">Positive Sentiment</div>
        <div class="metric-description">Excellent Reception</div>
    </div>
    <!-- NEUTRAL, NEGATIVE -->
</div>
```

### 2. SENTIMENT DISTRIBUTION (MANDATORY):
```html
<div class="grid-2">
    <div class="card p-lg">
        <h3 class="heading-3 mb-md">
            <i class="fas fa-chart-pie text-primary mr-sm"></i>
            Sentiment Distribution
        </h3>
        <div class="space-y-sm">
            <div class="insight-card status-high">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-excellent rounded-full mr-sm"></div>
                        <span>Positive Reactions</span>
                    </div>
                    <span class="text-excellent font-bold">81% (432 comments)</span>
                </div>
            </div>
            <!-- NEUTRAL, NEGATIVE -->
        </div>
    </div>
    
    <div class="card p-lg">
        <h3 class="heading-3 mb-md">
            <i class="fas fa-link text-primary mr-sm"></i>
            Performance Correlation
        </h3>
        [Impact metrics]
    </div>
</div>
```

### 3. TOP PERFORMING COMMENTS (MANDATORY):
```html
<div class="card p-lg">
    <h3 class="heading-3 mb-md">
        <i class="fas fa-trophy text-primary mr-sm"></i>
        Top Performing Comments
    </h3>
    <div class="space-y-md">
        <div class="quote-block">
            <div class="quote-header">
                <span class="quote-author">@Username</span>
                <span class="quote-stats">247 likes • 23 replies</span>
            </div>
            <p class="quote-text">"[ACTUAL COMMENT TEXT]"</p>
            <div class="quote-meta">[Impact description]</div>
        </div>
        <!-- REPEAT FOR TOP COMMENTS -->
    </div>
</div>
```

### 4. ENGAGEMENT PATTERNS (MANDATORY):
```html
<div class="grid-2">
    <div class="card p-lg">
        <h3 class="heading-3 mb-md">
            <i class="fas fa-chart-line text-primary mr-sm"></i>
            Engagement Patterns
        </h3>
        <div class="space-y-sm">
            <div class="insight-card status-high">
                <div class="insight-title text-primary">Peak Activity</div>
                <div class="insight-content">First 24 hours: 67% of total comments</div>
            </div>
            <!-- REPLY CHAINS, COMMUNITY BUILDING -->
        </div>
    </div>
    
    <div class="card p-lg">
        <h3 class="heading-3 mb-md">
            <i class="fas fa-users text-primary mr-sm"></i>
            Community Insights
        </h3>
        [User categories and behaviors]
    </div>
</div>
```

### 5. CONTENT OPPORTUNITIES (MANDATORY):
```html
<div class="grid-2">
    <div class="card p-lg">
        <h3 class="heading-3 mb-md">Interest Indicators</h3>
        [Topic mentions and requests]
    </div>
    <div class="card p-lg">
        <h3 class="heading-3 mb-md">Tutorial Requests</h3>
        [Content opportunities from comments]
    </div>
</div>
```

### 6. FINAL COMMUNITY SCORE (MANDATORY):
```html
<div class="grade-hero">
    <div class="grade-circle grade-a">
        <span class="grade-text">A+</span>
    </div>
    <h2 class="grade-title">Overall Community Engagement Score</h2>
    <p class="grade-subtitle">Exceptional Community Response</p>
    <div class="grid-3 text-center mt-lg">
        <div>
            <div class="metric-value text-primary">81%</div>
            <div class="metric-label">Positive</div>
        </div>
        <!-- THREAD AVG, TOTAL COMMENTS -->
    </div>
</div>
```

## COMMENT-SPECIFIC FEATURES:
- **EXTRACT** actual comment text for quote blocks
- **SHOW** sentiment with color-coded percentages
- **IDENTIFY** top contributors and engagement patterns
- **HIGHLIGHT** content opportunities and requests

## COMMENT ICONS:
- fa-comments (main comments)
- fa-chart-pie (sentiment)
- fa-trophy (top comments)
- fa-quote-left (quotes)
- fa-users (community)
- fa-graduation-cap (tutorials)

MAKE COMMENTS ACTIONABLE. Show creators what their audience wants.

## AVAILABLE DESIGN SYSTEM:
{self.design_system[:2000]}...

OUTPUT: Pure HTML using design system classes. Make it COMMUNITY-FOCUSED and ACTIONABLE.
""",
            agent=agent,
            expected_output="Complete HTML comment analysis dashboard"
        )
        
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()
        
        return self._clean_html_output(str(result))
    
    def _clean_html_output(self, html_string):
        """Clean and validate HTML output from agents"""
        
        # Remove any markdown formatting
        html_string = re.sub(r'```html\n?', '', html_string)
        html_string = re.sub(r'```\n?', '', html_string)
        
        # Ensure it starts with the wrapper div
        if not html_string.strip().startswith('<div class="video-analyzer-report">'):
            html_string = f'<div class="video-analyzer-report">\n{html_string}\n</div>'
        
        # Remove any extra whitespace
        html_string = re.sub(r'\n\s*\n', '\n', html_string)
        
        return html_string.strip()


def get_styling_agents(llm):
    """Factory function to create styling agents"""
    return VideoAnalyzerStylingAgents(llm)