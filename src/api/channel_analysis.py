"""
FastAPI endpoints for Channel DNA Analysis
"""

import os
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, BackgroundTasks, HTTPException, Depends
from pydantic import BaseModel
import logging

from ..crews.channel_dna_crew import Channel<PERSON><PERSON><PERSON><PERSON>
from ..models.channel_dna import ChannelDNAAnalysis, AnalysisStatus
from ..storage.analysis_storage import AnalysisStorage

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/channel-dna", tags=["Channel DNA Analysis"])

# In-memory storage for demo (replace with proper database)
analysis_store: Dict[str, ChannelDNAAnalysis] = {}
crew_instance = None

def get_crew():
    """Get or create Channel DNA crew instance"""
    global crew_instance
    if crew_instance is None:
        crew_instance = ChannelDNACrew()
    return crew_instance


class ChannelAnalysisRequest(BaseModel):
    channel_id: str
    force_refresh: bool = False


class ChannelAnalysisResponse(BaseModel):
    analysis_id: str
    status: AnalysisStatus
    message: str
    estimated_duration_minutes: int = 5


class AnalysisStatusResponse(BaseModel):
    analysis_id: str
    status: AnalysisStatus
    progress_percentage: int
    current_step: str
    estimated_remaining_minutes: int
    results: Optional[ChannelDNAAnalysis] = None


@router.post("/analyze", response_model=ChannelAnalysisResponse)
async def start_channel_analysis(
    request: ChannelAnalysisRequest,
    background_tasks: BackgroundTasks
):
    """
    Start a new Channel DNA analysis
    
    Args:
        request: Channel analysis request
        background_tasks: FastAPI background tasks
        
    Returns:
        Analysis response with ID and status
    """
    try:
        # Generate analysis ID
        analysis_id = str(uuid.uuid4())
        
        # Check if analysis already exists and is recent
        if not request.force_refresh:
            existing = _find_recent_analysis(request.channel_id)
            if existing:
                return ChannelAnalysisResponse(
                    analysis_id=existing.analysis_id,
                    status=existing.status,
                    message=f"Using existing analysis for channel {request.channel_id}",
                    estimated_duration_minutes=0
                )
        
        # Create initial analysis record
        analysis = ChannelDNAAnalysis(
            channel_id=request.channel_id,
            channel_name="",  # Will be updated during analysis
            analysis_id=analysis_id,
            status=AnalysisStatus.PENDING
        )
        
        # Store initial analysis
        analysis_store[analysis_id] = analysis
        
        # Start background analysis
        background_tasks.add_task(
            _run_channel_analysis,
            analysis_id,
            request.channel_id
        )
        
        logger.info(f"Started Channel DNA analysis: {analysis_id} for channel: {request.channel_id}")
        
        return ChannelAnalysisResponse(
            analysis_id=analysis_id,
            status=AnalysisStatus.PENDING,
            message=f"Channel DNA analysis started for {request.channel_id}",
            estimated_duration_minutes=5
        )
        
    except Exception as e:
        logger.error(f"Error starting channel analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/status/{analysis_id}", response_model=AnalysisStatusResponse)
async def get_analysis_status(analysis_id: str):
    """
    Get the status of a Channel DNA analysis
    
    Args:
        analysis_id: Analysis ID
        
    Returns:
        Analysis status and results
    """
    try:
        analysis = analysis_store.get(analysis_id)
        
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        # Calculate progress and remaining time
        progress_percentage = _calculate_progress(analysis)
        estimated_remaining = _estimate_remaining_time(analysis)
        current_step = _get_current_step(analysis)
        
        return AnalysisStatusResponse(
            analysis_id=analysis_id,
            status=analysis.status,
            progress_percentage=progress_percentage,
            current_step=current_step,
            estimated_remaining_minutes=estimated_remaining,
            results=analysis if analysis.status == AnalysisStatus.COMPLETED else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analysis status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/results/{analysis_id}", response_model=ChannelDNAAnalysis)
async def get_analysis_results(analysis_id: str):
    """
    Get complete Channel DNA analysis results
    
    Args:
        analysis_id: Analysis ID
        
    Returns:
        Complete analysis results
    """
    try:
        analysis = analysis_store.get(analysis_id)
        
        if not analysis:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        if analysis.status != AnalysisStatus.COMPLETED:
            raise HTTPException(
                status_code=400, 
                detail=f"Analysis not completed. Current status: {analysis.status}"
            )
        
        return analysis
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analysis results: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/recent", response_model=List[ChannelDNAAnalysis])
async def get_recent_analyses(limit: int = 10):
    """
    Get recent Channel DNA analyses
    
    Args:
        limit: Maximum number of analyses to return
        
    Returns:
        List of recent analyses
    """
    try:
        # Sort by creation date and return recent ones
        recent_analyses = sorted(
            analysis_store.values(),
            key=lambda x: x.created_at,
            reverse=True
        )[:limit]
        
        return recent_analyses
        
    except Exception as e:
        logger.error(f"Error getting recent analyses: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/analysis/{analysis_id}")
async def delete_analysis(analysis_id: str):
    """
    Delete a Channel DNA analysis
    
    Args:
        analysis_id: Analysis ID
        
    Returns:
        Success message
    """
    try:
        if analysis_id not in analysis_store:
            raise HTTPException(status_code=404, detail="Analysis not found")
        
        del analysis_store[analysis_id]
        
        return {"message": f"Analysis {analysis_id} deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def _run_channel_analysis(analysis_id: str, channel_id: str):
    """
    Background task to run Channel DNA analysis
    
    Args:
        analysis_id: Analysis ID
        channel_id: YouTube channel ID
    """
    try:
        # Update status to in progress
        if analysis_id in analysis_store:
            analysis_store[analysis_id].status = AnalysisStatus.IN_PROGRESS
            analysis_store[analysis_id].updated_at = datetime.utcnow()
        
        # Get crew and run analysis
        crew = get_crew()
        result = crew.analyze_channel(channel_id)
        
        # Update stored analysis with results
        analysis_store[analysis_id] = result
        
        logger.info(f"Completed Channel DNA analysis: {analysis_id}")
        
    except Exception as e:
        logger.error(f"Error in background analysis {analysis_id}: {e}")
        
        # Update status to failed
        if analysis_id in analysis_store:
            analysis_store[analysis_id].status = AnalysisStatus.FAILED
            analysis_store[analysis_id].updated_at = datetime.utcnow()


def _find_recent_analysis(channel_id: str) -> Optional[ChannelDNAAnalysis]:
    """Find recent analysis for a channel"""
    for analysis in analysis_store.values():
        if (analysis.channel_id == channel_id and 
            analysis.status == AnalysisStatus.COMPLETED and
            (datetime.utcnow() - analysis.created_at).days < 1):
            return analysis
    return None


def _calculate_progress(analysis: ChannelDNAAnalysis) -> int:
    """Calculate analysis progress percentage"""
    if analysis.status == AnalysisStatus.PENDING:
        return 0
    elif analysis.status == AnalysisStatus.IN_PROGRESS:
        # Estimate based on time elapsed and typical duration
        elapsed = (datetime.utcnow() - analysis.created_at).total_seconds()
        typical_duration = 300  # 5 minutes
        progress = min(90, int((elapsed / typical_duration) * 100))
        return progress
    elif analysis.status == AnalysisStatus.COMPLETED:
        return 100
    else:  # FAILED
        return 0


def _estimate_remaining_time(analysis: ChannelDNAAnalysis) -> int:
    """Estimate remaining time in minutes"""
    if analysis.status == AnalysisStatus.PENDING:
        return 5
    elif analysis.status == AnalysisStatus.IN_PROGRESS:
        elapsed = (datetime.utcnow() - analysis.created_at).total_seconds()
        typical_duration = 300  # 5 minutes
        remaining = max(0, int((typical_duration - elapsed) / 60))
        return remaining
    else:
        return 0


def _get_current_step(analysis: ChannelDNAAnalysis) -> str:
    """Get current analysis step description"""
    if analysis.status == AnalysisStatus.PENDING:
        return "Queued for analysis"
    elif analysis.status == AnalysisStatus.IN_PROGRESS:
        elapsed = (datetime.utcnow() - analysis.created_at).total_seconds()
        if elapsed < 60:
            return "Fetching channel data"
        elif elapsed < 120:
            return "Analyzing content patterns"
        elif elapsed < 180:
            return "Processing audience insights"
        elif elapsed < 240:
            return "Evaluating engagement metrics"
        elif elapsed < 300:
            return "Generating strategic recommendations"
        else:
            return "Finalizing analysis"
    elif analysis.status == AnalysisStatus.COMPLETED:
        return "Analysis complete"
    else:
        return "Analysis failed"