#!/usr/bin/env python3
"""
Test script for Market Research Hub functionality
"""

import requests
import json

def test_market_research_api():
    """Test the market research API endpoint"""
    
    print("🧪 Testing Market Research Hub API")
    print("=" * 50)
    
    # Test data
    test_query = "AI tutorials"
    
    # API endpoint
    url = "http://localhost:8003/api/market-research"
    
    # Request payload
    payload = {
        "query": test_query,
        "time_range": "month",
        "max_results": 25,
        "sort_by": "relevance"
    }
    
    print(f"🔍 Testing query: '{test_query}'")
    print(f"📡 Sending request to: {url}")
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print("✅ API request successful!")
            print(f"   Query: {data.get('query', 'Unknown')}")
            print(f"   Timestamp: {data.get('timestamp', 'Unknown')}")
            
            # Check market overview
            market_overview = data.get('market_overview', {})
            print(f"\n📈 Market Overview:")
            print(f"   Total Videos: {market_overview.get('total_videos', 0)}")
            print(f"   Total Views: {market_overview.get('total_views', 0):,}")
            print(f"   Unique Channels: {market_overview.get('unique_channels', 0)}")
            print(f"   Avg Engagement: {market_overview.get('avg_engagement', 0)}%")
            
            # Check trending content
            trending_content = data.get('trending_content', [])
            print(f"\n🔥 Trending Content:")
            print(f"   Found {len(trending_content)} trending videos")
            if trending_content:
                top_video = trending_content[0]
                print(f"   Top video: {top_video.get('title', 'Unknown')}")
                print(f"   Views: {top_video.get('views', 0):,}")
                print(f"   Performance Score: {top_video.get('performance_score', 0)}")
            
            # Check competition analysis
            competition = data.get('competition_analysis', {})
            top_channels = competition.get('top_channels', [])
            print(f"\n🏆 Competition Analysis:")
            print(f"   Top channels found: {len(top_channels)}")
            if top_channels:
                top_channel = top_channels[0]
                print(f"   Leading channel: {top_channel.get('name', 'Unknown')}")
                print(f"   Dominance score: {top_channel.get('dominance_score', 0)}")
            
            # Check content opportunities
            opportunities = data.get('content_opportunities', {})
            trending_topics = opportunities.get('trending_topics', [])
            print(f"\n💡 Content Opportunities:")
            print(f"   Trending topics: {len(trending_topics)}")
            if trending_topics:
                top_topic = trending_topics[0]
                print(f"   Top topic: {top_topic.get('keyword', 'Unknown')}")
                print(f"   Trend: {top_topic.get('trend_direction', 'Unknown')} {top_topic.get('trend_percentage', 0)}%")
            
            # Check AI insights
            ai_insights = data.get('ai_insights', {})
            print(f"\n🤖 AI Insights:")
            print(f"   Market summary: {ai_insights.get('market_summary', 'Not available')[:100]}...")
            
            recommendations = ai_insights.get('recommendations', [])
            print(f"   Recommendations: {len(recommendations)}")
            if recommendations:
                top_rec = recommendations[0]
                print(f"   Top recommendation: {top_rec.get('title', 'Unknown')} (Priority: {top_rec.get('priority', 'Unknown')})")
            
            print(f"\n✅ Market Research Hub is working perfectly!")
            
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("⏰ Request timed out - this is normal for AI analysis")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - make sure the server is running on localhost:8003")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")

if __name__ == "__main__":
    test_market_research_api()
