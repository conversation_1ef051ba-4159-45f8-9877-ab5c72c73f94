<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YouTube Research v2 - Market Research Hub</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/static/favicon.svg">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- YouTube Research v2 Design System -->
    <link rel="stylesheet" href="/static/styles/youtube-research-v2-design-system.css?v=2025080301">
</head>
<body>
    <div class="app-layout">
        <!-- Premium Sidebar -->
        <aside class="app-sidebar" id="sidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <a href="/" class="sidebar-logo">
                    <div class="sidebar-logo-icon">YR</div>
                    <span class="sidebar-logo-text">YouTube Research</span>
                </a>
                <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                    <i data-lucide="sun" class="theme-icon-light"></i>
                    <i data-lucide="moon" class="theme-icon-dark" style="display: none;"></i>
                </button>
            </div>

            <!-- Navigation Menu -->
            <nav class="sidebar-nav">
                <!-- Overview Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Overview</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/" class="nav-item" data-page="dashboard">
                                <i data-lucide="layout-dashboard" class="nav-icon"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <a href="/analytics" class="nav-item" data-page="analytics">
                                <i data-lucide="bar-chart-3" class="nav-icon"></i>
                                <span>Analytics</span>
                            </a>
                        </li>
                        <li>
                            <a href="/reports" class="nav-item" data-page="reports">
                                <i data-lucide="folder" class="nav-icon"></i>
                                <span>My Reports</span>
                                <span class="nav-badge" id="reportsCount">0</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Analysis Tools Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">AI Analysis Tools</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/video-analyzer" class="nav-item" data-page="video-analyzer">
                                <i data-lucide="video" class="nav-icon"></i>
                                <span>Video Analyzer</span>
                                <span class="nav-badge nav-badge-purple">6 AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/channel-analyzer" class="nav-item" data-page="channel-analyzer">
                                <i data-lucide="users" class="nav-icon"></i>
                                <span>Channel DNA</span>
                                <span class="nav-badge nav-badge-blue">5 AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/market-research" class="nav-item active" data-page="market-research">
                                <i data-lucide="trending-up" class="nav-icon"></i>
                                <span>Market Research</span>
                                <span class="nav-badge nav-badge-green">AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/agent/comment" class="nav-item" data-page="comment-intelligence">
                                <i data-lucide="message-circle" class="nav-icon"></i>
                                <span>Comment Intel</span>
                                <span class="nav-badge nav-badge-orange">AI</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Account Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Account</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/settings" class="nav-item" data-page="settings">
                                <i data-lucide="settings" class="nav-icon"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li>
                            <a href="/help" class="nav-item" data-page="help">
                                <i data-lucide="help-circle" class="nav-icon"></i>
                                <span>Help & Support</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="sidebar-user">
                    <div class="sidebar-user-avatar">A</div>
                    <div class="sidebar-user-info">
                        <div class="sidebar-user-name">Ardi</div>
                        <div class="sidebar-user-plan">Premium Plan</div>
                    </div>
                </div>
                <div class="version-info">
                    <span class="version-label">YouTube Research v2</span>
                    <span class="version-number">2.0.0</span>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Header -->
            <header class="app-header">
                <div class="header-left">
                    <button class="btn btn-ghost btn-sm" id="sidebar-toggle">
                        <i data-lucide="menu" class="sidebar-nav-icon"></i>
                    </button>
                    <nav class="breadcrumb">
                        <span class="breadcrumb-item">Analysis Tools</span>
                        <i data-lucide="chevron-right" class="breadcrumb-separator"></i>
                        <span class="breadcrumb-item">Market Research Hub</span>
                    </nav>
                </div>
                <div class="header-right">
                    <div class="history-dropdown-container">
                        <button class="btn btn-ghost btn-sm" id="historyBtn" onclick="toggleHistoryDropdown()">
                            <i data-lucide="history" class="sidebar-nav-icon"></i>
                            <span>Recent</span>
                        </button>
                        <div class="history-dropdown" id="historyDropdown" style="display: none;">
                            <div class="history-header">Recent Market Research</div>
                            <div class="history-empty">No recent research</div>
                        </div>
                    </div>
                    <button class="btn btn-ghost btn-sm" id="exportBtn" style="display: none;">
                        <i data-lucide="download" class="sidebar-nav-icon"></i>
                        <span>Export</span>
                    </button>
                </div>
            </header>

            <!-- Content Area -->
            <div class="premium-content">
                <!-- Market Research Form -->
                <div class="analyzer-form-section">
                    <div class="welcome-section">
                        <div>
                            <div class="form-header">
                                <div class="market-icon">
                                    <i data-lucide="trending-up"></i>
                                </div>
                                <h1>Market Research Hub</h1>
                                <p>AI-powered market intelligence. Discover trending topics, analyze competition, and find content opportunities with comprehensive data analysis.</p>
                            </div>
                        </div>
                        <div class="welcome-stats">
                            <div class="premium-card glass-card">
                                <div class="stat-item">
                                    <div class="stat-value">AI</div>
                                    <div class="stat-label">Agents</div>
                                </div>
                            </div>
                            <div class="premium-card glass-card">
                                <div class="stat-item">
                                    <div class="stat-value">∞</div>
                                    <div class="stat-label">Topics</div>
                                </div>
                            </div>
                            <div class="premium-card glass-card">
                                <div class="stat-item">
                                    <div class="stat-value">100</div>
                                    <div class="stat-label">Max Results</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="premium-card">
                        <div class="premium-card-header">
                            <div>
                                <h2 class="premium-card-title">
                                    <i data-lucide="search" style="margin-right: var(--space-sm);"></i>
                                    Market Research Query
                                </h2>
                                <p class="premium-card-subtitle">Enter topic, keyword, or niche to begin comprehensive market analysis</p>
                            </div>
                            <div class="agent-badge-large">
                                <i data-lucide="trending-up" class="agent-icon-large"></i>
                                <span class="agent-label">AI Analysis</span>
                            </div>
                        </div>
                        <div class="premium-card-content">
                        
                        <div class="premium-input-group">
                            <div class="form-group">
                                <label for="searchQuery" class="form-label">
                                    <i data-lucide="search" class="label-icon"></i>
                                    Market Research Query
                                </label>
                                <input type="text" id="searchQuery" class="premium-form-input" placeholder="Enter topic, keyword, or niche (e.g., 'AI tutorials', 'cooking tips', 'fitness workouts')">
                            </div>
                            <button id="searchButton" class="premium-analyze-btn">
                                <i data-lucide="trending-up" class="btn-icon"></i>
                                Analyze Market
                            </button>
                        </div>
                        
                        <div class="form-options">
                            <div class="form-option">
                                <label for="timeRange">
                                    <i data-lucide="calendar" class="label-icon"></i>
                                    Time Range
                                </label>
                                <select id="timeRange" class="premium-select">
                                    <option value="week">Past Week</option>
                                    <option value="month" selected>Past Month</option>
                                    <option value="3months">Past 3 Months</option>
                                    <option value="year">Past Year</option>
                                </select>
                            </div>
                            
                            <div class="form-option">
                                <label for="maxResults">
                                    <i data-lucide="list" class="label-icon"></i>
                                    Results
                                </label>
                                <select id="maxResults" class="premium-select">
                                    <option value="25">25 Videos</option>
                                    <option value="50" selected>50 Videos</option>
                                    <option value="100">100 Videos</option>
                                </select>
                            </div>
                            
                            <div class="form-option">
                                <label for="sortBy">
                                    <i data-lucide="arrow-up-down" class="label-icon"></i>
                                    Sort By
                                </label>
                                <select id="sortBy" class="premium-select">
                                    <option value="relevance" selected>Relevance</option>
                                    <option value="date">Upload Date</option>
                                    <option value="viewCount">View Count</option>
                                    <option value="rating">Rating</option>
                                </select>
                            </div>
                        </div>
                        </div>
                    </div>
                </div>

                <!-- Loading State -->
                <div id="loadingContainer" class="loading-section" style="display: none;">
                    <div class="premium-card">
                        <div class="premium-card-content">
                            <div class="loading-state">
                                <div class="loading-spinner"></div>
                                <div class="loading-content">
                                    <h3>🔍 Analyzing Market Intelligence</h3>
                                    <p>Gathering trending content, analyzing competition, and generating AI-powered insights...</p>
                                    <div class="loading-progress">
                                        <div class="progress-bar">
                                            <div class="progress-fill"></div>
                                        </div>
                                        <span class="progress-text">Processing market data...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Error State -->
                <div id="errorContainer" class="error-section" style="display: none;">
                    <div class="premium-card">
                        <div class="premium-card-content">
                            <div class="error-state">
                                <div class="error-icon">
                                    <i data-lucide="alert-triangle"></i>
                                </div>
                                <div class="error-content">
                                    <h3>Analysis Failed</h3>
                                    <p id="errorMessage">Something went wrong during market analysis. Please try again.</p>
                                    <button id="retryButton" class="btn btn-secondary">
                                        <i data-lucide="refresh-cw"></i>
                                        Try Again
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Results Container -->
                <div id="resultsContainer" style="display: none;">
                    <!-- Market Overview -->
                    <div class="premium-card">
                        <div class="premium-card-header">
                            <div>
                                <h2 class="premium-card-title">
                                    <i data-lucide="bar-chart-3" class="premium-card-icon"></i>
                                    Market Overview
                                </h2>
                                <p class="premium-card-subtitle">Key metrics and performance indicators</p>
                            </div>
                        </div>
                        <div class="premium-card-content">
                            <div class="stats-grid" id="marketStats">
                                <!-- Stats will be populated here -->
                            </div>
                        </div>
                    </div>

                    <!-- Tab Navigation -->
                    <div class="tab-navigation">
                        <div class="premium-card">
                            <div class="premium-card-header">
                                <h2 class="premium-card-title">🎯 Market Intelligence Analysis</h2>
                                <div class="tab-counter">
                                    <span id="activeTabIndicator">1</span> of 4 analyses
                                </div>
                            </div>
                            <div class="premium-card-content">
                                <div class="tab-buttons">
                                    <button class="tab-button active" onclick="switchMarketTab('trending')" data-tab="trending">
                                        <i data-lucide="trending-up" class="tab-icon"></i>
                                        <span>Trending Content</span>
                                    </button>
                                    <button class="tab-button" onclick="switchMarketTab('competition')" data-tab="competition">
                                        <i data-lucide="users" class="tab-icon"></i>
                                        <span>Competition</span>
                                    </button>
                                    <button class="tab-button" onclick="switchMarketTab('opportunities')" data-tab="opportunities">
                                        <i data-lucide="lightbulb" class="tab-icon"></i>
                                        <span>Opportunities</span>
                                    </button>
                                    <button class="tab-button" onclick="switchMarketTab('insights')" data-tab="insights">
                                        <i data-lucide="brain" class="tab-icon"></i>
                                        <span>AI Insights</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Tab Content -->
                    <div class="tab-content active" id="tab-trending">
                        <div class="premium-card">
                            <div class="premium-card-header">
                                <div>
                                    <h2 class="premium-card-title">
                                        <i data-lucide="trending-up" class="premium-card-icon"></i>
                                        Trending Content
                                    </h2>
                                    <p class="premium-card-subtitle">Most popular videos in your market</p>
                                </div>
                                <div class="status-indicator completed">
                                    <i data-lucide="check-circle"></i>
                                    <span>Complete</span>
                                </div>
                            </div>
                            <div class="premium-card-content">
<div class="content-grid" id="trendingContent" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: var(--space-md);">
                                    <!-- Trending content will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="tab-competition">
                        <div class="premium-card">
                            <div class="premium-card-header">
                                <div>
                                    <h2 class="premium-card-title">
                                        <i data-lucide="users" class="premium-card-icon"></i>
                                        Competition Analysis
                                    </h2>
                                    <p class="premium-card-subtitle">Top competitors and market gaps</p>
                                </div>
                                <div class="status-indicator completed">
                                    <i data-lucide="check-circle"></i>
                                    <span>Complete</span>
                                </div>
                            </div>
                            <div class="premium-card-content">
                                <div id="competitionAnalysis">
                                    <!-- Competition analysis will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="tab-opportunities">
                        <div class="premium-card">
                            <div class="premium-card-header">
                                <div>
                                    <h2 class="premium-card-title">
                                        <i data-lucide="lightbulb" class="premium-card-icon"></i>
                                        Content Opportunities
                                    </h2>
                                    <p class="premium-card-subtitle">Trending topics and optimal formats</p>
                                </div>
                                <div class="status-indicator completed">
                                    <i data-lucide="check-circle"></i>
                                    <span>Complete</span>
                                </div>
                            </div>
                            <div class="premium-card-content">
                                <div id="contentOpportunities">
                                    <!-- Content opportunities will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="tab-content" id="tab-insights">
                        <div class="premium-card">
                            <div class="premium-card-header">
                                <div>
                                    <h2 class="premium-card-title">
                                        <i data-lucide="brain" class="premium-card-icon"></i>
                                        AI Insights
                                    </h2>
                                    <p class="premium-card-subtitle">Strategic recommendations and market intelligence</p>
                                </div>
                                <div class="status-indicator completed">
                                    <i data-lucide="check-circle"></i>
                                    <span>Complete</span>
                                </div>
                            </div>
                            <div class="premium-card-content">
                                <div id="aiInsights">
                                    <!-- AI insights will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Market Research Hub styles now in youtube-research-v2-design-system.css -->


    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Navigation Manager
        class NavigationManager {
            constructor() {
                this.currentTheme = localStorage.getItem('theme') || 'dark';
                this.init();
            }

            init() {
                this.setupThemeToggle();
                this.setupNavigation();
                this.updateActiveNavItem();
            }

            setupThemeToggle() {
                const themeToggle = document.getElementById('themeToggle');
                const lightIcon = themeToggle.querySelector('.theme-icon-light');
                const darkIcon = themeToggle.querySelector('.theme-icon-dark');

                // Apply saved theme
                document.documentElement.setAttribute('data-theme', this.currentTheme);
                this.updateThemeIcons(lightIcon, darkIcon);

                themeToggle.addEventListener('click', () => {
                    this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
                    document.documentElement.setAttribute('data-theme', this.currentTheme);
                    localStorage.setItem('theme', this.currentTheme);
                    this.updateThemeIcons(lightIcon, darkIcon);

                    // Add theme transition effect
                    document.body.classList.add('theme-transitioning');
                    setTimeout(() => {
                        document.body.classList.remove('theme-transitioning');
                    }, 300);
                });
            }

            updateThemeIcons(lightIcon, darkIcon) {
                if (this.currentTheme === 'dark') {
                    lightIcon.style.display = 'none';
                    darkIcon.style.display = 'block';
                } else {
                    lightIcon.style.display = 'block';
                    darkIcon.style.display = 'none';
                }
            }

            setupNavigation() {
                // Add click handlers for navigation items
                const navItems = document.querySelectorAll('.nav-item');
                navItems.forEach(item => {
                    item.addEventListener('click', (e) => {
                        // Update active state
                        navItems.forEach(nav => nav.classList.remove('active'));
                        item.classList.add('active');

                        // Store active page
                        const page = item.getAttribute('data-page');
                        if (page) {
                            localStorage.setItem('activePage', page);
                        }
                    });
                });
            }

            updateActiveNavItem() {
                const activePage = localStorage.getItem('activePage') || 'market-research';
                const currentPath = window.location.pathname;

                // Remove all active states
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });

                // Set active based on current path or stored page
                let activeItem = document.querySelector(`[data-page="${activePage}"]`);

                if (!activeItem) {
                    // Fallback to path matching
                    activeItem = document.querySelector(`[href="${currentPath}"]`);
                }

                if (activeItem) {
                    activeItem.classList.add('active');
                }
            }
        }

        // Initialize navigation when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            window.navigationManager = new NavigationManager();
        });

        // Sidebar toggle functionality
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('collapsed');
        });

        // Tab switching functionality
        function switchMarketTab(tabId) {
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));

            // Add active class to clicked tab button
            document.querySelector(`[data-tab="${tabId}"]`).classList.add('active');

            // Hide all tab content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(`tab-${tabId}`).classList.add('active');

            // Update tab counter
            const tabOrder = ['trending', 'competition', 'opportunities', 'insights'];
            const activeIndex = tabOrder.indexOf(tabId) + 1;
            document.getElementById('activeTabIndicator').textContent = activeIndex;
        }

        // Search functionality
        document.getElementById('searchButton').addEventListener('click', performSearch);
        document.getElementById('searchQuery').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        document.getElementById('retryButton').addEventListener('click', performSearch);

        async function performSearch() {
            const query = document.getElementById('searchQuery').value.trim();
            if (!query) {
                alert('Please enter a search query');
                return;
            }

            const timeRange = document.getElementById('timeRange').value;
            const maxResults = parseInt(document.getElementById('maxResults').value);
            const sortBy = document.getElementById('sortBy').value;

            // Show loading state
            document.getElementById('loadingContainer').style.display = 'block';
            document.getElementById('errorContainer').style.display = 'none';
            document.getElementById('resultsContainer').style.display = 'none';

            try {
                const response = await fetch('/api/market-research', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        query: query,
                        time_range: timeRange,
                        max_results: maxResults,
                        sort_by: sortBy
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.error) {
                    throw new Error(data.error);
                }

                // Hide loading state and show results
                document.getElementById('loadingContainer').style.display = 'none';
                document.getElementById('resultsContainer').style.display = 'block';

                // Populate results
                populateMarketStats(data.market_overview);
                populateTrendingContent(data.trending_content);
                populateCompetitionAnalysis(data.competition_analysis);
                populateContentOpportunities(data.content_opportunities);
                populateAIInsights(data.ai_insights);

            } catch (error) {
                console.error('Search failed:', error);

                // Show error state
                document.getElementById('loadingContainer').style.display = 'none';
                document.getElementById('errorContainer').style.display = 'block';
                document.getElementById('errorMessage').textContent = error.message;
            }
        }

        // Result population functions
        function populateMarketStats(data) {
            const statsContainer = document.getElementById('marketStats');
            statsContainer.innerHTML = `
                <div class="stat-card">
                    <div class="stat-icon"><i data-lucide="video"></i></div>
                    <div class="stat-content">
                        <div class="stat-value">${data.total_videos || 0}</div>
                        <div class="stat-label">Videos Analyzed</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i data-lucide="eye"></i></div>
                    <div class="stat-content">
                        <div class="stat-value">${formatNumber(data.total_views || 0)}</div>
                        <div class="stat-label">Total Views</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i data-lucide="users"></i></div>
                    <div class="stat-content">
                        <div class="stat-value">${data.unique_channels || 0}</div>
                        <div class="stat-label">Unique Channels</div>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon"><i data-lucide="trending-up"></i></div>
                    <div class="stat-content">
                        <div class="stat-value">${data.avg_engagement || 0}%</div>
                        <div class="stat-label">Avg Engagement</div>
                    </div>
                </div>
            `;
            lucide.createIcons();
        }

        function populateTrendingContent(data) {
            const container = document.getElementById('trendingContent');
            if (!data || data.length === 0) {
                container.innerHTML = '<div class="no-data">No trending content found.</div>';
                return;
            }

            container.innerHTML = data.map(video => `
                <div class="video-card" onclick="openVideoOptions('${video.video_id}', '${video.title.replace(/'/g, "\\'")}')">
                    <div class="video-thumbnail">
                        <img src="${video.thumbnail || `https://img.youtube.com/vi/${video.video_id}/hqdefault.jpg`}"
                             alt="${video.title}"
                             loading="lazy"
                             onerror="this.src='https://img.youtube.com/vi/${video.video_id}/hqdefault.jpg'; this.onerror=null;">
                        <div class="video-actions">
                            <button class="video-action-btn" onclick="event.stopPropagation(); watchVideo('${video.video_id}')" title="Watch Video">
                                <i data-lucide="play"></i>
                            </button>
                            <button class="video-action-btn" onclick="event.stopPropagation(); analyzeVideo('${video.video_id}')" title="Analyze Video">
                                <i data-lucide="zap"></i>
                            </button>
                        </div>
                        <div class="video-duration">${video.duration || ''}</div>
                        <div class="video-views">${formatNumber(video.views || 0)} views</div>
                    </div>
                    <div class="video-content">
                        <h3 class="video-title">${video.title}</h3>
                        <div class="video-channel">
                            <img src="${video.channel_thumbnail || '/static/images/default-avatar.svg'}"
                                 alt="${video.channel_name}"
                                 class="channel-avatar"
                                 loading="lazy"
                                 onerror="this.src='/static/images/default-avatar.svg'; this.onerror=null;">
                            <span class="channel-name">${video.channel_name}</span>
                        </div>
                        <div class="video-stats">
                            <span class="stat-item">
                                <i data-lucide="thumbs-up"></i>
                                ${formatNumber(video.likes || 0)}
                            </span>
                            <span class="stat-item">
                                <i data-lucide="message-circle"></i>
                                ${formatNumber(video.comments || 0)}
                            </span>
                            <span class="stat-item">
                                <i data-lucide="calendar"></i>
                                ${video.published_date}
                            </span>
                        </div>
                        <div class="video-performance">
                            <div class="performance-bar">
                                <div class="performance-fill" style="width: ${video.performance_score || 0}%"></div>
                            </div>
                            <span class="performance-label">${video.performance_score || 0}% Performance Score</span>
                        </div>
                    </div>
                </div>
            `).join('');
            lucide.createIcons();
        }

        // Video interaction functions
        function openVideoOptions(videoId, title) {
            // Show options modal or dropdown
            console.log('Opening options for video:', videoId, title);
        }

        function watchVideo(videoId) {
            window.open(`https://www.youtube.com/watch?v=${videoId}`, '_blank');
        }

        function analyzeVideo(videoId) {
            window.open(`/video-analyzer?url=https://www.youtube.com/watch?v=${videoId}`, '_blank');
        }

        function populateCompetitionAnalysis(data) {
            const container = document.getElementById('competitionAnalysis');
            if (!data) {
                container.innerHTML = '<p class="no-data">No competition data available.</p>';
                return;
            }

            container.innerHTML = `
                <div class="competition-overview">
                    <h3>Top Competitors</h3>
                    <div class="competitor-list">
                        ${(data.top_channels || []).map(channel => `
                            <div class="analysis-card" style="margin-bottom: 16px;">
                                <div class="card-content">
                                    <div class="competitor-header" style="display: flex; align-items: center; gap: 16px; margin-bottom: 16px;">
                                        <img src="${channel.thumbnail || '/static/images/default-avatar.svg'}"
                                             alt="${channel.name}"
                                             style="width: 48px; height: 48px; border-radius: 50%;"
                                             onerror="this.src='/static/images/default-avatar.svg'; this.onerror=null;">
                                        <div style="flex: 1;">
                                            <h4 style="margin: 0 0 4px 0; color: var(--text-primary);">${channel.name}</h4>
                                            <p style="margin: 0; font-size: 14px; color: var(--text-secondary);">${formatNumber(channel.subscribers || 0)} subscribers</p>
                                        </div>
                                        <div style="text-align: center;">
                                            <div style="font-size: 24px; font-weight: 700; color: var(--accent-primary); line-height: 1;">${channel.dominance_score}/100</div>
                                            <div style="font-size: 12px; color: var(--text-secondary);">Dominance</div>
                                        </div>
                                    </div>
                                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 12px; margin-bottom: 16px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; background: var(--surface-primary); border-radius: 8px;">
                                            <span style="font-size: 14px; color: var(--text-secondary);">Avg Views:</span>
                                            <span style="font-size: 14px; font-weight: 600; color: var(--text-primary);">${formatNumber(channel.avg_views || 0)}</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; background: var(--surface-primary); border-radius: 8px;">
                                            <span style="font-size: 14px; color: var(--text-secondary);">Upload Freq:</span>
                                            <span style="font-size: 14px; font-weight: 600; color: var(--text-primary);">${channel.upload_frequency}</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 12px; background: var(--surface-primary); border-radius: 8px;">
                                            <span style="font-size: 14px; color: var(--text-secondary);">Engagement:</span>
                                            <span style="font-size: 14px; font-weight: 600; color: var(--text-primary);">${channel.engagement_rate}%</span>
                                        </div>
                                    </div>
                                    <div>
                                        <h5 style="font-size: 14px; font-weight: 600; color: var(--text-primary); margin: 0 0 8px 0;">Content Strategy:</h5>
                                        <p style="font-size: 14px; color: var(--text-secondary); margin: 0;">${channel.content_strategy}</p>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="market-gaps" style="margin-top: 32px;">
                    <h3>Market Gaps & Opportunities</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
                        ${(data.market_gaps || []).map(gap => `
                            <div class="analysis-card">
                                <div class="card-content">
                                    <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 16px;">
                                        <i data-lucide="target" style="color: var(--accent-primary);"></i>
                                        <h4 style="margin: 0; color: var(--text-primary);">${gap.opportunity}</h4>
                                    </div>
                                    <div style="display: flex; flex-direction: column; gap: 8px; margin-bottom: 16px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span style="font-size: 14px; color: var(--text-secondary);">Search Volume:</span>
                                            <span style="font-size: 14px; font-weight: 600; color: var(--success-primary);">${gap.search_volume}</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span style="font-size: 14px; color: var(--text-secondary);">Competition:</span>
                                            <span style="font-size: 14px; font-weight: 600; color: var(--warning-primary);">${gap.competition_level}</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span style="font-size: 14px; color: var(--text-secondary);">Opportunity Score:</span>
                                            <span style="font-size: 14px; font-weight: 600; color: var(--text-primary);">${gap.opportunity_score}/100</span>
                                        </div>
                                    </div>
                                    <p style="font-size: 14px; color: var(--text-secondary); margin: 0;">${gap.description}</p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            lucide.createIcons();
        }

        function populateContentOpportunities(data) {
            const container = document.getElementById('contentOpportunities');
            if (!data) {
                container.innerHTML = '<p class="no-data">No content opportunities found.</p>';
                return;
            }

            try {

            container.innerHTML = `
                <div class="opportunities-section" style="margin-bottom: 32px;">
                    <h3>Trending Topics</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; margin-top: 20px;">
                        ${(data.trending_topics || []).map(topic => `
                            <div class="analysis-card">
                                <div class="card-content">
                                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                                        <h4 style="margin: 0; color: var(--text-primary);">${topic.keyword}</h4>
                                        <div style="display: flex; align-items: center; gap: 4px; padding: 4px 8px; border-radius: 6px; font-size: 12px; font-weight: 600; background: rgba(34, 197, 94, 0.1); color: var(--success-primary);">
                                            <i data-lucide="trending-up"></i>
                                            ${topic.trend_percentage}%
                                        </div>
                                    </div>
                                    <div style="display: flex; flex-direction: column; gap: 8px; margin-bottom: 16px;">
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span style="font-size: 14px; color: var(--text-secondary);">Search Volume:</span>
                                            <span style="font-size: 14px; font-weight: 600; color: var(--text-primary);">${formatNumber(topic.search_volume || 0)}</span>
                                        </div>
                                        <div style="display: flex; justify-content: space-between; align-items: center;">
                                            <span style="font-size: 14px; color: var(--text-secondary);">Competition:</span>
                                            <span style="font-size: 14px; font-weight: 600; color: var(--warning-primary);">${topic.competition_level}</span>
                                        </div>
                                    </div>
                                    <div style="margin-top: 16px;">
                                        <h5 style="font-size: 14px; font-weight: 600; color: var(--text-primary); margin: 0 0 8px 0;">Content Ideas:</h5>
                                        <ul style="margin: 0; padding-left: 16px;">
                                            ${topic.content_ideas.map(idea => `<li style="font-size: 14px; color: var(--text-secondary); margin-bottom: 4px;">${idea}</li>`).join('')}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="format-opportunities">
                    <h3>Optimal Content Formats</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 24px; margin-top: 20px;">
                        ${(data.optimal_formats || []).map(format => `
                            <div class="analysis-card">
                                <div class="card-content">
                                    <div style="width: 48px; height: 48px; background: var(--accent-primary); border-radius: 12px; display: flex; align-items: center; justify-content: center; color: white; margin-bottom: 16px;">
                                        <i data-lucide="${format.icon}"></i>
                                    </div>
                                    <h4 style="margin: 0 0 12px 0; color: var(--text-primary);">${format.type}</h4>
                                    <div style="display: flex; gap: 20px; margin-bottom: 16px;">
                                        <div style="display: flex; flex-direction: column; gap: 4px;">
                                            <span style="font-size: 14px; color: var(--text-secondary);">Avg Views:</span>
                                            <span style="font-size: 14px; font-weight: 600; color: var(--text-primary);">${formatNumber(format.avg_views || 0)}</span>
                                        </div>
                                        <div style="display: flex; flex-direction: column; gap: 4px;">
                                            <span style="font-size: 14px; color: var(--text-secondary);">Success Rate:</span>
                                            <span style="font-size: 14px; font-weight: 600; color: var(--text-primary);">${format.success_rate}%</span>
                                        </div>
                                    </div>
                                    <p style="font-size: 14px; color: var(--text-secondary); margin-bottom: 16px;">${format.description}</p>
                                    <div>
                                        <h5 style="font-size: 14px; font-weight: 600; color: var(--text-primary); margin: 0 0 8px 0;">Best Practices:</h5>
                                        <ul style="margin: 0; padding-left: 16px;">
                                            ${format.best_practices.map(tip => `<li style="font-size: 14px; color: var(--text-secondary); margin-bottom: 4px;">${tip}</li>`).join('')}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            lucide.createIcons();

            } catch (error) {
                console.error('Error populating content opportunities:', error);
                container.innerHTML = '<p class="no-data">Error loading content opportunities. Please try again.</p>';
            }
        }

        function populateAIInsights(data) {
            const container = document.getElementById('aiInsights');
            if (!data) {
                container.innerHTML = '<p class="no-data">No AI insights available.</p>';
                return;
            }

            container.innerHTML = `
                <div class="ai-insights-container">
                    <div class="premium-card premium-card-gradient market-summary-card">
                        <div class="premium-card-content">
                            <div class="premium-card-header-inline">
                                <i data-lucide="brain" class="premium-card-icon"></i>
                                <h3 class="premium-card-title">Market Intelligence Summary</h3>
                            </div>
                            <div class="premium-card-description">
                                ${data.market_summary || 'No summary available'}
                            </div>
                        </div>
                    </div>

                    <div class="insights-grid">
                        <div class="premium-card insight-card">
                            <div class="premium-card-content">
                                <div class="premium-card-header-inline">
                                    <i data-lucide="target" class="insight-icon"></i>
                                    <h4 class="insight-title">Success Patterns</h4>
                                </div>
                                <div class="insight-description">
                                    ${data.success_patterns || 'No patterns identified'}
                                </div>
                            </div>
                        </div>

                        <div class="premium-card insight-card">
                            <div class="premium-card-content">
                                <div class="premium-card-header-inline">
                                    <i data-lucide="lightbulb" class="insight-icon"></i>
                                    <h4 class="insight-title">Content Strategy</h4>
                                </div>
                                <div class="insight-description">
                                    ${data.content_strategy || 'No strategy recommendations'}
                                </div>
                            </div>
                        </div>

                        <div class="premium-card insight-card">
                            <div class="premium-card-content">
                                <div class="premium-card-header-inline">
                                    <i data-lucide="trending-up" class="insight-icon"></i>
                                    <h4 class="insight-title">Growth Opportunities</h4>
                                </div>
                                <div class="insight-description">
                                    ${data.growth_opportunities || 'No opportunities identified'}
                                </div>
                            </div>
                        </div>

                        <div class="premium-card insight-card">
                            <div class="premium-card-content">
                                <div class="premium-card-header-inline">
                                    <i data-lucide="alert-triangle" class="insight-icon"></i>
                                    <h4 class="insight-title">Market Risks</h4>
                                </div>
                                <div class="insight-description">
                                    ${data.market_risks || 'No risks identified'}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="recommendations-section">
                        <h3 class="section-title">Actionable Recommendations</h3>
                        <div class="recommendations-list">
                            ${(data.recommendations || []).map((rec, index) => `
                                <div class="premium-card recommendation-card">
                                    <div class="premium-card-content">
                                        <div class="recommendation-layout">
                                            <div class="recommendation-number">
                                                ${index + 1}
                                            </div>
                                            <div class="recommendation-content">
                                                <h4 class="recommendation-title">${rec.title}</h4>
                                                <p class="recommendation-description">${rec.description}</p>
                                                <div class="priority-badge priority-${rec.priority.toLowerCase()}">
                                                    Priority: ${rec.priority}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
            lucide.createIcons();
        }

        // Utility function for number formatting
        function formatNumber(num) {
            // Handle undefined, null, or non-numeric values
            if (num === undefined || num === null || isNaN(num)) {
                return '0';
            }

            // Convert to number if it's a string
            const numValue = typeof num === 'string' ? parseFloat(num) : num;

            // Handle invalid numbers after conversion
            if (isNaN(numValue)) {
                return '0';
            }

            if (numValue >= 1000000) {
                return (numValue / 1000000).toFixed(1) + 'M';
            } else if (numValue >= 1000) {
                return (numValue / 1000).toFixed(1) + 'K';
            }
            return Math.round(numValue).toString();
        }
    </script>
</body>
</html>
