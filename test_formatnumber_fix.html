<!DOCTYPE html>
<html>
<head>
    <title>Test formatNumber Fix</title>
</head>
<body>
    <h1>Testing formatNumber Function Fix</h1>
    <div id="results"></div>

    <script>
        // Copy the fixed formatNumber function from the market research page
        function formatNumber(num) {
            // Handle undefined, null, or non-numeric values
            if (num === undefined || num === null || isNaN(num)) {
                return '0';
            }
            
            // Convert to number if it's a string
            const numValue = typeof num === 'string' ? parseFloat(num) : num;
            
            // Handle invalid numbers after conversion
            if (isNaN(numValue)) {
                return '0';
            }
            
            if (numValue >= 1000000) {
                return (numValue / 1000000).toFixed(1) + 'M';
            } else if (numValue >= 1000) {
                return (numValue / 1000).toFixed(1) + 'K';
            }
            return Math.round(numValue).toString();
        }

        // Test cases that were causing the original error
        const testCases = [
            { input: undefined, expected: '0', description: 'undefined value' },
            { input: null, expected: '0', description: 'null value' },
            { input: '', expected: '0', description: 'empty string' },
            { input: 'invalid', expected: '0', description: 'invalid string' },
            { input: 0, expected: '0', description: 'zero' },
            { input: 500, expected: '500', description: 'small number' },
            { input: 1500, expected: '1.5K', description: 'thousands' },
            { input: 1500000, expected: '1.5M', description: 'millions' },
            { input: '2500', expected: '2.5K', description: 'string number' },
            { input: NaN, expected: '0', description: 'NaN value' }
        ];

        let results = '<h2>Test Results:</h2>';
        let allPassed = true;

        testCases.forEach((testCase, index) => {
            try {
                const result = formatNumber(testCase.input);
                const passed = result === testCase.expected;
                allPassed = allPassed && passed;
                
                results += `<div style="margin: 10px 0; padding: 10px; border: 1px solid ${passed ? 'green' : 'red'}; background: ${passed ? '#e8f5e8' : '#ffe8e8'};">
                    <strong>Test ${index + 1}:</strong> ${testCase.description}<br>
                    <strong>Input:</strong> ${testCase.input} (${typeof testCase.input})<br>
                    <strong>Expected:</strong> ${testCase.expected}<br>
                    <strong>Actual:</strong> ${result}<br>
                    <strong>Status:</strong> ${passed ? '✅ PASS' : '❌ FAIL'}
                </div>`;
            } catch (error) {
                allPassed = false;
                results += `<div style="margin: 10px 0; padding: 10px; border: 1px solid red; background: #ffe8e8;">
                    <strong>Test ${index + 1}:</strong> ${testCase.description}<br>
                    <strong>Input:</strong> ${testCase.input}<br>
                    <strong>Error:</strong> ${error.message}<br>
                    <strong>Status:</strong> ❌ ERROR
                </div>`;
            }
        });

        results += `<h2 style="color: ${allPassed ? 'green' : 'red'};">
            Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}
        </h2>`;

        document.getElementById('results').innerHTML = results;

        // Log results to console as well
        console.log('formatNumber test results:', allPassed ? 'ALL PASSED' : 'SOME FAILED');
    </script>
</body>
</html>
