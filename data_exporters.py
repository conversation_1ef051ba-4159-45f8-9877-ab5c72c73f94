import os
import json
import logging
from typing import Dict, Any
from datetime import datetime, timezone

# To ensure safety and consistency, we will reuse the existing, tested tool components.
from src.tools.youtube_tools import YouTubeChannelInfoTool, YouTubeVideosFetcherTool, YouTubeCommentsFetcherTool, YouTubeTranscriptTool
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

# Load environment variables to get the API key
from dotenv import load_dotenv
load_dotenv()

# Setup logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ChannelProfileExporter:
    """
    Fetches and structures a complete profile for a given YouTube channel.
    This tool is for data gathering and does not involve any AI analysis.
    """

    def __init__(self, youtube_api_key: str):
        """
        Initializes the exporter with the necessary API key.

        Args:
            youtube_api_key: The YouTube Data API v3 key.
        """
        if not youtube_api_key:
            raise ValueError("A valid YouTube API key is required to use the exporter.")
        self.youtube_api_key = youtube_api_key
        
        # We instantiate the existing, individual tools from youtube_tools.py
        self.channel_tool = YouTubeChannelInfoTool(api_key=self.youtube_api_key)
        self.videos_tool = YouTubeVideosFetcherTool(api_key=self.youtube_api_key)

    def get_profile(self, channel_id: str, max_videos: int = 50) -> Dict[str, Any]:
        """
        Fetches all channel-level data and a list of its most recent videos.

        Args:
            channel_id: The ID or handle of the YouTube channel (e.g., "UCX6OQ3DkcsbYNE6H8uQQuVA" or "@MrBeast").
            max_videos: The maximum number of recent videos to fetch. Defaults to 50.

        Returns:
            A dictionary containing the structured channel profile data.
            Returns a dictionary with an 'error' key if fetching fails.
        """
        logger.info(f"Starting channel profile export for: {channel_id}")

        # Step 1: Fetch the core channel information.
        channel_info = self.channel_tool._run(channel_id=channel_id)
        if 'error' in channel_info:
            logger.error(f"Could not fetch channel info for {channel_id}: {channel_info['error']}")
            return channel_info

        actual_channel_id = channel_info.get('id')
        if not actual_channel_id:
            error_message = "Failed to resolve the actual channel ID."
            logger.error(error_message)
            return {"error": error_message}
            
        logger.info(f"Successfully fetched info for channel: {channel_info.get('title', 'N/A')} ({actual_channel_id})")

        # Step 2: Fetch the list of recent videos from the channel.
        logger.info(f"Fetching the latest {max_videos} videos...")
        videos = self.videos_tool._run(channel_id=actual_channel_id, max_videos=max_videos)
        logger.info(f"Successfully fetched {len(videos)} videos.")

        # Step 3: Assemble the final, structured data object.
        profile_data = {
            "channel_profile": channel_info,
            "video_library": videos,
            "export_metadata": {
                "source_channel_id": channel_id,
                "resolved_channel_id": actual_channel_id,
                "videos_requested": max_videos,
                "videos_found": len(videos),
                "exported_at_utc": datetime.now(timezone.utc).isoformat()
            }
        }
        
        return profile_data


class VideoDataExporter:
    """
    Fetches and structures comprehensive data for a given YouTube video.
    This tool is for data gathering and does not involve any AI analysis.
    """

    def __init__(self, youtube_api_key: str, supadata_api_key: str = None):
        """
        Initializes the exporter with the necessary API keys.

        Args:
            youtube_api_key: The YouTube Data API v3 key.
            supadata_api_key: Optional Supadata API key for transcripts.
        """
        if not youtube_api_key:
            raise ValueError("A valid YouTube API key is required to use the exporter.")
        self.youtube_api_key = youtube_api_key
        self.supadata_api_key = supadata_api_key
        
        # We instantiate the existing, individual tools from youtube_tools.py
        self.comments_tool = YouTubeCommentsFetcherTool(api_key=self.youtube_api_key)
        self.transcript_tool = YouTubeTranscriptTool(supadata_api_key=self.supadata_api_key)
        
        # Initialize YouTube API client directly for video details
        self._youtube_client = build('youtube', 'v3', developerKey=self.youtube_api_key)

    def get_data(self, video_id: str, max_comments: int = 100) -> Dict[str, Any]:
        """
        Fetches detailed information, comments, and transcript for a single video.

        Args:
            video_id: The ID of the YouTube video.
            max_comments: The maximum number of comments to fetch. Defaults to 100.

        Returns:
            A dictionary containing the structured video data.
            Returns a dictionary with an 'error' key if fetching fails.
        """
        logger.info(f"Starting video data export for video ID: {video_id}")

        try:
            # Step 1: Fetch video snippet, statistics, and content details
            video_request = self._youtube_client.videos().list(
                part='snippet,statistics,contentDetails',
                id=video_id
            )
            video_response = video_request.execute()

            if not video_response.get('items'):
                error_message = f"Video with ID {video_id} not found."
                logger.error(error_message)
                return {"error": error_message}
            
            video_info = video_response['items'][0]
            
            # Extract relevant video details
            video_details = {
                'id': video_info['id'],
                'title': video_info['snippet']['title'],
                'description': video_info['snippet']['description'],
                'publishedAt': video_info['snippet']['publishedAt'],
                'thumbnails': video_info['snippet']['thumbnails'],
                'duration': video_info['contentDetails']['duration'],
                'statistics': {
                    'viewCount': int(video_info['statistics'].get('viewCount', 0)),
                    'likeCount': int(video_info['statistics'].get('likeCount', 0)),
                    'commentCount': int(video_info['statistics'].get('commentCount', 0))
                },
                'tags': video_info['snippet'].get('tags', [])
            }
            logger.info(f"Successfully fetched details for video: {video_details['title']}")

            # Step 2: Fetch comments
            logger.info(f"Fetching up to {max_comments} comments for video ID: {video_id}...")
            comments = self.comments_tool._run(video_ids=[video_id], max_comments_per_video=max_comments)
            video_comments = comments.get(video_id, [])
            logger.info(f"Successfully fetched {len(video_comments)} comments.")

            # Step 3: Fetch transcript
            logger.info(f"Fetching transcript for video ID: {video_id}...")
            transcripts = self.transcript_tool._run(video_ids=[video_id])
            video_transcript = transcripts.get(video_id, 'No transcript available.')
            logger.info(f"Transcript status: {'Available' if video_transcript != 'No transcript available.' else 'Not available'}.")

            # Step 4: Assemble the final, structured data object
            video_data = {
                "video_details": video_details,
                "comments": video_comments,
                "transcript": video_transcript,
                "export_metadata": {
                    "source_video_id": video_id,
                    "comments_requested": max_comments,
                    "comments_found": len(video_comments),
                    "transcript_available": (video_transcript != 'No transcript available.'),
                    "exported_at_utc": datetime.now(timezone.utc).isoformat()
                }
            }
            
            return video_data

        except HttpError as e:
            error_message = f"YouTube API error fetching video {video_id}: {e}"
            logger.error(error_message)
            return {"error": error_message}
        except Exception as e:
            error_message = f"Error fetching video data for {video_id}: {e}"
            logger.error(error_message)
            return {"error": error_message}