| Category | Variable | Value |
|---------|----------|-------|
| color | --bg-primary | #FFFFFF |
| color | --bg-secondary | #F8F9FA |
| color | --bg-tertiary | #F1F3F4 |
| color | --bg-elevated | #FFFFFF |
| color | --bg-glass | rgba(255, 255, 255, 0.95) |
| color | --bg-overlay | rgba(0, 0, 0, 0.05) |
| color | --bg-muted | #F1F3F4 |
| color | --text-primary | #1A1A1A |
| color | --text-secondary | #4A4A4A |
| color | --text-tertiary | #6B7280 |
| color | --text-muted | #9CA3AF |
| color | --border-light | #E5E7EB |
| color | --border-medium | #D1D5DB |
| color | --border-strong | #9CA3AF |
| color | --purple-primary | #8B5CF6 |
| color | --purple-secondary | #A78BFA |
| color | --purple-tertiary | #C4B5FD |
| color | --purple-light | #EDE9FE |
| color | --purple-gradient | linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%) |
| color | --agent-1 | #8B5CF6 |
| color | --agent-2 | #3B82F6 |
| color | --agent-3 | #10B981 |
| color | --agent-4 | #F59E0B |
| color | --agent-5 | #EF4444 |
| color | --agent-6 | #6366F1 |
| color | --success | #10B981 |
| color | --success-light | #D1FAE5 |
| color | --warning | #F59E0B |
| color | --warning-light | #FEF3C7 |
| color | --error | #EF4444 |
| color | --error-light | #FEE2E2 |
| color | --info | #3B82F6 |
| color | --info-light | #DBEAFE |
| color | --font-primary | 'Inter', -apple-system, BlinkMacSystemFont, sans-serif |
| color | --text-xs | 0.75rem |
| color | --text-sm | 0.875rem |
| color | --text-base | 1rem |
| color | --text-lg | 1.125rem |
| color | --text-xl | 1.25rem |
| color | --text-2xl | 1.5rem |
| color | --text-3xl | 1.875rem |
| color | --text-4xl | 2.25rem |
| color | --shadow-sm | 0 2px 4px 0 rgba(0, 0, 0, 0.15) |
| color | --shadow-md | 0 8px 16px -4px rgba(0, 0, 0, 0.25), 0 4px 8px -2px rgba(0, 0, 0, 0.15) |
| color | --shadow-lg | 0 16px 32px -8px rgba(0, 0, 0, 0.35), 0 8px 16px -4px rgba(0, 0, 0, 0.25) |
| color | --shadow-xl | 0 32px 64px -16px rgba(0, 0, 0, 0.45), 0 16px 32px -8px rgba(0, 0, 0, 0.35) |
| color | --shadow-purple | 0 16px 32px -8px rgba(139, 92, 246, 0.3), 0 8px 16px -4px rgba(139, 92, 246, 0.2) |
| color | --shadow-glass | 0 8px 32px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1) |
| color | --bg-primary | linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%) |
| color | --bg-secondary | linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e1b4b 100%) |
| color | --bg-tertiary | #1e293b |
| color | --bg-elevated | rgba(30, 41, 59, 0.95) |
| color | --bg-glass | rgba(255, 255, 255, 0.08) |
| color | --bg-overlay | rgba(255, 255, 255, 0.05) |
| color | --surface-primary | rgba(30, 41, 59, 0.8) |
| color | --surface-secondary | rgba(51, 65, 85, 0.6) |
| color | --surface-glass | rgba(255, 255, 255, 0.08) |
| color | --surface-card | rgba(30, 41, 59, 0.8) |
| color | --text-primary | #FAFAFA |
| color | --text-secondary | #E2E8F0 |
| color | --text-tertiary | #94A3B8 |
| color | --text-muted | #64748B |
| color | --border-light | rgba(255, 255, 255, 0.1) |
| color | --border-medium | rgba(255, 255, 255, 0.15) |
| color | --border-strong | rgba(255, 255, 255, 0.2) |
| color | --success-light | rgba(34, 197, 94, 0.15) |
| color | --warning-light | rgba(251, 191, 36, 0.15) |
| color | --error-light | rgba(239, 68, 68, 0.15) |
| color | --info-light | rgba(59, 130, 246, 0.15) |
| color | --primary-gradient | linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%) |
| color | --purple-gradient | linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%) |
| color | --font-primary);
  font-size | var(--text-base) |
| color | --text-primary);
  background | var(--bg-primary) |
| color | --bg-overlay);
  border-radius | var(--radius-md) |
| color | --purple-gradient);
  border-radius | 50% |
| color | --text-secondary);
}

[data-theme="dark"] .theme-toggle | hover {
  background: var(--purple-primary) |
| color | --radius-xl);
  box-shadow | 0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) |
| color | --bg-primary);
}

.app-sidebar {
  width | var(--sidebar-width) |
| color | --bg-elevated);
  border-right | 1px solid var(--border-light) |
| color | --transition-normal);
  box-shadow | var(--shadow-lg) |
| color | --border-light);
  display | flex |
| color | --border-light);
  display | flex |
| color | --space-md);
  text-decoration | none |
| color | --text-primary);
  font-weight | var(--font-weight-bold) |
| color | --text-lg);
}

.sidebar-logo-icon {
  width | 40px |
| color | --purple-gradient);
  border-radius | var(--radius-lg) |
| color | --bg-overlay);
  border-radius | var(--radius-md) |
| color | --purple-light);
  color | var(--purple-primary) |
| color | --space-md);
  border-bottom | 1px solid var(--border-light) |
| color | --bg-elevated);
}

.user-profile-card {
  display | flex |
| color | --bg-overlay);
  border-radius | var(--radius-md) |
| color | --purple-primary);
  border-radius | var(--radius-full) |
| color | --font-weight-semibold);
}

.user-info {
  flex | 1 |
| color | --text-sm);
  white-space | nowrap |
| color | --text-xs);
  color | var(--text-secondary) |
| color | --bg-overlay);
  border-radius | var(--radius-md) |
| color | --text-secondary);
  font-weight | var(--font-weight-medium) |
| color | --text-xs);
  color | var(--text-primary) |
| color | --bg-muted);
  border-radius | var(--radius-sm) |
| color | --purple-gradient);
  border-radius | var(--radius-sm) |
| color | --text-xs);
  padding | var(--space-xs) var(--space-sm) |
| color | --radius-md);
  text-decoration | none |
| color | --text-secondary);
  font-weight | var(--font-weight-medium) |
| color | --text-sm);
  transition | all var(--transition-fast) |
| color | --text-primary);
  transform | translateX(2px) |
| color | --purple-gradient);
  color | white |
| color | --font-weight-semibold);
  box-shadow | var(--shadow-sm) |
| color | --purple-primary);
  border-radius | 0 2px 2px 0 |
| color | --bg-muted);
  color | var(--text-secondary) |
| color | --purple-primary);
  color | white |
| color | --bg-muted);
  transition | var(--transition-fast) |
| color | --purple-primary); }

.nav-item | hover .agent-indicator {
  transform: scale(1.2) |
| color | --space-md);
  border-top | 1px solid var(--border-light) |
| color | --bg-elevated);
}

.sidebar-user {
  display | flex |
| color | --radius-md);
  border | 1px solid var(--border-light) |
| color | --purple-gradient);
  border-radius | 50% |
| color | --text-sm);
  font-weight | var(--font-weight-medium) |
| color | --text-primary);
  white-space | nowrap |
| color | --text-xs);
  color | var(--purple-primary) |
| color | --text-xs);
  color | var(--text-secondary) |
| color | --purple-primary);
  font-weight | var(--font-weight-semibold) |
| color | --bg-overlay);
  border-radius | var(--radius-md) |
| color | --text-secondary);
  font-size | var(--text-xs) |
| color | --purple-primary); }
  100% { border-right-color | #ef4444 |
| color | --purple-primary);
  color | white |
| color | --text-xs);
  font-weight | var(--font-weight-semibold) |
| color | --space-xs) var(--space-sm);
  border-radius | var(--radius-full) |
| color | --surface-card);
  border | 1px solid var(--border-light) |
| color | --radius-xl);
  box-shadow | var(--shadow-glass) |
| color | --shadow-md);
  border-color | var(--border-medium) |
| color | --primary-gradient);
  border | 1px solid var(--purple-tertiary) |
| color | --shadow-purple);
  backdrop-filter | blur(12px) |
| color | --space-xl);
  border-bottom | 1px solid var(--border-light) |
| color | --text-2xl);
  font-weight | var(--font-weight-bold) |
| color | --text-primary);
  margin | 0 |
| color | --text-base);
  color | var(--text-secondary) |
| color | --space-xl);
  border-top | 1px solid var(--border-light) |
| color | --bg-overlay);
}

/* ====== BUTTON COMPONENTS ====== */
.btn {
  display | inline-flex |
| color | --text-sm);
  font-weight | var(--font-weight-medium) |
| color | --purple-gradient);
  color | white |
| color | --shadow-md);
}

.btn-primary | hover:not(:disabled) {
  transform: translateY(-0.5px) |
| color | --shadow-md);
  filter | brightness(1.05) |
| color | --bg-elevated);
  color | var(--text-primary) |
| color | --border-medium);
}

.btn-secondary | hover:not(:disabled) {
  background: var(--bg-overlay) |
| color | --purple-primary);
}

.btn-ghost {
  background | transparent |
| color | --text-secondary);
}

.btn-ghost | hover:not(:disabled) {
  background: var(--bg-overlay) |
| color | --text-primary);
}

.btn-xs {
  padding | 2px var(--space-xs) |
| color | --text-base);
  font-weight | var(--font-weight-semibold) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --transition-fast);
  box-shadow | var(--shadow-sm) |
| color | --shadow-md);
  transform | translateY(-1px) |
| color | --border-light);
  background | var(--bg-elevated) |
| color | --purple-gradient);
  border-radius | var(--radius-md) |
| color | --text-lg);
  font-weight | var(--font-weight-semibold) |
| color | --text-primary);
}

.analysis-card-subtitle {
  margin | 0 |
| color | --text-sm);
  color | var(--text-secondary) |
| color | --border-light);
}

.analysis-card-meta {
  display | flex |
| color | --text-secondary);
}

.analysis-timestamp {
  font-weight | var(--font-weight-medium) |
| color | --purple-primary);
  font-weight | var(--font-weight-semibold) |
| color | --space-xs);
}

/* Agent Status Cards */
.agent-status-card {
  display | flex |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --bg-overlay);
  border-color | var(--purple-primary) |
| color | --font-weight-semibold);
}

.agent-performance { background | #ef4444 |
| color | --purple-primary); }

.agent-icon {
  width | 20px |
| color | --radius-full);
  border | 2px solid var(--bg-elevated) |
| color | --text-tertiary); }
.status-starting { background | #f59e0b |
| color | --purple-primary); }
.status-complete { background | #10b981 |
| color | --text-secondary);
  line-height | 1.4 |
| color | --bg-muted);
  border-radius | var(--radius-sm) |
| color | --purple-gradient);
  border-radius | var(--radius-sm) |
| color | --transition-normal);
}

.progress-text {
  font-size | var(--text-xs) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --shadow-md);
}

.insight-header {
  display | flex |
| color | --text-xs);
  font-weight | var(--font-weight-semibold) |
| color | --purple-primary);
  text-transform | uppercase |
| color | --text-secondary);
  line-height | 1.5 |
| color | --text-secondary);
  font-weight | var(--font-weight-medium) |
| color | --text-lg);
  font-weight | var(--font-weight-bold) |
| color | --text-primary);
}

.insight-actions {
  display | flex |
| color | --border-light);
  border-radius | var(--radius-lg) |
| color | --shadow-glass);
  transition | var(--transition-fast) |
| color | --shadow-md);
  transform | translateY(-1px) |
| color | --text-2xl);
  font-weight | var(--font-weight-bold) |
| color | --text-primary);
  margin-bottom | var(--space-xs) |
| color | --text-sm);
  color | var(--text-secondary) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --shadow-sm);
  transform | translateY(-1px) |
| color | --bg-overlay);
  border-radius | var(--radius-md) |
| color | --text-secondary);
}

.metric-icon-svg {
  width | 20px |
| color | --text-tertiary); }

.trend-icon {
  width | 16px |
| color | --text-3xl);
  font-weight | var(--font-weight-bold) |
| color | --text-primary);
  line-height | 1 |
| color | --text-secondary);
  font-weight | var(--font-weight-medium) |
| color | --text-xs);
  color | var(--text-secondary) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --text-lg);
  font-weight | var(--font-weight-semibold) |
| color | --text-primary);
}

.progress-percentage {
  font-size | var(--text-lg) |
| color | --text-xs);
  color | var(--text-secondary) |
| color | --font-weight-medium);
  text-align | center |
| color | --text-primary);
  font-weight | var(--font-weight-semibold) |
| color | --text-sm);
  color | var(--text-secondary) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --border-light);
}

.feed-title {
  margin | 0 |
| color | --text-base);
  font-weight | var(--font-weight-semibold) |
| color | --text-primary);
  display | flex |
| color | --bg-overlay);
}

.status-item-avatar {
  width | 32px |
| color | --space-xs);
}

.status-agent-name {
  font-size | var(--text-sm) |
| color | --text-xs);
  color | var(--text-secondary) |
| color | --text-sm);
  color | var(--text-secondary) |
| color | --border-light);
}

.connection-status {
  display | flex |
| color | --text-secondary);
}

.status-dot {
  width | 8px |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --bg-muted);
  border-radius | var(--radius-full) |
| color | --bg-muted);
  border-radius | var(--radius-sm) |
| color | --bg-muted);
  border-radius | var(--radius-md) |
| color | --border-light);
  border-radius | var(--radius-lg) |
| color | --space-lg);
}

.error-icon-svg {
  width | 32px |
| color | --text-secondary);
  max-width | 400px |
| color | --border-light);
  border-radius | var(--radius-lg) |
| color | --bg-overlay);
  border-radius | var(--radius-full) |
| color | --text-secondary);
}

.empty-title {
  margin | 0 0 var(--space-sm) 0 |
| color | --text-xl);
  font-weight | var(--font-weight-semibold) |
| color | --text-primary);
}

.empty-message {
  margin | 0 0 var(--space-lg) 0 |
| color | --text-base);
  color | var(--text-secondary) |
| color | --transition-fast);
}

.agent-status-item | hover {
  background: var(--bg-overlay) |
| color | --purple-light);
  border | 1px solid var(--purple-tertiary) |
| color | --agent-1); }
.agent-avatar.agent-2 { background | var(--agent-2) |
| color | --agent-3); }
.agent-avatar.agent-4 { background | var(--agent-4) |
| color | --agent-5); }
.agent-avatar.agent-6 { background | var(--agent-6) |
| color | --text-sm);
  margin-bottom | var(--space-xs) |
| color | --text-secondary);
  font-size | var(--text-sm) |
| color | --text-muted);
  font-size | var(--text-xs) |
| color | --space-xs);
}

.agent-progress {
  width | 100% |
| color | --bg-overlay);
  border-radius | var(--radius-full) |
| color | --space-sm);
}

.agent-progress-fill {
  height | 100% |
| color | --purple-gradient);
  border-radius | var(--radius-full) |
| color | --text-sm);
  font-weight | var(--font-weight-medium) |
| color | --text-primary);
  margin-bottom | var(--space-sm) |
| color | --space-md);
  border | 1px solid var(--border-medium) |
| color | --text-base);
  color | var(--text-primary) |
| color | --bg-elevated);
  transition | all var(--transition-fast) |
| color | --purple-primary);
  box-shadow | 0 0 0 3px rgba(139, 92, 246, 0.1) |
| color | --text-muted);
}

.form-textarea {
  resize | vertical |
| color | --text-xl);
}

.analysis-title {
  font-size | var(--text-3xl) |
| color | --text-lg);
  color | var(--text-secondary) |
| color | --text-3xl);
  font-weight | var(--font-weight-bold) |
| color | --text-primary);
  margin | var(--space-sm) 0 |
| color | --text-sm);
  font-weight | var(--font-weight-medium) |
| color | --error);
}

.metric-change.neutral {
  color | var(--text-muted) |
| color | --text-xs); }
.text-sm { font-size | var(--text-sm) |
| color | --text-base); }
.text-lg { font-size | var(--text-lg) |
| color | --text-xl); }
.text-2xl { font-size | var(--text-2xl) |
| color | --text-3xl); }
.text-4xl { font-size | var(--text-4xl) |
| color | --font-weight-extrabold); }

.text-primary { color | var(--text-primary) |
| color | --text-secondary); }
.text-tertiary { color | var(--text-tertiary) |
| color | --text-muted); }
.text-purple { color | var(--purple-primary) |
| color | --success); }
.text-warning { color | var(--warning) |
| color | --error); }

.bg-primary { background-color | var(--bg-primary) |
| color | --bg-secondary); }
.bg-elevated { background-color | var(--bg-elevated) |
| color | --purple-gradient); }

.rounded-sm { border-radius | var(--radius-sm) |
| color | --radius-md); }
.rounded-lg { border-radius | var(--radius-lg) |
| color | --radius-xl); }
.rounded-2xl { border-radius | var(--radius-2xl) |
| color | --radius-full); }

.shadow-sm { box-shadow | var(--shadow-sm) |
| color | --shadow-md); }
.shadow-lg { box-shadow | var(--shadow-lg) |
| color | --shadow-xl); }
.shadow-purple { box-shadow | var(--shadow-purple) |
| color | --space-lg);
}

/* Status and loading components */
.agent-status-badge {
  display | flex |
| color | --bg-glass);
  border | 1px solid var(--border-medium) |
| color | --text-sm);
  font-weight | var(--font-weight-medium) |
| color | --text-muted);
}

.status-dot.active {
  background | var(--success) |
| color | --warning);
  animation | pulse 1s infinite |
| color | --error);
}

.btn-spinner {
  display | none |
| color | --border-light);
  border-radius | var(--radius-lg) |
| color | --space-xl);
  box-shadow | var(--shadow-sm) |
| color | --text-sm);
  color | var(--text-tertiary) |
| color | --text-base);
  color | var(--text-tertiary) |
| color | --purple-secondary);
}

/* Common responsive patterns */
@media (max-width | 1200px) {
  .analyzer-form.grid-layout {
    grid-template-columns: 1fr |
| color | --text-3xl);
  font-weight | var(--font-weight-bold) |
| color | --text-primary);
  margin-bottom | var(--space-sm) |
| color | --text-lg);
  color | var(--text-tertiary) |
| color | --purple-gradient);
  border-radius | var(--radius-xl) |
| color | --text-lg);
  font-weight | var(--font-weight-semibold) |
| color | --text-primary);
  margin-bottom | var(--space-sm) |
| color | --text-tertiary);
  line-height | var(--line-height-relaxed) |
| color | --bg-glass);
  color | var(--text-secondary) |
| color | --space-xs) var(--space-sm);
  border-radius | var(--radius-md) |
| color | --text-xs);
  font-weight | var(--font-weight-medium) |
| color | --border-medium);
}

.tool-footer {
  display | flex |
| color | --space-lg);
  border-top | 1px solid var(--border-medium) |
| color | --text-sm);
  font-weight | var(--font-weight-medium) |
| color | --space-xs) var(--space-sm);
  border-radius | var(--radius-md) |
| color | --color-success);
  color | white |
| color | --bg-glass);
  color | var(--text-tertiary) |
| color | --border-medium);
}

.recent-section {
  margin-bottom | var(--space-2xl) |
| color | --bg-quaternary);
  display | flex |
| color | --text-muted);
}

.recent-overlay {
  position | absolute |
| color | --purple-gradient);
  color | white |
| color | --text-base);
  font-weight | var(--font-weight-medium) |
| color | --text-primary);
  margin-bottom | var(--space-xs) |
| color | --text-sm);
  color | var(--text-tertiary) |
| color | --bg-glass);
  color | var(--text-secondary) |
| color | --font-weight-medium);
  border | 1px solid var(--border-medium) |
| color | --text-xl);
  font-weight | var(--font-weight-bold) |
| color | --purple-primary);
}

.usage-progress {
  margin-bottom | var(--space-lg) |
| color | --bg-tertiary);
  border-radius | var(--radius-full) |
| color | --purple-gradient);
  transition | width var(--transition-normal) |
| color | --text-sm);
  color | var(--text-tertiary) |
| color | --color-success);
}

.stats-title {
  font-size | var(--text-lg) |
| color | --space-lg);
}

.stat-item {
  text-align | center |
| color | --text-2xl);
  font-weight | var(--font-weight-bold) |
| color | --text-primary);
  margin-bottom | var(--space-xs) |
| color | --text-sm);
  color | var(--text-tertiary) |
| color | --text-secondary);
  font-size | var(--text-sm) |
| color | --space-2xl);
}

.agent-badge-large {
  display | flex |
| color | --bg-glass);
  border | 1px solid var(--border-medium) |
| color | --radius-xl);
}

.agent-icon-large {
  width | 32px |
| color | --purple-primary);
}

.agent-label {
  font-size | var(--text-sm) |
| color | --bg-glass);
  border | 1px solid var(--border-medium) |
| color | --bg-glass-hover);
  transform | translateY(-1px) |
| color | --purple-primary);
  flex-shrink | 0 |
| color | --text-base);
  font-weight | var(--font-weight-medium) |
| color | --text-primary);
  margin-bottom | var(--space-xs) |
| color | --text-sm);
  color | var(--text-tertiary) |
| color | --purple-primary);
}

.placeholder-text {
  color | var(--text-tertiary) |
| color | --text-base);
}

.analysis-text {
  line-height | var(--line-height-relaxed) |
| color | --text-secondary);
}

.analysis-text h2 {
  color | var(--text-primary) |
| color | --text-xl);
  font-weight | var(--font-weight-semibold) |
| color | --space-xl) 0 var(--space-md);
  border-bottom | 1px solid var(--border-medium) |
| color | --space-sm);
}

.analysis-text h3 {
  color | var(--text-primary) |
| color | --text-lg);
  font-weight | var(--font-weight-medium) |
| color | --space-lg) 0 var(--space-sm);
}

.analysis-text p {
  margin-bottom | var(--space-md) |
| color | --space-sm);
}

.analysis-text strong {
  color | var(--text-primary) |
| color | --font-weight-semibold);
}

.no-analysis, .error-state {
  display | flex |
| color | --text-quaternary);
}

.no-analysis h3, .error-state h3 {
  color | var(--text-secondary) |
| color | --text-lg);
  font-weight | var(--font-weight-medium) |
| color | --text-quaternary);
}

/* Video overview and analysis components */
.video-overview {
  margin-bottom | var(--space-2xl) |
| color | --text-xl);
  font-weight | var(--font-weight-semibold) |
| color | --text-primary);
  margin-bottom | var(--space-sm) |
| color | --text-sm);
}

.stat-item i {
  width | 14px |
| color | --text-sm);
  font-weight | var(--font-weight-medium) |
| color | --border-medium);
  border-radius | var(--radius-md) |
| color | --text-tertiary);
  font-size | var(--text-xs) |
| color | --success);
  border | 1px solid rgba(34, 197, 94, 0.3) |
| color | --success);
  border | 1px solid rgba(34, 197, 94, 0.3) |
| color | --error);
  border | 1px solid rgba(239, 68, 68, 0.3) |
| color | --border-light);
  border-radius | var(--radius-xl) |
| color | --space-xl);
  box-shadow | var(--shadow-md) |
| color | --shadow-lg);
  transform | translateY(-2px) |
| color | --text-lg);
  font-weight | var(--font-weight-semibold) |
| color | --text-primary);
  margin-bottom | var(--space-xs) |
| color | --text-sm);
  color | var(--text-tertiary) |
| color | --purple-primary);
  flex-shrink | 0 |
| color | --text-tertiary);
  line-height | var(--line-height-relaxed) |
| color | --purple-gradient);
  border-radius | var(--radius-xl) |
| color | --text-lg);
  font-weight | var(--font-weight-semibold) |
| color | --text-primary);
  margin-bottom | var(--space-sm) |
| color | --text-tertiary);
  line-height | var(--line-height-relaxed) |
| color | --bg-glass);
  color | var(--text-secondary) |
| color | --space-xs) var(--space-sm);
  border-radius | var(--radius-md) |
| color | --text-xs);
  font-weight | var(--font-weight-medium) |
| color | --border-medium);
}

.tool-footer {
  display | flex |
| color | --space-lg);
  border-top | 1px solid var(--border-medium) |
| color | --text-sm);
  font-weight | var(--font-weight-medium) |
| color | --space-xs) var(--space-sm);
  border-radius | var(--radius-md) |
| color | --success);
  color | white |
| color | --bg-glass);
  color | var(--text-tertiary) |
| color | --border-medium);
}

.recent-card {
  display | flex |
| color | --bg-tertiary);
  display | flex |
| color | --text-muted);
}

.recent-overlay {
  position | absolute |
| color | --purple-gradient);
  color | white |
| color | --text-base);
  font-weight | var(--font-weight-medium) |
| color | --text-primary);
  margin-bottom | var(--space-xs) |
| color | --text-sm);
  color | var(--text-tertiary) |
| color | --bg-glass);
  color | var(--text-secondary) |
| color | --font-weight-medium);
  border | 1px solid var(--border-medium) |
| color | --text-xl);
  font-weight | var(--font-weight-bold) |
| color | --purple-primary);
}

.usage-progress {
  margin-bottom | var(--space-lg) |
| color | --bg-tertiary);
  border-radius | var(--radius-full) |
| color | --purple-gradient);
  transition | width var(--transition-normal) |
| color | --text-sm);
  color | var(--text-tertiary) |
| color | --success);
}

.stats-card {
  padding | var(--space-xl) |
| color | --text-lg);
  font-weight | var(--font-weight-semibold) |
| color | --text-primary);
  margin-bottom | var(--space-lg) |
| color | --text-2xl);
  font-weight | var(--font-weight-bold) |
| color | --text-primary);
  margin-bottom | var(--space-xs) |
| color | --text-sm);
  color | var(--text-tertiary) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --radius-lg);
  box-shadow | var(--shadow-lg) |
| color | --border-light);
  font-weight | var(--font-weight-semibold) |
| color | --text-primary);
  font-size | var(--text-sm) |
| color | --space-xl);
  text-align | center |
| color | --text-tertiary);
  font-size | var(--text-sm) |
| color | --space-md) var(--space-lg);
  border-bottom | 1px solid var(--border-light) |
| color | --text-xs);
  color | var(--text-tertiary) |
| color | --success);
}

.export-buttons {
  display | flex |
| color | --bg-primary);
  border-radius | var(--radius-lg) |
| color | --border-light);
  box-shadow | var(--shadow-sm) |
| color | --border-light);
}

.channel-info h2 {
  font-size | var(--text-2xl) |
| color | --space-xs) 0;
}

.channel-info p {
  color | var(--text-secondary) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --purple-gradient);
  border-radius | var(--radius-lg) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --text-sm);
  font-weight | var(--font-weight-semibold) |
| color | --text-secondary);
  margin-bottom | var(--space-xs) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --shadow-md);
  transform | translateY(-1px) |
| color | --text-base);
  font-weight | var(--font-weight-medium) |
| color | --text-primary);
  margin-bottom | var(--space-sm) |
| color | --border-light);
  border-radius | var(--radius-lg) |
| color | --text-sm);
  font-weight | var(--font-weight-semibold) |
| color | --purple-primary);
  margin-bottom | var(--space-sm) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --shadow-md);
  transform | translateY(-1px) |
| color | --text-sm);
  font-weight | var(--font-weight-semibold) |
| color | --text-secondary);
  margin-bottom | var(--space-sm) |
| color | --purple-primary);
}

.loading-section, .error-section {
  margin-bottom | var(--space-2xl) |
| color | --space-lg);
  text-align | center |
| color | --border-light);
  border-top-color | var(--purple-primary) |
| color | --text-tertiary);
  font-size | var(--text-lg) |
| color | --error);
}

/* Tabbed interface components */
.tab-buttons {
  display | flex |
| color | --bg-secondary);
  border | 1px solid var(--border-light) |
| color | --bg-tertiary);
  border-color | var(--border-medium) |
| color | --text-primary);
}

.tab-button.active {
  background | var(--purple-primary) |
| color | --purple-primary);
  color | white |
| color | --border-light);
  border-radius | var(--radius-lg) |
| color | --bg-tertiary);
  border-color | var(--border-medium) |
| color | --purple-primary);
  border-radius | var(--radius-lg) |
| color | --text-2xl);
  font-weight | 700 |
| color | --text-primary);
  line-height | 1 |
| color | --text-sm);
  color | var(--text-tertiary) |
| color | --space-md);
}

.recommendation-card {
  border-left | 4px solid var(--purple-primary) |
| color | --purple-primary);
  color | white |
| color | --text-sm);
  flex-shrink | 0 |
| color | --text-base);
  font-weight | 600 |
| color | --text-sm);
  line-height | 1.6 |
| color | --bg-tertiary);
}

.video-thumbnail img {
  width | 100% |
| color | --text-sm);
}

.stat-item {
  display | flex |
| color | --space-xs) var(--space-sm);
  border-radius | var(--radius-sm) |
| color | --text-xs);
  font-weight | var(--font-weight-medium) |
| color | --purple-primary);
}

.shorts-indicator {
  position | absolute |
| color | --purple-primary);
  color | white |
| color | --space-xs) var(--space-sm);
  border-radius | var(--radius-sm) |
| color | --text-xs);
  font-weight | var(--font-weight-medium) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --bg-secondary);
  border-radius | var(--radius-md) |
| color | --purple-gradient);
  transition | width var(--transition-normal) |
| color | --bg-elevated);
  border | 1px solid var(--border-light) |
| color | --radius-lg);
  text-align | center |
| color | --border-light);
  border-radius | var(--radius-lg) |
| color | --text-lg);
  font-weight | var(--font-weight-medium) |
| color | --text-primary);
  margin | 0 0 var(--space-md) 0 |
| color | --text-sm);
}

.metric {
  display | flex |
| color | --border-light);
  border-radius | var(--radius-lg) |
| color | --text-base);
  font-weight | var(--font-weight-semibold) |
| color | --text-primary);
  margin | 0 0 var(--space-md) 0 |
| color | --text-secondary);
}

.psychology-insights {
  margin-top | var(--space-lg) |
| color | --border-light);
  border-radius | var(--radius-lg) |
| color | --space-xs) var(--space-sm);
  border-radius | var(--radius-sm) |
| color | --text-xs);
  font-weight | 600 |
| color | --text-primary);
  margin-bottom | var(--space-xs) |
| color | --text-sm);
  color | var(--text-tertiary) |
| color | --purple-gradient);
  border-radius | var(--radius-2xl) |
| color | --space-lg);
  box-shadow | 0 8px 24px rgba(139, 92, 246, 0.3),
    0 16px 48px rgba(139, 92, 246, 0.15) |
| color | --text-3xl);
  font-weight | var(--font-weight-bold) |
| color | --text-primary);
  margin | 0 0 var(--space-sm) 0 |
| color | --text-primary) 0%, var(--purple-primary) 100%);
  background-clip | text |
| color | --text-lg);
  color | var(--text-secondary) |
| color | --text-lg);
  font-weight | var(--font-weight-medium) |
| color | --text-primary);
  transition | all var(--transition-normal) |
| color | --text-primary);
  box-shadow | 0 4px 12px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05) |
| color | --purple-primary);
  box-shadow | 0 0 0 4px rgba(139, 92, 246, 0.1),
    0 8px 24px rgba(139, 92, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.8) |
| color | --text-muted);
  font-style | italic |
| color | --purple-gradient);
  border | none |
| color | --text-lg);
  font-weight | var(--font-weight-semibold) |
| color | --transition-normal);
  box-shadow | 0 8px 24px rgba(139, 92, 246, 0.3),
    0 16px 48px rgba(139, 92, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) |
| color | --purple-primary);
}

.premium-select {
  background | rgba(255, 255, 255, 0.9) |
| color | --text-base);
  color | var(--text-primary) |
| color | --text-primary);
}

.premium-select | focus {
  outline: none |
| color | --purple-primary);
  box-shadow | 0 0 0 3px rgba(139, 92, 246, 0.1),
    0 4px 12px rgba(139, 92, 246, 0.15) |
| color | --purple-primary);
}

/* Enhanced Results Section */
.results-section {
  margin-top | var(--space-2xl) |
| color | --border-light);
  position | relative |
| color | --purple-gradient);
}

.results-title {
  font-size | var(--text-2xl) |
| color | --text-secondary);
  margin | 0 |
| color | --purple-primary);
  box-shadow | 0 16px 48px rgba(0, 0, 0, 0.1),
    0 24px 80px rgba(139, 92, 246, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.9) |
| color | --purple-gradient);
  border-radius | var(--radius-2xl) |
| color | --space-lg);
  box-shadow | 0 8px 24px rgba(139, 92, 246, 0.3),
    0 16px 48px rgba(139, 92, 246, 0.15),
    inset 0 2px 4px rgba(255, 255, 255, 0.2) |
| color | --text-4xl);
  font-weight | var(--font-weight-extrabold) |
| color | --text-primary);
  margin | 0 0 var(--space-sm) 0 |
| color | --text-primary) 0%, var(--purple-primary) 100%);
  background-clip | text |
| color | --text-base);
  font-weight | var(--font-weight-semibold) |
| color | --text-secondary);
  margin | 0 |
| color | --text-base);
  }

  .premium-form-input {
    padding | var(--space-md) var(--space-lg) |
| color | --text-base);
  }

  .premium-analyze-btn {
    padding | var(--space-md) var(--space-xl) |
| color | --text-base);
  }

  .premium-stats-grid {
    grid-template-columns | 1fr |
| typography | --font-mono | 'JetBrains Mono', 'Fira Code', monospace |
| typography | --font-display | 'Inter', sans-serif |
| typography | --font-weight-normal | 400 |
| typography | --font-weight-medium | 500 |
| typography | --font-weight-semibold | 600 |
| typography | --font-weight-bold | 700 |
| typography | --font-weight-extrabold | 800 |
| typography | --font-weight-normal);
  line-height | 1.6 |
| typography | --font-weight-bold);
  font-size | var(--text-lg) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-semibold);
}

.usage-bar {
  height | 4px |
| typography | --font-weight-semibold);
  color | var(--text-tertiary) |
| typography | --font-weight-medium);
  transition | var(--transition-fast) |
| typography | --font-weight-semibold);
  font-size | var(--text-sm) |
| typography | --font-weight-medium);
}

.version-number {
  font-size | var(--text-xs) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-semibold);
  color | var(--text-secondary) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-medium);
}

.stat-trend.up {
  color | var(--success) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-semibold);
  color | var(--purple-primary) |
| typography | --font-weight-bold);
  color | var(--purple-primary) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-bold);
  font-size | var(--text-sm) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-bold);
  color | var(--text-primary) |
| typography | --font-weight-medium);
  color | var(--text-secondary) |
| typography | --font-mono); }
.font-display { font-family | var(--font-display) |
| typography | --font-weight-normal); }
.font-medium { font-weight | var(--font-weight-medium) |
| typography | --font-weight-semibold); }
.font-bold { font-weight | var(--font-weight-bold) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-medium);
  transition | color var(--transition-fast) |
| typography | --font-weight-bold);
}

.recent-content {
  flex | 1 |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-medium);
}

.header-left, .header-right {
  display | flex |
| typography | --font-weight-medium);
  color | var(--text-secondary) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-medium);
}

.performance-note {
  padding | var(--space-xs) var(--space-sm) |
| typography | --font-weight-medium);
}

.performance-note.viral-performance {
  background | rgba(34, 197, 94, 0.1) |
| typography | --font-weight-medium);
}

.status-indicator.processing {
  background | rgba(59, 130, 246, 0.1) |
| typography | --font-weight-bold);
}

.recent-content {
  flex | 1 |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-medium);
  color | var(--text-primary) |
| typography | --font-weight-bold);
  color | var(--text-primary) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-medium);
  color | var(--text-primary) |
| typography | --font-weight-medium);
  color | var(--text-secondary) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-semibold);
  color | var(--text-primary) |
| typography | --font-weight-bold);
  color | var(--text-primary) |
| spacing | --space-xs | 0.25rem |
| spacing | --space-sm | 0.5rem |
| spacing | --space-md | 1rem |
| spacing | --space-lg | 1.5rem |
| spacing | --space-xl | 2rem |
| spacing | --space-2xl | 3rem |
| spacing | --space-3xl | 4rem |
| spacing | --space-xl);
  position | sticky |
| spacing | --space-xl);
  background | var(--bg-secondary) |
| spacing | --space-2xl) var(--space-xl);
  background | var(--bg-secondary) |
| spacing | --space-sm);
  padding | var(--space-sm) |
| spacing | --space-md);
}

.user-avatar {
  width | 40px |
| spacing | --space-sm);
}

.usage-header {
  display | flex |
| spacing | --space-xs);
}

.usage-label {
  font-size | var(--text-xs) |
| spacing | --space-sm);
}

.usage-fill {
  height | 100% |
| spacing | --space-lg);
  overflow-y | auto |
| spacing | --space-xl);
}

.nav-section-title {
  font-size | var(--text-sm) |
| spacing | --space-md);
}

.nav-items {
  list-style | none |
| spacing | --space-xs);
}

.nav-item {
  display | flex |
| spacing | --space-sm);
  padding | var(--space-sm) var(--space-md) |
| spacing | --space-xs);
}

.nav-item | hover {
  background: var(--bg-overlay) |
| spacing | --space-md);
  top | 50% |
| spacing | --space-md);
  margin-bottom | var(--space-md) |
| spacing | --space-sm);
  background | var(--bg-secondary) |
| spacing | --space-xs);
  min-width | 0 |
| spacing | --font-weight-medium);
  white-space | nowrap |
| spacing | --space-xs);
  margin-bottom | var(--space-sm) |
| spacing | --space-xs);
  padding | var(--space-xs) |
| spacing | --space-xs) 0 0 0;
}

.card-content {
  padding | var(--space-xl) |
| spacing | --space-sm);
  padding | var(--space-sm) var(--space-md) |
| spacing | --space-xs) var(--space-sm);
  font-size | var(--text-xs) |
| spacing | --space-xs);
}

.btn-lg {
  padding | var(--space-md) var(--space-lg) |
| spacing | --space-md);
  padding | var(--space-lg) |
| spacing | --space-xs);
}

.card-action-btn {
  width | 32px |
| spacing | --space-lg);
}

.analysis-card-footer {
  display | flex |
| spacing | --space-md) var(--space-lg);
  background | var(--bg-overlay) |
| spacing | --space-md);
  font-size | var(--text-xs) |
| spacing | --space-md);
  padding | var(--space-md) |
| spacing | --space-xs) 0;
  font-size | var(--text-base) |
| spacing | --space-sm) 0;
  font-size | var(--text-sm) |
| spacing | --space-sm);
}

.progress-bar {
  flex | 1 |
| spacing | --space-md);
}

.insight-priority {
  width | 32px |
| spacing | --space-sm) 0;
  font-size | var(--text-lg) |
| spacing | --space-md) 0;
  font-size | var(--text-sm) |
| spacing | --space-lg);
  margin-bottom | var(--space-lg) |
| spacing | --space-xs);
}

.metric-label {
  font-size | var(--text-xs) |
| spacing | --space-sm);
}

/* Metric Cards */
.metric-card {
  background | var(--surface-card) |
| spacing | --space-lg);
  backdrop-filter | blur(12px) |
| spacing | --space-xs);
}

.stat-trend {
  font-size | var(--text-xs) |
| spacing | --space-md);
  padding | var(--space-md) |
| spacing | --space-xs) 0;
  font-size | var(--text-base) |
| spacing | --space-md);
}

.metric-icon {
  width | 40px |
| spacing | --space-xs) 0 var(--space-sm) 0;
  font-size | var(--text-sm) |
| spacing | --space-xs);
}

.change-value {
  font-size | var(--text-sm) |
| spacing | --space-md);
}

.progress-title {
  margin | 0 |
| spacing | --space-lg);
}

.progress-steps {
  display | flex |
| spacing | --space-md);
}

.progress-step {
  display | flex |
| spacing | --space-xs);
  flex | 1 |
| spacing | --space-md) var(--space-lg);
  background | var(--bg-overlay) |
| spacing | --space-sm);
}

.feed-icon {
  width | 16px |
| spacing | --space-sm);
}

.status-feed-item {
  display | flex |
| spacing | --space-sm);
  padding | var(--space-sm) |
| spacing | --space-sm) var(--space-lg);
  background | var(--bg-overlay) |
| spacing | --space-xs);
  font-size | var(--text-xs) |
| spacing | --space-md);
  margin-bottom | var(--space-lg) |
| spacing | --space-sm);
  margin-bottom | var(--space-lg) |
| spacing | --space-sm);
}

.skeleton-button {
  height | 32px |
| spacing | --space-xl);
  background | var(--bg-elevated) |
| spacing | --space-sm) 0;
  font-size | var(--text-xl) |
| spacing | --space-lg) 0;
  font-size | var(--text-base) |
| spacing | --space-sm);
}

/* Empty State */
.empty-state {
  display | flex |
| spacing | --space-xl);
  background | var(--bg-elevated) |
| spacing | --space-lg);
}

.empty-icon-svg {
  width | 32px |
| spacing | --space-sm);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity | 0 |
| spacing | --space-md);
  padding | var(--space-md) |
| spacing | --space-md) center;
  background-repeat | no-repeat |
| spacing | --space-2xl);
}

/* ====== ANALYSIS REPORT COMPONENTS ====== */
.analysis-section {
  margin-bottom | var(--space-2xl) |
| spacing | --space-md);
  margin-bottom | var(--space-lg) |
| spacing | --space-xs) 0 0 0;
}

.metric-grid {
  display | grid |
| spacing | --space-lg);
  margin-bottom | var(--space-xl) |
| spacing | --space-md);
}

.metric-label {
  font-size | var(--text-sm) |
| spacing | --space-xs);
}

.metric-change.positive {
  color | var(--success) |
| spacing | --space-lg);
  }

  .premium-content {
    padding | var(--space-xl) var(--space-lg) |
| spacing | --space-lg);
  }

  .card-content {
    padding | var(--space-lg) |
| spacing | --space-2xl);
}

.analyzer-form {
  display | grid |
| spacing | --space-lg);
}

.analyzer-form.grid-layout {
  grid-template-columns | 1fr 120px auto |
| spacing | --space-sm);
  padding | var(--space-sm) var(--space-md) |
| spacing | --space-sm);
}

.btn-spinner.active {
  display | flex |
| spacing | --space-2xl);
}

.analyzer-form-section {
  margin-bottom | var(--space-2xl) |
| spacing | --space-xl);
}

.analysis-card {
  background | var(--bg-elevated) |
| spacing | --space-lg);
}

.card-title {
  font-size | var(--text-lg) |
| spacing | --space-xs);
}

/* Section headers */
.section-header {
  display | flex |
| spacing | --space-xl);
}

.section-title {
  font-size | var(--text-2xl) |
| spacing | --space-xs);
}

.section-link {
  color | var(--purple-primary) |
| spacing | --space-lg);
  }

  .results-grid {
    grid-template-columns | 1fr |
| spacing | --space-md);
  }

  .form-row {
    grid-template-columns | 1fr |
| spacing | --space-sm);
  }

  .card-header {
    flex-direction | column |
| spacing | --space-sm);
  }
}

/* ====== DASHBOARD SPECIFIC COMPONENTS ====== */
/* Unique dashboard layout components */

.welcome-section {
  display | grid |
| spacing | --space-2xl);
  margin-bottom | var(--space-2xl) |
| spacing | --space-lg);
  min-width | 600px |
| spacing | --space-2xl);
}

.quick-analysis-form {
  display | flex |
| spacing | --space-lg);
  align-items | flex-end |
| spacing | --space-2xl);
}

.tools-grid {
  display | grid |
| spacing | --space-xl);
}

.tool-card {
  display | flex |
| spacing | --space-lg);
}

.tool-icon-svg {
  width | 24px |
| spacing | --space-lg);
}

.tool-features {
  display | flex |
| spacing | --space-sm);
  margin-bottom | var(--space-lg) |
| spacing | --space-lg);
}

.recent-card {
  display | flex |
| spacing | --space-lg);
  padding | var(--space-lg) |
| spacing | --space-xs);
  right | var(--space-xs) |
| spacing | --space-sm);
}

.recent-insights {
  display | flex |
| spacing | --space-xs);
  flex-wrap | wrap |
| spacing | --space-2xl);
}

.usage-grid {
  display | grid |
| spacing | --space-xl);
}

.usage-header {
  display | flex |
| spacing | --space-lg);
}

.usage-title {
  font-size | var(--text-lg) |
| spacing | --space-sm);
}

.progress-fill {
  height | 100% |
| spacing | --space-sm);
}

.usage-feature {
  display | flex |
| spacing | --space-sm);
  color | var(--text-secondary) |
| spacing | --space-lg);
}

.stats-grid {
  display | grid |
| spacing | --space-sm);
  margin-left | var(--space-lg) |
| spacing | --space-sm);
}

.metric-icon {
  width | 14px |
| spacing | --space-xl);
  }

  .usage-grid {
    grid-template-columns | 1fr |
| spacing | --space-sm);
  padding | var(--space-lg) |
| spacing | --space-xl);
}

.capabilities-title {
  font-size | var(--text-lg) |
| spacing | --space-lg);
}

.capabilities-grid {
  display | grid |
| spacing | --space-lg);
}

.capability-item {
  display | flex |
| spacing | --space-md);
  padding | var(--space-lg) |
| spacing | --space-lg);
}

.placeholder-spinner .spinner {
  width | 32px |
| spacing | --space-md) 0;
  padding-left | var(--space-xl) |
| spacing | --space-lg);
  color | var(--text-tertiary) |
| spacing | --space-lg);
  align-items | start |
| spacing | --space-lg);
  margin-bottom | var(--space-sm) |
| spacing | --space-xs);
  color | var(--text-tertiary) |
| spacing | --space-lg);
  align-items | center |
| spacing | --space-xs);
  color | var(--text-secondary) |
| spacing | --space-xs) var(--space-sm);
  background | var(--bg-glass) |
| spacing | --space-2xl);
}

.analysis-status {
  display | flex |
| spacing | --space-sm);
  padding | var(--space-sm) var(--space-md) |
| spacing | --space-lg);
}

/* Comment Intelligence responsive adjustments */
@media (max-width | 768px) {
  .capabilities-grid {
    grid-template-columns: 1fr |
| spacing | --space-sm);
  }
}

/* ====== PREMIUM CARD SYSTEM ====== */
.premium-card {
  background | var(--bg-elevated) |
| spacing | --space-lg);
  padding | var(--space-lg) |
| spacing | --space-lg)) calc(-1 * var(--space-lg)) var(--space-lg) calc(-1 * var(--space-lg));
  background | linear-gradient(135deg,
    rgba(139, 92, 246, 0.03) 0%,
    rgba(139, 92, 246, 0.01) 50%,
    rgba(139, 92, 246, 0.05) 100%) |
| spacing | --space-lg);
}

/* ====== DASHBOARD SPECIFIC CARDS ====== */
.tool-card {
  display | flex |
| spacing | --space-lg);
}

.tool-icon-svg {
  width | 24px |
| spacing | --space-lg);
}

.tool-features {
  display | flex |
| spacing | --space-sm);
  margin-bottom | var(--space-lg) |
| spacing | --space-lg);
  padding | var(--space-lg) |
| spacing | --space-xs);
  right | var(--space-xs) |
| spacing | --space-sm);
}

.recent-insights {
  display | flex |
| spacing | --space-xs);
  flex-wrap | wrap |
| spacing | --space-xl);
}

.usage-header {
  display | flex |
| spacing | --space-lg);
}

.usage-title {
  font-size | var(--text-lg) |
| spacing | --space-sm);
}

.progress-fill {
  height | 100% |
| spacing | --space-sm);
}

.usage-feature {
  display | flex |
| spacing | --space-sm);
  color | var(--text-secondary) |
| spacing | --space-xs);
}

.history-header {
  padding | var(--space-md) var(--space-lg) |
| spacing | --space-xs);
  white-space | nowrap |
| spacing | --space-2xl);
}

.export-status {
  display | flex |
| spacing | --space-xs);
}

.status-dot {
  width | 8px |
| spacing | --space-md);
  flex-wrap | wrap |
| spacing | --space-xl);
  padding | var(--space-lg) |
| spacing | --space-2xl);
}

.channel-header {
  display | flex |
| spacing | --space-lg);
  margin-bottom | var(--space-xl) |
| spacing | --space-lg);
  margin-top | var(--space-xl) |
| spacing | --space-md);
}

.stat-icon i {
  width | 20px |
| spacing | --space-md);
}

.video-thumbnail img {
  width | 100% |
| spacing | --space-md);
  transition | all var(--transition-normal) |
| spacing | --space-lg);
  margin-bottom | var(--space-md) |
| spacing | --space-md);
  transition | all var(--transition-normal) |
| spacing | --space-md);
}

.input-group .form-input {
  flex | 1 |
| spacing | --space-sm);
  flex-wrap | wrap |
| spacing | --space-sm);
  padding | var(--space-md) |
| spacing | --space-xl);
}

.tab-content.active {
  display | block |
| spacing | --space-lg);
}

.stat-card {
  background | var(--bg-secondary) |
| spacing | --space-lg);
  display | flex |
| spacing | --space-md);
  transition | all 0.2s ease |
| spacing | --space-xs);
}

/* Research recommendations and insights */
.recommendations-section {
  margin-top | var(--space-xl) |
| spacing | --space-md);
}

.recommendation-number {
  width | 32px |
| spacing | --space-xs) 0;
  color | var(--text-primary) |
| spacing | --space-sm) 0;
  color | var(--text-secondary) |
| spacing | --space-lg);
  margin-top | var(--space-xl) |
| spacing | --space-md);
}

.video-title {
  font-size | var(--text-lg) |
| spacing | --space-sm) 0;
  line-height | 1.4 |
| spacing | --space-md);
  color | var(--text-secondary) |
| spacing | --space-xs);
}

.stat-item i {
  width | 14px |
| spacing | --space-xs);
  right | var(--space-xs) |
| spacing | --space-xs);
  left | var(--space-xs) |
| spacing | --space-xl);
}

.schedule-overview {
  margin-bottom | var(--space-xl) |
| spacing | --space-lg);
  margin-top | var(--space-lg) |
| spacing | --space-xs);
  padding | var(--space-lg) |
| spacing | --space-md);
  margin-top | var(--space-lg) |
| spacing | --space-md);
}

.day-name {
  min-width | 80px |
| spacing | --space-xl);
}

.title-overview {
  margin-bottom | var(--space-xl) |
| spacing | --space-lg);
  margin-top | var(--space-lg) |
| spacing | --space-xs);
  padding | var(--space-lg) |
| spacing | --space-xl);
}

.top-titles {
  display | flex |
| spacing | --space-md);
  margin-top | var(--space-lg) |
| spacing | --space-lg);
  background | var(--bg-elevated) |
| spacing | --space-lg);
  color | var(--text-secondary) |
| spacing | --space-xs);
}

.psychology-analysis {
  margin-bottom | var(--space-xl) |
| spacing | --space-lg);
  margin-top | var(--space-lg) |
| spacing | --space-lg);
  background | var(--bg-elevated) |
| spacing | --space-xs);
  font-size | var(--text-sm) |
| spacing | --space-lg);
  background | var(--bg-elevated) |
| spacing | --space-sm) 0;
  color | var(--text-secondary) |
| spacing | --space-sm);
  right | var(--space-sm) |
| spacing | --space-md);
}

.video-title {
  font-size | var(--text-base) |
| spacing | --space-xs);
}

.video-date {
  font-size | var(--text-xs) |
| spacing | --space-2xl);
}

.form-header .market-icon {
  width | 64px |
| spacing | --space-lg);
  align-items | end |
| spacing | --space-xl);
}

.premium-form-input {
  background | rgba(255, 255, 255, 0.9) |
| spacing | --space-sm);
}

.form-options {
  display | grid |
| spacing | --space-lg);
  margin-bottom | var(--space-xl) |
| spacing | --space-sm);
}

.form-option label {
  font-size | var(--text-sm) |
| spacing | --space-sm);
}

.form-option .label-icon {
  width | 16px |
| spacing | --space-md) center;
  background-repeat | no-repeat |
| spacing | --space-2xl);
}

[data-theme="dark"] .premium-select {
  background | rgba(30, 41, 59, 0.8) |
| spacing | --space-sm);
}

.form-label {
  font-size | var(--text-sm) |
| spacing | --space-sm);
}

.form-label .label-icon {
  width | 16px |
| spacing | --space-2xl);
  padding | var(--space-xl) 0 |
| spacing | --space-sm) 0;
}

.results-subtitle {
  font-size | var(--text-lg) |
| spacing | --space-xl);
  margin-bottom | var(--space-2xl) |
| spacing | --space-lg);
  }

  .form-options {
    grid-template-columns | 1fr |
| spacing | --space-xl);
  }

  .form-header h1 {
    font-size | var(--text-2xl) |
| radius | --radius-sm | 0.375rem |
| radius | --radius-md | 0.5rem |
| radius | --radius-lg | 0.75rem |
| radius | --radius-xl | 1rem |
| radius | --radius-2xl | 1.5rem |
| radius | --radius-full | 9999px |
| radius | --radius-sm);
  font-size | var(--text-xs) |
| radius | --radius-full);
  margin-left | auto |
| radius | --radius-md);
  font-family | var(--font-primary) |
| radius | --radius-sm);
  line-height | 1.2 |
| radius | --radius-lg);
  overflow | hidden |
| radius | --radius-md);
  transition | var(--transition-fast) |
| radius | --radius-full);
  display | flex |
| radius | --radius-lg);
  padding | var(--space-lg) |
| radius | --radius-lg);
  padding | var(--space-lg) |
| radius | --radius-md);
  display | flex |
| radius | --radius-md);
  transition | var(--transition-fast) |
| radius | --radius-sm);
  background | var(--bg-secondary) |
| radius | --radius-md);
  display | flex |
| radius | --radius-lg);
  padding | var(--space-lg) |
| radius | --radius-full);
  background | var(--bg-muted) |
| radius | --radius-lg);
  overflow | hidden |
| radius | --radius-md);
  transition | var(--transition-fast) |
| radius | --radius-full);
  background | var(--purple-primary) |
| radius | --radius-full);
  background | var(--text-tertiary) |
| radius | --radius-lg);
  padding | var(--space-lg) |
| radius | --radius-full);
  display | flex |
| radius | --radius-md);
  margin-bottom | var(--space-sm) |
| radius | --radius-full);
  display | flex |
| radius | --radius-md);
  font-family | var(--font-primary) |
| radius | --radius-lg);
  display | flex |
| radius | --radius-md);
  color | var(--text-secondary) |
| radius | --radius-lg);
  padding | var(--space-2xl) |
| radius | --radius-lg);
  overflow | hidden |
| radius | --radius-sm);
  font-size | var(--text-xs) |
| radius | --radius-sm);
  font-size | var(--text-xs) |
| radius | --radius-lg);
  transition | all var(--transition-fast) |
| radius | --radius-md);
  font-size | var(--text-xs) |
| radius | --radius-md);
  font-size | var(--text-sm) |
| radius | --radius-md) var(--radius-md) 0 0;
  position | relative |
| radius | --radius-lg);
  overflow | hidden |
| radius | --radius-sm);
  font-size | var(--text-xs) |
| radius | --radius-sm);
  font-size | var(--text-xs) |
| radius | --radius-lg);
  padding | var(--space-lg) |
| radius | --radius-lg);
  padding | var(--space-lg) |
| radius | --radius-md);
  overflow | hidden |
| radius | --radius-lg);
  padding | var(--space-lg) |
| radius | --radius-lg);
  padding | var(--space-lg) |
| radius | --radius-lg);
  padding | var(--space-lg) |
| radius | --radius-lg);
  color | var(--text-secondary) |
| radius | --radius-lg);
}

.schedule-patterns {
  margin-bottom | var(--space-xl) |
| radius | --radius-2xl);
  padding | var(--space-2xl) |
| radius | --radius-xl);
  padding | var(--space-lg) var(--space-xl) |
| radius | --radius-xl);
  padding | var(--space-lg) var(--space-2xl) |
| radius | --radius-lg);
  padding | var(--space-md) var(--space-lg) |
| radius | --radius-2xl);
  padding | var(--space-2xl) |
| transition | --transition-fast | 150ms cubic-bezier(0.4, 0, 0.2, 1) |
| transition | --transition-normal | 300ms cubic-bezier(0.4, 0, 0.2, 1) |
| transition | --transition-slow | 500ms cubic-bezier(0.4, 0, 0.2, 1) |
| transition | --transition-normal);
}

/* Enhanced theme transitions */
* {
  transition | background-color var(--transition-normal), 
              color var(--transition-normal), 
              border-color var(--transition-normal),
              box-shadow var(--transition-normal) |
| transition | --transition-fast);
  color | var(--text-secondary) |
| transition | --transition-normal);
  z-index | 0 |
| transition | --transition-fast);
}

.theme-toggle | hover {
  color: white |
| transition | --transition-normal);
  position | relative |
| transition | --transition-fast);
  color | var(--text-secondary) |
| transition | --transition-normal);
}

.upgrade-btn {
  width | 100% |
| transition | --transition-fast);
  overflow | hidden |
| transition | --transition-fast);
  position | relative |
| transition | --transition-fast);
}

.insight-card | hover {
  border-color: var(--purple-primary) |
| transition | --transition-fast);
}

.progress-step.active .step-indicator {
  background | var(--purple-primary) |
| transition | --transition-normal);
}

/* ====== FORM COMPONENTS ====== */
.form-group {
  margin-bottom | var(--space-lg) |
| transition | --transition-normal);
}

.analysis-card | hover {
  box-shadow: var(--shadow-md) |
| transition | --transition-normal);
  position | relative |
| transition | --transition-fast);
}

.history-item | hover {
  background: var(--bg-tertiary) |
| transition | --transition-normal);
}

.stat-card | hover {
  box-shadow: var(--shadow-md) |
| transition | --transition-normal);
}

.video-card | hover {
  box-shadow: var(--shadow-md) |
| transition | --transition-normal);
}

.insight-card | hover {
  box-shadow: var(--shadow-md) |
| transition | --transition-normal);
}

.trigger-card | hover {
  box-shadow: var(--shadow-md) |
| transition | --transition-slow);
}

.premium-analyze-btn | hover {
  transform: translateY(-2px) |
| transition | --transition-normal);
  appearance | none |
| transition | --transition-slow);
}

.results-section.visible {
  opacity | 1 |
| transition | --transition-normal);
  position | relative |