<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YoutubePulse - Video Analyzer</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/static/favicon.svg">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    <meta name="version" content="2.0">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=JetBrains+Mono&display=swap" rel="stylesheet">
    
    <!-- YouTube Research v2 Design System -->
    <link rel="stylesheet" href="/static/styles/youtube-research-v2-design-system.css?v=2025073003">
    
    <style>
        /* Video Analyzer Specific Styles */
        .analyzer-header {
            background: var(--surface-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-xl);
            margin-bottom: var(--space-lg);
            position: relative;
            overflow: hidden;
        }
        
        .analyzer-header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(139, 92, 246, 0.1) 0%, transparent 70%);
            pointer-events: none;
        }
        
        .analyzer-form {
            display: grid;
            grid-template-columns: 1fr 1fr auto;
            gap: var(--space-md);
            align-items: end;
            position: relative;
            z-index: 2;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
            gap: var(--space-xs);
        }
        
        .form-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
        }
        
        .form-input {
            padding: 1rem 1.5rem;
            background: var(--surface-secondary);
            border: 2px solid rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-md);
            color: var(--text-primary);
            font-size: 1rem;
            transition: all var(--transition-fast);
            outline: none;
        }
        
        .form-input:focus {
            border-color: var(--accent-purple);
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
            background: var(--surface-hover);
        }
        
        .form-textarea {
            min-height: 120px;
            resize: vertical;
            font-family: var(--font-body);
        }
        
        .transcript-status {
            font-size: 0.75rem;
            font-weight: 500;
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-sm);
            margin-left: var(--space-xs);
        }
        
        .transcript-status.auto {
            background: rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }
        
        .transcript-status.manual {
            background: rgba(249, 115, 22, 0.2);
            color: #f97316;
        }
        
        .transcript-status.failed {
            background: rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }
        
        .transcript-info {
            margin-top: var(--space-xs);
            padding: var(--space-sm);
            background: rgba(139, 92, 246, 0.1);
            border-radius: var(--radius-sm);
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        .transcript-status-banner {
            margin-top: var(--space-md);
            padding: var(--space-md);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .transcript-status-banner.auto {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            color: #22c55e;
        }
        
        .transcript-status-banner.manual {
            background: rgba(249, 115, 22, 0.1);
            border: 1px solid rgba(249, 115, 22, 0.2);
            color: #f97316;
        }
        
        .transcript-status-banner.failed {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            color: #ef4444;
        }
        
        .timing-analysis {
            margin-top: var(--space-xs);
            font-size: 0.8rem;
            opacity: 0.8;
        }
        
        .analyze-button {
            height: 56px;
            padding: 0 2rem;
            background: var(--primary-gradient);
            border: none;
            border-radius: var(--radius-md);
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            gap: var(--space-xs);
            box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .analyze-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(139, 92, 246, 0.4);
        }
        
        .analyze-button:active {
            transform: translateY(0);
        }
        
        .analyze-button.loading {
            pointer-events: none;
        }
        
        .analyze-button.loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
            animation: shimmer 1.5s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }
        
        /* Tabs */
        .tab-navigation {
            display: flex;
            gap: var(--space-xs);
            background: var(--surface-primary);
            padding: var(--space-sm);
            border-radius: var(--radius-lg);
            margin-bottom: var(--space-lg);
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .tab-navigation::-webkit-scrollbar {
            display: none;
        }
        
        .tab-button {
            display: flex;
            align-items: center;
            gap: var(--space-xs);
            padding: 0.75rem 1.25rem;
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-md);
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
            white-space: nowrap;
            position: relative;
        }
        
        .tab-button:hover {
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
            border-color: rgba(255, 255, 255, 0.1);
        }
        
        .tab-button.active {
            background: var(--primary-gradient);
            color: white;
            border-color: var(--accent-purple);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }
        
        .tab-icon {
            width: 16px;
            height: 16px;
            flex-shrink: 0;
        }
        
        /* Tab Content */
        .tab-content {
            display: none;
            animation: fade-in-up var(--transition-base) ease-out;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @keyframes fade-in-up {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Analysis Sections */
        .analysis-section {
            background: var(--surface-primary);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            margin-bottom: var(--space-lg);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .section-header {
            display: flex;
            align-items: center;
            gap: var(--space-md);
            margin-bottom: var(--space-lg);
            padding-bottom: var(--space-md);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .section-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--surface-secondary);
            border-radius: var(--radius-md);
            font-size: 1.5rem;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }
        
        .section-subtitle {
            font-size: 1rem;
            color: var(--text-secondary);
        }
        
        /* Metrics Grid */
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-md);
            margin-bottom: var(--space-lg);
        }
        
        .metric-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: var(--space-md);
            background: var(--surface-secondary);
            border-radius: var(--radius-md);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            background: var(--primary-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.25rem;
        }
        
        .metric-label {
            font-size: 0.875rem;
            color: var(--text-tertiary);
            text-align: center;
        }
        
        /* Performance indicators */
        .viral-performance {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
        
        .strong-performance {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(37, 99, 235, 0.1) 100%);
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        
        .weak-performance {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.1) 100%);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }
        
        /* Analysis Content */
        .analysis-content {
            color: var(--text-secondary);
            line-height: 1.7;
        }
        
        .analysis-content h1,
        .analysis-content h2,
        .analysis-content h3,
        .analysis-content h4 {
            color: var(--text-primary);
            margin-top: var(--space-lg);
            margin-bottom: var(--space-md);
        }
        
        .analysis-content h1 {
            font-size: 1.75rem;
            font-weight: 700;
        }
        
        .analysis-content h2 {
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .analysis-content h3 {
            font-size: 1.25rem;
            font-weight: 600;
        }
        
        .analysis-content h4 {
            font-size: 1.125rem;
            font-weight: 600;
        }
        
        .analysis-content p {
            margin-bottom: var(--space-md);
        }
        
        .analysis-content ul,
        .analysis-content ol {
            margin-left: var(--space-lg);
            margin-bottom: var(--space-md);
        }
        
        .analysis-content li {
            margin-bottom: var(--space-xs);
        }
        
        .analysis-content strong {
            color: var(--text-primary);
            font-weight: 600;
        }
        
        .analysis-content em {
            color: var(--accent-purple);
            font-style: normal;
            font-weight: 500;
        }
        
        /* Loading States */
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: var(--space-2xl);
            text-align: center;
        }
        
        .loading-spinner {
            width: 48px;
            height: 48px;
            border: 3px solid var(--surface-secondary);
            border-top: 3px solid var(--accent-purple);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: var(--space-md);
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin-bottom: var(--space-sm);
        }
        
        .loading-subtext {
            font-size: 0.875rem;
            color: var(--text-tertiary);
        }
        
        /* Error States */
        .error-container {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            text-align: center;
        }
        
        .error-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #EF4444;
            margin-bottom: var(--space-sm);
        }
        
        .error-message {
            color: var(--text-secondary);
        }
        
        /* Success States */
        .success-container {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            text-align: center;
            margin-bottom: var(--space-lg);
        }
        
        .success-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #10B981;
            margin-bottom: var(--space-sm);
        }
        
        .success-message {
            color: var(--text-secondary);
        }
        
        /* Fix for styling agent container conflicts */
        #results-area .video-analyzer-report {
            background: none !important;
            min-height: auto !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        #results-area .report-container {
            max-width: none !important;
            margin: 0 !important;
            padding: 0 !important;
        }

        /* Ensure proper spacing and positioning */
        #results-area {
            position: relative;
            z-index: 1;
            margin-top: var(--space-lg);
        }

        /* Prevent analysis content from interfering with navigation */
        .analysis-content {
            position: relative;
            z-index: 1;
        }

        /* Fix any potential overflow issues */
        #results-area * {
            max-width: 100%;
            box-sizing: border-box;
        }
        
        /* Agent Selection Panel Styles */
        .agent-selection-panel {
            background: var(--surface-primary);
            border-radius: var(--radius-xl);
            padding: var(--space-xl);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .panel-header {
            margin-bottom: var(--space-lg);
        }
        
        .panel-header h3 {
            margin: 0 0 var(--space-sm) 0;
            color: var(--text-primary);
            font-size: 1.25rem;
            font-weight: 600;
        }
        
        .panel-header p {
            margin: 0 0 var(--space-md) 0;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .analysis-mode-toggle {
            display: flex;
            gap: var(--space-md);
            background: var(--surface-secondary);
            padding: var(--space-xs);
            border-radius: var(--radius-md);
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .toggle-option {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: all var(--transition-fast);
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .toggle-option input[type="radio"] {
            display: none;
        }
        
        .toggle-option input[type="radio"]:checked + span {
            background: var(--primary-gradient);
            color: white;
            border-radius: var(--radius-sm);
            padding: var(--space-sm) var(--space-md);
            margin: calc(-1 * var(--space-sm)) calc(-1 * var(--space-md));
        }
        
        .agent-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-md);
            margin-bottom: var(--space-lg);
        }
        
        .agent-card {
            background: var(--surface-secondary);
            border: 1px solid rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-lg);
            padding: var(--space-md);
            transition: all var(--transition-fast);
            cursor: pointer;
        }
        
        .agent-card:hover {
            border-color: rgba(139, 92, 246, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }
        
        .agent-card[data-selected="true"] {
            border-color: var(--accent-purple);
            background: rgba(139, 92, 246, 0.05);
        }
        
        .agent-header {
            display: flex;
            align-items: flex-start;
            gap: var(--space-sm);
            margin-bottom: var(--space-sm);
        }
        
        .agent-icon {
            font-size: 1.5rem;
            width: 2.5rem;
            height: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-gradient);
            border-radius: var(--radius-md);
            flex-shrink: 0;
        }
        
        .agent-info {
            flex: 1;
        }
        
        .agent-info h4 {
            margin: 0 0 var(--space-xs) 0;
            color: var(--text-primary);
            font-size: 1rem;
            font-weight: 600;
        }
        
        .agent-info p {
            margin: 0;
            color: var(--text-secondary);
            font-size: 0.875rem;
            line-height: 1.4;
        }
        
        .agent-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.75rem;
            color: var(--text-secondary);
        }
        
        .agent-duration {
            background: rgba(34, 197, 94, 0.1);
            color: #22c55e;
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-sm);
        }
        
        .agent-cost {
            background: rgba(249, 115, 22, 0.1);
            color: #f97316;
            padding: 0.25rem 0.5rem;
            border-radius: var(--radius-sm);
        }
        
        .analysis-options {
            margin-bottom: var(--space-lg);
        }
        
        .option-toggle {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            cursor: pointer;
            padding: var(--space-sm);
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
        }
        
        .option-toggle:hover {
            background: var(--surface-secondary);
        }
        
        .option-toggle input[type="checkbox"] {
            width: 1rem;
            height: 1rem;
            accent-color: var(--accent-purple);
        }
        
        .option-toggle span {
            font-weight: 500;
            color: var(--text-primary);
        }
        
        .option-toggle small {
            display: block;
            color: var(--text-secondary);
            font-size: 0.75rem;
            margin-top: 0.25rem;
        }
        
        .analysis-summary {
            background: var(--surface-secondary);
            border: 1px solid rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-lg);
            padding: var(--space-md);
        }
        
        .summary-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: var(--space-md);
        }
        
        .summary-stats .stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            font-size: 0.875rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .analyzer-form {
                grid-template-columns: 1fr;
                gap: var(--space-md);
            }
            
            .form-group {
                width: 100%;
            }
            
            .analyze-button {
                justify-self: stretch;
            }
            
            .metric-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }
        
        @media (max-width: 640px) {
            .tab-navigation {
                padding: var(--space-xs);
            }
            
            .tab-button {
                padding: 0.5rem 1rem;
                font-size: 0.75rem;
            }
            
            .section-header {
                flex-direction: column;
                align-items: start;
                gap: var(--space-sm);
            }
            
            .section-icon {
                width: 40px;
                height: 40px;
                font-size: 1.25rem;
            }
        }
    </style>
</head>
<body>
    <!-- Background Effects -->
    <div class="bg-gradient-mesh"></div>
    
    <!-- Navigation -->
    <nav class="nav-header">
        <div class="container">
            <div class="nav-content">
                <a href="/" class="nav-logo">
                    <span class="text-gradient">YoutubePulse</span>
                </a>
                <div class="nav-menu">
                    <a href="/" class="btn btn-ghost">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                            <polyline points="9,22 9,12 15,12 15,22"></polyline>
                        </svg>
                        Dashboard
                    </a>
                    <button class="btn btn-ghost">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"></circle>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                        </svg>
                        Settings
                    </button>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="dashboard-layout">
        <div class="container">
            <!-- Analyzer Header -->
            <section class="analyzer-header animate-fade-in">
                <h1 class="heading-1" style="margin-bottom: var(--space-sm); position: relative; z-index: 2;">🎬 AI Video Analyzer</h1>
                <p class="text-body" style="margin-bottom: var(--space-lg); position: relative; z-index: 2;">
                    Deep forensic analysis with 5 specialized AI agents. Uncover performance secrets, script psychology, SEO opportunities, and audience insights.
                </p>
                
                <form class="analyzer-form" id="videoAnalysisForm">
                    <div class="form-group">
                        <label class="form-label" for="video_url">YouTube Video URL</label>
                        <input 
                            type="url" 
                            id="video_url" 
                            class="form-input" 
                            placeholder="https://www.youtube.com/watch?v=..."
                            required
                        >
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label" for="script">
                            Video Script 
                            <span id="transcript-status" class="transcript-status"></span>
                        </label>
                        <textarea 
                            id="script" 
                            class="form-input form-textarea" 
                            placeholder="Optional: Paste script here. If left empty, we'll try to auto-fetch the transcript from YouTube."
                        ></textarea>
                        <div id="transcript-info" class="transcript-info" style="display: none;"></div>
                    </div>
                </form>
                
                <!-- Agent Selection Panel -->
                <div class="agent-selection-panel" id="agentSelectionPanel" style="margin-top: var(--space-lg);">
                    <div class="panel-header">
                        <h3>🤖 Select Analysis Agents</h3>
                        <p>Choose which AI agents to run for your analysis. Each agent provides specialized insights.</p>
                        <div class="analysis-mode-toggle">
                            <label class="toggle-option">
                                <input type="radio" name="analysisMode" value="full" checked>
                                <span>Full Analysis (All 6 Agents)</span>
                            </label>
                            <label class="toggle-option">
                                <input type="radio" name="analysisMode" value="custom">
                                <span>Custom Selection</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="agent-grid" id="agentGrid" style="display: none;">
                        <div class="agent-card" data-agent="performance">
                            <div class="agent-header">
                                <input type="checkbox" id="agent-performance" checked>
                                <div class="agent-icon">📊</div>
                                <div class="agent-info">
                                    <h4>Performance Intelligence</h4>
                                    <p>Channel-relative metrics & benchmarks</p>
                                </div>
                            </div>
                            <div class="agent-details">
                                <span class="agent-duration">~30-60s</span>
                                <span class="agent-cost">~$0.10</span>
                            </div>
                        </div>
                        
                        <div class="agent-card" data-agent="script">
                            <div class="agent-header">
                                <input type="checkbox" id="agent-script" checked>
                                <div class="agent-icon">📝</div>
                                <div class="agent-info">
                                    <h4>Script Forensics</h4>
                                    <p>Script structure & psychological triggers</p>
                                </div>
                            </div>
                            <div class="agent-details">
                                <span class="agent-duration">~45-90s</span>
                                <span class="agent-cost">~$0.15</span>
                            </div>
                        </div>
                        
                        <div class="agent-card" data-agent="seo">
                            <div class="agent-header">
                                <input type="checkbox" id="agent-seo" checked>
                                <div class="agent-icon">🔍</div>
                                <div class="agent-info">
                                    <h4>SEO & Discoverability</h4>
                                    <p>Keyword optimization & search intent</p>
                                </div>
                            </div>
                            <div class="agent-details">
                                <span class="agent-duration">~30-60s</span>
                                <span class="agent-cost">~$0.10</span>
                            </div>
                        </div>
                        
                        <div class="agent-card" data-agent="psychology">
                            <div class="agent-header">
                                <input type="checkbox" id="agent-psychology" checked>
                                <div class="agent-icon">🧠</div>
                                <div class="agent-info">
                                    <h4>Audience Psychology</h4>
                                    <p>Emotional triggers & viewer motivations</p>
                                </div>
                            </div>
                            <div class="agent-details">
                                <span class="agent-duration">~30-60s</span>
                                <span class="agent-cost">~$0.10</span>
                            </div>
                        </div>
                        
                        <div class="agent-card" data-agent="comment">
                            <div class="agent-header">
                                <input type="checkbox" id="agent-comment" checked>
                                <div class="agent-icon">💬</div>
                                <div class="agent-info">
                                    <h4>Comment Intelligence</h4>
                                    <p>Real comment sentiment analysis</p>
                                </div>
                            </div>
                            <div class="agent-details">
                                <span class="agent-duration">~30-60s</span>
                                <span class="agent-cost">~$0.10</span>
                            </div>
                        </div>
                        
                        <div class="agent-card" data-agent="synthesis">
                            <div class="agent-header">
                                <input type="checkbox" id="agent-synthesis" checked>
                                <div class="agent-icon">🎯</div>
                                <div class="agent-info">
                                    <h4>Strategic Synthesis</h4>
                                    <p>Cross-agent correlation & strategy</p>
                                </div>
                            </div>
                            <div class="agent-details">
                                <span class="agent-duration">~60-120s</span>
                                <span class="agent-cost">~$0.20</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="analysis-options">
                        <label class="option-toggle">
                            <input type="checkbox" id="sequentialThinking" checked>
                            <span>Sequential Thinking (Higher Quality)</span>
                            <small>+60% analysis quality, +5-10s per agent</small>
                        </label>
                    </div>
                    
                    <div class="analysis-summary" id="analysisSummary">
                        <div class="summary-stats">
                            <span class="stat">6 Agents Selected</span>
                            <span class="stat">~3-5 min</span>
                            <span class="stat">~$0.75</span>
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="analyze-button" id="analyzeButton" form="videoAnalysisForm">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                        Analyze Video
                    </button>
                </form>
            </section>
            
            <!-- Results Area -->
            <div id="results-area">
                <!-- Initial state - empty -->
            </div>
        </div>
    </main>
    
    <script>
        console.log('VIDEO ANALYZER SCRIPT LOADED - Version 2.0');
        
        // Agent Selection Logic
        document.addEventListener('DOMContentLoaded', function() {
            const analysisMode = document.getElementsByName('analysisMode');
            const agentGrid = document.getElementById('agentGrid');
            const agentCheckboxes = document.querySelectorAll('.agent-card input[type="checkbox"]');
            const sequentialThinking = document.getElementById('sequentialThinking');
            const analysisSummary = document.getElementById('analysisSummary');
            
            // Handle analysis mode toggle
            analysisMode.forEach(radio => {
                radio.addEventListener('change', function() {
                    if (this.value === 'custom') {
                        agentGrid.style.display = 'grid';
                    } else {
                        agentGrid.style.display = 'none';
                        // Check all agents for full analysis
                        agentCheckboxes.forEach(checkbox => {
                            checkbox.checked = true;
                            checkbox.closest('.agent-card').setAttribute('data-selected', 'true');
                        });
                    }
                    updateAnalysisSummary();
                });
            });
            
            // Handle agent card clicks
            document.querySelectorAll('.agent-card').forEach(card => {
                card.addEventListener('click', function(e) {
                    if (e.target.type === 'checkbox') return; // Let checkbox handle itself
                    
                    const checkbox = this.querySelector('input[type="checkbox"]');
                    checkbox.checked = !checkbox.checked;
                    this.setAttribute('data-selected', checkbox.checked);
                    updateAnalysisSummary();
                });
            });
            
            // Handle checkbox changes
            agentCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    this.closest('.agent-card').setAttribute('data-selected', this.checked);
                    updateAnalysisSummary();
                });
            });
            
            // Handle sequential thinking toggle
            sequentialThinking.addEventListener('change', updateAnalysisSummary);
            
            function updateAnalysisSummary() {
                const selectedAgents = Array.from(agentCheckboxes).filter(cb => cb.checked);
                const useSequentialThinking = sequentialThinking.checked;
                
                // Calculate duration and cost
                const baseTime = selectedAgents.length * 45; // Base 45s per agent
                const sequentialBonus = useSequentialThinking ? selectedAgents.length * 10 : 0; // +10s per agent
                const totalTime = baseTime + sequentialBonus;
                
                const baseCost = selectedAgents.length * 0.12; // Base $0.12 per agent
                const sequentialCost = useSequentialThinking ? selectedAgents.length * 0.03 : 0; // +$0.03 per agent
                const totalCost = baseCost + sequentialCost;
                
                // Format time
                let timeStr;
                if (totalTime < 60) {
                    timeStr = `~${totalTime}s`;
                } else {
                    const minutes = Math.ceil(totalTime / 60);
                    timeStr = `~${minutes} min`;
                }
                
                // Update summary
                analysisSummary.innerHTML = `
                    <div class="summary-stats">
                        <span class="stat">
                            ${selectedAgents.length} Agent${selectedAgents.length !== 1 ? 's' : ''} Selected
                        </span>
                        <span class="stat">${timeStr}</span>
                        <span class="stat">~$${totalCost.toFixed(2)}</span>
                    </div>
                `;
            }
            
            // Initialize summary
            updateAnalysisSummary();
        });
        
        // Get selected agents for analysis
        function getSelectedAgents() {
            const fullAnalysis = document.querySelector('input[name="analysisMode"][value="full"]').checked;
            if (fullAnalysis) {
                return ['performance', 'script', 'seo', 'psychology', 'comment', 'synthesis'];
            }
            
            const selectedAgents = [];
            document.querySelectorAll('.agent-card input[type="checkbox"]:checked').forEach(checkbox => {
                const agentName = checkbox.closest('.agent-card').getAttribute('data-agent');
                selectedAgents.push(agentName);
            });
            return selectedAgents;
        }
        
        // Video Analysis Form Handler
        document.getElementById('videoAnalysisForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const videoUrl = document.getElementById('video_url').value;
            const script = document.getElementById('script').value;
            const analyzeButton = document.getElementById('analyzeButton');
            const resultsArea = document.getElementById('results-area');
            
            if (!videoUrl) {
                alert('Please enter a YouTube video URL');
                return;
            }
            
            // Show loading state
            analyzeButton.classList.add('loading');
            analyzeButton.innerHTML = `
                <div class="loading-spinner" style="width: 20px; height: 20px; margin: 0; border-width: 2px;"></div>
                Analyzing...
            `;
            
            resultsArea.innerHTML = `
                <div class="loading-container">
                    <div class="loading-spinner"></div>
                    <div class="loading-text">AI agents are analyzing your video...</div>
                    <div class="loading-subtext">This may take 2-3 minutes</div>
                </div>
            `;
            
            try {
                const selectedAgents = getSelectedAgents();
                const useSequentialThinking = document.getElementById('sequentialThinking').checked;
                const isFullAnalysis = document.querySelector('input[name="analysisMode"][value="full"]').checked;
                
                // Choose endpoint based on analysis mode
                const endpoint = isFullAnalysis ? '/analyze-video' : '/analyze-standalone';
                const requestBody = isFullAnalysis ? {
                    video_url: videoUrl,
                    script: script || ''
                } : {
                    video_url: videoUrl,
                    script: script || '',
                    selected_agents: selectedAgents,
                    use_sequential_thinking: useSequentialThinking
                };
                
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });
                
                const result = await response.json();
                
                // Check for HTTP errors
                if (!response.ok) {
                    throw new Error(result.detail || result.error || 'Analysis failed');
                }
                
                // Check for application errors
                if (result.error) {
                    throw new Error(result.error);
                }
                
                console.log('About to display results:', result);
                displayVideoResults(result);
                console.log('Results displayed successfully');
                
            } catch (error) {
                console.error('Error in analyze function:', error);
                resultsArea.innerHTML = `
                    <div class="error-container">
                        <div class="error-title">❌ Analysis Failed</div>
                        <div class="error-message">${error.message}</div>
                    </div>
                `;
            } finally {
                // Reset button state
                analyzeButton.classList.remove('loading');
                analyzeButton.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <polygon points="5 3 19 12 5 21 5 3"></polygon>
                    </svg>
                    Analyze Video
                `;
            }
        });
        
        function displayVideoResults(data) {
            console.log('displayVideoResults called with:', data);
            const resultsArea = document.getElementById('results-area');
            
            if (!resultsArea) {
                alert('Error: results-area element not found!');
                return;
            }
            
            try {
                // 🔥 STORE DATA GLOBALLY FOR EXPORT FUNCTIONALITY
                window.currentAnalysisData = data;
                
                console.log('Video Analysis Data:', data);
            
            function extractHTML(content) {
                if (!content) return 'No analysis available';
                
                console.log('Content structure:', content); // Debug log
                
                // First check for styled_html (beautiful styling agent output)
                if (content.styled_html) {
                    return content.styled_html;
                }
                
                // Fall back to formatted_html if styled_html doesn't exist
                if (content.formatted_html) {
                    return content.formatted_html;
                }
                
                // Final fallback - extract only the FINAL ANALYSIS from agent output
                if (!content.analysis) return 'No analysis available';
                
                let fullAnalysis = content.analysis;
                
                // ENHANCED FILTERING - Remove all internal agent thinking and extract final analysis
                if (typeof fullAnalysis === 'string') {
                    console.log('🔍 Raw analysis length:', fullAnalysis.length);
                    
                    // METHOD 1: Extract everything after last occurrence of structured analysis headers
                    const analysisHeaders = [
                        '### PERFORMANCE METRICS CALCULATION',
                        '### PERFORMANCE METRICS',
                        '### SCRIPT METRICS',
                        '### SEO ANALYSIS',
                        '### PSYCHOLOGY ANALYSIS', 
                        '### COMMENT ANALYSIS',
                        '## PERFORMANCE ANALYSIS',
                        '## SCRIPT ANALYSIS',
                        '## SEO ANALYSIS',
                        '## AUDIENCE ANALYSIS',
                        '## COMMENT INTELLIGENCE'
                    ];
                    
                    let startIndex = -1;
                    let matchedHeader = '';
                    
                    // Find the last occurrence of any analysis header
                    for (const header of analysisHeaders) {
                        const headerIndex = fullAnalysis.lastIndexOf(header);
                        if (headerIndex > startIndex) {
                            startIndex = headerIndex;
                            matchedHeader = header;
                        }
                    }
                    
                    if (startIndex !== -1) {
                        let extracted = fullAnalysis.substring(startIndex);
                        // Remove any remaining crew execution logs that might appear after
                        extracted = extracted.replace(/🚀 Crew:.*$/s, '').trim();
                        console.log('✅ Extracted from header:', matchedHeader);
                        console.log('📏 Extracted length:', extracted.length);
                        return formatAnalysisHTML(extracted);
                    }
                    
                    // METHOD 2: Remove everything before the first ### header and crew logs
                    let cleaned = fullAnalysis;
                    
                    // Remove everything up to first ### header (likely actual analysis start)
                    const firstHeaderMatch = cleaned.match(/(###.*)/s);
                    if (firstHeaderMatch) {
                        cleaned = firstHeaderMatch[1];
                        console.log('✅ Extracted from first header forward');
                    }
                    
                    // Remove crew execution patterns and internal thinking
                    cleaned = cleaned
                        // Remove Crew execution blocks
                        .replace(/╭─+.*?╰─+/gs, '')
                        .replace(/🚀 Crew:.*$/s, '')
                        
                        // Remove agent metadata
                        .replace(/\[1m\[95m# Agent:.*?\[00m.*?\[92m.*?\[00m/gs, '')
                        .replace(/\[95m## Task:.*?\[00m/gs, '')
                        .replace(/\[1m\[95m.*?\[00m/gs, '')
                        .replace(/\[95m.*?\[00m/gs, '')
                        .replace(/\[92m.*?\[00m/gs, '')
                        
                        // Remove sequential thinking output
                        .replace(/## Sequential Thinking Framework Applied[\s\S]*?(?=###|$)/g, '')
                        .replace(/\*\*Question:\*\*.*?(?=###|$)/gs, '')
                        .replace(/\*\*Context:\*\*.*?(?=###|$)/gs, '')
                        .replace(/### Step-by-Step Reasoning Process:[\s\S]*?(?=###|$)/g, '')
                        
                        // Remove tool usage patterns
                        .replace(/Action: sequential_thinking.*?(?=###|$)/gs, '')
                        .replace(/## Using tool:.*?(?=###|$)/gs, '')
                        .replace(/## Tool Input:.*?(?=###|$)/gs, '')
                        .replace(/## Tool Output:.*?(?=###|$)/gs, '')
                        
                        // Remove internal thoughts and planning
                        .replace(/## Thought:.*?(?=###|$)/gs, '')
                        .replace(/Thought:.*?(?=\n\n|$)/gs, '')
                        .replace(/I (?:need to|will|should|am going to).*?(?=\n\n|$)/gs, '')
                        .replace(/Let me.*?(?=\n\n|$)/gs, '')
                        .replace(/I'll (?:start|begin|proceed).*?(?=\n\n|$)/gs, '')
                        .replace(/Action:.*?(?=\n\n|$)/gs, '')
                        .replace(/\*\*(?:Step \d+:|Calculation|Planning).*?\*\*.*?(?=\n\n|###)/gs, '')
                        
                        // Clean up ANSI color codes and formatting
                        .replace(/\[[\d;]+m/g, '')
                        .replace(/\[\d+m/g, '')
                        
                        // Remove empty lines and extra whitespace
                        .replace(/\n\n\n+/g, '\n\n')
                        .trim();
                    
                    if (cleaned && cleaned !== fullAnalysis && cleaned.length > 100) {
                        console.log('✅ Applied comprehensive filtering');
                        console.log('📏 Original length:', fullAnalysis.length, 'Cleaned length:', cleaned.length);
                        return formatAnalysisHTML(cleaned);
                    }
                }
                
                // If all filtering fails, return the full analysis with basic HTML formatting
                console.log('⚠️ Using full analysis - filtering incomplete');
                return formatAnalysisHTML(fullAnalysis);
            }
            
            function formatAnalysisHTML(text) {
                if (!text || typeof text !== 'string') return 'No analysis available';
                
                let html = text;
                
                // Clean up the HTML formatting
                html = html
                    .replace(/\\n/g, '\n')
                    .replace(/\\"/g, '"')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/\n\n/g, '</p><p>')
                    .replace(/\n/g, '<br>');
                
                if (!html.startsWith('<p>')) {
                    html = '<p>' + html + '</p>';
                }
                
                return html;
            }
            
            // Update transcript status indicator
            updateTranscriptStatus(data.transcript_info);
            
            resultsArea.innerHTML = `
                <div class="success-container animate-fade-in">
                    <div class="success-title">✅ Video Analysis Complete</div>
                    <div class="success-message">Analyzed: ${data.video_data?.title || 'Video analysis completed successfully'}</div>
                    ${generateTranscriptStatusHTML(data.transcript_info)}
                </div>
                
                <!-- Tab Navigation -->
                <div class="tab-navigation animate-fade-in" style="animation-delay: 0.1s;">
                    <button class="tab-button active" onclick="switchVideoTab('overview')">
                        <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                        </svg>
                        Overview
                    </button>
                    <button class="tab-button" onclick="switchVideoTab('performance')">
                        <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z"/>
                        </svg>
                        Performance Intelligence
                    </button>
                    <button class="tab-button" onclick="switchVideoTab('script')">
                        <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/>
                        </svg>
                        Script Forensics
                    </button>
                    <button class="tab-button" onclick="switchVideoTab('seo')">
                        <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                        SEO & Discoverability
                    </button>
                    <button class="tab-button" onclick="switchVideoTab('audience')">
                        <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                        </svg>
                        Audience Psychology
                    </button>
                    <button class="tab-button" onclick="switchVideoTab('comments')">
                        <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M21.99 4c0-1.1-.89-2-2-2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h14l4 4-.01-18zM18 14H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                        </svg>
                        Comment Intelligence
                    </button>
                    <button class="tab-button" onclick="switchVideoTab('synthesis')">
                        <svg class="tab-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2zm0 4.24L9.47 8.63 7.14 8.91l1.68 1.64-.4 2.33L12 11.77l3.58 1.88-.4-2.33 1.68-1.64-2.33-.28L12 6.24z"/>
                        </svg>
                        Strategic Synthesis
                    </button>
                </div>
                
                <!-- 📥 EXPORT SECTION -->
                <div class="export-section animate-fade-in" style="animation-delay: 0.3s; margin: var(--space-lg) 0;">
                    <div class="section-header" style="margin-bottom: var(--space-md);">
                        <div class="section-icon">📥</div>
                        <div>
                            <div class="section-title">Export Your Report</div>
                            <div class="section-subtitle">Download your analysis in multiple formats</div>
                        </div>
                    </div>
                    <div class="export-buttons" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--space-md);">
                        <button class="btn btn-primary export-btn" onclick="exportToPDF()" style="display: flex; align-items: center; gap: var(--space-xs);">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                            📄 PDF Report
                        </button>
                        <button class="btn btn-secondary export-btn" onclick="exportToMarkdown()" style="display: flex; align-items: center; gap: var(--space-xs);">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M22.269,19.385c-1.9-2.7-4.8-4.2-8.269-4.2s-6.369,1.5-8.269,4.2L2,22l3.731-2.615C8.431,20.285,11.2,21,14,21s5.569-0.715,8.269-1.615L26,22L22.269,19.385z"/>
                            </svg>
                            📝 Markdown (LLM-Ready)
                        </button>
                        <button class="btn btn-secondary export-btn" onclick="exportToJSON()" style="display: flex; align-items: center; gap: var(--space-xs);">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M5,3H7V5H5V10A2,2 0 0,1 3,12A2,2 0 0,1 5,14V19H7V21H5C3.93,20.93 3.07,20.07 3,19V15A2,2 0 0,0 1,13H0V11H1A2,2 0 0,0 3,9V5C3.07,3.93 3.93,3.07 5,3M19,3A2,2 0 0,1 21,5V9A2,2 0 0,0 23,11H24V13H23A2,2 0 0,0 21,15V19A2,2 0 0,1 19,21H17V19H19V14A2,2 0 0,1 21,12A2,2 0 0,1 19,10V5H17V3H19Z"/>
                            </svg>
                            📊 JSON Data
                        </button>
                        <button class="btn btn-secondary export-btn" onclick="exportToHTML()" style="display: flex; align-items: center; gap: var(--space-xs);">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M14,17H12L13.5,15.5L15,17H14M9.5,17L11,15.5L9.5,14L8,15.5L9.5,17M12,3A9,9 0 0,1 21,12A9,9 0 0,1 12,21A9,9 0 0,1 3,12A9,9 0 0,1 12,3M12,5A7,7 0 0,0 5,12A7,7 0 0,0 12,19A7,7 0 0,0 19,12A7,7 0 0,0 12,5Z"/>
                            </svg>
                            🌐 HTML File
                        </button>
                    </div>
                </div>
                
                <!-- Tab Content -->
                <div class="tab-content active" id="video-overview">
                    <div class="analysis-section animate-fade-in" style="animation-delay: 0.2s;">
                        <div class="section-header">
                            <div class="section-icon">📊</div>
                            <div>
                                <div class="section-title">Video Analysis Summary</div>
                                <div class="section-subtitle">Key metrics and insights overview</div>
                            </div>
                        </div>
                        
                        <div class="metric-grid">
                            <div class="metric-item">
                                <div class="metric-value">${data.video_data?.views?.toLocaleString() || 'N/A'}</div>
                                <div class="metric-label">Views</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">${data.video_data?.likes?.toLocaleString() || 'N/A'}</div>
                                <div class="metric-label">Likes</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">${data.video_data?.comments?.toLocaleString() || 'N/A'}</div>
                                <div class="metric-label">Comments</div>
                            </div>
                            <div class="metric-item">
                                <div class="metric-value">${data.video_data?.duration_minutes || 0}:${String(data.video_data?.duration_seconds || 0).padStart(2, '0')}</div>
                                <div class="metric-label">Duration</div>
                            </div>
                        </div>
                        
                        ${generateChannelContextHTML(data.video_data)}
                        
                        <div class="analysis-content">
                            <h3>Analysis Complete</h3>
                            <p>Your video has been analyzed by 5 specialized AI agents. Use the tabs above to explore detailed insights:</p>
                            <ul>
                                <li><strong>Performance Intelligence:</strong> Metrics, engagement, and benchmarking</li>
                                <li><strong>Script Forensics:</strong> Psychological triggers and structure analysis</li>
                                <li><strong>SEO & Discoverability:</strong> Search optimization opportunities</li>
                                <li><strong>Audience Psychology:</strong> Viewer motivation and engagement patterns</li>
                                <li><strong>Comment Intelligence:</strong> Sentiment analysis and audience insights</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="video-performance">
                    <div class="analysis-section">
                        <div class="section-header">
                            <div class="section-icon">📈</div>
                            <div>
                                <div class="section-title">Performance Intelligence Analysis</div>
                                <div class="section-subtitle">Deep performance metrics and benchmarks</div>
                            </div>
                        </div>
                        <div class="analysis-content">
                            ${extractHTML(data.performance_analysis)}
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="video-script">
                    <div class="analysis-section">
                        <div class="section-header">
                            <div class="section-icon">📝</div>
                            <div>
                                <div class="section-title">Script Forensics Analysis</div>
                                <div class="section-subtitle">Psychological triggers and structural elements</div>
                            </div>
                        </div>
                        <div class="analysis-content">
                            ${extractHTML(data.script_analysis)}
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="video-seo">
                    <div class="analysis-section">
                        <div class="section-header">
                            <div class="section-icon">🔍</div>
                            <div>
                                <div class="section-title">SEO & Discoverability Analysis</div>
                                <div class="section-subtitle">Search optimization and visibility insights</div>
                            </div>
                        </div>
                        <div class="analysis-content">
                            ${extractHTML(data.seo_analysis)}
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="video-audience">
                    <div class="analysis-section">
                        <div class="section-header">
                            <div class="section-icon">👥</div>
                            <div>
                                <div class="section-title">Audience Psychology Analysis</div>
                                <div class="section-subtitle">Viewer motivations and engagement patterns</div>
                            </div>
                        </div>
                        <div class="analysis-content">
                            ${extractHTML(data.psychology_analysis)}
                        </div>
                    </div>
                </div>
                
                <div class="tab-content" id="video-comments">
                    <div class="analysis-section">
                        <div class="section-header">
                            <div class="section-icon">💬</div>
                            <div>
                                <div class="section-title">Comment Intelligence Analysis</div>
                                <div class="section-subtitle">Sentiment analysis and audience feedback insights</div>
                            </div>
                        </div>
                        <div class="analysis-content">
                            ${extractHTML(data.comment_analysis)}
                        </div>
                    </div>
                </div>
                <div class="tab-content" id="video-synthesis">
                    <div class="analysis-section">
                        <div class="section-header">
                            <div class="section-icon">🧠</div>
                            <div>
                                <div class="section-title">Strategic Synthesis</div>
                                <div class="section-subtitle">Integrated analysis connecting all insights into actionable strategy</div>
                            </div>
                        </div>
                        <div class="analysis-content">
                            ${extractHTML(data.strategic_synthesis)}
                        </div>
                    </div>
                </div>
            `;
        }
        
        function updateTranscriptStatus(transcriptInfo) {
            const statusElement = document.getElementById('transcript-status');
            const infoElement = document.getElementById('transcript-info');
            
            if (!transcriptInfo || !statusElement) return;
            
            // Clear previous classes
            statusElement.className = 'transcript-status';
            
            const source = transcriptInfo.source || 'manual';
            
            if (source.includes('auto')) {
                statusElement.classList.add('auto');
                statusElement.textContent = '✓ Auto-fetched';
                
                if (transcriptInfo.has_timestamps) {
                    infoElement.textContent = '🎯 Timestamped transcript auto-fetched for advanced pacing analysis';
                } else {
                    infoElement.textContent = '📝 Plain transcript auto-fetched from YouTube';
                }
                infoElement.style.display = 'block';
                
            } else if (source === 'manual_timestamped') {
                statusElement.classList.add('manual');
                statusElement.textContent = '👤 Manual (Timestamped)';
                infoElement.textContent = '🎯 Your timestamped transcript enables advanced pacing analysis';
                infoElement.style.display = 'block';
                
            } else if (source === 'manual_plain') {
                statusElement.classList.add('manual');
                statusElement.textContent = '👤 Manual';
                infoElement.style.display = 'none';
                
            } else {
                statusElement.classList.add('failed');
                statusElement.textContent = '⚠️ Auto-fetch failed';
                infoElement.textContent = 'Auto-fetch failed. Using manual input or proceeding without transcript.';
                infoElement.style.display = 'block';
            }
        }
        
        function generateTranscriptStatusHTML(transcriptInfo) {
            if (!transcriptInfo) return '';
            
            const source = transcriptInfo.source || 'manual';
            const hasTimestamps = transcriptInfo.has_timestamps;
            
            let statusClass = 'auto';
            let statusText = 'Auto-fetched transcript';
            let statusIcon = '🤖';
            
            if (source.includes('manual')) {
                statusClass = 'manual';
                statusText = hasTimestamps ? 'Manual timestamped transcript' : 'Manual transcript';
                statusIcon = '👤';
            } else if (source === 'failed') {
                statusClass = 'failed';
                statusText = 'No transcript available';
                statusIcon = '⚠️';
            }
            
            let timingInfo = '';
            if (hasTimestamps && transcriptInfo.timing_analysis) {
                const timing = transcriptInfo.timing_analysis;
                timingInfo = `
                    <div class="timing-analysis">
                        <strong>📊 Timing Analysis Available:</strong>
                        ${timing.total_words} words • ${timing.speaking_rate_wpm?.toFixed(1)} WPM • ${timing.total_duration_seconds?.toFixed(1)}s duration
                    </div>
                `;
            }
            
            return `
                <div class="transcript-status-banner ${statusClass}">
                    <span class="status-icon">${statusIcon}</span>
                    <span class="status-text">${statusText}</span>
                    ${timingInfo}
                </div>
            `;
        }
        
        function generateChannelContextHTML(videoData) {
            if (!videoData) return '';
            
            const channelContext = videoData.channel_context;
            const performanceMetrics = videoData.performance_metrics;
            
            if (!channelContext) return '';
            
            // Generate performance indicator based on overperformance ratio
            let performanceClass = 'metric-item';
            let performanceIcon = '📊';
            let performanceLabel = 'Standard Performance';
            
            if (performanceMetrics) {
                const ratio = performanceMetrics.overperformance_ratio || 0;
                if (ratio >= 10) {
                    performanceClass += ' viral-performance';
                    performanceIcon = '🚀';
                    performanceLabel = 'Viral Performance';
                } else if (ratio >= 2) {
                    performanceClass += ' strong-performance';
                    performanceIcon = '📈';
                    performanceLabel = 'Strong Performance';
                } else if (ratio >= 0.5) {
                    performanceIcon = '📊';
                    performanceLabel = 'Standard Performance';
                } else {
                    performanceClass += ' weak-performance';
                    performanceIcon = '📉';
                    performanceLabel = 'Below Average';
                }
            }
            
            return `
                <div class="analysis-section" style="margin-top: var(--space-lg);">
                    <div class="section-header">
                        <div class="section-icon">🏆</div>
                        <div>
                            <div class="section-title">Channel Context Intelligence</div>
                            <div class="section-subtitle">Channel-relative performance analysis</div>
                        </div>
                    </div>
                    
                    <div class="metric-grid">
                        <div class="metric-item">
                            <div class="metric-value">📺 ${channelContext.channel_name || 'Unknown'}</div>
                            <div class="metric-label">Channel Name</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${channelContext.channel_tier || 'Unknown'}</div>
                            <div class="metric-label">Channel Tier</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${(channelContext.channel_subscribers || 0).toLocaleString()}</div>
                            <div class="metric-label">Subscribers</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${(channelContext.avg_views_per_video || 0).toLocaleString()}</div>
                            <div class="metric-label">Avg Views/Video</div>
                        </div>
                    </div>
                    
                    ${performanceMetrics ? `
                    <div class="metric-grid" style="margin-top: var(--space-md);">
                        <div class="${performanceClass}">
                            <div class="metric-value">${performanceIcon} ${performanceMetrics.overperformance_ratio?.toFixed(1) || '0'}x</div>
                            <div class="metric-label">Overperformance Ratio</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${performanceMetrics.subscriber_reach_rate?.toFixed(1) || '0'}%</div>
                            <div class="metric-label">Subscriber Reach</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${performanceMetrics.performance_note || 'Unknown'}</div>
                            <div class="metric-label">Performance Classification</div>
                        </div>
                        <div class="metric-item">
                            <div class="metric-value">${channelContext.channel_country || 'Unknown'}</div>
                            <div class="metric-label">Country</div>
                        </div>
                    </div>
                    ` : ''}
                </div>
            `;
            } catch (error) {
                console.error('Error displaying results:', error);
                resultsArea.innerHTML = `
                    <div class="error-container">
                        <div class="error-title">❌ Display Error</div>
                        <div class="error-message">Results received but could not be displayed. Check console for details.</div>
                        <div class="error-message">${error.message}</div>
                    </div>
                `;
            }
        }
        
        function switchVideoTab(tabName) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab content
            const targetTab = document.getElementById(`video-${tabName}`);
            if (targetTab) {
                targetTab.classList.add('active');
            }
            
            // Add active class to clicked button
            event.target.classList.add('active');
        }
        
        // Add URL parameter support for pre-filling form
        window.addEventListener('load', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const url = urlParams.get('url');
            if (url) {
                document.getElementById('video_url').value = decodeURIComponent(url);
            }
        });

        // BULLETPROOF NAVIGATION HEIGHT FIX - MEASURE AND ADAPT
        function fixNavigationOverlap() {
            const navHeader = document.querySelector('.nav-header');
            if (navHeader) {
                // Measure actual navigation height
                const navHeight = navHeader.offsetHeight;
                
                // Update CSS custom property with actual measured height
                document.documentElement.style.setProperty('--nav-height', navHeight + 'px');
                
                console.log(`🔧 Navigation height measured: ${navHeight}px - Layout updated dynamically`);
            }
        }

        // Fix navigation on page load and resize
        window.addEventListener('load', fixNavigationOverlap);
        window.addEventListener('resize', fixNavigationOverlap);
        
        // Also fix immediately in case navigation is already rendered
        fixNavigationOverlap();

        // 📥 EXPORT FUNCTIONS
        
        // Helper function to download file
        function downloadFile(content, filename, mimeType) {
            const blob = new Blob([content], { type: mimeType });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }

        // Generate filename with video title and timestamp
        function generateFilename(extension) {
            if (!window.currentAnalysisData?.video_data?.title) return `video_analysis_${Date.now()}.${extension}`;
            
            const title = window.currentAnalysisData.video_data.title
                .replace(/[^a-zA-Z0-9\s]/g, '') // Remove special characters
                .replace(/\s+/g, '_') // Replace spaces with underscores
                .substring(0, 50); // Limit length
            
            const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
            return `${title}_${timestamp}.${extension}`;
        }

        // Extract FULL analysis text preserving ALL content for export
        function extractCleanText(content) {
            if (!content) return 'No analysis available';
            
            // Handle direct string content first
            if (typeof content === 'string') {
                // If it's HTML, convert to readable text but preserve structure
                if (content.includes('<')) {
                    return convertHTMLToReadableText(content);
                }
                return content;
            }
            
            // Handle object content structure
            if (typeof content === 'object') {
                // Priority 1: Look for analysis property (main agent output)
                if (content.analysis) {
                    return content.analysis;
                }
                
                // Priority 2: Look for styled_html (styled agent output) 
                if (content.styled_html) {
                    return convertHTMLToReadableText(content.styled_html);
                }
                
                // Priority 3: Look for any text-like properties
                for (let key of ['text', 'content', 'result', 'output']) {
                    if (content[key]) {
                        return content[key];
                    }
                }
                
                // Last resort: stringify the object
                return JSON.stringify(content, null, 2);
            }
            
            // Fallback for any other type
            return content.toString();
        }
        
        // Convert HTML to readable text while preserving structure and formatting
        function convertHTMLToReadableText(html) {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;
            
            // Convert various HTML elements to readable text equivalents
            // Headers
            tempDiv.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(el => {
                const level = el.tagName.substring(1);
                const prefix = '#'.repeat(parseInt(level)) + ' ';
                el.outerHTML = '\n\n' + prefix + el.textContent + '\n';
            });
            
            // Lists
            tempDiv.querySelectorAll('ul, ol').forEach(el => {
                el.querySelectorAll('li').forEach((li, index) => {
                    const bullet = el.tagName === 'UL' ? '- ' : `${index + 1}. `;
                    li.outerHTML = bullet + li.textContent + '\n';
                });
                el.outerHTML = '\n' + el.innerHTML + '\n';
            });
            
            // Paragraphs
            tempDiv.querySelectorAll('p').forEach(el => {
                el.outerHTML = el.textContent + '\n\n';
            });
            
            // Divs and spans
            tempDiv.querySelectorAll('div, span').forEach(el => {
                el.outerHTML = el.textContent + '\n';
            });
            
            // Strong/bold text
            tempDiv.querySelectorAll('strong, b').forEach(el => {
                el.outerHTML = '**' + el.textContent + '**';
            });
            
            // Emphasis/italic text
            tempDiv.querySelectorAll('em, i').forEach(el => {
                el.outerHTML = '*' + el.textContent + '*';
            });
            
            // Clean up extra whitespace and return
            return tempDiv.textContent
                .replace(/\n\s*\n\s*\n/g, '\n\n')  // Remove extra line breaks
                .replace(/^\s+|\s+$/g, '')          // Trim whitespace
                .replace(/\t/g, '    ');            // Convert tabs to spaces
        }

        // 📄 PDF Export (using jsPDF)
        function exportToPDF() {
            if (!window.currentAnalysisData) {
                alert('No analysis data available for export');
                return;
            }

            // Load jsPDF dynamically
            if (typeof jsPDF === 'undefined') {
                const script = document.createElement('script');
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
                script.onload = function() {
                    setTimeout(generatePDF, 100); // Small delay to ensure library loads
                };
                document.head.appendChild(script);
            } else {
                generatePDF();
            }
        }

        function generatePDF() {
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF();
            const data = window.currentAnalysisData;
            
            let yPosition = 20;
            const lineHeight = 10;
            const pageHeight = 280;
            
            // Helper function to add text with page break
            function addTextWithPageBreak(text, fontSize = 12) {
                doc.setFontSize(fontSize);
                const lines = doc.splitTextToSize(text, 180);
                
                for (let line of lines) {
                    if (yPosition > pageHeight) {
                        doc.addPage();
                        yPosition = 20;
                    }
                    doc.text(line, 20, yPosition);
                    yPosition += lineHeight;
                }
                yPosition += 5; // Extra spacing
            }
            
            // Title
            doc.setFontSize(20);
            doc.text('YouTube Video Analysis Report', 20, yPosition);
            yPosition += 15;
            
            // Video info
            if (data.video_data) {
                addTextWithPageBreak(`Video: ${data.video_data.title || 'N/A'}`, 16);
                addTextWithPageBreak(`Channel: ${data.video_data.channel_title || 'N/A'}`, 14);
                addTextWithPageBreak(`Views: ${data.video_data.view_count || 'N/A'}`, 12);
                yPosition += 10;
            }
            
            // Analysis sections
            const sections = [
                { title: 'Performance Intelligence', data: data.performance_analysis },
                { title: 'Script Forensics', data: data.script_analysis },
                { title: 'SEO & Discoverability', data: data.seo_analysis },
                { title: 'Audience Psychology', data: data.audience_analysis },
                { title: 'Comment Intelligence', data: data.comment_analysis },
                { title: 'Strategic Synthesis', data: data.strategic_synthesis }
            ];
            
            sections.forEach(section => {
                if (section.data) {
                    addTextWithPageBreak(section.title, 16);
                    const cleanText = extractCleanText(section.data);
                    addTextWithPageBreak(cleanText.substring(0, 2000) + (cleanText.length > 2000 ? '...' : ''), 10);
                    yPosition += 10;
                }
            });
            
            // Save the PDF
            doc.save(generateFilename('pdf'));
        }

        // 📝 Markdown Export
        function exportToMarkdown() {
            if (!window.currentAnalysisData) {
                alert('No analysis data available for export');
                return;
            }

            const data = window.currentAnalysisData;
            let markdown = '';
            
            // Header
            markdown += `# YouTube Video Analysis Report\n\n`;
            markdown += `**Generated:** ${new Date().toLocaleString()}\n\n`;
            
            // Video information
            if (data.video_data) {
                markdown += `## Video Information\n\n`;
                markdown += `- **Title:** ${data.video_data.title || 'N/A'}\n`;
                markdown += `- **Channel:** ${data.video_data.channel_title || 'N/A'}\n`;
                markdown += `- **Views:** ${data.video_data.view_count || 'N/A'}\n`;
                markdown += `- **Likes:** ${data.video_data.like_count || 'N/A'}\n`;
                markdown += `- **Duration:** ${data.video_data.duration || 'N/A'}\n\n`;
            }
            
            // Analysis sections
            const sections = [
                { title: 'Performance Intelligence', data: data.performance_analysis },
                { title: 'Script Forensics', data: data.script_analysis },
                { title: 'SEO & Discoverability', data: data.seo_analysis },
                { title: 'Audience Psychology', data: data.audience_analysis },
                { title: 'Comment Intelligence', data: data.comment_analysis },
                { title: 'Strategic Synthesis', data: data.strategic_synthesis }
            ];
            
            let totalChars = 0;
            sections.forEach(section => {
                if (section.data) {
                    markdown += `## ${section.title}\n\n`;
                    const cleanText = extractCleanText(section.data);
                    totalChars += cleanText.length;
                    markdown += `${cleanText}\n\n---\n\n`;
                    console.log(`📝 ${section.title}: ${cleanText.length} characters`);
                }
            });
            
            // Export summary
            markdown += `## Export Summary\n\n`;
            markdown += `- **Total Analysis Content:** ${totalChars.toLocaleString()} characters\n`;
            markdown += `- **Sections Exported:** ${sections.filter(s => s.data).length} complete analyses\n`;
            markdown += `- **Export Date:** ${new Date().toISOString()}\n\n`;
            
            // Footer
            markdown += `*Report generated by YoutubePulse Video Analyzer*\n`;
            
            downloadFile(markdown, generateFilename('md'), 'text/markdown');
            
            // Show user export summary
            console.log(`📝 Markdown Export Complete: ${totalChars.toLocaleString()} total characters exported`);
            alert(`Markdown export complete!\n\nTotal content: ${totalChars.toLocaleString()} characters\nSections: ${sections.filter(s => s.data).length}\n\nCheck browser console for detailed breakdown.`);
        }

        // 📊 JSON Export - Enhanced to capture ALL analysis content
        function exportToJSON() {
            if (!window.currentAnalysisData) {
                alert('No analysis data available for export');
                return;
            }

            console.log('🔍 DEBUG: Full analysis data structure:', window.currentAnalysisData);

            // Create comprehensive JSON data with ALL content
            const exportData = {
                generated_at: new Date().toISOString(),
                video_data: window.currentAnalysisData.video_data,
                transcript_info: window.currentAnalysisData.transcript_info,
                analyses: {
                    performance: {
                        raw_text: extractCleanText(window.currentAnalysisData.performance_analysis),
                        full_object: window.currentAnalysisData.performance_analysis,
                        html: window.currentAnalysisData.performance_analysis?.styled_html,
                        character_count: extractCleanText(window.currentAnalysisData.performance_analysis).length
                    },
                    script: {
                        raw_text: extractCleanText(window.currentAnalysisData.script_analysis),
                        full_object: window.currentAnalysisData.script_analysis,
                        html: window.currentAnalysisData.script_analysis?.styled_html,
                        character_count: extractCleanText(window.currentAnalysisData.script_analysis).length
                    },
                    seo: {
                        raw_text: extractCleanText(window.currentAnalysisData.seo_analysis),
                        full_object: window.currentAnalysisData.seo_analysis,
                        html: window.currentAnalysisData.seo_analysis?.styled_html,
                        character_count: extractCleanText(window.currentAnalysisData.seo_analysis).length
                    },
                    audience: {
                        raw_text: extractCleanText(window.currentAnalysisData.audience_analysis),
                        full_object: window.currentAnalysisData.audience_analysis,
                        html: window.currentAnalysisData.audience_analysis?.styled_html,
                        character_count: extractCleanText(window.currentAnalysisData.audience_analysis).length
                    },
                    comments: {
                        raw_text: extractCleanText(window.currentAnalysisData.comment_analysis),
                        full_object: window.currentAnalysisData.comment_analysis,
                        html: window.currentAnalysisData.comment_analysis?.styled_html,
                        character_count: extractCleanText(window.currentAnalysisData.comment_analysis).length
                    },
                    strategic_synthesis: {
                        raw_text: extractCleanText(window.currentAnalysisData.strategic_synthesis),
                        full_object: window.currentAnalysisData.strategic_synthesis,
                        html: window.currentAnalysisData.strategic_synthesis?.styled_html,
                        character_count: extractCleanText(window.currentAnalysisData.strategic_synthesis).length
                    }
                },
                // Add summary of content lengths for verification
                export_summary: {
                    total_analysis_characters: Object.keys(window.currentAnalysisData).filter(key => key.includes('_analysis')).reduce((total, key) => {
                        return total + extractCleanText(window.currentAnalysisData[key]).length;
                    }, 0),
                    analysis_sections_found: Object.keys(window.currentAnalysisData).filter(key => key.includes('_analysis')).length,
                    export_timestamp: new Date().toISOString()
                }
            };
            
            // Log export summary for debugging
            console.log('📊 Export Summary:', exportData.export_summary);
            console.log('📝 Performance text length:', exportData.analyses.performance.character_count);
            console.log('📝 Script text length:', exportData.analyses.script.character_count);
            console.log('📝 SEO text length:', exportData.analyses.seo.character_count);
            console.log('📝 Audience text length:', exportData.analyses.audience.character_count);
            console.log('📝 Comments text length:', exportData.analyses.comments.character_count);
            
            const jsonString = JSON.stringify(exportData, null, 2);
            downloadFile(jsonString, generateFilename('json'), 'application/json');
            
            // Show user summary of what was exported
            alert(`Export complete!\n\nTotal analysis content: ${exportData.export_summary.total_analysis_characters.toLocaleString()} characters\nSections exported: ${exportData.export_summary.analysis_sections_found}\n\nCheck browser console for detailed export log.`);
        }

        // 🌐 HTML Export
        function exportToHTML() {
            if (!window.currentAnalysisData) {
                alert('No analysis data available for export');
                return;
            }

            const data = window.currentAnalysisData;
            
            // Create self-contained HTML file
            const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Analysis Report - ${data.video_data?.title || 'Report'}</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: 'Inter', sans-serif; 
            line-height: 1.6; 
            color: #333; 
            max-width: 1200px; 
            margin: 0 auto; 
            padding: 2rem; 
            background: #f8f9fa;
        }
        .header { 
            background: linear-gradient(135deg, #8B5CF6, #EC4899); 
            color: white; 
            padding: 2rem; 
            border-radius: 1rem; 
            margin-bottom: 2rem;
        }
        .section { 
            background: white; 
            padding: 2rem; 
            margin-bottom: 1.5rem; 
            border-radius: 0.75rem; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section h2 { 
            color: #8B5CF6; 
            margin-bottom: 1rem; 
            font-size: 1.5rem;
        }
        .video-info { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            gap: 1rem; 
            margin-bottom: 2rem;
        }
        .info-card { 
            background: #f8f9fa; 
            padding: 1rem; 
            border-radius: 0.5rem; 
            border-left: 4px solid #8B5CF6;
        }
        .analysis-content { 
            line-height: 1.8; 
        }
        @media print { 
            body { background: white; } 
            .section { box-shadow: none; border: 1px solid #eee; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📊 YouTube Video Analysis Report</h1>
        <p>Generated on ${new Date().toLocaleString()}</p>
    </div>
    
    <div class="section">
        <h2>🎥 Video Information</h2>
        <div class="video-info">
            <div class="info-card"><strong>Title:</strong><br>${data.video_data?.title || 'N/A'}</div>
            <div class="info-card"><strong>Channel:</strong><br>${data.video_data?.channel_title || 'N/A'}</div>
            <div class="info-card"><strong>Views:</strong><br>${data.video_data?.view_count || 'N/A'}</div>
            <div class="info-card"><strong>Likes:</strong><br>${data.video_data?.like_count || 'N/A'}</div>
        </div>
    </div>
    
    ${data.performance_analysis ? `
    <div class="section">
        <h2>📈 Performance Intelligence</h2>
        <div class="analysis-content">${data.performance_analysis.styled_html || extractCleanText(data.performance_analysis)}</div>
    </div>` : ''}
    
    ${data.script_analysis ? `
    <div class="section">
        <h2>📝 Script Forensics</h2>
        <div class="analysis-content">${data.script_analysis.styled_html || extractCleanText(data.script_analysis)}</div>
    </div>` : ''}
    
    ${data.seo_analysis ? `
    <div class="section">
        <h2>🔍 SEO & Discoverability</h2>
        <div class="analysis-content">${data.seo_analysis.styled_html || extractCleanText(data.seo_analysis)}</div>
    </div>` : ''}
    
    ${data.audience_analysis ? `
    <div class="section">
        <h2>👥 Audience Psychology</h2>
        <div class="analysis-content">${data.audience_analysis.styled_html || extractCleanText(data.audience_analysis)}</div>
    </div>` : ''}
    
    ${data.comment_analysis ? `
    <div class="section">
        <h2>💬 Comment Intelligence</h2>
        <div class="analysis-content">${data.comment_analysis.styled_html || extractCleanText(data.comment_analysis)}</div>
    </div>` : ''}
    
    ${data.strategic_synthesis ? `
    <div class="section">
        <h2>🧠 Strategic Synthesis</h2>
        <div class="analysis-content">${data.strategic_synthesis.styled_html || extractCleanText(data.strategic_synthesis)}</div>
    </div>` : ''}
    
    <div class="section" style="text-align: center; color: #666;">
        <p><em>Report generated by YoutubePulse Video Analyzer</em></p>
    </div>
</body>
</html>`;
            
            downloadFile(html, generateFilename('html'), 'text/html');
        }
    </script>
</body>
</html>