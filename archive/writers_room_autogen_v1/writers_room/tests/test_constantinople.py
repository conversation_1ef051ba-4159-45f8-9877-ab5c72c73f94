#!/usr/bin/env python3
"""
Quick test of AutoGen Writers' Room with Constantinople 1453
"""

import os
import sys
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'core_system'))

from autogen_writers_room import WritersRoomInput, SequentialWritersRoom


def test_constantinople():
    """Test Constantinople 1453 - The Fall of the Last Roman Empire"""
    
    input_data = WritersRoomInput(
        topic="The Fall of Constantinople 1453: The End of an Empire",
        golden_nuggets=[
            "The Theodosian Walls had never been breached in 1,058 years",
            "<PERSON><PERSON><PERSON> was only 21 years old when he conquered the city",
            "The Orban cannon weighed 17 tons and took 3 hours to reload",
            "The Byzantines stretched a massive chain across the Golden Horn",
            "<PERSON> died fighting on the walls, his body never found",
            "The city's population had dropped from 400,000 to just 50,000"
        ],
        persona={
            "channel": "Frontier",
            "voice": "Awestruck Strategist",
            "focus": "How geographic advantages can be overcome by technological innovation"
        },
        temperature=0.8
    )
    
    print("🎬 AutoGen Writers' Room - Constantinople 1453 Test")
    print("=" * 80)
    print(f"📍 Topic: {input_data.topic}")
    print(f"🎯 Focus: {input_data.persona['focus']}")
    print(f"💎 Golden Nuggets: {len(input_data.golden_nuggets)} historical facts")
    print("=" * 80)
    
    try:
        print("\n🚀 Initializing Writers' Room...")
        writers_room = SequentialWritersRoom(model="gpt-4")
        
        print("📝 Running 5-agent collaboration...")
        print("   🎬 Director: Creating story structure...")
        print("   🎣 Hooksmith: Crafting compelling hooks...")  
        print("   📚 Story-Weaver: Building narrative flow...")
        print("   🎪 Closer: Creating powerful ending...")
        print("   🎭 Sensory Director: Adding production cues...")
        
        output = writers_room.run_writers_room(input_data)
        
        print(f"\n✅ SUCCESS! Script completed in {output.execution_time:.1f} seconds")
        print(f"\n📊 SCRIPT METRICS:")
        print(f"   📏 Estimated Duration: {output.estimated_duration}")
        print(f"   🎣 Hook Variations: {len(output.hook_variations)}")
        print(f"   👁️ Visual Cues: {len(output.visual_cues)}")
        print(f"   🎤 Vocal Annotations: {output.vocal_annotations}")
        print(f"   📝 Total Characters: {len(output.final_script):,}")
        
        # Show hooks
        print(f"\n🎣 HOOK VARIATIONS:")
        for i, hook in enumerate(output.hook_variations, 1):
            preview = hook[:100] + "..." if len(hook) > 100 else hook
            print(f"   {i}. {preview}")
        
        # Show some visual cues
        if output.visual_cues:
            print(f"\n👁️ VISUAL CUES (sample):")
            for cue in output.visual_cues[:3]:
                print(f"   • {cue}")
            if len(output.visual_cues) > 3:
                print(f"   ... and {len(output.visual_cues) - 3} more")
        
        # Show script preview
        print(f"\n📜 SCRIPT PREVIEW (first 500 characters):")
        print("-" * 60)
        script_preview = output.final_script.replace('\n\n', '\n')[:500]
        print(script_preview + "...")
        print("-" * 60)
        
        # Save output
        output_path = "/Users/<USER>/Documents/CrewAI/youtube-research-v2/writers_room/outputs/autogen_tests/constantinople_test.json"
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        writers_room.save_output(output, output_path)
        
        # Also save as readable text
        text_path = output_path.replace('.json', '_script.txt')
        with open(text_path, 'w', encoding='utf-8') as f:
            f.write(f"CONSTANTINOPLE 1453 - AUTOGEN WRITERS' ROOM OUTPUT\n")
            f.write("=" * 80 + "\n\n")
            f.write(output.final_script)
        
        print(f"\n💾 Files saved:")
        print(f"   📄 Full data: {output_path}")
        print(f"   📝 Script text: {text_path}")
        
        # Cost estimate
        estimated_cost = 0.35  # Approximate GPT-4 cost
        print(f"\n💰 Estimated cost: ~${estimated_cost:.2f}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # Check API key
    if not os.environ.get("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY not found. Please set it first:")
        print("export OPENAI_API_KEY='your-key-here'")
    else:
        test_constantinople()