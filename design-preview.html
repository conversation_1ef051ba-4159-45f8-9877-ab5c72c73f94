<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Professional Design System - Preview</title>
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- Self-contained CSS variables -->
    <style>
        /* Design System Variables */
        :root {
            /* Monochromatic Base Palette */
            --bg-primary: #FFFFFF;
            --bg-secondary: #F8F9FA;
            --bg-tertiary: #F1F3F4;
            --bg-elevated: #FFFFFF;
            --bg-glass: rgba(255, 255, 255, 0.95);
            --bg-overlay: rgba(0, 0, 0, 0.05);
            
            --text-primary: #1A1A1A;
            --text-secondary: #4A4A4A;
            --text-tertiary: #6B7280;
            --text-muted: #9CA3AF;
            
            --border-light: #E5E7EB;
            --border-medium: #D1D5DB;
            --border-strong: #9CA3AF;
            
            /* Purple Accent System */
            --purple-primary: #8B5CF6;
            --purple-secondary: #A78BFA;
            --purple-tertiary: #C4B5FD;
            --purple-light: #EDE9FE;
            --purple-gradient: linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%);
            
            /* Status & Feedback Colors */
            --success: #10B981;
            --success-light: #D1FAE5;
            --error: #EF4444;
            --error-light: #FEE2E2;
            
            /* Typography System */
            --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
            
            --text-xs: 0.75rem;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --text-lg: 1.125rem;
            --text-xl: 1.25rem;
            --text-2xl: 1.5rem;
            --text-3xl: 1.875rem;
            
            --font-weight-normal: 400;
            --font-weight-medium: 500;
            --font-weight-semibold: 600;
            --font-weight-bold: 700;
            
            /* Spacing System */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;
            
            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-full: 9999px;
            
            /* Shadows */
            --shadow-sm: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
            --shadow-md: 0 8px 16px -4px rgba(0, 0, 0, 0.1), 0 4px 8px -2px rgba(0, 0, 0, 0.05);
            --shadow-lg: 0 16px 32px -8px rgba(0, 0, 0, 0.1), 0 8px 16px -4px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 32px 64px -16px rgba(0, 0, 0, 0.15), 0 16px 32px -8px rgba(0, 0, 0, 0.1);
            
            /* Transitions */
            --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
            --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* Dark Mode Variables */
        [data-theme="dark"] {
            --bg-primary: #0F172A;
            --bg-secondary: #1E293B;
            --bg-tertiary: #334155;
            --bg-elevated: #1E293B;
            --bg-glass: rgba(30, 41, 59, 0.95);
            --bg-overlay: rgba(255, 255, 255, 0.05);
            
            --text-primary: #F8FAFC;
            --text-secondary: #E2E8F0;
            --text-tertiary: #94A3B8;
            --text-muted: #64748B;
            
            --border-light: rgba(255, 255, 255, 0.1);
            --border-medium: rgba(255, 255, 255, 0.15);
            --border-strong: rgba(255, 255, 255, 0.2);
        }
        
        /* Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html {
            font-size: 16px;
            line-height: 1.5;
        }
        
        body {
            font-family: var(--font-primary);
            font-size: var(--text-base);
            color: var(--text-primary);
            background: var(--bg-primary);
            transition: background var(--transition-normal), color var(--transition-normal);
        }
    </style>
    
    <style>
        /* New Professional Design System Styles */
        
        /* Reset specific overrides for preview */
        .preview-content {
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .preview-header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: linear-gradient(135deg, var(--bg-elevated) 0%, var(--bg-secondary) 100%);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
        }
        
        .preview-title {
            font-size: 2.5rem;
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin: 0 0 0.5rem 0;
        }
        
        .preview-subtitle {
            font-size: 1.125rem;
            color: var(--text-secondary);
            margin: 0 0 1rem 0;
        }
        
        .preview-badges {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }
        
        .preview-badge {
            padding: 0.375rem 0.75rem;
            background: var(--purple-light);
            color: var(--purple-primary);
            border: 1px solid var(--purple-primary);
            border-radius: var(--radius-full);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-medium);
        }
        
        /* Tool Layout Styles from component */
        .tool-intro-section {
            margin-bottom: 2rem;
        }

        .tool-hero {
            background: linear-gradient(135deg, 
                var(--bg-elevated) 0%, 
                var(--bg-secondary) 100%);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-sm);
        }

        .tool-hero-content {
            max-width: 100%;
        }

        .tool-header {
            display: flex;
            align-items: flex-start;
            gap: 1.5rem;
        }

        .tool-icon-wrapper {
            width: 4rem;
            height: 4rem;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--purple-light);
            border: 2px solid var(--purple-primary);
            flex-shrink: 0;
        }

        .tool-icon {
            width: 2rem;
            height: 2rem;
            color: var(--purple-primary);
        }

        .tool-info {
            flex: 1;
        }

        .tool-title {
            font-size: var(--text-3xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin: 0 0 0.5rem 0;
            line-height: 1.2;
        }

        .tool-subtitle {
            font-size: var(--text-lg);
            color: var(--text-secondary);
            margin: 0 0 1rem 0;
            line-height: 1.4;
        }

        .tool-badges {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
        }

        .tool-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            background: var(--bg-tertiary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-full);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-secondary);
        }

        .tool-badge[data-badge-type="agents"] {
            background: var(--purple-light);
            color: var(--purple-primary);
            border-color: var(--purple-primary);
        }

        /* Capabilities Section */
        .capabilities-section {
            margin-bottom: 2rem;
        }

        .capabilities-container {
            background: var(--bg-elevated);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-xl);
            padding: 2rem;
            box-shadow: var(--shadow-sm);
        }

        .capabilities-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .capabilities-title {
            font-size: var(--text-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin: 0 0 0.5rem 0;
        }

        .capabilities-subtitle {
            font-size: var(--text-base);
            color: var(--text-secondary);
            margin: 0;
        }

        .capabilities-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
        }

        .capability-item {
            background: var(--bg-primary);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            transition: all var(--transition-fast);
        }

        .capability-item:hover {
            border-color: var(--purple-primary);
            box-shadow: var(--shadow-sm);
            transform: translateY(-1px);
        }

        .capability-icon-wrapper {
            width: 3rem;
            height: 3rem;
            border-radius: var(--radius-md);
            background: var(--purple-light);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
        }

        .capability-icon {
            width: 1.5rem;
            height: 1.5rem;
            color: var(--purple-primary);
        }

        .capability-title {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0 0 0.5rem 0;
        }

        .capability-description {
            font-size: var(--text-base);
            color: var(--text-secondary);
            margin: 0;
            line-height: 1.5;
        }

        /* Professional Form */
        .analysis-form-section {
            margin-bottom: 2rem;
        }

        .analysis-form-container {
            background: var(--bg-elevated);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
            overflow: hidden;
        }

        .form-header {
            background: var(--bg-secondary);
            border-bottom: 1px solid var(--border-light);
            padding: 1.5rem 2rem;
        }

        .form-header-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .form-title {
            font-size: var(--text-xl);
            font-weight: var(--font-weight-semibold);
            color: var(--text-primary);
            margin: 0;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.375rem 0.75rem;
            border-radius: var(--radius-full);
            font-size: var(--text-sm);
            font-weight: var(--font-weight-medium);
        }

        .status-indicator.ready {
            background: var(--success-light);
            color: var(--success);
        }

        .status-dot {
            width: 0.5rem;
            height: 0.5rem;
            border-radius: 50%;
            background: currentColor;
        }

        .form-content {
            padding: 2rem;
        }

        .professional-form {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .form-field {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .field-label {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: var(--text-sm);
            font-weight: var(--font-weight-medium);
            color: var(--text-primary);
        }

        .label-required {
            color: var(--error);
        }

        .label-optional {
            color: var(--text-muted);
            font-weight: var(--font-weight-normal);
        }

        .field-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
        }

        .input-icon {
            position: absolute;
            left: 0.75rem;
            z-index: 2;
            color: var(--text-muted);
        }

        .input-icon-svg {
            width: 1rem;
            height: 1rem;
        }

        .field-input, .field-textarea {
            width: 100%;
            padding: 0.75rem 0.75rem 0.75rem 2.5rem;
            border: 1px solid var(--border-medium);
            border-radius: var(--radius-md);
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: var(--text-base);
            transition: all var(--transition-fast);
        }

        .field-input:focus, .field-textarea:focus {
            outline: none;
            border-color: var(--purple-primary);
            box-shadow: 0 0 0 3px var(--purple-light);
        }

        .field-textarea {
            padding-left: 0.75rem;
            resize: vertical;
            font-family: var(--font-mono);
        }

        .field-help {
            font-size: var(--text-sm);
            color: var(--text-muted);
        }

        /* Analyze Button */
        .btn-analyze {
            width: 100%;
            background: var(--purple-primary);
            color: white;
            border: none;
            border-radius: var(--radius-lg);
            padding: 1rem 2rem;
            font-size: var(--text-lg);
            font-weight: var(--font-weight-semibold);
            cursor: pointer;
            transition: all var(--transition-fast);
            position: relative;
            overflow: hidden;
        }

        .btn-analyze:hover {
            background: var(--purple-secondary);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-analyze:active {
            transform: translateY(0);
        }

        .btn-content {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .btn-icon {
            width: 1.25rem;
            height: 1.25rem;
        }

        /* Comparison Section */
        .comparison-section {
            margin: 3rem 0;
            padding: 2rem;
            background: var(--bg-elevated);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-xl);
            box-shadow: var(--shadow-sm);
        }
        
        .comparison-title {
            font-size: var(--text-2xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
        }
        
        .comparison-item {
            padding: 1.5rem;
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-light);
        }
        
        .comparison-item.old {
            background: var(--error-light);
            border-color: var(--error);
        }
        
        .comparison-item.new {
            background: var(--success-light);
            border-color: var(--success);
        }
        
        .comparison-item h3 {
            font-size: var(--text-lg);
            font-weight: var(--font-weight-semibold);
            margin-bottom: 1rem;
        }
        
        .comparison-item.old h3 {
            color: var(--error);
        }
        
        .comparison-item.new h3 {
            color: var(--success);
        }
        
        .comparison-item ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .comparison-item li {
            padding: 0.5rem 0;
            color: var(--text-secondary);
        }
        
        /* Theme Toggle for Preview */
        .preview-theme-toggle {
            position: fixed;
            top: 1rem;
            right: 1rem;
            z-index: 1000;
            background: var(--purple-primary);
            color: white;
            border: none;
            border-radius: var(--radius-full);
            width: 3rem;
            height: 3rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: var(--shadow-lg);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .tool-header {
                flex-direction: column;
                text-align: center;
            }
            
            .capabilities-grid {
                grid-template-columns: 1fr;
            }
            
            .form-header-content {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .comparison-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Theme Toggle -->
    <button class="preview-theme-toggle" id="themeToggle" title="Toggle theme">
        <i data-lucide="sun" class="theme-icon-light"></i>
        <i data-lucide="moon" class="theme-icon-dark" style="display: none;"></i>
    </button>

    <div class="preview-content">
        <!-- Preview Header -->
        <div class="preview-header">
            <h1 class="preview-title">🎨 New Professional Design System</h1>
            <p class="preview-subtitle">Enterprise-grade components that make your tools look like a million bucks</p>
            <div class="preview-badges">
                <span class="preview-badge">✨ Professional</span>
                <span class="preview-badge">🚀 Unified</span>
                <span class="preview-badge">💎 Enterprise</span>
            </div>
        </div>

        <!-- Before/After Comparison -->
        <div class="comparison-section">
            <h2 class="comparison-title">Before vs After</h2>
            <div class="comparison-grid">
                <div class="comparison-item old">
                    <h3>❌ Old Design (Current)</h3>
                    <ul>
                        <li>• Heavy purple gradient forms</li>
                        <li>• Consumer-grade appearance</li>
                        <li>• Inconsistent layouts across tools</li>
                        <li>• Poor visual hierarchy</li>
                        <li>• Missing capability explanations</li>
                        <li>• Overwhelming dense results</li>
                    </ul>
                </div>
                <div class="comparison-item new">
                    <h3>✅ New Design (Preview Below)</h3>
                    <ul>
                        <li>• Clean, professional forms</li>
                        <li>• Enterprise SaaS appearance</li>
                        <li>• Unified design across all tools</li>
                        <li>• Clear information hierarchy</li>
                        <li>• Feature modules explain value</li>
                        <li>• Better organized results</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Tool Introduction Section DEMO -->
        <div class="tool-intro-section">
            <div class="tool-hero">
                <div class="tool-hero-content">
                    <div class="tool-header">
                        <div class="tool-icon-wrapper">
                            <i data-lucide="video" class="tool-icon"></i>
                        </div>
                        <div class="tool-info">
                            <h1 class="tool-title">🎬 AI Video Analyzer</h1>
                            <p class="tool-subtitle">Deep forensic analysis with 7 specialized AI agents. Uncover performance secrets, script psychology, SEO opportunities, and audience insights.</p>
                            <div class="tool-badges">
                                <span class="tool-badge" data-badge-type="agents">7 AI Agents</span>
                                <span class="tool-badge" data-badge-type="time">~3-5 min Analysis</span>
                                <span class="tool-badge" data-badge-type="enterprise">Enterprise Grade</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Capabilities Grid DEMO -->
        <div class="capabilities-section">
            <div class="capabilities-container">
                <div class="capabilities-header">
                    <h2 class="capabilities-title">What This Tool Delivers</h2>
                    <p class="capabilities-subtitle">Professional-grade analysis powered by specialized AI agents</p>
                </div>
                <div class="capabilities-grid">
                    <div class="capability-item">
                        <div class="capability-icon-wrapper">
                            <i data-lucide="trending-up" class="capability-icon"></i>
                        </div>
                        <div class="capability-content">
                            <h4 class="capability-title">Performance Intelligence</h4>
                            <p class="capability-description">Channel-relative metrics analysis with overperformance detection and tier classification</p>
                        </div>
                    </div>
                    <div class="capability-item">
                        <div class="capability-icon-wrapper">
                            <i data-lucide="file-text" class="capability-icon"></i>
                        </div>
                        <div class="capability-content">
                            <h4 class="capability-title">Script Forensics</h4>
                            <p class="capability-description">Psychological trigger analysis, pacing optimization, and retention hook identification</p>
                        </div>
                    </div>
                    <div class="capability-item">
                        <div class="capability-icon-wrapper">
                            <i data-lucide="search" class="capability-icon"></i>
                        </div>
                        <div class="capability-content">
                            <h4 class="capability-title">SEO & Discoverability</h4>
                            <p class="capability-description">Keyword optimization, search intent alignment, and discoverability enhancement</p>
                        </div>
                    </div>
                    <div class="capability-item">
                        <div class="capability-icon-wrapper">
                            <i data-lucide="brain" class="capability-icon"></i>
                        </div>
                        <div class="capability-content">
                            <h4 class="capability-title">Audience Psychology</h4>
                            <p class="capability-description">Emotional trigger mapping, viewer motivation analysis, and engagement driver identification</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Professional Analysis Form DEMO -->
        <div class="analysis-form-section">
            <div class="analysis-form-container">
                <div class="form-header">
                    <div class="form-header-content">
                        <h3 class="form-title">Start Analysis</h3>
                        <div class="form-status">
                            <div class="status-indicator ready">
                                <div class="status-dot"></div>
                                <span class="status-text">Ready</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-content">
                    <form class="professional-form" id="analysisForm">
                        <div class="form-field">
                            <label class="field-label">
                                <span class="label-text">YouTube Video URL</span>
                                <span class="label-required">*</span>
                            </label>
                            <div class="field-input-wrapper">
                                <div class="input-icon">
                                    <i data-lucide="link" class="input-icon-svg"></i>
                                </div>
                                <input 
                                    type="url" 
                                    class="field-input" 
                                    id="videoUrl"
                                    placeholder="https://www.youtube.com/watch?v=..."
                                    required
                                    autocomplete="off"
                                >
                            </div>
                            <div class="field-help">Paste any YouTube video URL for comprehensive analysis</div>
                        </div>
                        
                        <div class="form-field">
                            <label class="field-label">
                                <span class="label-text">Video Script</span>
                                <span class="label-optional">Optional</span>
                            </label>
                            <div class="field-input-wrapper">
                                <textarea 
                                    class="field-textarea" 
                                    id="script"
                                    placeholder="Paste the video script here for deeper analysis..."
                                    rows="4"
                                ></textarea>
                            </div>
                            <div class="field-help">Optional: Adding the script enables advanced forensic analysis</div>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn-analyze" id="analyzeBtn">
                                <div class="btn-content">
                                    <i data-lucide="play" class="btn-icon"></i>
                                    <span class="btn-text">Analyze Video</span>
                                </div>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Theme toggle functionality
        let currentTheme = localStorage.getItem('theme') || 'dark';
        const themeToggle = document.getElementById('themeToggle');
        const lightIcon = themeToggle.querySelector('.theme-icon-light');
        const darkIcon = themeToggle.querySelector('.theme-icon-dark');

        // Apply saved theme
        document.documentElement.setAttribute('data-theme', currentTheme);
        updateThemeIcons();

        themeToggle.addEventListener('click', () => {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', currentTheme);
            localStorage.setItem('theme', currentTheme);
            updateThemeIcons();
        });

        function updateThemeIcons() {
            if (currentTheme === 'dark') {
                lightIcon.style.display = 'none';
                darkIcon.style.display = 'block';
            } else {
                lightIcon.style.display = 'block';
                darkIcon.style.display = 'none';
            }
        }

        // Demo form interaction
        document.getElementById('analysisForm').addEventListener('submit', (e) => {
            e.preventDefault();
            alert('This is just a design preview! The new professional design looks amazing, right? 🎉');
        });
    </script>
</body>
</html>
