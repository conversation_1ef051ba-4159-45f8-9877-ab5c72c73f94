"""
Standalone YouTube Research v2 App
No CrewAI dependency - pure FastAPI + our enhanced analyzer
"""

import os
import uuid
import json
from datetime import datetime
from typing import Dict, Any
from fastapi import FastAPI, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Create FastAPI app
app = FastAPI(title="YouTube Research v2 - Standalone")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple in-memory storage
analysis_store = {}

# Pydantic models
class AnalysisRequest(BaseModel):
    channel_id: str
    max_videos: int = 20
    max_comments: int = 50

class AnalysisResponse(BaseModel):
    analysis_id: str
    status: str
    message: str

# Import our standalone analyzer
ANALYZER_AVAILABLE = False
analyzer_class = None

try:
    import sys
    sys.path.append('.')
    from enhanced_analyzer import EnhancedChannelAnalyzer
    analyzer_class = EnhancedChannelAnalyzer
    ANALYZER_AVAILABLE = True
    logger.info("Enhanced analyzer loaded successfully")
except Exception as e:
    logger.warning(f"Enhanced analyzer not available: {e}")
    try:
        from test_channel_analysis import ChannelAnalyzer
        analyzer_class = ChannelAnalyzer
        ANALYZER_AVAILABLE = True
        logger.info("Basic analyzer loaded successfully")
    except Exception as e2:
        logger.warning(f"Basic analyzer not available: {e2}")
        # Try direct import
        try:
            sys.path.append('/Users/<USER>/Documents/CrewAI/youtube-research-v2')
            from test_channel_analysis import ChannelAnalyzer
            analyzer_class = ChannelAnalyzer
            ANALYZER_AVAILABLE = True
            logger.info("Direct import analyzer loaded successfully")
        except Exception as e3:
            logger.error(f"No analyzer available: {e3}")
            ANALYZER_AVAILABLE = False

# Routes
@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve simple web interface"""
    return """
<!DOCTYPE html>
<html>
<head>
    <title>YouTube Research v2 - Standalone</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        .container { max-width: 900px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; }
        .card { 
            background: white; 
            border-radius: 15px; 
            padding: 25px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .input-group { margin-bottom: 15px; }
        .input-group label { display: block; margin-bottom: 5px; font-weight: 600; color: #555; }
        .input-row { display: grid; grid-template-columns: 2fr 1fr 1fr; gap: 15px; margin-bottom: 15px; }
        input { 
            padding: 12px; 
            border: 2px solid #e1e5e9; 
            border-radius: 8px; 
            font-size: 16px;
            width: 100%;
        }
        input:focus { outline: none; border-color: #667eea; }
        .btn { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            border: none; 
            padding: 15px 25px; 
            border-radius: 8px; 
            font-size: 16px;
            cursor: pointer;
            width: 100%;
            margin-top: 10px;
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3); }
        .btn:disabled { background: #ccc; cursor: not-allowed; transform: none; }
        .result { 
            background: #f8f9fa; 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .error { border-left-color: #dc3545; background: #f8d7da; color: #721c24; }
        .success { border-left-color: #28a745; background: #d4edda; color: #155724; }
        .warning { border-left-color: #ffc107; background: #fff3cd; color: #856404; }
        .progress-bar { 
            width: 100%; 
            height: 8px; 
            background: #e1e5e9; 
            border-radius: 4px; 
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill { 
            height: 100%; 
            background: linear-gradient(90deg, #667eea, #764ba2); 
            width: 0%; 
            transition: width 0.3s ease;
        }
        .analysis-result { 
            background: white; 
            border: 1px solid #ddd; 
            border-radius: 8px; 
            padding: 15px; 
            margin: 10px 0;
        }
        .analysis-section { 
            margin-bottom: 20px; 
            padding-bottom: 15px; 
            border-bottom: 1px solid #eee;
        }
        .analysis-section:last-child { border-bottom: none; }
        .analysis-section h4 { color: #667eea; margin-bottom: 10px; }
        pre { 
            background: #f8f9fa; 
            padding: 10px; 
            border-radius: 5px; 
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .metric { 
            display: flex; 
            justify-content: space-between; 
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .metric:last-child { border-bottom: none; }
        .recent-item { 
            display: flex; 
            justify-content: space-between; 
            align-items: center;
            padding: 10px; 
            background: #f8f9fa; 
            border-radius: 5px; 
            margin-bottom: 8px;
        }
        .status-badge { 
            padding: 4px 8px; 
            border-radius: 12px; 
            font-size: 12px; 
            font-weight: 600;
        }
        .status-completed { background: #d4edda; color: #155724; }
        .status-failed { background: #f8d7da; color: #721c24; }
        .status-pending { background: #fff3cd; color: #856404; }
        .status-in_progress { background: #cce7ff; color: #004085; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧬 YouTube Research v2</h1>
            <p>Standalone Channel DNA Analyzer</p>
        </div>
        
        <div class="card">
            <h2>Analyze YouTube Channel</h2>
            <div class="input-group">
                <label for="channelId">YouTube Channel ID or @Handle</label>
                <input type="text" id="channelId" placeholder="e.g., UCX6OQ3DkcsbYNE6H8uQQuVA or @MrBeast" />
            </div>
            <div class="input-row">
                <div class="input-group">
                    <label for="maxVideos">Max Videos (5-50)</label>
                    <input type="number" id="maxVideos" value="20" min="5" max="50" />
                </div>
                <div class="input-group">
                    <label for="maxComments">Max Comments (10-100)</label>
                    <input type="number" id="maxComments" value="50" min="10" max="100" />
                </div>
            </div>
            <button class="btn" onclick="startAnalysis()" id="analyzeBtn">🚀 Start Analysis</button>
        </div>
        
        <div id="statusCard" class="card" style="display: none;">
            <h3>Analysis Status</h3>
            <div id="statusContent"></div>
        </div>
        
        <div id="resultsCard" class="card" style="display: none;">
            <h3>Analysis Results</h3>
            <div id="resultsContent"></div>
        </div>
        
        <div class="card">
            <h3>Recent Analyses</h3>
            <div id="recentContent">
                <div class="result">Loading recent analyses...</div>
            </div>
            <button class="btn" onclick="loadRecent()">🔄 Refresh Recent</button>
        </div>
    </div>

    <script>
        let currentAnalysisId = null;
        let statusInterval = null;

        async function startAnalysis() {
            const channelId = document.getElementById('channelId').value.trim();
            const maxVideos = parseInt(document.getElementById('maxVideos').value);
            const maxComments = parseInt(document.getElementById('maxComments').value);
            
            if (!channelId) {
                showMessage('Please enter a channel ID or handle', 'error');
                return;
            }
            
            // Clean channel ID
            const cleanChannelId = channelId.startsWith('@') ? channelId.substring(1) : channelId;
            
            try {
                document.getElementById('analyzeBtn').disabled = true;
                document.getElementById('analyzeBtn').textContent = '⏳ Starting...';
                
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        channel_id: cleanChannelId,
                        max_videos: maxVideos,
                        max_comments: maxComments
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    currentAnalysisId = data.analysis_id;
                    showMessage(`Analysis started! ID: ${data.analysis_id}`, 'success');
                    document.getElementById('statusCard').style.display = 'block';
                    document.getElementById('resultsCard').style.display = 'none';
                    startStatusPolling();
                } else {
                    throw new Error(data.detail || 'Analysis failed to start');
                }
                
            } catch (error) {
                showMessage(`Error: ${error.message}`, 'error');
                document.getElementById('analyzeBtn').disabled = false;
                document.getElementById('analyzeBtn').textContent = '🚀 Start Analysis';
            }
        }
        
        function startStatusPolling() {
            if (statusInterval) clearInterval(statusInterval);
            
            statusInterval = setInterval(async () => {
                if (!currentAnalysisId) return;
                
                try {
                    const response = await fetch(`/api/status/${currentAnalysisId}`);
                    const status = await response.json();
                    
                    updateStatusDisplay(status);
                    
                    if (status.status === 'completed') {
                        clearInterval(statusInterval);
                        await loadResults(currentAnalysisId);
                        loadRecent();
                        document.getElementById('analyzeBtn').disabled = false;
                        document.getElementById('analyzeBtn').textContent = '🚀 Start Analysis';
                    } else if (status.status === 'failed') {
                        clearInterval(statusInterval);
                        showMessage(`Analysis failed: ${status.current_step}`, 'error');
                        document.getElementById('analyzeBtn').disabled = false;
                        document.getElementById('analyzeBtn').textContent = '🚀 Start Analysis';
                    }
                    
                } catch (error) {
                    console.error('Status polling error:', error);
                }
            }, 2000);
        }
        
        function updateStatusDisplay(status) {
            const content = document.getElementById('statusContent');
            content.innerHTML = `
                <div class="result">
                    <strong>Status:</strong> ${status.status}<br>
                    <strong>Step:</strong> ${status.current_step}<br>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${status.progress}%"></div>
                    </div>
                    <small>Progress: ${status.progress}%</small>
                </div>
            `;
        }
        
        async function loadResults(analysisId) {
            try {
                const response = await fetch(`/api/results/${analysisId}`);
                const results = await response.json();
                
                displayResults(results);
                document.getElementById('resultsCard').style.display = 'block';
                
            } catch (error) {
                showMessage(`Failed to load results: ${error.message}`, 'error');
            }
        }
        
        function displayResults(results) {
            const content = document.getElementById('resultsContent');
            let html = '';
            
            // Display each analysis section
            const sections = [
                { key: 'channel_profile', title: '🏢 Channel Profile', icon: '🏢' },
                { key: 'content_patterns', title: '📝 Content Patterns', icon: '📝' },
                { key: 'audience_psychology', title: '👥 Audience Psychology', icon: '👥' },
                { key: 'performance_metrics', title: '📊 Performance Metrics', icon: '📊' },
                { key: 'strategic_recommendations', title: '🎯 Strategic Recommendations', icon: '🎯' },
                { key: 'ai_insights', title: '🤖 AI Insights', icon: '🤖' }
            ];
            
            sections.forEach(section => {
                if (results[section.key]) {
                    html += createSectionHTML(section.title, results[section.key]);
                }
            });
            
            if (!html) {
                html = '<div class="result warning">No analysis results available</div>';
            }
            
            content.innerHTML = html;
        }
        
        function createSectionHTML(title, data) {
            let content = '';
            
            if (typeof data === 'object') {
                for (const [key, value] of Object.entries(data)) {
                    if (typeof value === 'object') {
                        content += `<strong>${formatKey(key)}:</strong><br>`;
                        content += `<pre>${JSON.stringify(value, null, 2)}</pre>`;
                    } else {
                        content += `<div class="metric">
                            <span>${formatKey(key)}</span>
                            <span>${formatValue(value)}</span>
                        </div>`;
                    }
                }
            } else {
                content = `<p>${data}</p>`;
            }
            
            return `
                <div class="analysis-section">
                    <h4>${title}</h4>
                    ${content}
                </div>
            `;
        }
        
        function formatKey(key) {
            return key.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());
        }
        
        function formatValue(value) {
            if (typeof value === 'number') {
                return value < 1 ? (value * 100).toFixed(2) + '%' : value.toLocaleString();
            }
            return String(value);
        }
        
        async function loadRecent() {
            try {
                const response = await fetch('/api/recent');
                const analyses = await response.json();
                
                const container = document.getElementById('recentContent');
                
                if (analyses.length === 0) {
                    container.innerHTML = '<div class="result">No recent analyses found</div>';
                    return;
                }
                
                let html = '';
                analyses.forEach(analysis => {
                    const date = new Date(analysis.created_at).toLocaleString();
                    html += `
                        <div class="recent-item">
                            <div>
                                <strong>${analysis.channel_name}</strong><br>
                                <small>${analysis.channel_id} • ${date}</small>
                            </div>
                            <span class="status-badge status-${analysis.status}">${analysis.status}</span>
                        </div>
                    `;
                });
                
                container.innerHTML = html;
                
            } catch (error) {
                document.getElementById('recentContent').innerHTML = 
                    '<div class="result error">Error loading recent analyses</div>';
            }
        }
        
        function showMessage(message, type) {
            const existing = document.querySelector('.temp-message');
            if (existing) existing.remove();
            
            const div = document.createElement('div');
            div.className = `result ${type} temp-message`;
            div.textContent = message;
            document.querySelector('.container').insertBefore(div, document.querySelector('.card'));
            
            setTimeout(() => div.remove(), 5000);
        }
        
        // Load recent analyses on page load
        window.onload = loadRecent;
    </script>
</body>
</html>
"""

@app.get("/health")
async def health():
    """Health check"""
    youtube_key = os.getenv('YOUTUBE_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY')
    
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "youtube_api": "configured" if youtube_key else "missing",
        "gemini_api": "configured" if gemini_key else "missing",
        "analyzer": "available" if ANALYZER_AVAILABLE else "unavailable"
    }

@app.post("/api/analyze", response_model=AnalysisResponse)
async def analyze(request: AnalysisRequest, background_tasks: BackgroundTasks):
    """Start analysis"""
    analysis_id = str(uuid.uuid4())
    
    # Store analysis
    analysis_store[analysis_id] = {
        "analysis_id": analysis_id,
        "channel_id": request.channel_id,
        "status": "pending",
        "created_at": datetime.utcnow(),
        "progress": 0,
        "current_step": "Queuing analysis...",
        "results": None,
        "channel_name": f"Channel {request.channel_id}"
    }
    
    # Start background task
    background_tasks.add_task(run_analysis, analysis_id, request)
    
    return AnalysisResponse(
        analysis_id=analysis_id,
        status="pending",
        message=f"Analysis started for {request.channel_id}"
    )

@app.get("/api/status/{analysis_id}")
async def get_status(analysis_id: str):
    """Get analysis status"""
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    return analysis_store[analysis_id]

@app.get("/api/results/{analysis_id}")
async def get_results(analysis_id: str):
    """Get analysis results"""
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    analysis = analysis_store[analysis_id]
    if analysis["status"] != "completed":
        raise HTTPException(status_code=400, detail="Analysis not completed")
    
    return analysis["results"]

@app.get("/api/recent")
async def get_recent():
    """Get recent analyses"""
    recent = sorted(
        analysis_store.values(),
        key=lambda x: x["created_at"],
        reverse=True
    )[:10]
    
    return [{
        "analysis_id": r["analysis_id"],
        "channel_id": r["channel_id"],
        "channel_name": r.get("channel_name", "Unknown"),
        "status": r["status"],
        "created_at": r["created_at"]
    } for r in recent]

async def run_analysis(analysis_id: str, request: AnalysisRequest):
    """Run the actual analysis"""
    import asyncio
    
    try:
        # Update status
        analysis_store[analysis_id]["status"] = "in_progress"
        analysis_store[analysis_id]["current_step"] = "Initializing analysis..."
        analysis_store[analysis_id]["progress"] = 10
        await asyncio.sleep(1)
        
        # Check if we have API keys
        youtube_key = os.getenv('YOUTUBE_API_KEY')
        gemini_key = os.getenv('GEMINI_API_KEY')
        
        if not youtube_key:
            raise Exception("YouTube API key not configured in .env file")
        
        if not ANALYZER_AVAILABLE:
            raise Exception("Analyzer not available - check imports")
        
        # Try to run real analysis
        analysis_store[analysis_id]["current_step"] = "Fetching channel data..."
        analysis_store[analysis_id]["progress"] = 30
        await asyncio.sleep(1)
        
        # Import and run analyzer
        if analyzer_class is None:
            raise Exception("No analyzer class available")
            
        if analyzer_class.__name__ == 'EnhancedChannelAnalyzer':
            analyzer = analyzer_class(youtube_key, gemini_key)
        else:
            analyzer = analyzer_class(youtube_key)
        
        analysis_store[analysis_id]["current_step"] = "Analyzing patterns..."
        analysis_store[analysis_id]["progress"] = 60
        
        # Run the analysis (this is the actual work)
        results = analyzer.analyze_channel(request.channel_id, request.max_videos, request.max_comments)
        
        if not results:
            raise Exception("Analysis returned no results")
        
        # Extract channel name
        channel_name = f"Channel {request.channel_id}"
        try:
            if hasattr(analyzer, '_get_channel_info'):
                channel_info = analyzer._get_channel_info(request.channel_id)
                if channel_info and 'snippet' in channel_info:
                    channel_name = channel_info['snippet']['title']
        except:
            pass
        
        analysis_store[analysis_id]["current_step"] = "Finalizing results..."
        analysis_store[analysis_id]["progress"] = 90
        await asyncio.sleep(1)
        
        # Store results
        analysis_store[analysis_id]["channel_name"] = channel_name
        analysis_store[analysis_id]["results"] = results
        analysis_store[analysis_id]["status"] = "completed"
        analysis_store[analysis_id]["progress"] = 100
        analysis_store[analysis_id]["current_step"] = "Analysis complete!"
        
        logger.info(f"Completed analysis {analysis_id} for {channel_name}")
        
    except Exception as e:
        logger.error(f"Analysis {analysis_id} failed: {e}")
        analysis_store[analysis_id]["status"] = "failed"
        analysis_store[analysis_id]["current_step"] = f"Error: {str(e)}"
        analysis_store[analysis_id]["error"] = str(e)

if __name__ == "__main__":
    import uvicorn
    
    print("🧬 YouTube Research v2 - Standalone Version")
    print("=" * 60)
    
    # Check environment
    youtube_key = os.getenv('YOUTUBE_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY')
    
    print(f"📊 Analyzer Available: {'✅' if ANALYZER_AVAILABLE else '❌'}")
    print(f"🔑 YouTube API Key: {'✅' if youtube_key else '❌'}")
    print(f"🤖 Gemini API Key: {'✅' if gemini_key else '❌'}")
    
    if not youtube_key:
        print("\n⚠️  WARNING: YouTube API key missing!")
        print("   Add YOUTUBE_API_KEY to .env file for real analysis")
        print("   Get key from: https://console.developers.google.com/")
    
    if not gemini_key:
        print("\n💡 OPTIONAL: Add GEMINI_API_KEY for AI insights")
        print("   Get key from: https://makersuite.google.com/app/apikey")
    
    print("\n🚀 Starting server...")
    print("🌐 Open http://localhost:8000 in your browser")
    print("⌨️  Press Ctrl+C to stop")
    print("-" * 60)
    
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")