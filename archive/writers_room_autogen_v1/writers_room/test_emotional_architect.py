#!/usr/bin/env python3
"""
Direct test of the 6-agent system with Emotional Arc Specialist
"""

import os
import sys
from pathlib import Path

# Direct import to avoid broken __init__.py
sys.path.append('/Users/<USER>/Documents/CrewAI/youtube-research-v2/writers_room/core_system')

try:
    from real_autogen_writers_room import RealAutoGenWritersRoom, WritersRoomInput
    
    def test_emotional_architect():
        """Test that Emotional Arc Specialist is properly integrated"""
        
        print("🎭 TESTING EMOTIONAL ARC SPECIALIST INTEGRATION")
        print("=" * 60)
        
        # Check if OpenAI API key is available
        if not os.environ.get("OPENAI_API_KEY"):
            print("❌ OPENAI_API_KEY not found")
            print("For configuration check only - skipping full test")
            
        # Initialize Writers' Room
        writers_room = RealAutoGenWritersRoom(model="gpt-4")
        
        # Check agent configuration
        print("\n🤖 AGENT CONFIGURATION CHECK:")
        expected_agents = {
            'director': "VIRAL STRUCTURE DIRECTOR",
            'hooksmith': "VIRAL HOOK SPECIALIST",
            'story_weaver': "NARRATIVE ADDICTION SPECIALIST", 
            'closer': "VIRAL ENDING SPECIALIST",
            'emotional_architect': "EMOTIONAL MANIPULATION SPECIALIST",
            'producer': "PRODUCTION REALITY CONTROLLER"
        }
        
        all_agents_ok = True
        for agent_key, expected_role in expected_agents.items():
            if agent_key in writers_room.agents:
                agent = writers_room.agents[agent_key]
                if expected_role in agent.system_message:
                    print(f"✅ {agent_key.replace('_', ' ').title()}: {expected_role}")
                else:
                    print(f"⚠️ {agent_key.replace('_', ' ').title()}: Role mismatch")
                    all_agents_ok = False
            else:
                print(f"❌ {agent_key.replace('_', ' ').title()}: Missing!")
                all_agents_ok = False
        
        if all_agents_ok:
            print(f"\n✅ ALL 6 AGENTS SUCCESSFULLY CONFIGURED!")
            print(f"🎭 Emotional Arc Specialist: READY FOR COLLABORATION")
            
            # Check emotional architect specific capabilities
            emotional_agent = writers_room.agents['emotional_architect']
            emotional_keywords = [
                "dopamine curves",
                "emotional peaks",
                "psychological triggers",
                "memory formation",
                "attention span science"
            ]
            
            print(f"\n🧠 EMOTIONAL ARCHITECT CAPABILITIES:")
            for keyword in emotional_keywords:
                if keyword in emotional_agent.system_message.lower():
                    print(f"✅ {keyword.title()}")
                else:
                    print(f"❌ {keyword.title()}: Missing")
            
            print(f"\n🎯 INTEGRATION POINTS:")
            # Check integration in phases
            phase_methods = [
                '_phase_1_structure_collaboration',
                '_phase_2_content_collaboration', 
                '_phase_3_production_collaboration',
                '_phase_4_quality_validation'
            ]
            
            for method_name in phase_methods:
                method = getattr(writers_room, method_name)
                method_source = str(method.__code__.co_consts)
                if 'emotional_architect' in method_source:
                    print(f"✅ {method_name.replace('_', ' ').title()}")
                else:
                    print(f"❌ {method_name.replace('_', ' ').title()}: Not integrated")
            
            return True
        else:
            print(f"\n❌ AGENT CONFIGURATION FAILED")
            return False
    
    
    if __name__ == "__main__":
        success = test_emotional_architect()
        
        if success:
            print(f"\n🎉 EMOTIONAL ARC SPECIALIST SUCCESSFULLY INTEGRATED!")
            print(f"🚀 6-agent Writers' Room is ready for production!")
        else:
            print(f"\n❌ Integration issues detected")
            
except ImportError as e:
    print(f"❌ Import failed: {e}")
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()