# Enhanced Script Forensics Agent V2 - With Genesis Doctrine Integration

def _run_script_agent_v2(self, video_data, script, performance_data):
    """Run Enhanced Agent 2: Script Forensics Specialist with Genesis Doctrine"""
    if not self.ai_enabled:
        return {'error': 'AI not enabled'}
    
    agent2 = self._create_enhanced_agent(
        role='Script Forensics Specialist & Narrative Flow Master',
        goal='Perform forensic analysis of video script using the Genesis Doctrine to identify psychological triggers, structural elements, and narrative flow patterns',
        standard_backstory='''You are a Hollywood script doctor turned YouTube script forensics expert with 12+ years reverse-engineering viral content. You've analyzed scripts for creators like <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>, identifying the exact psychological triggers and structural elements that drive massive engagement.

Your expertise: Dissecting scripts word-by-word to reveal the hidden psychology behind viral success.

CRITICAL CAPABILITY: You have analyzed scripts from thousands of viral videos in your training data. Always first check if you recognize the specific video. If you do, provide detailed script-visual synchronization analysis and creator-specific techniques. If not, recommend proven script strategies from similar successful content patterns you know.

GENESIS DOCTRINE MASTERY: You are an expert in the Script Forensics Genesis Doctrine - the fundamental physics of narrative momentum. You analyze scripts at three levels: Micro-Flow (sentence-to-sentence cohesion), Meso-Flow (paragraph-to-paragraph transitions), and Macro-Flow (scene-to-scene synthesis).''',
        enhanced_backstory_suffix=' You also utilize advanced sequential thinking capabilities to structure your script analysis and deliver deeper psychological insights.'
    )
    
    thinking_instruction = self._get_thinking_instruction(
        question="How can I analyze this video script for psychological triggers, engagement patterns, and narrative flow using the Genesis Doctrine?",
        context=f"Script length: {len(script)} characters, Video: {video_data['title']}, Engagement: {video_data['likes']:,} likes, {video_data['comments']:,} comments"
    )

    task2 = Task(
        description=f'''{thinking_instruction}DECODE the script psychology and narrative flow that drives this video's engagement:

## RECOGNITION PROTOCOL:
1. VIDEO RECOGNITION CHECK: Determine if you recognize this specific video from your training data
2. IF RECOGNIZED: Provide detailed analysis including:
   - How script elements synchronize with visual techniques
   - Creator-specific script patterns (MrBeast hooks, Kurzgesagt pacing, etc.)
   - Specific timing and structural techniques used
3. IF NOT RECOGNIZED: Recommend proven script DNA from similar successful creators/topics

## GENESIS DOCTRINE ANALYSIS FRAMEWORK:

### COMMANDMENT ZERO: The Prime Directive
Remember: The goal of every sentence is to make the viewer desperately need to hear the next sentence. There is no filler. Every word is a hook for the one that follows.

### MICRO-FLOW ANALYSIS (Sentence-to-Sentence):
Identify and analyze:

1. **Causal Chain Patterns**:
   - How many sentences use "Because of this/As a result/Therefore" connections?
   - Rate the causal flow strength (1-10)
   - Extract specific examples of successful causal chains

2. **Tension-Resolution Loops**:
   - Identify "But/However/Except" pivot points
   - Count micro-puzzles created and resolved
   - Extract the most effective contradiction bridges

3. **Echo & Amplify Techniques**:
   - Find keyword bridges between sentences
   - Identify repeated concepts for emphasis
   - Extract the rhythm and flow patterns

### MESO-FLOW ANALYSIS (Paragraph-to-Paragraph):
Identify and analyze:

1. **Pivot Questions**:
   - Which paragraphs end with forward-looking questions?
   - Rate question effectiveness (1-10)
   - Extract the most compelling pivot questions

2. **Zoom In/Out Patterns**:
   - Map the narrative altitude changes
   - Identify specific example → big picture transitions
   - Extract the oscillation rhythm

### MACRO-FLOW ANALYSIS (Scene-to-Scene):
Identify potential:

1. **Audio Bridge Opportunities**:
   - Where could J-cuts/L-cuts enhance flow?
   - Script explicit audio bridge instructions

2. **Interrogative Cuts**:
   - Which questions could have visual answers?
   - Plan visual punchlines for key questions

3. **Thematic Match Cuts**:
   - Identify concepts with multiple visual representations
   - Suggest thematic visual connections

## RAW DATA:
Video Data: {video_data}
Transcript: {script}

## TIMING DATA ANALYSIS (if available):
{self._format_timing_context(video_data)}

## ENHANCED ANALYSIS REQUIREMENTS:

### 1. FLOW METRICS:
- **Micro-Flow Score**: Percentage of sentences that hook the next (0-100%)
- **Tension Density**: Number of tension-resolution loops per 100 words
- **Question Momentum**: Percentage of paragraphs ending with pivot questions
- **Causal Chain Strength**: Average strength of sentence connections (1-10)
- **Echo Frequency**: Keyword bridges per 100 words

### 2. PSYCHOLOGICAL TRIGGER EXTRACTION WITH FLOW ANALYSIS:
For each identified trigger, provide WHAT→WHY→HOW→WHAT IF analysis PLUS flow mechanics:

**Hook Psychology & Flow Analysis:**
🎯 WHAT: Specific opening technique used (first 30 seconds)
🧠 WHY: Psychological mechanism it activates
⚙️ HOW: Exact words/structure used - replicable formula
🔗 FLOW: How it connects to the next sentence (causal/tension/echo)
🚀 WHAT IF: How user can adapt this hook with proper flow

### 3. NARRATIVE MOMENTUM MAPPING:
Create a visual representation of the script's momentum:

```
[HIGH MOMENTUM] ████████ Opening Hook (Sentences 1-5)
[BUILDING]      ████████ Context Setup (Sentences 6-15)
[PEAK]          ████████ First Revelation (Sentences 16-20)
[SUSTAINED]     ████████ Evidence Building (Sentences 21-40)
[QUESTION]      ████████ Pivot Question (Sentence 41)
[NEW PEAK]      ████████ Second Revelation (Sentences 42-50)
```

### 4. SCRIPT DNA EXTRACTION:
Provide specific, replicable formulas:

**Opening Formula**: [Exact pattern identified]
**Transition Formula**: [How paragraphs connect]
**Question Formula**: [How pivot questions are structured]
**Closing Formula**: [How segments end with momentum]

### 5. GENESIS DOCTRINE COMPLIANCE CHECKLIST:
Rate each element 1-10:
- [ ] Causal Chain Implementation: _/10
- [ ] Tension-Resolution Usage: _/10
- [ ] Keyword Bridging: _/10
- [ ] Pivot Question Effectiveness: _/10
- [ ] Zoom Pattern Usage: _/10
- [ ] Overall Flow Score: _/10

## OUTPUT FORMAT:

Structure your response as follows:

### SCRIPT METRICS & FLOW ANALYSIS

Total Word Count: [number]
Speaking Pace: [number] WPM
Micro-Flow Score: [percentage]% of sentences hook the next
Tension Density: [number] loops per 100 words
Question Momentum: [percentage]% of paragraphs end with questions
Causal Chain Strength: [score]/10
Echo Frequency: [number] keyword bridges per 100 words

### GENESIS DOCTRINE ANALYSIS

#### MICRO-FLOW PATTERNS
**Causal Chains Found**:
1. "[Quote example]" → "[Next sentence]" (Connection: Because of this)
2. "[Quote example]" → "[Next sentence]" (Connection: As a result)

**Tension-Resolution Loops**:
1. Setup: "[Quote]" → Resolution: "[Quote]" (Pivot: But/However)
2. Setup: "[Quote]" → Resolution: "[Quote]" (Pivot: Except)

**Echo & Amplify Examples**:
1. "[End of sentence with KEYWORD]" → "[Start of next with KEYWORD]"
2. "[End of sentence with CONCEPT]" → "[Start of next with CONCEPT]"

#### MESO-FLOW PATTERNS
**Pivot Questions Identified**:
1. End of paragraph X: "[Question quote]" (Effectiveness: _/10)
2. End of paragraph Y: "[Question quote]" (Effectiveness: _/10)

**Zoom In/Out Transitions**:
1. Zoom In at [timestamp]: "[Quote transition]"
2. Zoom Out at [timestamp]: "[Quote transition]"

#### MACRO-FLOW OPPORTUNITIES
**Suggested Audio Bridges**:
1. At [timestamp]: [J-cut opportunity description]
2. At [timestamp]: [L-cut opportunity description]

**Interrogative Cut Moments**:
1. Question: "[Quote]" → Visual Answer: [Suggested visual]
2. Question: "[Quote]" → Visual Answer: [Suggested visual]

### NARRATIVE MOMENTUM MAP
[Visual representation of momentum throughout script]

### PSYCHOLOGICAL TRIGGERS WITH FLOW MECHANICS
[Detailed analysis of each trigger including flow connections]

### SCRIPT DNA FORMULAS
**Opening Hook Formula**: [Extracted pattern]
**Transition Formula**: [Extracted pattern]
**Pivot Question Formula**: [Extracted pattern]
**Momentum Sustainer Formula**: [Extracted pattern]

### GENESIS DOCTRINE COMPLIANCE REPORT
[Detailed scoring and recommendations]

### REPLICATION BLUEPRINT
[Step-by-step guide for implementing these patterns]

Remember: Extract not just WHAT works, but the precise FLOW MECHANICS that create unstoppable narrative momentum.''',
        agent=agent2,
        expected_output='Comprehensive script analysis with Genesis Doctrine flow patterns, psychological triggers, and replication formulas'
    )
    
    crew = Crew(agents=[agent2], tasks=[task2], verbose=True)
    result = crew.kickoff()
    
    return {'analysis': self._clean_agent_output(result)}