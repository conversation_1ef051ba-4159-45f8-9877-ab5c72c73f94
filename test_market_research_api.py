#!/usr/bin/env python3
"""
Test Market Research API functionality
"""

import requests
import time
import subprocess
import sys
import os
import signal
import json
from datetime import datetime

class MarketResearchTester:
    def __init__(self):
        self.base_url = "http://localhost:8003"
        self.server_process = None
    
    def start_server(self, timeout=30):
        """Start the main application server"""
        print("🚀 Starting server...")
        try:
            # Start server in background
            self.server_process = subprocess.Popen(
                [sys.executable, "working_crewai_app_tabbed.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )
            
            # Wait for server to be ready
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=5)
                    if response.status_code == 200:
                        print("✅ Server started successfully")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)
                    continue
            
            print("❌ Server failed to start within timeout")
            return False
            
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return False
    
    def stop_server(self):
        """Stop the server"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                print("🛑 Server stopped")
            except subprocess.TimeoutExpired:
                self.server_process.kill()
                print("🛑 Server force killed")
            except Exception as e:
                print(f"⚠️ Error stopping server: {e}")
    
    def test_market_research_page(self):
        """Test if the market research page loads"""
        print("\n📄 Testing Market Research Page...")
        try:
            response = requests.get(f"{self.base_url}/market-research", timeout=10)
            if response.status_code == 200:
                print("✅ Market Research page loads successfully")
                # Check if the page contains the search button
                if 'id="searchButton"' in response.text:
                    print("✅ Search button found in HTML")
                else:
                    print("❌ Search button not found in HTML")
                return True
            else:
                print(f"❌ Page failed to load: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error loading page: {e}")
            return False
    
    def test_market_research_api(self):
        """Test the market research API endpoint"""
        print("\n🔬 Testing Market Research API...")
        
        test_payload = {
            "query": "python programming",
            "time_range": "month",
            "max_results": 5,
            "sort_by": "relevance"
        }
        
        try:
            print(f"📡 Sending request to /api/market-research")
            print(f"📊 Payload: {json.dumps(test_payload, indent=2)}")
            
            response = requests.post(
                f"{self.base_url}/api/market-research",
                json=test_payload,
                timeout=60  # Longer timeout for AI processing
            )
            
            print(f"📈 Response status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ API request successful!")
                
                # Check response structure
                expected_keys = ['query', 'timestamp', 'market_overview', 'trending_content', 
                               'competition_analysis', 'content_opportunities', 'ai_insights']
                
                for key in expected_keys:
                    if key in data:
                        print(f"   ✅ {key}: Present")
                    else:
                        print(f"   ❌ {key}: Missing")
                
                return True
            else:
                print(f"❌ API request failed: {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                return False
                
        except requests.exceptions.Timeout:
            print("⏰ API request timed out (this may be normal for AI processing)")
            return False
        except Exception as e:
            print(f"❌ API error: {e}")
            return False
    
    def run_tests(self):
        """Run all tests"""
        print("🧪 Market Research Functionality Test")
        print("=" * 50)
        
        # Start server
        if not self.start_server():
            return False
        
        try:
            # Test page loading
            page_test = self.test_market_research_page()
            
            # Test API functionality
            api_test = self.test_market_research_api()
            
            # Summary
            print(f"\n📊 Test Results:")
            print(f"   Page Loading: {'✅ PASS' if page_test else '❌ FAIL'}")
            print(f"   API Functionality: {'✅ PASS' if api_test else '❌ FAIL'}")
            
            if page_test and api_test:
                print(f"\n🎉 All tests passed! Market Research is working correctly.")
                return True
            else:
                print(f"\n⚠️ Some tests failed. Check the issues above.")
                return False
                
        finally:
            self.stop_server()

if __name__ == "__main__":
    tester = MarketResearchTester()
    success = tester.run_tests()
    sys.exit(0 if success else 1)
