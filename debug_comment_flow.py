#!/usr/bin/env python3
"""
Debug comment fetching in the full analysis flow
"""
import os
import sys
import json
from dotenv import load_dotenv
from googleapiclient.discovery import build

load_dotenv()

def test_comment_fetching_directly():
    """Test comment fetching directly with YouTube API"""
    
    # Test with the same video from the screenshot
    test_videos = [
        "https://www.youtube.com/watch?v=Evm4t4prtWo",  # CaspianReport video that works
        "https://www.youtube.com/watch?v=dQw4w9WgXcQ",  # <PERSON> (known to work)
    ]
    
    # Initialize YouTube API
    youtube = build('youtube', 'v3', developerKey=os.getenv('GOOGLE_API_KEY'))
    
    for video_url in test_videos:
        print(f"\n🧪 Testing: {video_url}")
        
        # Extract video ID
        if 'v=' in video_url:
            video_id = video_url.split('v=')[1].split('&')[0]
        else:
            print("❌ Invalid video URL format")
            continue
            
        print(f"📺 Video ID: {video_id}")
        
        # Get video metadata
        try:
            video_response = youtube.videos().list(
                part='snippet,statistics',
                id=video_id
            ).execute()
            
            if video_response['items']:
                video_info = video_response['items'][0]
                title = video_info['snippet']['title']
                stats = video_info['statistics']
                comment_count = stats.get('commentCount', 0)
                
                print(f"📊 Title: {title}")
                print(f"📊 Views: {stats.get('viewCount', 0):,}")
                print(f"📊 Comments (metadata): {comment_count:,}")
                
                # Test comment fetching
                try:
                    comment_response = youtube.commentThreads().list(
                        part='snippet',
                        videoId=video_id,
                        maxResults=10,
                        order='relevance'
                    ).execute()
                    
                    comments = comment_response.get('items', [])
                    print(f"💬 Comments fetched: {len(comments)}")
                    
                    if comments:
                        print("✅ Comment fetching successful!")
                        first_comment = comments[0]['snippet']['topLevelComment']['snippet']
                        print(f"   First comment: {first_comment['textDisplay'][:100]}...")
                    else:
                        print("❌ No comments fetched despite metadata showing comments")
                        
                except Exception as e:
                    print(f"❌ Comment fetching failed: {e}")
                    
                    # Check if it's a specific error
                    if "commentsDisabled" in str(e):
                        print("   🔍 Reason: Comments are disabled for this video")
                    elif "quotaExceeded" in str(e):
                        print("   🔍 Reason: API quota exceeded")
                    else:
                        print(f"   🔍 Reason: {e}")
                        
            else:
                print("❌ Video not found or private")
                
        except Exception as e:
            print(f"❌ Failed to get video metadata: {e}")

if __name__ == "__main__":
    test_comment_fetching_directly()