"""
Channel DNA Analysis Crew
Orchestrates 5 specialized agents to perform comprehensive channel analysis
"""

import os
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
import yaml
from crewai import Agent, Crew, Task, Process
from crewai.project import CrewBase, agent, crew, task
from langchain_google_genai import ChatGoogleGenerativeAI
import logging

from ..tools.youtube_tools import YouTubeChannelAnalysisTool
from ..models.channel_dna import (
    ChannelDNAAnalysis, ContentDNA, AudienceDNA, 
    EngagementDNA, VisualDNA, StrategyDNA,
    AnalysisStatus, ChannelDataCache
)

logger = logging.getLogger(__name__)


@CrewBase
class ChannelDNACrew:
    """Channel DNA Analysis Crew with 5 specialized agents"""
    
    agents_config = 'config/channel_dna_agents.yaml'
    tasks_config = 'config/channel_dna_tasks.yaml'
    
    def __init__(self):
        # Initialize LLMs with appropriate models
        self.llm_pro = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            google_api_key=os.getenv("GEMINI_API_KEY"),
            temperature=0.7
        )
        
        self.llm_flash = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-exp",
            google_api_key=os.getenv("GEMINI_API_KEY"),
            temperature=0.7
        )
        
        # Initialize YouTube tools
        self.youtube_tool = YouTubeChannelAnalysisTool(
            youtube_api_key=os.getenv("YOUTUBE_API_KEY"),
            supadata_api_key=os.getenv("SUPADATA_API_KEY")
        )
    
    @agent
    def content_dna_agent(self) -> Agent:
        """Content DNA Agent - Analyzes content patterns and strategy"""
        return Agent(
            config=self.agents_config['content_dna_agent'],
            llm=self.llm_flash,
            tools=[self.youtube_tool],
            verbose=True
        )
    
    @agent
    def audience_dna_agent(self) -> Agent:
        """Audience DNA Agent - Analyzes audience psychology and demographics"""
        return Agent(
            config=self.agents_config['audience_dna_agent'],
            llm=self.llm_pro,
            tools=[self.youtube_tool],
            verbose=True
        )
    
    @agent
    def engagement_dna_agent(self) -> Agent:
        """Engagement DNA Agent - Analyzes performance patterns and optimization"""
        return Agent(
            config=self.agents_config['engagement_dna_agent'],
            llm=self.llm_flash,
            tools=[self.youtube_tool],
            verbose=True
        )
    
    @agent
    def visual_dna_agent(self) -> Agent:
        """Visual DNA Agent - Analyzes visual branding and thumbnails"""
        return Agent(
            config=self.agents_config['visual_dna_agent'],
            llm=self.llm_pro,
            tools=[self.youtube_tool],
            verbose=True
        )
    
    @agent
    def strategy_synthesis_agent(self) -> Agent:
        """Strategy Synthesis Agent - Creates comprehensive strategic recommendations"""
        return Agent(
            config=self.agents_config['strategy_synthesis_agent'],
            llm=self.llm_pro,
            tools=[],  # This agent synthesizes outputs from other agents
            verbose=True
        )
    
    @task
    def analyze_content_dna(self) -> Task:
        """Task for content DNA analysis"""
        return Task(
            config=self.tasks_config['analyze_content_dna'],
            agent=self.content_dna_agent()
        )
    
    @task
    def analyze_audience_dna(self) -> Task:
        """Task for audience DNA analysis"""
        return Task(
            config=self.tasks_config['analyze_audience_dna'],
            agent=self.audience_dna_agent()
        )
    
    @task
    def analyze_engagement_dna(self) -> Task:
        """Task for engagement DNA analysis"""
        return Task(
            config=self.tasks_config['analyze_engagement_dna'],
            agent=self.engagement_dna_agent()
        )
    
    @task
    def analyze_visual_dna(self) -> Task:
        """Task for visual DNA analysis"""
        return Task(
            config=self.tasks_config['analyze_visual_dna'],
            agent=self.visual_dna_agent()
        )
    
    @task
    def synthesize_strategy(self) -> Task:
        """Task for strategic synthesis"""
        return Task(
            config=self.tasks_config['synthesize_strategy'],
            agent=self.strategy_synthesis_agent(),
            context=[
                self.analyze_content_dna(),
                self.analyze_audience_dna(),
                self.analyze_engagement_dna(),
                self.analyze_visual_dna()
            ]
        )
    
    @crew
    def crew(self) -> Crew:
        """Creates the Channel DNA Analysis crew"""
        return Crew(
            agents=[
                self.content_dna_agent(),
                self.audience_dna_agent(),
                self.engagement_dna_agent(),
                self.visual_dna_agent(),
                self.strategy_synthesis_agent()
            ],
            tasks=[
                self.analyze_content_dna(),
                self.analyze_audience_dna(),
                self.analyze_engagement_dna(),
                self.analyze_visual_dna(),
                self.synthesize_strategy()
            ],
            process=Process.sequential,
            memory=True,
            cache=True,
            max_rpm=10,
            share_crew=True
        )
    
    def analyze_channel(self, channel_id: str) -> ChannelDNAAnalysis:
        """
        Run the complete Channel DNA analysis
        
        Args:
            channel_id: YouTube channel ID
            
        Returns:
            ChannelDNAAnalysis object with complete results
        """
        start_time = datetime.utcnow()
        
        # Create analysis record
        analysis = ChannelDNAAnalysis(
            channel_id=channel_id,
            channel_name="",  # Will be updated
            analysis_id=f"cdna_{channel_id}_{start_time.timestamp()}",
            status=AnalysisStatus.IN_PROGRESS
        )
        
        try:
            # First, fetch channel data
            logger.info(f"Fetching channel data for {channel_id}")
            channel_data = self.youtube_tool._run(channel_id)
            
            if 'error' in channel_data:
                raise Exception(f"Failed to fetch channel data: {channel_data['error']}")
            
            # Update channel name
            analysis.channel_name = channel_data['channel']['title']
            
            # Cache the data for agents to use
            cache = ChannelDataCache(
                channel_id=channel_id,
                channel_info=channel_data['channel'],
                videos=channel_data['videos'],
                comments=channel_data['comments'],
                transcripts=channel_data['transcripts'],
                expires_at=datetime.utcnow() + timedelta(hours=24)
            )
            
            # Store cache in a temporary file for agents to access
            cache_file = f"/tmp/channel_cache_{channel_id}.json"
            with open(cache_file, 'w') as f:
                json.dump(cache.dict(), f, default=str)
            
            # Run the crew with the channel data
            logger.info(f"Running Channel DNA crew for {channel_id}")
            crew_output = self.crew().kickoff(
                inputs={
                    'channel_id': channel_id,
                    'channel_data_file': cache_file,
                    'channel_name': analysis.channel_name,
                    'total_videos': len(channel_data['videos']),
                    'total_comments': sum(len(c) for c in channel_data['comments'].values()),
                    'transcripts_available': len(channel_data['transcripts'])
                }
            )
            
            # Parse crew outputs and populate analysis
            analysis = self._parse_crew_outputs(analysis, crew_output)
            
            # Update metadata
            analysis.status = AnalysisStatus.COMPLETED
            analysis.updated_at = datetime.utcnow()
            analysis.analysis_duration_seconds = (
                datetime.utcnow() - start_time
            ).total_seconds()
            analysis.data_points_analyzed = {
                'videos': len(channel_data['videos']),
                'comments': sum(len(c) for c in channel_data['comments'].values()),
                'transcripts': len(channel_data['transcripts'])
            }
            
            # Clean up cache file
            os.remove(cache_file)
            
            logger.info(f"Channel DNA analysis completed for {channel_id}")
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing channel {channel_id}: {e}")
            analysis.status = AnalysisStatus.FAILED
            analysis.updated_at = datetime.utcnow()
            
            # Clean up cache file if it exists
            cache_file = f"/tmp/channel_cache_{channel_id}.json"
            if os.path.exists(cache_file):
                os.remove(cache_file)
            
            return analysis
    
    def _parse_crew_outputs(self, analysis: ChannelDNAAnalysis, crew_output) -> ChannelDNAAnalysis:
        """Parse crew outputs and populate the analysis object"""
        
        # The crew output will contain results from each task
        # We need to extract and structure them appropriately
        
        try:
            # Extract outputs from each agent/task
            # CrewAI stores task outputs in crew_output.tasks_output
            task_outputs = crew_output.tasks_output if hasattr(crew_output, 'tasks_output') else []
            
            # Map task outputs to DNA components
            if len(task_outputs) >= 5:
                # Content DNA
                analysis.content_dna = self._parse_content_dna(task_outputs[0])
                
                # Audience DNA
                analysis.audience_dna = self._parse_audience_dna(task_outputs[1])
                
                # Engagement DNA
                analysis.engagement_dna = self._parse_engagement_dna(task_outputs[2])
                
                # Visual DNA
                analysis.visual_dna = self._parse_visual_dna(task_outputs[3])
                
                # Strategy DNA
                analysis.strategy_dna = self._parse_strategy_dna(task_outputs[4])
            
        except Exception as e:
            logger.error(f"Error parsing crew outputs: {e}")
        
        return analysis
    
    def _parse_content_dna(self, output) -> ContentDNA:
        """Parse content DNA from agent output"""
        # This would parse the structured output from the content agent
        # For now, returning a structured example
        return ContentDNA(
            content_personality=str(output),
            content_evolution="Content has evolved from basic tutorials to comprehensive guides",
            signature_elements=["In-depth analysis", "Visual demonstrations", "Community engagement"],
            content_strategy="Focus on educational content with entertainment elements",
            optimization_opportunities=["Shorter intros", "More consistent posting schedule"],
            competitive_advantages=["Unique perspective", "High production quality"]
        )
    
    def _parse_audience_dna(self, output) -> AudienceDNA:
        """Parse audience DNA from agent output"""
        return AudienceDNA(
            demographics={"age_range": "18-34", "gender": "60% male, 40% female"},
            psychographics=str(output),
            pain_points=["Learning curve", "Information overload", "Implementation challenges"],
            engagement_patterns="High engagement on tutorial content, moderate on vlogs",
            community_culture="Supportive and knowledge-sharing focused",
            growth_opportunities=["Expand into related niches", "Target professionals"]
        )
    
    def _parse_engagement_dna(self, output) -> EngagementDNA:
        """Parse engagement DNA from agent output"""
        return EngagementDNA(
            mathematical_patterns={"avg_views": 50000, "engagement_rate": 0.08},
            upload_strategy=str(output),
            performance_insights="Videos posted on Tuesday/Thursday perform best",
            engagement_hooks=["Questions in intro", "Cliffhangers", "Community challenges"],
            optimization_framework="Focus on first 30 seconds, clear CTAs, community interaction",
            viral_factors=["Trending topics", "Collaborations", "Controversial takes"]
        )
    
    def _parse_visual_dna(self, output) -> VisualDNA:
        """Parse visual DNA from agent output"""
        return VisualDNA(
            visual_identity=str(output),
            color_psychology={"primary": "blue", "secondary": "orange", "impact": "trust and energy"},
            thumbnail_patterns=["Bold text", "Expressive faces", "Bright colors"],
            design_evolution="Moved from simple to more sophisticated designs",
            visual_hooks=["Contrast", "Curiosity gaps", "Emotional expressions"],
            brand_consistency=0.85
        )
    
    def _parse_strategy_dna(self, output) -> StrategyDNA:
        """Parse strategy DNA from agent output"""
        return StrategyDNA(
            executive_summary=str(output),
            competitive_positioning="Top 10% in niche, strong differentiation",
            growth_roadmap=[
                {"phase": "1", "action": "Optimize posting schedule", "timeline": "Month 1"},
                {"phase": "2", "action": "Launch new series", "timeline": "Month 2-3"},
                {"phase": "3", "action": "Expand to new platforms", "timeline": "Month 4-6"}
            ],
            content_calendar_strategy="3 videos/week: 2 tutorials, 1 community video",
            monetization_opportunities=["Course creation", "Affiliate marketing", "Sponsorships"],
            risk_factors=["Platform changes", "Increased competition", "Burnout"],
            success_metrics={"subscriber_growth": "10% monthly", "engagement_rate": "8%+"}
        )