#!/usr/bin/env python3
"""
Upload Schedule Intelligence System
Analyzes channel upload patterns for optimization insights
"""

from datetime import datetime, timedelta
from collections import defaultdict, Counter
import statistics
from typing import Dict, List, Any, Tuple
import logging

logger = logging.getLogger(__name__)

class UploadScheduleAnalyzer:
    """Analyzes upload patterns and provides scheduling intelligence"""
    
    def __init__(self):
        self.days_of_week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        self.time_slots = {
            'Early Morning': (6, 9),
            'Morning': (9, 12),
            'Afternoon': (12, 17),
            'Evening': (17, 21),
            'Night': (21, 24),
            'Late Night': (0, 6)
        }
    
    def analyze_upload_schedule(self, video_library: List[Dict[str, Any]], channel_profile: Dict[str, Any]) -> Dict[str, Any]:
        """
        Comprehensive upload schedule analysis
        
        Args:
            video_library: List of video data with publish dates
            channel_profile: Channel information
            
        Returns:
            Complete schedule intelligence report
        """
        
        if not video_library:
            return {'error': 'No video data available for schedule analysis'}
        
        # Parse upload times
        upload_times = self._parse_upload_times(video_library)
        if not upload_times:
            return {'error': 'No valid upload times found'}
        
        # Generate comprehensive analysis
        analysis = {
            'upload_frequency': self._analyze_upload_frequency(upload_times),
            'day_of_week_patterns': self._analyze_day_patterns(upload_times),
            'time_of_day_patterns': self._analyze_time_patterns(upload_times),
            'performance_correlation': self._analyze_performance_correlation(video_library, upload_times),
            'consistency_metrics': self._analyze_consistency(upload_times),
            'seasonal_patterns': self._analyze_seasonal_patterns(upload_times),
            'optimization_recommendations': None  # Will be filled after analysis
        }
        
        # Generate optimization recommendations
        analysis['optimization_recommendations'] = self._generate_recommendations(analysis, channel_profile)
        
        return analysis
    
    def _parse_upload_times(self, video_library: List[Dict[str, Any]]) -> List[datetime]:
        """Extract and parse upload timestamps"""
        upload_times = []
        
        for video in video_library:
            publish_date = video.get('publishedAt')
            if publish_date:
                try:
                    # Parse ISO format: 2023-12-15T18:00:00Z
                    dt = datetime.fromisoformat(publish_date.replace('Z', '+00:00'))
                    upload_times.append(dt)
                except (ValueError, AttributeError):
                    continue
        
        return sorted(upload_times)
    
    def _analyze_upload_frequency(self, upload_times: List[datetime]) -> Dict[str, Any]:
        """Analyze how often the channel uploads"""
        
        if len(upload_times) < 2:
            return {'frequency': 'Insufficient data', 'intervals': []}
        
        # Calculate intervals between uploads
        intervals = []
        for i in range(1, len(upload_times)):
            interval = (upload_times[i] - upload_times[i-1]).days
            intervals.append(interval)
        
        # Calculate frequency metrics
        avg_interval = statistics.mean(intervals)
        median_interval = statistics.median(intervals)
        
        # Determine upload frequency category
        if avg_interval <= 1:
            frequency_category = "Daily"
        elif avg_interval <= 3:
            frequency_category = "Every 2-3 days"
        elif avg_interval <= 7:
            frequency_category = "Weekly"
        elif avg_interval <= 14:
            frequency_category = "Bi-weekly"
        elif avg_interval <= 30:
            frequency_category = "Monthly"
        else:
            frequency_category = "Irregular"
        
        return {
            'frequency_category': frequency_category,
            'average_days_between_uploads': round(avg_interval, 1),
            'median_days_between_uploads': round(median_interval, 1),
            'intervals': intervals,
            'consistency_score': self._calculate_consistency_score(intervals)
        }
    
    def _analyze_day_patterns(self, upload_times: List[datetime]) -> Dict[str, Any]:
        """Analyze which days of the week are preferred"""
        
        day_counts = Counter()
        for dt in upload_times:
            day_name = self.days_of_week[dt.weekday()]
            day_counts[day_name] += 1
        
        total_uploads = len(upload_times)
        day_percentages = {day: (count / total_uploads) * 100 for day, count in day_counts.items()}
        
        # Find best and worst days
        best_day = max(day_counts.items(), key=lambda x: x[1])
        worst_day = min(day_counts.items(), key=lambda x: x[1]) if len(day_counts) > 1 else ("None", 0)
        
        return {
            'day_distribution': dict(day_counts),
            'day_percentages': day_percentages,
            'preferred_day': best_day[0],
            'preferred_day_percentage': day_percentages.get(best_day[0], 0),
            'least_used_day': worst_day[0],
            'weekend_vs_weekday': self._weekend_vs_weekday_analysis(upload_times)
        }
    
    def _analyze_time_patterns(self, upload_times: List[datetime]) -> Dict[str, Any]:
        """Analyze preferred times of day for uploads"""
        
        hour_counts = Counter()
        time_slot_counts = Counter()
        
        for dt in upload_times:
            hour = dt.hour
            hour_counts[hour] += 1
            
            # Categorize into time slots
            for slot_name, (start, end) in self.time_slots.items():
                if start <= hour < end:
                    time_slot_counts[slot_name] += 1
                    break
        
        total_uploads = len(upload_times)
        
        # Find peak hour and time slot
        peak_hour = max(hour_counts.items(), key=lambda x: x[1]) if hour_counts else (0, 0)
        peak_time_slot = max(time_slot_counts.items(), key=lambda x: x[1]) if time_slot_counts else ("Unknown", 0)
        
        return {
            'hour_distribution': dict(hour_counts),
            'time_slot_distribution': dict(time_slot_counts),
            'peak_upload_hour': peak_hour[0],
            'peak_hour_count': peak_hour[1],
            'preferred_time_slot': peak_time_slot[0],
            'time_slot_percentage': (peak_time_slot[1] / total_uploads) * 100 if total_uploads > 0 else 0
        }
    
    def _analyze_performance_correlation(self, video_library: List[Dict[str, Any]], upload_times: List[datetime]) -> Dict[str, Any]:
        """Analyze if upload timing correlates with performance"""
        
        if len(video_library) != len(upload_times):
            return {'error': 'Mismatch between video count and upload times'}
        
        # Create performance data by upload characteristics
        day_performance = defaultdict(list)
        hour_performance = defaultdict(list)
        time_slot_performance = defaultdict(list)
        
        for video, upload_time in zip(video_library, upload_times):
            views = int(video.get('statistics', {}).get('viewCount', 0))
            likes = int(video.get('statistics', {}).get('likeCount', 0))
            
            # Performance metrics
            day_name = self.days_of_week[upload_time.weekday()]
            hour = upload_time.hour
            
            day_performance[day_name].append(views)
            hour_performance[hour].append(views)
            
            # Time slot analysis
            for slot_name, (start, end) in self.time_slots.items():
                if start <= hour < end:
                    time_slot_performance[slot_name].append(views)
                    break
        
        # Calculate averages
        day_avg_views = {day: statistics.mean(views) for day, views in day_performance.items() if views}
        hour_avg_views = {hour: statistics.mean(views) for hour, views in hour_performance.items() if views}
        slot_avg_views = {slot: statistics.mean(views) for slot, views in time_slot_performance.items() if views}
        
        # Find best performing times
        best_day = max(day_avg_views.items(), key=lambda x: x[1]) if day_avg_views else ("Unknown", 0)
        best_hour = max(hour_avg_views.items(), key=lambda x: x[1]) if hour_avg_views else (0, 0)
        best_time_slot = max(slot_avg_views.items(), key=lambda x: x[1]) if slot_avg_views else ("Unknown", 0)
        
        return {
            'day_performance': day_avg_views,
            'hour_performance': hour_avg_views,
            'time_slot_performance': slot_avg_views,
            'best_performing_day': best_day[0],
            'best_day_avg_views': int(best_day[1]),
            'best_performing_hour': best_hour[0],
            'best_hour_avg_views': int(best_hour[1]),
            'best_performing_time_slot': best_time_slot[0],
            'best_slot_avg_views': int(best_time_slot[1])
        }
    
    def _analyze_consistency(self, upload_times: List[datetime]) -> Dict[str, Any]:
        """Analyze upload consistency and reliability"""
        
        if len(upload_times) < 3:
            return {'consistency_score': 0, 'reliability': 'Insufficient data'}
        
        # Calculate interval variance
        intervals = [(upload_times[i] - upload_times[i-1]).days for i in range(1, len(upload_times))]
        
        avg_interval = statistics.mean(intervals)
        interval_variance = statistics.variance(intervals) if len(intervals) > 1 else 0
        
        # Consistency score (lower variance = higher consistency)
        consistency_score = max(0, 100 - (interval_variance / avg_interval) * 10) if avg_interval > 0 else 0
        
        # Reliability assessment
        if consistency_score >= 80:
            reliability = "Highly consistent"
        elif consistency_score >= 60:
            reliability = "Moderately consistent"
        elif consistency_score >= 40:
            reliability = "Somewhat inconsistent"
        else:
            reliability = "Highly irregular"
        
        return {
            'consistency_score': round(consistency_score, 1),
            'reliability': reliability,
            'interval_variance': round(interval_variance, 1),
            'longest_gap_days': max(intervals) if intervals else 0,
            'shortest_gap_days': min(intervals) if intervals else 0
        }
    
    def _analyze_seasonal_patterns(self, upload_times: List[datetime]) -> Dict[str, Any]:
        """Analyze seasonal upload patterns"""
        
        month_counts = Counter()
        quarter_counts = Counter()
        
        for dt in upload_times:
            month_counts[dt.strftime('%B')] += 1
            
            # Quarterly analysis
            if dt.month in [1, 2, 3]:
                quarter_counts['Q1'] += 1
            elif dt.month in [4, 5, 6]:
                quarter_counts['Q2'] += 1
            elif dt.month in [7, 8, 9]:
                quarter_counts['Q3'] += 1
            else:
                quarter_counts['Q4'] += 1
        
        return {
            'monthly_distribution': dict(month_counts),
            'quarterly_distribution': dict(quarter_counts),
            'most_active_month': max(month_counts.items(), key=lambda x: x[1])[0] if month_counts else "Unknown",
            'most_active_quarter': max(quarter_counts.items(), key=lambda x: x[1])[0] if quarter_counts else "Unknown"
        }
    
    def _weekend_vs_weekday_analysis(self, upload_times: List[datetime]) -> Dict[str, Any]:
        """Compare weekend vs weekday upload patterns"""
        
        weekday_count = 0
        weekend_count = 0
        
        for dt in upload_times:
            if dt.weekday() >= 5:  # Saturday = 5, Sunday = 6
                weekend_count += 1
            else:
                weekday_count += 1
        
        total = len(upload_times)
        return {
            'weekday_uploads': weekday_count,
            'weekend_uploads': weekend_count,
            'weekday_percentage': (weekday_count / total) * 100 if total > 0 else 0,
            'weekend_percentage': (weekend_count / total) * 100 if total > 0 else 0,
            'preferred_period': 'Weekdays' if weekday_count > weekend_count else 'Weekends'
        }
    
    def _calculate_consistency_score(self, intervals: List[int]) -> float:
        """Calculate how consistent the upload schedule is"""
        
        if not intervals:
            return 0
        
        avg_interval = statistics.mean(intervals)
        if avg_interval == 0:
            return 100  # Daily uploads = perfect consistency
        
        # Calculate coefficient of variation
        std_dev = statistics.stdev(intervals) if len(intervals) > 1 else 0
        cv = (std_dev / avg_interval) * 100 if avg_interval > 0 else 0
        
        # Convert to consistency score (lower CV = higher consistency)
        consistency_score = max(0, 100 - cv)
        return round(consistency_score, 1)
    
    def _generate_recommendations(self, analysis: Dict[str, Any], channel_profile: Dict[str, Any]) -> Dict[str, Any]:
        """Generate actionable upload schedule recommendations"""
        
        recommendations = {
            'optimal_schedule': {},
            'improvement_opportunities': [],
            'strategic_insights': [],
            'next_actions': []
        }
        
        # Frequency recommendations
        freq_data = analysis.get('upload_frequency', {})
        consistency_score = freq_data.get('consistency_score', 0)
        
        if consistency_score < 60:
            recommendations['improvement_opportunities'].append(
                "Improve upload consistency - current schedule is irregular"
            )
        
        # Day recommendations
        day_data = analysis.get('day_of_week_patterns', {})
        performance_data = analysis.get('performance_correlation', {})
        
        best_day = performance_data.get('best_performing_day', 'Unknown')
        preferred_day = day_data.get('preferred_day', 'Unknown')
        
        if best_day != preferred_day and best_day != 'Unknown':
            recommendations['improvement_opportunities'].append(
                f"Consider uploading more on {best_day} - it shows {performance_data.get('best_day_avg_views', 0):,} avg views vs current preference for {preferred_day}"
            )
        
        # Time recommendations
        time_data = analysis.get('time_of_day_patterns', {})
        best_time_slot = performance_data.get('best_performing_time_slot', 'Unknown')
        preferred_time_slot = time_data.get('preferred_time_slot', 'Unknown')
        
        if best_time_slot != preferred_time_slot and best_time_slot != 'Unknown':
            recommendations['improvement_opportunities'].append(
                f"Test uploading during {best_time_slot} - shows {performance_data.get('best_slot_avg_views', 0):,} avg views"
            )
        
        # Optimal schedule synthesis
        recommendations['optimal_schedule'] = {
            'recommended_day': best_day if best_day != 'Unknown' else preferred_day,
            'recommended_time_slot': best_time_slot if best_time_slot != 'Unknown' else preferred_time_slot,
            'recommended_frequency': freq_data.get('frequency_category', 'Weekly'),
            'confidence_level': self._calculate_recommendation_confidence(analysis)
        }
        
        # Strategic insights
        weekend_data = day_data.get('weekend_vs_weekday', {})
        if weekend_data.get('weekend_percentage', 0) > 60:
            recommendations['strategic_insights'].append(
                "Strong weekend upload preference - good for leisure/entertainment content"
            )
        
        # Next actions
        recommendations['next_actions'] = [
            f"Test uploading on {best_day} during {best_time_slot}",
            f"Maintain {freq_data.get('frequency_category', 'regular')} upload schedule",
            "Monitor performance for 4-6 weeks after schedule changes",
            "Consider seasonal adjustments based on historical patterns"
        ]
        
        return recommendations
    
    def _calculate_recommendation_confidence(self, analysis: Dict[str, Any]) -> str:
        """Calculate confidence level in recommendations"""
        
        # Base confidence on data quality and consistency
        consistency_score = analysis.get('consistency_metrics', {}).get('consistency_score', 0)
        
        if consistency_score >= 80:
            return "High"
        elif consistency_score >= 60:
            return "Medium"
        else:
            return "Low"

# Create global analyzer instance
upload_analyzer = UploadScheduleAnalyzer()