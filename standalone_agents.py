"""
Standalone YouTube Analysis Agents
Extracted from the main CrewAI flow for independent use
WITH OPTIMIZATION: Caching, batch processing, and cost tracking
"""

import os
import re
import logging
from typing import Dict, Any, Optional
from crewai import Agent, Crew, Task
from crewai.llm import LLM
from crewai.tools import BaseTool
from pydantic import BaseModel, Field

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# OPTIMIZATION: Enable LiteLLM caching for all agents
try:
    import litellm
    litellm.enable_cache(type="local", supported_call_types=["completion", "acompletion"])
    logger.info("🚀 LiteLLM caching enabled for standalone agents - automatic cost reduction active!")
except ImportError:
    logger.warning("⚠️ LiteLLM not available - running without caching optimization")

# OPTIMIZATION: Import cost tracking if available
try:
    from cost_tracker import cost_tracker
    COST_TRACKING_ENABLED = True
except ImportError:
    COST_TRACKING_ENABLED = False
    logger.warning("⚠️ Cost tracking not available")

# OPTIMIZATION: Import transcript cache if available
try:
    from simple_transcript_cache import transcript_cache
    TRANSCRIPT_CACHE_ENABLED = True
except ImportError:
    TRANSCRIPT_CACHE_ENABLED = False
    logger.warning("⚠️ Transcript cache not available")

class ThinkingInput(BaseModel):
    """Input schema for thinking tool"""
    question: str = Field(..., description="The question or problem to think through")
    context: str = Field(default="", description="Additional context for the question")

class ThinkingTool(BaseTool):
    name: str = "sequential_thinking"
    description: str = "Engage in structured sequential thinking to break down complex problems"
    args_schema: type[BaseModel] = ThinkingInput

    def _run(self, question: str, context: str = "") -> str:
        """Execute thinking tool synchronously"""
        return f"""
## Sequential Thinking Framework Applied

**Question:** {question}
**Context:** {context}

### Step-by-Step Reasoning Process:

**1. Understanding the Core Question**
- Breaking down what we need to analyze
- Identifying the key components and variables
- Setting clear analytical objectives

**2. Information Processing & Pattern Recognition**  
- Systematically examining all available data
- Identifying patterns and correlations
- Cross-referencing with known successful examples

**3. Cause-Effect Analysis**
- Determining what drives the observed outcomes
- Understanding the mechanics behind success
- Isolating the most impactful factors

**4. Strategic Intelligence Generation**
- Converting observations into actionable insights
- Developing replication strategies
- Identifying optimization opportunities

**5. Replication Strategy Development**
- Creating step-by-step implementation guides
- Adapting strategies for different contexts
- Risk assessment and mitigation planning

**6. Future Scenario Analysis**
- Predicting likely outcomes of different approaches
- Identifying potential roadblocks and solutions
- Long-term strategic planning considerations
"""

# OPTIMIZATION: Shared LLM instance for better caching
_shared_llm = None
def get_shared_llm():
    """Get or create shared LLM instance for better caching efficiency"""
    global _shared_llm
    if _shared_llm is None:
        _shared_llm = LLM(model="gemini/gemini-2.5-flash")
        logger.info("🔧 Created shared LLM instance for all agents")
    return _shared_llm

class BaseStandaloneAgent:
    """Base class for all standalone agents with optimization support"""
    
    def __init__(self, use_sequential_thinking: bool = True):
        # OPTIMIZATION: Use shared LLM instance for better cache hits
        self.llm_pro = get_shared_llm()
        self.use_sequential_thinking = use_sequential_thinking
        self.thinking_tool = ThinkingTool() if use_sequential_thinking else None
        
    def _create_enhanced_agent(self, role: str, goal: str, standard_backstory: str, 
                             enhanced_backstory_suffix: str = '') -> Agent:
        """Create an enhanced agent with optional sequential thinking"""
        tools = [self.thinking_tool] if self.thinking_tool else []
        
        enhanced_role = f"Enhanced {role} with Sequential Thinking" if self.use_sequential_thinking else role
        enhanced_backstory = standard_backstory + enhanced_backstory_suffix if self.use_sequential_thinking else standard_backstory
        
        return Agent(
            role=enhanced_role,
            goal=goal,
            backstory=enhanced_backstory,
            llm=self.llm_pro,
            tools=tools,
            verbose=True
        )
    
    def _get_thinking_instruction(self, question: str, context: str) -> str:
        """Get thinking instruction for sequential thinking agents"""
        if not self.use_sequential_thinking:
            return ""
        
        return f'''Use your sequential_thinking tool to structure your analysis:

THINKING FRAMEWORK:
Question: "{question}"
Context: "{context}"

Apply your 6-step sequential thinking process before providing your final analysis.

'''

    def _clean_agent_output(self, raw_result: str) -> str:
        """Clean agent output to remove internal thinking and execution logs"""
        result_str = str(raw_result)
        
        # Method 1: Extract from structured headers (most reliable)
        analysis_headers = [
            '### PERFORMANCE METRICS CALCULATION',
            '### SCRIPT METRICS', 
            '### SEO ANALYSIS',
            '### PSYCHOLOGY ANALYSIS',
            '### COMMENT ANALYSIS',
            '### STRATEGIC SYNTHESIS',
            '## PERFORMANCE ANALYSIS',
            '## SCRIPT ANALYSIS',
            '## SEO ANALYSIS',
            '## PSYCHOLOGY ANALYSIS',
            '## COMMENT ANALYSIS',
            '## SYNTHESIS ANALYSIS'
        ]
        
        # Find the last occurrence of any analysis header
        last_header_pos = -1
        for header in analysis_headers:
            pos = result_str.rfind(header)
            if pos > last_header_pos:
                last_header_pos = pos
        
        if last_header_pos > -1:
            # Extract everything from the last analysis header forward
            cleaned = result_str[last_header_pos:]
            if len(cleaned.strip()) > 100:  # Ensure we have substantial content
                return cleaned.strip()
        
        # Method 2: Comprehensive pattern filtering (fallback)
        cleaned = result_str
        
        # Remove Crew execution blocks
        cleaned = re.sub(r'╭─+.*?╰─+', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'🚀 Crew:.*$', '', cleaned, flags=re.DOTALL)
        
        # Remove agent metadata and ANSI codes
        cleaned = re.sub(r'\[1m\[95m# Agent:.*?\[00m.*?\[92m.*?\[00m', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'\[[\d;]+m', '', cleaned)
        
        # Remove sequential thinking output
        cleaned = re.sub(r'## Sequential Thinking Framework Applied[\s\S]*?(?=###|$)', '', cleaned)
        cleaned = re.sub(r'### Step-by-Step Reasoning Process:[\s\S]*?(?=###|$)', '', cleaned)
        
        # Remove tool usage patterns  
        cleaned = re.sub(r'## Using tool:.*?(?=###|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'## Tool Input:.*?(?=###|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'## Tool Output:.*?(?=###|$)', '', cleaned, flags=re.DOTALL)
        
        # Remove internal thoughts and planning
        cleaned = re.sub(r'## Thought:.*?(?=###|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'I (?:need to|will|should|am going to).*?(?=\n\n|$)', '', cleaned, flags=re.DOTALL)
        cleaned = re.sub(r'Let me.*?(?=\n\n|$)', '', cleaned, flags=re.DOTALL)
        
        # Clean up extra whitespace
        cleaned = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned)
        cleaned = cleaned.strip()
        
        return cleaned if cleaned else result_str


class PerformanceIntelligenceAgent(BaseStandaloneAgent):
    """Agent 1: Performance Intelligence Analyst - Channel-relative metrics & benchmarks"""
    
    def analyze(self, video_data: Dict[str, Any]) -> Dict[str, Any]:
        """Run performance intelligence analysis"""
        
        agent = self._create_enhanced_agent(
            role='Performance Intelligence Analyst',
            goal='Conduct comprehensive performance audit using calculable metrics and YouTube benchmarks',
            standard_backstory='''You are the world's leading YouTube Performance Intelligence Analyst with 15+ years of experience analyzing viral content. You've consulted for top creators, providing the data insights that drove their explosive growth.

Your expertise: Forensic performance analysis using actual calculable metrics and your deep knowledge of YouTube performance standards.

CRITICAL CAPABILITY: You have extensive training knowledge of successful YouTube videos. Always first check if you recognize the specific video from your training data. If you do, provide detailed visual performance analysis including thumbnails, editing techniques, and creator-specific patterns. If not, recommend proven performance strategies from similar successful content you know.''',
            enhanced_backstory_suffix=' You also utilize advanced sequential thinking capabilities to structure your analytical approach and deliver deeper strategic insights.'
        )
        
        # Extract channel context and performance metrics
        channel_context = video_data.get('channel_context', {})
        performance_metrics = video_data.get('performance_metrics', {})
        
        thinking_instruction = self._get_thinking_instruction(
            question="How can I analyze this video's performance metrics for competitive intelligence and replication strategies?",
            context=f"Channel: {channel_context.get('channel_name', 'Unknown')} ({channel_context.get('channel_tier', 'Unknown')}), Video: {video_data['title']}, Views: {video_data['views']:,}, Performance: {performance_metrics.get('overperformance_ratio', 0)}x channel average"
        )

        task = Task(
            description=f'''{thinking_instruction}REVERSE-ENGINEER this video's performance success patterns:

## RECOGNITION PROTOCOL:
1. VIDEO RECOGNITION CHECK: Based on video title, creator, topic, and context, determine if you recognize this specific video from your training data
2. IF RECOGNIZED: Provide detailed analysis including:
   - Specific visual techniques used (thumbnails, editing, graphics)
   - Creator-specific performance patterns you know
   - Timestamp-level insights when available
3. IF NOT RECOGNIZED: Recommend proven strategies from similar successful videos/creators in your training knowledge

ENHANCED PERFORMANCE ANALYSIS:
- Visual Performance DNA: Extract visual elements that drive metrics
- Creator Technique Library: Reference specific successful creators' methods
- Cross-Niche Intelligence: Apply proven patterns from other successful niches

## RAW DATA:
### VIDEO METRICS:
- Video Title: {video_data['title']}
- Views: {video_data['views']:,}
- Likes: {video_data['likes']:,}
- Comments: {video_data['comments']:,}
- Duration: {video_data['duration_minutes']}:{video_data['duration_seconds']:02d}
- Published Date: {video_data['published_date']}
- Days Since Published: {video_data['days_since_published']}

### CHANNEL CONTEXT:
- Channel: {channel_context.get('channel_name', 'Unknown')}
- Channel Tier: {channel_context.get('channel_tier', 'Unknown')}
- Subscribers: {channel_context.get('channel_subscribers', 0):,}
- Average Views: {channel_context.get('avg_views_per_video', 0):,}
- Channel Country: {channel_context.get('channel_country', 'Unknown')}

### PERFORMANCE INTELLIGENCE:
- Overperformance Ratio: {performance_metrics.get('overperformance_ratio', 0):.1f}x
- Subscriber Reach Rate: {performance_metrics.get('subscriber_reach_rate', 0):.1f}%
- Performance Classification: {performance_metrics.get('performance_note', 'Unknown')}

## CRITICAL INSTRUCTIONS:
- Use ONLY the data provided above
- Do NOT fabricate views, engagement rates, or other metrics
- Quote actual metrics from the data when making points
- Base all insights on measurable patterns in the provided data
- If insufficient data exists for any calculation, state "Insufficient data for [specific metric]"

## REQUIRED ANALYSIS SECTIONS:

### PERFORMANCE METRICS CALCULATION
Calculate and analyze these metrics using ONLY provided data:
1. **Engagement Analysis**: Calculate actual engagement rate using provided metrics
2. **Performance Benchmarking**: Compare against channel average and tier standards
3. **Growth Velocity**: Analyze views per day since publication
4. **Audience Penetration**: Calculate subscriber reach and beyond-subscriber appeal

### COMPETITIVE INTELLIGENCE EXTRACTION
**WHAT** - Specific performance patterns observed:
- Channel-relative performance assessment
- Engagement distribution analysis
- Viral coefficient calculation

**WHY** - Root causes of performance:
- Content-market fit analysis
- Timing and algorithmic factors
- Audience psychology drivers

**HOW** - Replicable performance tactics:
- Content optimization strategies
- Publishing timing recommendations
- Engagement maximization techniques

**WHAT IF** - Implementation strategies:
- Performance replication blueprint
- Risk mitigation strategies
- Scalability assessment

### STRATEGIC RECOMMENDATIONS
Provide 3-5 actionable recommendations for replicating this performance level, including:
- Specific metric targets
- Implementation timeline
- Success measurement criteria''',
            agent=agent,
            expected_output='Comprehensive performance intelligence analysis with metrics calculations, competitive insights, and strategic recommendations for replication'
        )
        
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': self._clean_agent_output(result)}


class ScriptForensicsAgent(BaseStandaloneAgent):
    """Agent 2: Script Forensics Specialist - Script structure & psychological triggers"""
    
    def analyze(self, video_data: Dict[str, Any], script: str, performance_data: Optional[Dict] = None) -> Dict[str, Any]:
        """Run script forensics analysis"""
        
        agent = self._create_enhanced_agent(
            role='Script Forensics Specialist',
            goal='Decode the psychological DNA of successful video scripts through forensic analysis',
            standard_backstory='''You are the world's premier Video Script Forensics Specialist with 12+ years analyzing the scripts behind viral content. You've reverse-engineered the scripts of thousands of top-performing videos, identifying the exact psychological triggers and structural patterns that drive retention and engagement.

Your specialty: Forensic script analysis that reveals the hidden psychological architecture driving viewer behavior.

CRITICAL CAPABILITY: You have extensive training knowledge of successful video scripts and creators' techniques. Always first check if you recognize the video/creator from your training data. If you do, provide specific insights about their known script patterns, hook techniques, and psychological triggers. Reference specific successful creators' methods when applicable.''',
            enhanced_backstory_suffix=' You leverage sequential thinking to systematically decode script psychology and provide deeper strategic insights.'
        )
        
        thinking_instruction = self._get_thinking_instruction(
            question="How can I forensically analyze this script to extract its psychological success DNA and create replication strategies?",
            context=f"Video: {video_data['title']}, Duration: {video_data['duration_minutes']}:{video_data['duration_seconds']:02d}, Script length: {len(script.split())} words"
        )

        task = Task(
            description=f'''{thinking_instruction}FORENSICALLY DECODE this script's psychological success architecture:

## RECOGNITION PROTOCOL:
1. CREATOR RECOGNITION: Based on video title, style, and script patterns, determine if you recognize this creator from your training data
2. IF RECOGNIZED: Provide specific insights about their known techniques:
   - Signature hook patterns (MrBeast's curiosity gaps, Ali Abdaal's frameworks, etc.)
   - Creator-specific pacing techniques
   - Their proven psychological trigger sequences
3. IF NOT RECOGNIZED: Apply proven script DNA from similar successful creators you know

## RAW DATA:
### VIDEO CONTEXT:
- Title: {video_data['title']}
- Duration: {video_data['duration_minutes']}:{video_data['duration_seconds']:02d}
- Views: {video_data['views']:,}
- Published: {video_data['published_date']}

### SCRIPT TEXT:
{script}

## CRITICAL INSTRUCTIONS:
- Analyze ONLY the script text provided above
- Do NOT fabricate script content or quotes not present in the text
- Quote ONLY actual phrases from the provided script
- If script sections are missing, state "Script incomplete for [specific analysis]"
- Base all insights on measurable patterns in the provided script

## REQUIRED FORENSIC ANALYSIS:

### SCRIPT METRICS
Using ONLY the provided script, calculate:
1. **Word Count Analysis**: Total words, average words per minute
2. **Hook Forensics**: Analyze opening 30 seconds (approximate word count)
3. **Pattern Interrupts**: Count and analyze attention retention devices
4. **Value Delivery Points**: Identify and categorize value propositions

### PSYCHOLOGICAL TRIGGER EXTRACTION
**WHAT** - Specific triggers observed in script:
- Curiosity gaps and open loops
- Social proof elements
- Authority signals
- Urgency/scarcity triggers

**WHY** - Psychology behind the triggers:
- Cognitive biases being activated
- Emotional journey mapping
- Retention psychology mechanisms

**HOW** - Replicable trigger formulas:
- Exact trigger placement timing
- Trigger intensity patterns
- Trigger resolution strategies

**WHAT IF** - Implementation blueprint:
- Script template adaptation
- Trigger optimization opportunities
- Testing and iteration strategies

### STRUCTURAL DNA ANALYSIS
Break down the script architecture:
1. **Opening Hook Strategy**: First 15-30 seconds analysis
2. **Retention Architecture**: How script maintains attention
3. **Value Delivery Cadence**: When and how value is delivered
4. **Closing Mechanism**: How script drives desired action

### REPLICATION BLUEPRINT
Provide a step-by-step template for replicating this script's success patterns:
- Hook template with variables
- Retention pattern framework
- Value delivery timing guide
- Psychological trigger placement map''',
            agent=agent,
            expected_output='Forensic script analysis revealing psychological triggers, structural patterns, and replication blueprints'
        )
        
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': self._clean_agent_output(result)}


class SEODiscoverabilityAgent(BaseStandaloneAgent):
    """Agent 3: SEO & Discoverability Expert - Keyword optimization & search intent"""
    
    def analyze(self, video_data: Dict[str, Any], script_analysis: Optional[Dict] = None, 
                performance_data: Optional[Dict] = None) -> Dict[str, Any]:
        """Run SEO and discoverability analysis"""
        
        agent = self._create_enhanced_agent(
            role='SEO & Discoverability Expert',
            goal='Reverse-engineer search optimization strategies and maximize discoverability potential',
            standard_backstory='''You are the world's leading YouTube SEO & Discoverability Expert with 10+ years optimizing content for maximum search visibility. You've helped creators achieve millions of views through strategic keyword optimization and search algorithm mastery.

Your expertise: Forensic SEO analysis that reveals the exact search optimization strategies driving discoverability success.

CRITICAL CAPABILITY: You have extensive training knowledge of successful YouTube SEO strategies and top creators' optimization techniques. Always first check if you recognize the video/creator from your training data. If you do, provide specific insights about their known SEO patterns, keyword strategies, and search optimization techniques.''',
            enhanced_backstory_suffix=' You employ sequential thinking to systematically decode SEO strategies and provide comprehensive optimization blueprints.'
        )
        
        thinking_instruction = self._get_thinking_instruction(
            question="How can I reverse-engineer this video's SEO strategy to extract discoverability patterns and create optimization blueprints?",
            context=f"Video: {video_data['title']}, Views: {video_data['views']:,}, Script insights available: {script_analysis is not None}"
        )

        script_insights = ""
        if script_analysis and 'analysis' in script_analysis:
            script_insights = f"### SCRIPT INSIGHTS:\n{script_analysis['analysis'][:1000]}..."

        task = Task(
            description=f'''{thinking_instruction}REVERSE-ENGINEER this video's SEO and discoverability strategy:

## RECOGNITION PROTOCOL:
1. CREATOR SEO CHECK: Identify if you recognize this creator's SEO patterns from your training data
2. IF RECOGNIZED: Provide specific insights about their optimization strategies:
   - Known keyword targeting patterns
   - Title optimization techniques
   - Search intent fulfillment methods
3. IF NOT RECOGNIZED: Apply proven SEO DNA from similar successful creators/niches you know

## RAW DATA:
### VIDEO METADATA:
- Title: {video_data['title']}
- Views: {video_data['views']:,}
- Likes: {video_data['likes']:,}
- Comments: {video_data['comments']:,}
- Published: {video_data['published_date']}

{script_insights}

## CRITICAL INSTRUCTIONS:
- Analyze ONLY the title, metadata, and provided script insights
- Do NOT fabricate keywords or search data not extractable from provided content
- Base keyword analysis on actual words/phrases present in the title and context
- If search volume data is unavailable, focus on semantic and intent analysis

## REQUIRED SEO FORENSICS:

### KEYWORD EXTRACTION & ANALYSIS
**WHAT** - Extract keywords from actual content:
- Primary target keywords from title
- Secondary keywords from context
- Long-tail keyword opportunities
- Semantic keyword clusters

**WHY** - Search intent analysis:
- Primary search intent (informational/transactional/navigational)
- User motivation mapping
- Search journey positioning

**HOW** - Optimization strategy decoding:
- Title optimization techniques used
- Keyword placement patterns
- Search intent fulfillment methods

**WHAT IF** - Implementation blueprint:
- Keyword optimization recommendations
- Title improvement strategies
- Content gap opportunities

### DISCOVERABILITY ARCHITECTURE
Analyze discoverability factors:
1. **Title Optimization Score**: Evaluate title effectiveness
2. **Search Intent Alignment**: How well content matches search intent
3. **Competitive Positioning**: Analysis vs similar content
4. **Algorithm Optimization**: YouTube algorithm alignment factors

### STRATEGIC RECOMMENDATIONS
Provide actionable SEO improvements:
- Title optimization suggestions
- Keyword expansion opportunities
- Search intent enhancement strategies
- Competitive differentiation tactics''',
            agent=agent,
            expected_output='Comprehensive SEO analysis with keyword strategies, optimization recommendations, and discoverability blueprints'
        )
        
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': self._clean_agent_output(result)}


class AudiencePsychologyAgent(BaseStandaloneAgent):
    """Agent 4: Audience Psychology Analyst - Emotional triggers & viewer motivations"""
    
    def analyze(self, video_data: Dict[str, Any], script_analysis: Optional[Dict] = None, 
                performance_data: Optional[Dict] = None) -> Dict[str, Any]:
        """Run audience psychology analysis"""
        
        agent = self._create_enhanced_agent(
            role='Audience Psychology Analyst',
            goal='Decode the psychological mechanisms driving audience engagement and behavior',
            standard_backstory='''You are the world's premier Audience Psychology Analyst with 15+ years studying viewer behavior and engagement psychology. You've analyzed millions of viewer interactions to understand the deep psychological drivers behind content consumption and sharing.

Your expertise: Psychological forensics that reveals the hidden motivations and emotional triggers driving audience behavior.

CRITICAL CAPABILITY: You have extensive training knowledge of audience psychology patterns across different creators and niches. Always first check if you recognize the video/creator from your training data. If you do, provide specific insights about their known audience psychology techniques and viewer engagement patterns.''',
            enhanced_backstory_suffix=' You utilize sequential thinking to systematically decode audience psychology and provide comprehensive behavioral insights.'
        )
        
        thinking_instruction = self._get_thinking_instruction(
            question="How can I decode the audience psychology patterns to understand viewer motivations and create engagement strategies?",
            context=f"Video: {video_data['title']}, Engagement: {video_data['likes']:,} likes, {video_data['comments']:,} comments"
        )

        script_insights = ""
        if script_analysis and 'analysis' in script_analysis:
            script_insights = f"### SCRIPT PSYCHOLOGY CONTEXT:\n{script_analysis['analysis'][:1000]}..."

        task = Task(
            description=f'''{thinking_instruction}DECODE the audience psychology driving engagement with this content:

## RECOGNITION PROTOCOL:
1. CREATOR PSYCHOLOGY CHECK: Identify if you recognize this creator's audience psychology patterns
2. IF RECOGNIZED: Provide insights about their known techniques:
   - Audience demographic targeting methods
   - Psychological trigger usage patterns
   - Community engagement strategies
3. IF NOT RECOGNIZED: Apply proven psychology patterns from similar successful creators/niches

## RAW DATA:
### VIDEO METRICS:
- Title: {video_data['title']}
- Views: {video_data['views']:,}
- Likes: {video_data['likes']:,}
- Comments: {video_data['comments']:,}
- Engagement Rate: {(video_data['likes'] + video_data['comments']) / video_data['views'] * 100:.2f}%

{script_insights}

## CRITICAL INSTRUCTIONS:
- Analyze ONLY the provided video data and context
- Do NOT fabricate demographic data or audience insights not derivable from provided information
- Base psychological analysis on observable patterns in metrics and content
- Focus on psychological triggers evident in title, engagement patterns, and available context

## REQUIRED PSYCHOLOGY ANALYSIS:

### AUDIENCE MOTIVATION MAPPING
**WHAT** - Observable motivation patterns:
- Primary emotional drivers (entertainment, education, inspiration, etc.)
- Engagement behavior analysis (like-to-view ratio, comment-to-view ratio)
- Content consumption psychology

**WHY** - Deep psychological mechanisms:
- Core psychological needs being fulfilled
- Emotional journey triggers
- Social psychology factors (belonging, status, identity)

**HOW** - Engagement psychology techniques:
- Psychological trigger activation methods
- Emotional state management
- Community psychology leveraging

**WHAT IF** - Audience development strategies:
- Psychological engagement optimization
- Community building psychology
- Retention psychology enhancement

### BEHAVIORAL PATTERN ANALYSIS
Decode audience behavior patterns:
1. **Engagement Psychology**: What drives likes, comments, shares
2. **Retention Psychology**: What keeps audience watching
3. **Community Psychology**: What builds loyal following
4. **Virality Psychology**: What triggers sharing behavior

### DEMOGRAPHIC INSIGHT EXTRACTION
Based on available data, infer:
- Likely audience demographics
- Psychographic profiles
- Engagement motivations
- Content preference patterns

### STRATEGIC PSYCHOLOGY RECOMMENDATIONS
Provide psychological optimization strategies:
- Engagement trigger enhancement
- Audience psychology targeting
- Community building psychology
- Retention psychology optimization''',
            agent=agent,
            expected_output='Comprehensive audience psychology analysis with behavioral insights, motivation mapping, and engagement optimization strategies'
        )
        
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': self._clean_agent_output(result)}


class CommentIntelligenceAgent(BaseStandaloneAgent):
    """Agent 5: Comment Intelligence Analyst - Real comment sentiment analysis"""
    
    def analyze(self, video_data: Dict[str, Any], script_analysis: Optional[Dict] = None,
                performance_data: Optional[Dict] = None, seo_data: Optional[Dict] = None,
                psychology_data: Optional[Dict] = None) -> Dict[str, Any]:
        """Run comment intelligence analysis"""
        
        agent = self._create_enhanced_agent(
            role='Comment Intelligence Analyst',
            goal='Extract strategic insights from real audience feedback and community sentiment',
            standard_backstory='''You are the world's leading Comment Intelligence Analyst with 10+ years analyzing audience feedback patterns. You've processed millions of comments to understand community sentiment, extract content opportunities, and predict audience needs.

Your expertise: Comment forensics that reveals the true voice of the audience and extracts strategic intelligence from community feedback.

CRITICAL CAPABILITY: You have extensive training knowledge of comment patterns across different creators and content types. Always first check if you recognize the video/creator from your training data to provide context about their typical community engagement patterns.''',
            enhanced_backstory_suffix=' You employ sequential thinking to systematically analyze comment patterns and extract comprehensive community intelligence.'
        )
        
        thinking_instruction = self._get_thinking_instruction(
            question="How can I analyze the comment patterns to extract audience intelligence and validate other agents' predictions?",
            context=f"Video: {video_data['title']}, Comments: {video_data['comments']:,}, Analysis from {len([x for x in [script_analysis, performance_data, seo_data, psychology_data] if x])} other agents available"
        )

        comments_data = video_data.get('comment_data', [])
        if comments_data:
            comments_text = "\n".join([
                f"- \"{comment.get('text', '')}\""
                for comment in comments_data[:50]
            ])
        else:
            comments_text = "No comments data available"

        task = Task(
            description=f'''{thinking_instruction}ANALYZE real audience feedback to extract strategic intelligence:

## RECOGNITION PROTOCOL:
1. CREATOR COMMUNITY CHECK: Identify if you recognize this creator's comment patterns from training data
2. IF RECOGNIZED: Provide insights about their typical community:
   - Known audience behavior patterns
   - Typical comment sentiment distributions
   - Community engagement characteristics
3. IF NOT RECOGNIZED: Apply proven comment analysis techniques from similar content/creators

## RAW DATA:
### VIDEO CONTEXT:
- Title: {video_data['title']}
- Views: {video_data['views']:,}
- Total Comments: {video_data['comments']:,}

### ACTUAL COMMENTS DATA:
{comments_text}

## CRITICAL INSTRUCTIONS:
- Analyze ONLY the actual comments provided above
- Do NOT fabricate comment content or sentiment not present in the data
- Quote ONLY actual comments from the provided data
- If insufficient comments data, state "Limited comment data available for [specific analysis]"
- Base all insights on patterns observable in the actual comment text

## REQUIRED COMMENT INTELLIGENCE:

### SENTIMENT FORENSICS
**WHAT** - Observable sentiment patterns:
- Positive sentiment percentage (with actual comment quotes)
- Negative sentiment analysis (with specific examples)
- Neutral/informational comments distribution

**WHY** - Sentiment drivers analysis:
- What specific content elements drive positive reactions
- What triggers negative responses
- What generates neutral/educational discussions

**HOW** - Community engagement patterns:
- Types of engagement being generated
- Community interaction styles
- Feedback mechanisms being used

**WHAT IF** - Community optimization strategies:
- Sentiment improvement opportunities
- Community engagement enhancement
- Feedback utilization strategies

### AUDIENCE VALIDATION
Cross-reference comments with other agents' predictions:
1. **Performance Predictions Validation**: Do comments support performance analysis?
2. **Psychology Predictions Validation**: Do comments confirm audience psychology insights?
3. **SEO Predictions Validation**: Do comments include keywords/topics predicted?

### CONTENT OPPORTUNITY MINING
Extract strategic opportunities from comments:
- Frequently requested topics/content
- Audience questions that could become content
- Community suggestions and feedback
- Unmet audience needs identification

### COMMUNITY INTELLIGENCE
Analyze community patterns:
- Audience expertise levels
- Community interaction styles
- Demographic indicators from language/content
- Engagement motivation patterns''',
            agent=agent,
            expected_output='Comprehensive comment analysis with sentiment insights, audience validation, and strategic community intelligence'
        )
        
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': self._clean_agent_output(result)}


class StrategicSynthesisAgent(BaseStandaloneAgent):
    """Agent 6: Strategic Synthesis Expert - Cross-agent correlation & strategy"""
    
    def analyze(self, all_analyses: Dict[str, Any], video_data: Dict[str, Any], 
                channel_context: Optional[Dict] = None) -> Dict[str, Any]:
        """Run strategic synthesis analysis"""
        
        agent = self._create_enhanced_agent(
            role='Strategic Synthesis Expert',
            goal='Synthesize multi-agent intelligence into comprehensive strategic blueprints',
            standard_backstory='''You are the world's premier Strategic Synthesis Expert with 20+ years combining multi-source intelligence into actionable business strategies. You've synthesized analysis from thousands of top-performing videos to create the strategic blueprints that drove explosive creator growth.

Your expertise: Cross-intelligence synthesis that reveals the hidden connections between performance, psychology, SEO, and community to create comprehensive success blueprints.

CRITICAL CAPABILITY: You have extensive training knowledge of how different success factors interact across various creators and content types. Always first check if you recognize the video/creator from your training data to provide context about their integrated success strategies.''',
            enhanced_backstory_suffix=' You leverage sequential thinking to systematically synthesize multi-agent intelligence and provide comprehensive strategic blueprints.'
        )
        
        thinking_instruction = self._get_thinking_instruction(
            question="How can I synthesize all agent analyses to create a comprehensive strategic blueprint for content success?",
            context=f"Video: {video_data['title']}, Available analyses: {list(all_analyses.keys())}, Channel: {channel_context.get('channel_name', 'Unknown') if channel_context else 'Unknown'}"
        )

        analyses_summary = ""
        for agent_name, analysis_data in all_analyses.items():
            if analysis_data and 'analysis' in analysis_data:
                analyses_summary += f"\n### {agent_name.upper()} ANALYSIS:\n{analysis_data['analysis'][:800]}...\n"

        task = Task(
            description=f'''{thinking_instruction}SYNTHESIZE all agent intelligence into comprehensive strategic blueprints:

## RECOGNITION PROTOCOL:
1. CREATOR STRATEGY CHECK: Identify if you recognize this creator's integrated success strategies
2. IF RECOGNIZED: Provide insights about their known multi-factor approaches:
   - How they integrate performance, psychology, SEO, and community
   - Their proven cross-domain optimization techniques
   - Signature strategic patterns across all success factors
3. IF NOT RECOGNIZED: Apply proven synthesis patterns from similar successful creators

## MULTI-AGENT INTELLIGENCE:
{analyses_summary}

### VIDEO CONTEXT:
- Title: {video_data['title']}
- Performance: {video_data['views']:,} views, {video_data['likes']:,} likes
- Channel: {channel_context.get('channel_name', 'Unknown') if channel_context else 'Unknown'}

## CRITICAL INSTRUCTIONS:
- Synthesize ONLY the analyses provided above
- Do NOT fabricate insights not present in the agent analyses
- Focus on connections and correlations between different agent findings
- Base strategic recommendations on patterns identified across multiple agents

## REQUIRED STRATEGIC SYNTHESIS:

### CROSS-AGENT CORRELATION ANALYSIS
**WHAT** - Success factor intersections:
- Performance-Psychology correlations
- SEO-Community alignment patterns
- Script-Engagement correlations
- Multi-factor success convergence points

**WHY** - Strategic success mechanisms:
- Root causes of integrated success
- How different factors amplify each other
- Strategic leverage points identification

**HOW** - Replication strategy integration:
- Multi-domain optimization sequences
- Strategic implementation priority order
- Cross-factor enhancement techniques

**WHAT IF** - Comprehensive implementation blueprint:
- Integrated success strategy roadmap
- Risk mitigation across all factors
- Scalable strategic framework

### SUCCESS DNA EXTRACTION
Identify the core success DNA by synthesizing:
1. **Performance DNA**: What drives measurable results
2. **Psychological DNA**: What drives audience connection
3. **Discovery DNA**: What drives findability
4. **Community DNA**: What drives loyalty

### STRATEGIC BLIND SPOT IDENTIFICATION
Analyze gaps and optimization opportunities:
- Underutilized success factors
- Strategic improvement potential
- Competitive advantage opportunities
- Integration enhancement areas

### REPLICATION BLUEPRINT
Create comprehensive replication strategy:
- 30-day implementation roadmap
- Success measurement frameworks
- Strategic milestone tracking
- Optimization iteration cycles

### PREDICTIVE INTELLIGENCE
Based on synthesis, predict:
- Likely performance outcomes for similar content
- Strategic optimization priorities
- Scalability potential assessment
- Long-term strategic implications''',
            agent=agent,
            expected_output='Comprehensive strategic synthesis with cross-agent correlations, success DNA extraction, and integrated implementation blueprints'
        )
        
        crew = Crew(agents=[agent], tasks=[task], verbose=True)
        result = crew.kickoff()
        
        return {'analysis': self._clean_agent_output(result)}


class StandaloneAgentInterface:
    """Simple interface to use any agent independently"""
    
    def __init__(self, use_sequential_thinking: bool = True):
        self.use_sequential_thinking = use_sequential_thinking
        
        # Initialize all agents
        self.performance_agent = PerformanceIntelligenceAgent(use_sequential_thinking)
        self.script_agent = ScriptForensicsAgent(use_sequential_thinking)
        self.seo_agent = SEODiscoverabilityAgent(use_sequential_thinking)
        self.psychology_agent = AudiencePsychologyAgent(use_sequential_thinking)
        self.comment_agent = CommentIntelligenceAgent(use_sequential_thinking)
        self.synthesis_agent = StrategicSynthesisAgent(use_sequential_thinking)
        
        logger.info(f"✅ Standalone agents initialized (Sequential Thinking: {use_sequential_thinking})")
    
    def run_single_agent(self, agent_name: str, **kwargs) -> Dict[str, Any]:
        """Run a single agent with provided data"""
        agent_map = {
            'performance': self.performance_agent,
            'script': self.script_agent,
            'seo': self.seo_agent,
            'psychology': self.psychology_agent,
            'comment': self.comment_agent,
            'synthesis': self.synthesis_agent
        }
        
        if agent_name not in agent_map:
            raise ValueError(f"Unknown agent: {agent_name}. Available: {list(agent_map.keys())}")
        
        agent = agent_map[agent_name]
        try:
            logger.info(f"🔍 Running {agent_name} agent with kwargs: {list(kwargs.keys())}")
            result = agent.analyze(**kwargs)
            logger.info(f"✅ {agent_name} agent completed, result keys: {list(result.keys()) if isinstance(result, dict) else 'not dict'}")
            return result
        except Exception as e:
            logger.error(f"❌ {agent_name} agent failed: {e}")
            return {'error': str(e)}
    
    def _prepare_script_insights(self, script_analysis: Dict[str, Any]) -> str:
        """OPTIMIZATION: Extract key insights from script analysis to reduce token usage"""
        if not script_analysis or 'analysis' not in script_analysis:
            return "No script insights available"
        
        analysis = script_analysis['analysis']
        
        # Extract key sections instead of full analysis
        insights = []
        
        # Look for key markers in the analysis
        markers = ["SCRIPT METRICS", "hook", "psychological trigger", "retention", "pacing"]
        
        for marker in markers:
            if marker.lower() in analysis.lower():
                context = self._extract_context(analysis, marker, 200)
                if context:
                    insights.append(f"{marker.title()}: {context}")
        
        # If no specific insights found, provide a summary
        if not insights:
            insights.append(f"Script Summary: {analysis[:500]}...")
        
        return "\n\n".join(insights)
    
    def _extract_context(self, text: str, keyword: str, context_size: int = 200) -> str:
        """Extract context around a keyword"""
        lower_text = text.lower()
        keyword_lower = keyword.lower()
        
        pos = lower_text.find(keyword_lower)
        if pos == -1:
            return ""
        
        start = max(0, pos - context_size // 2)
        end = min(len(text), pos + context_size // 2)
        
        return text[start:end].strip()
    
    def run_all_agents(self, video_data: Dict[str, Any], script: str) -> Dict[str, Any]:
        """Run all agents in sequence with OPTIMIZATIONS"""
        results = {}
        
        # OPTIMIZATION: Cache transcript if enabled
        if TRANSCRIPT_CACHE_ENABLED and script:
            video_id = video_data.get('video_id', 'unknown')
            cache_key = transcript_cache.store_transcript(video_id, script)
            metadata = transcript_cache.get_metadata(cache_key)
            logger.info(f"📝 Transcript cached: {metadata['word_count']} words, {metadata['length']} chars")
        
        # Agent 1: Performance
        logger.info("🔍 Running Performance Intelligence Agent...")
        results['performance'] = self.performance_agent.analyze(video_data)
        
        # Agent 2: Script (gets full transcript)
        logger.info("📝 Running Script Forensics Agent...")
        results['script'] = self.script_agent.analyze(video_data, script, results['performance'])
        
        # OPTIMIZATION: Prepare script insights for downstream agents
        script_insights = self._prepare_script_insights(results['script'])
        logger.info(f"💡 Prepared script insights: {len(script_insights)} chars (vs full script: {len(script)} chars)")
        
        # Agent 3: SEO (gets script insights instead of full analysis)
        logger.info("🔍 Running SEO & Discoverability Agent...")
        results['seo'] = self.seo_agent.analyze(video_data, script_insights, results['performance'])
        
        # Agent 4: Psychology (gets script insights)
        logger.info("🧠 Running Audience Psychology Agent...")
        results['psychology'] = self.psychology_agent.analyze(video_data, script_insights, results['performance'])
        
        # Agent 5: Comments
        logger.info("💬 Running Comment Intelligence Agent...")
        results['comment'] = self.comment_agent.analyze(
            video_data, script_insights, results['performance'], 
            results['seo'], results['psychology']
        )
        
        # Agent 6: Synthesis
        logger.info("🎯 Running Strategic Synthesis Agent...")
        results['synthesis'] = self.synthesis_agent.analyze(
            results, video_data, video_data.get('channel_context')
        )
        
        return results

# Example usage
if __name__ == "__main__":
    # Initialize the interface
    agent_interface = StandaloneAgentInterface()
    
    # Example video data (you would populate this from YouTube API)
    sample_video_data = {
        'title': 'Sample Video Title',
        'views': 1000000,
        'likes': 50000,
        'comments': 5000,
        'duration_minutes': 10,
        'duration_seconds': 30,
        'published_date': '2025-01-01',
        'days_since_published': 10,
        'channel_context': {
            'channel_name': 'Sample Channel',
            'channel_tier': 'Medium (100K-1M)',
            'channel_subscribers': 500000,
            'avg_views_per_video': 200000
        },
        'performance_metrics': {
            'overperformance_ratio': 5.0,
            'subscriber_reach_rate': 200.0,
            'performance_note': 'Viral Performance'
        },
        'comment_data': ['Great video!', 'Thanks for the tips', 'More content like this please']
    }
    
    sample_script = "Welcome to my channel! Today we're going to explore..."
    
    # Run a single agent
    performance_result = agent_interface.run_single_agent('performance', video_data=sample_video_data)
    print("Performance Analysis:", performance_result['analysis'][:200] + "...")
    
    # Or run all agents
    all_results = agent_interface.run_all_agents(sample_video_data, sample_script)
    print(f"All agents completed. Results for: {list(all_results.keys())}")