#!/usr/bin/env python3
"""
Test script to isolate thumbnail analysis issues
"""

import os
import sys
from working_crewai_app_tabbed import VideoAnalysisCrewAI

def test_thumbnail_analysis():
    """Test the thumbnail analysis agent in isolation"""
    
    print("🧪 Testing Thumbnail Analysis Agent")
    print("=" * 50)
    
    # Initialize the VideoAnalysisCrewAI
    video_ai = VideoAnalysisCrewAI()
    
    # Check if AI is enabled
    print(f"AI Enabled: {video_ai.ai_enabled}")
    print(f"LLM Pro: {video_ai.llm_pro is not None}")
    
    if not video_ai.ai_enabled:
        print("❌ AI is not enabled. Check your GEMINI_API_KEY environment variable.")
        return
    
    # Test with a known YouTube video
    test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Rick Roll
    print(f"\n🎬 Testing with video: {test_url}")
    
    # Get video data
    print("\n📊 Fetching video data...")
    video_data = video_ai._get_video_data_from_url(test_url)
    
    if 'error' in video_data:
        print(f"❌ Error fetching video data: {video_data['error']}")
        return
    
    print(f"✅ Video data fetched successfully")
    print(f"   Title: {video_data.get('title', 'Unknown')}")
    print(f"   Views: {video_data.get('views', 0):,}")
    
    # Check thumbnail URL
    thumbnail_url = video_data.get('thumbnail_url', '')
    if not thumbnail_url:
        print("❌ No thumbnail URL found in video data")
        return
    
    print(f"✅ Thumbnail URL found: {thumbnail_url[:100]}...")
    
    # Test thumbnail analysis
    print("\n🖼️  Running thumbnail analysis...")
    try:
        result = video_ai._run_thumbnail_intelligence_agent(video_data, thumbnail_url)
        
        if 'error' in result:
            print(f"❌ Thumbnail analysis failed: {result['error']}")
        else:
            print("✅ Thumbnail analysis completed successfully!")
            print(f"   Result keys: {list(result.keys())}")
            if 'analysis' in result:
                analysis_length = len(result['analysis'])
                print(f"   Analysis length: {analysis_length} characters")
                print(f"   Analysis preview: {result['analysis'][:200]}...")
            
    except Exception as e:
        print(f"❌ Exception during thumbnail analysis: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_thumbnail_analysis()
