#!/usr/bin/env python3
"""
Debug the content validation to see what's happening
"""

from bs4 import BeautifulSoup

# Sample HTML output from the agent
html_output = '''<div class="metrics-grid">
    <div class="metric-card"><div class="metric-value">100K</div><div class="metric-label">Views</div></div>
    <div class="metric-card"><div class="metric-value">5K</div><div class="metric-label">Likes</div></div>
    <div class="metric-card"><div class="metric-value">5%</div><div class="metric-label">Engagement</div></div>
  </div>
  <div class="insight-card">
    <div class="insight-content">Views: 100K, Likes: 5K, Engagement: 5%. Great performance!</div>
  </div>'''

original_text = "Views: 100K, Likes: 5K, Engagement: 5%. Great performance!"

print("DEBUGGING VALIDATION:")
print("=" * 50)
print(f"Original: {original_text}")
print()

# Extract text from HTML
soup = BeautifulSoup(html_output, 'html.parser')
styled_text = soup.get_text()
print(f"Extracted: {styled_text}")
print()

# Compare words
original_words = set(original_text.lower().split())
styled_words = set(styled_text.lower().split())

print(f"Original words: {original_words}")
print(f"Styled words: {styled_words}")
print()

# Check preservation
preserved = original_words & styled_words
missing = original_words - styled_words

print(f"Preserved: {preserved}")
print(f"Missing: {missing}")
print()

preservation_ratio = len(preserved) / len(original_words) if original_words else 1
print(f"Preservation ratio: {preservation_ratio:.2f}")

# The issue is clear - BeautifulSoup.get_text() extracts ALL text, including metric values
# which creates duplicates and different formatting
print("\nDEBUG: Full extracted text with spacing:")
print(repr(styled_text))