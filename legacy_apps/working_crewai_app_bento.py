"""
Working CrewAI YouTube Research v2 App
BENTO BOX DESIGN - Dynamic, Content-Aware Layout
"""

import os
import uuid
import json
from datetime import datetime, timezone
from typing import Dict, Any
from fastapi import FastAPI, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import logging

# CrewAI imports
from crewai import Agent, Crew, Task, Process
from crewai.llm import LLM

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="YouTube Research v2 - CrewAI")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple in-memory storage
analysis_store = {}

# Import our YouTube tools
from src.tools.youtube_tools import YouTube<PERSON>hannelAnalysisTool

# Pydantic models
class AnalysisRequest(BaseModel):
    channel_id: str
    max_videos: int = 20
    max_comments: int = 50

class AnalysisResponse(BaseModel):
    analysis_id: str
    status: str
    message: str

class MarketResearchCrewAI:
    """CrewAI implementation of Market Research for AI Channel Creation"""
    
    def __init__(self):
        # Initialize Gemini models
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if gemini_api_key:
            # Set environment variable for LiteLLM
            os.environ['GEMINI_API_KEY'] = gemini_api_key
            
            self.llm_pro = LLM(
                model="gemini/gemini-1.5-pro"
            )
            self.llm_flash = LLM(
                model="gemini/gemini-1.5-flash"
            )
            self.ai_enabled = True
        else:
            self.llm_pro = None
            self.llm_flash = None
            self.ai_enabled = False
            
        # Initialize YouTube tools
        youtube_api_key = os.getenv('YOUTUBE_API_KEY')
        if youtube_api_key:
            self.youtube_tool = YouTubeChannelAnalysisTool(
                youtube_api_key=youtube_api_key,
                supadata_api_key=os.getenv('SUPADATA_API_KEY')
            )
            self.tools_available = True
        else:
            self.youtube_tool = None
            self.tools_available = False
    
    def create_agents(self):
        """Create the 6 Market Research agents"""
        
        if not self.ai_enabled:
            # Create simple agents without LLM
            return [
                Agent(
                    role="Market Opportunity Scout",
                    goal="Find content gaps and underserved audiences for new AI-generated channels",
                    backstory="Expert at identifying blue ocean opportunities in saturated markets",
                    verbose=True
                ),
                Agent(
                    role="Content Strategy Architect", 
                    goal="Extract viral patterns and content formats that work",
                    backstory="Specializes in reverse-engineering successful content strategies",
                    verbose=True
                ),
                Agent(
                    role="Audience Intelligence Analyst",
                    goal="Mine comments for unmet needs and content requests",
                    backstory="Expert at finding 'I wish someone would make...' opportunities in audience feedback",
                    verbose=True
                ),
                Agent(
                    role="Competitive Landscape Mapper",
                    goal="Benchmark performance and identify positioning opportunities",
                    backstory="Analyzes competitive dynamics and market positioning strategies",
                    verbose=True
                ),
                Agent(
                    role="Production Intelligence Agent",
                    goal="Assess AI feasibility and production complexity",
                    backstory="Evaluates content for automation potential and production efficiency",
                    verbose=True
                ),
                Agent(
                    role="Launch Strategy Synthesizer",
                    goal="Create comprehensive go-to-market plans for new channels",
                    backstory="Develops strategic launch plans combining all intelligence gathered",
                    verbose=True
                )
            ]
        
        # Full agents with LLM
        agents = [
            Agent(
                role="Market Opportunity Scout",
                goal="Find content gaps and underserved audiences for new AI-generated channels",
                backstory="""You are an expert market researcher specializing in identifying blue ocean opportunities 
                in saturated content markets. Your focus is finding untapped niches where new AI-generated channels 
                can succeed without competing directly with established creators.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_flash,
                verbose=True
            ),
            
            Agent(
                role="Content Strategy Architect",
                goal="Extract viral patterns and content formats that work",
                backstory="""You specialize in reverse-engineering successful content strategies. You analyze 
                what makes content go viral, identify repeatable patterns, and extract actionable insights 
                for creating similar but improved content.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_pro,
                verbose=True
            ),
            
            Agent(
                role="Audience Intelligence Analyst", 
                goal="Mine comments for unmet needs and content requests",
                backstory="""You are an expert at finding hidden opportunities in audience feedback. You excel 
                at identifying 'I wish someone would make...' comments, recurring complaints, and unmet needs 
                that represent content opportunities.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_flash,
                verbose=True
            ),
            
            Agent(
                role="Competitive Landscape Mapper",
                goal="Benchmark performance and identify positioning opportunities", 
                backstory="""You analyze competitive dynamics and market positioning. You understand how to 
                position new channels to avoid direct competition while capturing market share through 
                strategic differentiation.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_flash,
                verbose=True
            ),
            
            Agent(
                role="Production Intelligence Agent",
                goal="Assess AI feasibility and production complexity for content automation",
                backstory="""You evaluate content for automation potential using AI tools. You understand 
                production workflows, identify what can be automated vs requires human creativity, and 
                calculate ROI for AI-generated content strategies.""",
                tools=[self.youtube_tool] if self.tools_available else [],
                llm=self.llm_pro,
                verbose=True
            ),
            
            Agent(
                role="Launch Strategy Synthesizer",
                goal="Create comprehensive go-to-market plans for new channels",
                backstory="""You synthesize all market intelligence into actionable launch strategies. 
                You create detailed plans that combine content strategy, audience targeting, competitive 
                positioning, and production workflows into cohesive channel launch blueprints.""",
                llm=self.llm_pro,
                verbose=True
            )
        ]
        
        return agents
    
    def create_tasks(self, agents, channel_data):
        """Create tasks for market research analysis"""
        
        tasks = [
            Task(
                description=f"""Analyze this YouTube channel data to identify market opportunities for new AI channels:
                
                Channel: {channel_data.get('channel', {}).get('title', 'Unknown')}
                Videos analyzed: {len(channel_data.get('videos', []))}
                
                Your mission: Find content gaps, underserved audiences, and blue ocean opportunities where a new 
                AI-generated channel could succeed. Look for:
                
                1. Content types that are popular but have low competition
                2. Audience segments that seem underserved 
                3. Geographic or demographic gaps
                4. Trending topics with room for new players
                5. Format innovations that could disrupt the space
                
                Focus on opportunities that favor AI-generated content (scripted formats, educational content, 
                list-based videos, etc.)
                
                Data: {json.dumps(channel_data, indent=2)}""",
                agent=agents[0],
                expected_output="Detailed market opportunity analysis with specific content gaps and target audiences"
            ),
            
            Task(
                description=f"""Extract viral content patterns and strategies from this channel data:
                
                Analyze the most successful videos to understand:
                1. What content formats work best (tutorials, lists, stories, etc.)
                2. Optimal video length and structure patterns  
                3. Title formulas and thumbnail strategies
                4. Content themes that generate high engagement
                5. Upload timing and frequency patterns
                
                Create actionable insights for replicating success with AI-generated content.
                
                Data: {json.dumps(channel_data, indent=2)}""",
                agent=agents[1], 
                expected_output="Viral content strategy blueprint with specific patterns and formulas"
            ),
            
            Task(
                description=f"""Mine the comments data to find unmet audience needs:
                
                Look for:
                1. "I wish someone would make..." type comments
                2. Recurring questions that aren't being answered
                3. Complaints about existing content 
                4. Requests for different formats or topics
                5. Geographic or language gaps mentioned
                
                Identify specific content opportunities hiding in audience feedback.
                
                Comments data: {json.dumps(channel_data.get('comments', {}), indent=2)}""",
                agent=agents[2],
                expected_output="List of unmet audience needs and content opportunities from comment analysis"
            ),
            
            Task(
                description=f"""Map the competitive landscape and positioning opportunities:
                
                Analyze this channel's performance against typical benchmarks:
                1. Engagement rates vs subscriber count
                2. View consistency across videos
                3. Growth trajectory indicators
                4. Content saturation in this niche
                5. Opportunities for strategic differentiation
                
                Identify how a new channel could position itself competitively.
                
                Data: {json.dumps(channel_data, indent=2)}""",
                agent=agents[3],
                expected_output="Competitive analysis with strategic positioning recommendations"
            ),
            
            Task(
                description=f"""Assess AI production feasibility for this content niche:
                
                Evaluate:
                1. How easily this content type can be automated with AI
                2. Production complexity and resource requirements  
                3. Quality expectations vs AI capabilities
                4. ROI potential for AI-generated versions
                5. Tools and workflows needed for automation
                
                Rate feasibility from 1-100 and provide specific recommendations.
                
                Content analysis: {json.dumps(channel_data.get('videos', [])[:5], indent=2)}""",
                agent=agents[4],
                expected_output="AI production feasibility assessment with automation recommendations"
            ),
            
            Task(
                description="""Synthesize all intelligence into a comprehensive launch strategy:
                
                Using insights from the previous analyses, create a detailed plan for launching a new AI-generated 
                channel in this space including:
                
                1. Recommended content pillars and format strategy
                2. Target audience and positioning approach
                3. Competitive differentiation tactics
                4. Production workflow and tool recommendations
                5. Launch timeline and milestone targets
                6. Success metrics and optimization approach
                
                Make this actionable and specific.""",
                agent=agents[5],
                expected_output="Comprehensive channel launch strategy with specific recommendations"
            )
        ]
        
        return tasks
    
    def run_analysis(self, channel_id: str, max_videos: int = 20, max_comments: int = 50):
        """Run the complete market research analysis"""
        
        try:
            # Get channel data
            if self.tools_available:
                logger.info(f"Fetching data for channel: {channel_id}")
                channel_data = self.youtube_tool.get_comprehensive_analysis(
                    channel_id=channel_id,
                    max_videos=max_videos, 
                    max_comments=max_comments
                )
                
                if 'error' in channel_data:
                    return {'error': channel_data['error']}
            else:
                logger.info("Using mock data - tools not available")
                channel_data = self._get_mock_data(channel_id)
            
            # Create agents and tasks
            agents = self.create_agents()
            tasks = self.create_tasks(agents, channel_data)
            
            # Run CrewAI analysis if AI is enabled
            crew_result = None
            if self.ai_enabled and len(agents) > 0:
                logger.info("Starting CrewAI analysis...")
                crew = Crew(
                    agents=agents,
                    tasks=tasks,
                    process=Process.sequential,
                    verbose=True
                )
                
                crew_result = crew.kickoff()
                logger.info("CrewAI analysis completed")
            
            # Format results for UI
            results = self._format_results(channel_data, crew_result)
            
            return results
            
        except Exception as e:
            logger.error(f"Analysis failed: {str(e)}")
            return {'error': str(e)}
    
    def _format_results(self, channel_data, crew_result):
        """Format analysis results for the UI"""
        
        # Extract and deeply analyze channel data
        channel_info = channel_data.get('channel', {})
        videos = channel_data.get('videos', [])
        comments_data = channel_data.get('comments', {})
        
        # DEEP CHANNEL PERFORMANCE ANALYSIS
        channel_analysis = self._analyze_channel_performance(channel_info, videos)
        
        # DEEP COMMENT ANALYSIS 
        comment_analysis = self._analyze_comments_deeply(comments_data, videos)
        
        # CONTENT GAP ANALYSIS from real data
        content_gaps = self._identify_real_content_gaps(comments_data, videos)
        
        # VIDEO PERFORMANCE BREAKDOWN
        video_performance = self._analyze_video_performance(videos)
        
        # AI PRODUCTION FEASIBILITY ASSESSMENT
        ai_feasibility = self._assess_ai_production_feasibility(videos, channel_info)
        
        results = {
            'channel_metrics': channel_analysis['metrics'],
            'channel_performance': channel_analysis['performance'],
            'comment_analysis': comment_analysis,
            'content_gaps': content_gaps,
            'video_performance': video_performance,
            'ai_feasibility': ai_feasibility,
            'market_intelligence': content_gaps,
            'content_strategy': video_performance,
            'audience_intelligence': comment_analysis,
            'production_score': ai_feasibility['score'],
            'production_analysis': ai_feasibility,
            'production_launch': {
                'strategy_points': content_gaps['launch_strategy']
            },
            'competition_level': channel_analysis['competition_level'],
            'video_ideas_count': len(content_gaps.get('opportunities', [])),
            'roi_rating': ai_feasibility['roi_rating'],
            'time_to_market': ai_feasibility['time_to_market'],
            'metadata': {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'analysis_type': 'Professional Market Research Report',
                'ai_enabled': self.ai_enabled,
                'tools_available': self.tools_available,
                'total_videos_analyzed': len(videos),
                'total_comments_analyzed': sum(len(comments) for comments in comments_data.values()),
                'raw_result': str(crew_result) if crew_result else None
            }
        }
        
        return results
    
    def _analyze_channel_performance(self, channel_info, videos):
        """Deep channel performance analysis"""
        stats = channel_info.get('statistics', {})
        subscriber_count = int(stats.get('subscriberCount', 0))
        
        # Calculate comprehensive metrics
        total_views = sum(int(v.get('statistics', {}).get('viewCount', 0)) for v in videos)
        total_likes = sum(int(v.get('statistics', {}).get('likeCount', 0)) for v in videos)
        total_comments = sum(int(v.get('statistics', {}).get('commentCount', 0)) for v in videos)
        
        avg_views = total_views // len(videos) if videos else 0
        avg_likes = total_likes // len(videos) if videos else 0
        avg_comments = total_comments // len(videos) if videos else 0
        
        engagement_rate = (total_likes / total_views * 100) if total_views > 0 else 0
        views_per_subscriber = total_views / subscriber_count if subscriber_count > 0 else 0
        
        # Performance categorization
        if views_per_subscriber > 10:
            performance_tier = "Exceptional"
        elif views_per_subscriber > 5:
            performance_tier = "Strong"
        elif views_per_subscriber > 2:
            performance_tier = "Average"
        else:
            performance_tier = "Below Average"
            
        # Competition level assessment
        if subscriber_count > 1000000 and engagement_rate > 5:
            competition_level = "High (Established Market Leader)"
        elif subscriber_count > 100000 and engagement_rate > 3:
            competition_level = "Medium-High (Strong Competitor)"
        elif subscriber_count > 10000:
            competition_level = "Medium (Growing Channel)"
        else:
            competition_level = "Low (Emerging Channel)"
        
        return {
            'metrics': {
                'subscribers': f"{subscriber_count:,}",
                'videos': len(videos),
                'avg_views': f"{avg_views:,}",
                'engagement_rate': f"{engagement_rate:.1f}%",
                'views_per_subscriber': f"{views_per_subscriber:.1f}",
                'total_views': f"{total_views:,}",
                'avg_likes': f"{avg_likes:,}",
                'avg_comments': f"{avg_comments:,}"
            },
            'performance': {
                'tier': performance_tier,
                'views_per_sub_ratio': views_per_subscriber,
                'engagement_strength': 'High' if engagement_rate > 5 else 'Medium' if engagement_rate > 2 else 'Low',
                'content_consistency': 'Analyzing upload patterns...'
            },
            'competition_level': competition_level
        }
    
    def _analyze_comments_deeply(self, comments_data, videos):
        """Deep comment analysis with sentiment and insights"""
        all_comments = []
        for video_id, comments in comments_data.items():
            all_comments.extend(comments)
        
        if not all_comments:
            return {
                'sentiment': {'positive': 0, 'negative': 0, 'neutral': 100},
                'common_themes': ['No comments available for analysis'],
                'content_requests': ['No specific requests found'],
                'questions': ['No questions identified'],
                'positive_feedback': ['No positive feedback available'],
                'negative_feedback': ['No negative feedback available']
            }
        
        # Basic sentiment analysis (simplified)
        positive_keywords = ['great', 'awesome', 'love', 'amazing', 'excellent', 'perfect', 'fantastic', 'helpful', 'thank you', 'best']
        negative_keywords = ['bad', 'terrible', 'hate', 'worst', 'boring', 'sucks', 'disappointed', 'confused', 'unclear']
        request_keywords = ['please', 'can you', 'could you', 'make a video', 'do a tutorial', 'i wish', 'would love', 'next video']
        question_keywords = ['?', 'how', 'what', 'when', 'where', 'why', 'which']
        
        positive_comments = []
        negative_comments = []
        request_comments = []
        question_comments = []
        
        for comment in all_comments:
            if isinstance(comment, dict):
                text = comment.get('text', '').lower()
            else:
                text = str(comment).lower()
            
            # Sentiment classification
            if any(keyword in text for keyword in positive_keywords):
                positive_comments.append(text[:100] + "..." if len(text) > 100 else text)
            elif any(keyword in text for keyword in negative_keywords):
                negative_comments.append(text[:100] + "..." if len(text) > 100 else text)
            
            # Content requests
            if any(keyword in text for keyword in request_keywords):
                request_comments.append(text[:150] + "..." if len(text) > 150 else text)
            
            # Questions
            if any(keyword in text for keyword in question_keywords):
                question_comments.append(text[:150] + "..." if len(text) > 150 else text)
        
        total_comments = len(all_comments)
        positive_percent = (len(positive_comments) / total_comments * 100) if total_comments > 0 else 0
        negative_percent = (len(negative_comments) / total_comments * 100) if total_comments > 0 else 0
        neutral_percent = 100 - positive_percent - negative_percent
        
        return {
            'total_comments_analyzed': total_comments,
            'sentiment': {
                'positive': round(positive_percent, 1),
                'negative': round(negative_percent, 1), 
                'neutral': round(neutral_percent, 1)
            },
            'content_requests': request_comments[:10],  # Top 10 requests
            'questions': question_comments[:15],  # Top 15 questions
            'positive_feedback': positive_comments[:8],  # Top 8 positive
            'negative_feedback': negative_comments[:8],  # Top 8 negative
            'engagement_quality': 'High' if total_comments / len(videos) > 50 else 'Medium' if total_comments / len(videos) > 20 else 'Low'
        }
    
    def _identify_real_content_gaps(self, comments_data, videos):
        """Identify content gaps from actual comment analysis"""
        all_comments = []
        for video_id, comments in comments_data.items():
            all_comments.extend(comments)
        
        # Extract content gap indicators from comments
        gap_indicators = []
        tutorial_requests = []
        topic_requests = []
        
        for comment in all_comments:
            if isinstance(comment, dict):
                text = comment.get('text', '').lower()
            else:
                text = str(comment).lower()
            
            # Content gap patterns
            if 'tutorial on' in text or 'how to' in text:
                tutorial_requests.append(text[:100])
            elif 'video about' in text or 'make one about' in text:
                topic_requests.append(text[:100])
            elif 'i wish' in text or 'would love to see' in text:
                gap_indicators.append(text[:100])
        
        return {
            'opportunities': gap_indicators + tutorial_requests + topic_requests,
            'tutorial_gaps': tutorial_requests[:10],
            'topic_gaps': topic_requests[:10],
            'audience_requests': gap_indicators[:10],
            'launch_strategy': [
                "Focus on highly requested tutorial topics",
                "Address frequently asked questions in comments",
                "Create beginner-friendly versions of popular content",
                "Develop series based on audience requests",
                "Implement Q&A format videos"
            ]
        }
    
    def _analyze_video_performance(self, videos):
        """Analyze video performance patterns"""
        if not videos:
            return {'viral_patterns': ['No video data available']}
        
        # Sort videos by view count
        sorted_videos = sorted(videos, key=lambda x: int(x.get('statistics', {}).get('viewCount', 0)), reverse=True)
        
        top_performers = sorted_videos[:5]
        bottom_performers = sorted_videos[-3:]
        
        # Analyze top performers for patterns
        viral_patterns = []
        for video in top_performers:
            title = video.get('title', '')
            views = int(video.get('statistics', {}).get('viewCount', 0))
            likes = int(video.get('statistics', {}).get('likeCount', 0))
            
            viral_patterns.append(f"'{title[:50]}...' - {views:,} views, {likes:,} likes")
        
        return {
            'viral_patterns': viral_patterns,
            'top_performers': [
                {
                    'title': v.get('title', '')[:60],
                    'views': f"{int(v.get('statistics', {}).get('viewCount', 0)):,}",
                    'likes': f"{int(v.get('statistics', {}).get('likeCount', 0)):,}",
                    'engagement': f"{(int(v.get('statistics', {}).get('likeCount', 0)) / max(int(v.get('statistics', {}).get('viewCount', 1)), 1) * 100):.2f}%"
                } for v in top_performers
            ],
            'performance_insights': [
                f"Top video has {int(top_performers[0].get('statistics', {}).get('viewCount', 0)):,} views",
                f"Average top 5 performance: {sum(int(v.get('statistics', {}).get('viewCount', 0)) for v in top_performers) // 5:,} views",
                f"Performance range: {int(top_performers[0].get('statistics', {}).get('viewCount', 0)) // int(bottom_performers[0].get('statistics', {}).get('viewCount', 1)):.1f}x difference between best and worst"
            ]
        }
    
    def _assess_ai_production_feasibility(self, videos, channel_info):
        """Assess AI production feasibility with detailed breakdown"""
        
        # Analyze content types for AI feasibility
        tutorial_count = sum(1 for v in videos if 'tutorial' in v.get('title', '').lower() or 'how to' in v.get('title', '').lower())
        educational_count = sum(1 for v in videos if any(word in v.get('title', '').lower() for word in ['explain', 'guide', 'learn', 'tips']))
        
        tutorial_ratio = tutorial_count / len(videos) if videos else 0
        educational_ratio = educational_count / len(videos) if videos else 0
        
        # AI feasibility scoring
        base_score = 70
        if tutorial_ratio > 0.5:
            base_score += 15  # High tutorial content
        if educational_ratio > 0.3:
            base_score += 10  # Educational focus
        
        feasibility_score = min(base_score, 95)
        
        return {
            'score': feasibility_score,
            'content_analysis': {
                'tutorial_percentage': f"{tutorial_ratio * 100:.1f}%",
                'educational_percentage': f"{educational_ratio * 100:.1f}%",
                'ai_friendly_content': f"{(tutorial_ratio + educational_ratio) * 100:.1f}%"
            },
            'automation_breakdown': {
                'script_generation': 'High' if educational_ratio > 0.4 else 'Medium',
                'voice_synthesis': 'High' if tutorial_ratio > 0.3 else 'Medium',
                'video_editing': 'Medium' if tutorial_ratio > 0.5 else 'Low',
                'thumbnail_creation': 'High'
            },
            'roi_rating': 'High' if feasibility_score > 85 else 'Medium' if feasibility_score > 70 else 'Low',
            'time_to_market': '2-3wks' if feasibility_score > 85 else '4-6wks' if feasibility_score > 70 else '8-12wks',
            'feasibility': f'Score: {feasibility_score}/100 - {"High" if feasibility_score > 85 else "Medium" if feasibility_score > 70 else "Low"} automation potential'
        }

    def _get_mock_data(self, channel_id):
        """Generate mock data when tools aren't available"""
        return {
            'channel': {
                'id': channel_id,
                'title': f'Mock Channel {channel_id}',
                'description': 'Mock channel for testing',
                'statistics': {'subscriberCount': '100000'}
            },
            'videos': [
                {
                    'id': f'video_{i}',
                    'snippet': {'title': f'Mock Video {i}', 'publishedAt': '2024-01-01'},
                    'statistics': {'viewCount': str(1000 * i), 'likeCount': str(10 * i)}
                } for i in range(1, 6)
            ],
            'comments': {
                'video_1': ['Great video!', 'Very helpful'],
                'video_2': ['Love this content', 'More please!']
            }
        }

# Initialize CrewAI instance
crew_ai = None

def get_crewai():
    """Get or create CrewAI instance"""
    global crew_ai
    if crew_ai is None:
        crew_ai = MarketResearchCrewAI()
    return crew_ai

# Routes
@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve web interface"""
    return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YoutubePulse - AI Market Research</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #0a0b14;
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 0 24px;
        }
        
        header {
            background: rgba(15, 16, 23, 0.95);
            backdrop-filter: blur(12px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            padding: 1.25rem 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 16px;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ff1744, #d50000);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 4px 16px rgba(255, 23, 68, 0.3);
        }
        
        .logo-text {
            font-size: 1.75rem;
            font-weight: 800;
            background: linear-gradient(135deg, #ff1744, #ff6b6b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .nav-menu {
            display: flex;
            gap: 0.75rem;
        }
        
        .nav-item {
            padding: 0.75rem 1.25rem;
            background: rgba(255, 255, 255, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 12px;
            color: #e2e8f0;
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .nav-item:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: rgba(255, 23, 68, 0.5);
            transform: translateY(-2px);
        }
        
        .main-content {
            padding: 2.5rem 0;
        }
        
        /* Hero Section - Full Width Gradient */
        .hero-section {
            background: linear-gradient(135deg, rgba(255, 23, 68, 0.15), rgba(213, 0, 0, 0.1));
            border: 1px solid rgba(255, 23, 68, 0.2);
            border-radius: 20px;
            padding: 3rem;
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 30%, rgba(255, 23, 68, 0.05) 70%);
            pointer-events: none;
        }
        
        .hero-title {
            font-size: 2.5rem;
            font-weight: 900;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #ffffff, #cbd5e1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
            z-index: 1;
        }
        
        .hero-subtitle {
            font-size: 1.125rem;
            color: #94a3b8;
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }
        
        .search-form {
            display: grid;
            grid-template-columns: 2fr 1fr 1fr auto;
            gap: 1rem;
            align-items: end;
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 0.5rem;
            color: #cbd5e1;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .form-group input {
            padding: 1rem;
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 12px;
            color: #ffffff;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #ff1744;
            box-shadow: 0 0 0 3px rgba(255, 23, 68, 0.2);
            background: rgba(255, 255, 255, 0.12);
        }
        
        .btn {
            padding: 1rem 2rem;
            background: linear-gradient(135deg, #ff1744, #d50000);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 16px rgba(255, 23, 68, 0.3);
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(255, 23, 68, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        /* Tabbed Interface */
        .tab-container {
            margin-top: 2rem;
        }
        
        .tab-navigation {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            overflow-x: auto;
            padding-bottom: 1rem;
        }
        
        .tab-button {
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 12px 12px 0 0;
            color: #94a3b8;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            min-width: 140px;
            justify-content: center;
        }
        
        .tab-button:hover {
            background: rgba(255, 255, 255, 0.12);
            color: #e2e8f0;
        }
        
        .tab-button.active {
            background: linear-gradient(135deg, #ff1744, #d50000);
            border-color: #ff1744;
            color: #ffffff;
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(255, 23, 68, 0.3);
        }
        
        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }
        
        .tab-content.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .tab-icon {
            width: 16px;
            height: 16px;
            fill: currentColor;
        }
        
        /* Full-width content within tabs */
        .analysis-section {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .section-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .section-icon {
            width: 32px;
            height: 32px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            color: currentColor;
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ffffff;
        }
        
        .section-subtitle {
            font-size: 1rem;
            color: #94a3b8;
            margin-top: 0.25rem;
        }
        
        /* Flexible content grids within sections */
        .content-grid {
            display: grid;
            gap: 1.5rem;
            margin-top: 1.5rem;
        }
        
        .content-grid.two-column {
            grid-template-columns: 1fr 1fr;
        }
        
        .content-grid.three-column {
            grid-template-columns: 1fr 1fr 1fr;
        }
        
        .content-grid.four-column {
            grid-template-columns: repeat(4, 1fr);
        }
        
        .content-card {
            background: rgba(255, 255, 255, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 16px;
            padding: 1.5rem;
        }
        
        .content-card h4 {
            color: #ffffff;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        /* Bento Grid Layout (now used within tabs) */
        .bento-grid {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 1.5rem;
            margin-top: 2rem;
        }
        
        /* Card Sizes */
        .bento-card {
            border-radius: 20px;
            padding: 2rem;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .bento-card:hover {
            transform: translateY(-4px);
        }
        
        .bento-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 40%, rgba(255, 255, 255, 0.05) 100%);
            pointer-events: none;
        }
        
        /* Full Width Cards */
        .bento-full {
            grid-column: span 12;
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(16, 185, 129, 0.1));
            border: 1px solid rgba(34, 197, 94, 0.2);
        }
        
        /* Large Cards (8 columns) */
        .bento-large {
            grid-column: span 8;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(37, 99, 235, 0.1));
            border: 1px solid rgba(59, 130, 246, 0.2);
        }
        
        /* Medium Cards (4 columns) */
        .bento-medium {
            grid-column: span 4;
            background: linear-gradient(135deg, rgba(168, 85, 247, 0.15), rgba(147, 51, 234, 0.1));
            border: 1px solid rgba(168, 85, 247, 0.2);
        }
        
        /* Small Cards (6 columns) */
        .bento-small {
            grid-column: span 6;
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.15), rgba(217, 119, 6, 0.1));
            border: 1px solid rgba(245, 158, 11, 0.2);
        }
        
        /* Compact Cards (3 columns) */
        .bento-compact {
            grid-column: span 3;
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(220, 38, 38, 0.1));
            border: 1px solid rgba(239, 68, 68, 0.2);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 1;
        }
        
        .card-icon {
            width: 32px;
            height: 32px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            color: currentColor;
        }
        
        .card-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: #ffffff;
        }
        
        .card-subtitle {
            font-size: 0.875rem;
            color: #94a3b8;
            margin-top: 0.25rem;
        }
        
        .card-content {
            color: #e2e8f0;
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .metric-item {
            text-align: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.06);
            border-radius: 12px;
        }
        
        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #ffffff;
            display: block;
        }
        
        .metric-label {
            font-size: 0.75rem;
            color: #94a3b8;
            margin-top: 0.25rem;
        }
        
        .content-list {
            list-style: none;
            padding: 0;
        }
        
        .content-list li {
            padding: 0.75rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            color: #cbd5e1;
        }
        
        .content-list li:last-child {
            border-bottom: none;
        }
        
        .loading {
            text-align: center;
            padding: 3rem;
            color: #cbd5e1;
        }
        
        .spinner {
            display: inline-block;
            width: 24px;
            height: 24px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #ff1744;
            animation: spin 1s linear infinite;
            margin-right: 0.75rem;
        }
        
        @keyframes spin {
            to {
                transform: rotate(360deg);
            }
        }
        
        .error {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.15), rgba(220, 38, 38, 0.1));
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #fca5a5;
            padding: 2rem;
            border-radius: 16px;
            margin: 2rem 0;
            text-align: center;
        }
        
        .success {
            background: linear-gradient(135deg, rgba(34, 197, 94, 0.15), rgba(16, 185, 129, 0.1));
            border: 1px solid rgba(34, 197, 94, 0.3);
            color: #86efac;
            padding: 2rem;
            border-radius: 16px;
            margin: 2rem 0;
            text-align: center;
        }
        
        @media (max-width: 1200px) {
            .bento-large {
                grid-column: span 12;
            }
            .bento-medium {
                grid-column: span 6;
            }
        }
        
        @media (max-width: 768px) {
            .search-form {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
            
            .bento-card {
                grid-column: span 12 !important;
            }
            
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-section {
                padding: 2rem;
            }
            
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-menu {
                flex-wrap: wrap;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                        </svg>
                    </div>
                    <div class="logo-text">YoutubePulse</div>
                </div>
                <nav class="nav-menu">
                    <a href="#" class="nav-item">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                        </svg>
                        Video Analyzer
                    </a>
                    <a href="#" class="nav-item">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/>
                        </svg>
                        Script Analyzer
                    </a>
                    <a href="#" class="nav-item">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16 4l4 4-4 4-1.41-1.41L16.17 9H4V7h12.17l-1.58-1.59L16 4z"/>
                        </svg>
                        Channel Tracking
                    </a>
                    <a href="#" class="nav-item">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H19V1h-2v1H7V1H5v1H3.5C2.67 2 2 2.67 2 3.5v15C2 19.33 2.67 20 3.5 20h15c.83 0 1.5-.67 1.5-1.5v-15C20 2.67 19.33 2 18.5 2z"/>
                        </svg>
                        Niche Tracking
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <div class="container">
        <main class="main-content">
            <section class="hero-section">
                <h1 class="hero-title">AI-Powered YouTube Market Intelligence</h1>
                <p class="hero-subtitle">Discover content gaps, analyze competitors, and find profitable niches for AI-generated channels</p>
                
                <form class="search-form" onsubmit="startAnalysis(event)">
                    <div class="form-group">
                        <label for="channel_id">YouTube Channel ID or Handle</label>
                        <input type="text" id="channel_id" name="channel_id" placeholder="@mrbeast or UCX6OQ3DkcsbYNE6H8uQQuVA" required>
                    </div>
                    <div class="form-group">
                        <label for="max_videos">Max Videos</label>
                        <input type="number" id="max_videos" name="max_videos" value="20" min="1" max="100">
                    </div>
                    <div class="form-group">
                        <label for="max_comments">Max Comments</label>
                        <input type="number" id="max_comments" name="max_comments" value="50" min="1" max="200">
                    </div>
                    <button type="submit" class="btn">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                        </svg>
                        Analyze Channel
                    </button>
                </form>
            </section>

            <div id="results-area"></div>
        </main>
    </div>

    <script>
        let currentAnalysisId = null;

        async function startAnalysis(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const data = {
                channel_id: formData.get('channel_id'),
                max_videos: parseInt(formData.get('max_videos')),
                max_comments: parseInt(formData.get('max_comments'))
            };

            const resultsArea = document.getElementById('results-area');
            resultsArea.innerHTML = `
                <div class="loading">
                    <div class="spinner"></div>
                    Starting comprehensive market analysis for ${data.channel_id}...
                </div>
            `;

            try {
                const response = await fetch('/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (result.status === 'started') {
                    currentAnalysisId = result.analysis_id;
                    pollForResults(result.analysis_id);
                } else {
                    throw new Error(result.message || 'Failed to start analysis');
                }
            } catch (error) {
                resultsArea.innerHTML = `
                    <div class="error">
                        <h3>Analysis Failed</h3>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function pollForResults(analysisId) {
            const resultsArea = document.getElementById('results-area');
            
            const pollInterval = setInterval(async () => {
                try {
                    const response = await fetch(`/results/${analysisId}`);
                    const result = await response.json();
                    
                    if (result.status === 'completed') {
                        clearInterval(pollInterval);
                        displayResults(result.data);
                    } else if (result.status === 'failed') {
                        clearInterval(pollInterval);
                        resultsArea.innerHTML = `
                            <div class="error">
                                <h3>Analysis Failed</h3>
                                <p>${result.message || 'Unknown error occurred'}</p>
                            </div>
                        `;
                    } else {
                        resultsArea.innerHTML = `
                            <div class="loading">
                                <div class="spinner"></div>
                                Running 6-agent market research analysis... This may take 2-3 minutes.
                            </div>
                        `;
                    }
                } catch (error) {
                    clearInterval(pollInterval);
                    resultsArea.innerHTML = `
                        <div class="error">
                            <h3>Connection Error</h3>
                            <p>${error.message}</p>
                        </div>
                    `;
                }
            }, 3000);
        }

        function displayResults(data) {
            const resultsArea = document.getElementById('results-area');
            
            resultsArea.innerHTML = `
                <div class="success">
                    <h3>✅ Professional Market Analysis Complete</h3>
                    <p>Analyzed ${data.metadata?.total_videos_analyzed || 0} videos and ${data.metadata?.total_comments_analyzed || 0} comments</p>
                </div>
                
                <div class="bento-grid">
                    <!-- Channel Performance Overview - Full Width -->
                    <div class="bento-card bento-full">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <div>
                                <div class="card-title">Channel Performance Analysis</div>
                                <div class="card-subtitle">Comprehensive performance metrics and competitive positioning</div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div class="metric-grid">
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_metrics?.subscribers || 'N/A'}</span>
                                    <span class="metric-label">Subscribers</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_metrics?.total_views || 'N/A'}</span>
                                    <span class="metric-label">Total Views</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_metrics?.avg_views || 'N/A'}</span>
                                    <span class="metric-label">Avg Views</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_metrics?.engagement_rate || 'N/A'}</span>
                                    <span class="metric-label">Engagement Rate</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_metrics?.views_per_subscriber || 'N/A'}</span>
                                    <span class="metric-label">Views per Subscriber</span>
                                </div>
                                <div class="metric-item">
                                    <span class="metric-value">${data.channel_performance?.tier || 'N/A'}</span>
                                    <span class="metric-label">Performance Tier</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Deep Comment Analysis - Large -->
                    <div class="bento-card bento-large">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 12H7v-2h6v2zm3-4H7V8h9v2z"/>
                                </svg>
                            </div>
                            <div>
                                <div class="card-title">Comment Analysis Deep Dive</div>
                                <div class="card-subtitle">${data.comment_analysis?.total_comments_analyzed || 0} comments analyzed for sentiment and insights</div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div style="margin-bottom: 1.5rem;">
                                <h4 style="color: #ffffff; margin-bottom: 0.5rem;">Sentiment Breakdown</h4>
                                <div style="display: flex; gap: 1rem; margin-bottom: 1rem;">
                                    <div style="padding: 0.5rem; background: rgba(34, 197, 94, 0.2); border-radius: 8px; text-align: center; flex: 1;">
                                        <div style="font-size: 1.2rem; font-weight: bold; color: #22c55e;">${data.comment_analysis?.sentiment?.positive || 0}%</div>
                                        <div style="font-size: 0.8rem; color: #94a3b8;">Positive</div>
                                    </div>
                                    <div style="padding: 0.5rem; background: rgba(239, 68, 68, 0.2); border-radius: 8px; text-align: center; flex: 1;">
                                        <div style="font-size: 1.2rem; font-weight: bold; color: #ef4444;">${data.comment_analysis?.sentiment?.negative || 0}%</div>
                                        <div style="font-size: 0.8rem; color: #94a3b8;">Negative</div>
                                    </div>
                                    <div style="padding: 0.5rem; background: rgba(156, 163, 175, 0.2); border-radius: 8px; text-align: center; flex: 1;">
                                        <div style="font-size: 1.2rem; font-weight: bold; color: #9ca3af;">${data.comment_analysis?.sentiment?.neutral || 0}%</div>
                                        <div style="font-size: 0.8rem; color: #94a3b8;">Neutral</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div style="margin-bottom: 1.5rem;">
                                <h4 style="color: #ffffff; margin-bottom: 0.5rem;">Content Requests from Comments</h4>
                                <div style="max-height: 150px; overflow-y: auto; background: rgba(0,0,0,0.3); padding: 1rem; border-radius: 8px;">
                                    ${(data.comment_analysis?.content_requests || ['No content requests found']).map(req => `
                                        <div style="padding: 0.5rem 0; border-bottom: 1px solid rgba(255,255,255,0.1); font-size: 0.9rem; color: #cbd5e1;">
                                            "${req}"
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                            
                            <div>
                                <h4 style="color: #ffffff; margin-bottom: 0.5rem;">Most Asked Questions</h4>
                                <div style="max-height: 120px; overflow-y: auto; background: rgba(0,0,0,0.3); padding: 1rem; border-radius: 8px;">
                                    ${(data.comment_analysis?.questions || ['No questions found']).slice(0, 8).map(q => `
                                        <div style="padding: 0.3rem 0; font-size: 0.85rem; color: #e2e8f0;">• ${q}</div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- AI Production Feasibility - Medium -->
                    <div class="bento-card bento-medium">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
                                </svg>
                            </div>
                            <div>
                                <div class="card-title">AI Production Feasibility</div>
                                <div class="card-subtitle">Detailed automation potential analysis</div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div style="text-align: center; margin-bottom: 1.5rem;">
                                <div style="font-size: 3rem; font-weight: 900; color: #22c55e;">
                                    ${data.ai_feasibility?.score || '85'}/100
                                </div>
                                <p style="margin-top: 0.5rem; color: #94a3b8; font-size: 0.9rem;">
                                    ${data.ai_feasibility?.feasibility || 'High automation potential'}
                                </p>
                            </div>
                            
                            <div style="margin-bottom: 1rem;">
                                <h4 style="color: #ffffff; margin-bottom: 0.5rem; font-size: 0.9rem;">Content Analysis</h4>
                                <div style="font-size: 0.8rem; color: #cbd5e1;">
                                    <div>Tutorial Content: ${data.ai_feasibility?.content_analysis?.tutorial_percentage || '0%'}</div>
                                    <div>Educational Content: ${data.ai_feasibility?.content_analysis?.educational_percentage || '0%'}</div>
                                    <div>AI-Friendly: ${data.ai_feasibility?.content_analysis?.ai_friendly_content || '0%'}</div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 style="color: #ffffff; margin-bottom: 0.5rem; font-size: 0.9rem;">Automation Breakdown</h4>
                                <div style="font-size: 0.8rem; color: #cbd5e1;">
                                    <div>Script Generation: <span style="color: #22c55e;">${data.ai_feasibility?.automation_breakdown?.script_generation || 'Medium'}</span></div>
                                    <div>Voice Synthesis: <span style="color: #22c55e;">${data.ai_feasibility?.automation_breakdown?.voice_synthesis || 'Medium'}</span></div>
                                    <div>Video Editing: <span style="color: #f59e0b;">${data.ai_feasibility?.automation_breakdown?.video_editing || 'Low'}</span></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Top Performing Videos - Small -->
                    <div class="bento-card bento-small">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M18 3v2h-2V3H8v2H6V3H4v18h2v-2h2v2h8v-2h2v2h2V3h-2z"/>
                                </svg>
                            </div>
                            <div>
                                <div class="card-title">Top Performing Videos</div>
                                <div class="card-subtitle">Best content analysis</div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div style="max-height: 300px; overflow-y: auto;">
                                ${(data.video_performance?.top_performers || []).map(video => `
                                    <div style="padding: 0.75rem 0; border-bottom: 1px solid rgba(255,255,255,0.1);">
                                        <div style="font-size: 0.85rem; font-weight: 600; color: #ffffff; margin-bottom: 0.25rem;">
                                            ${video.title}
                                        </div>
                                        <div style="font-size: 0.75rem; color: #94a3b8;">
                                            ${video.views} views • ${video.likes} likes • ${video.engagement} engagement
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Content Gaps from Real Data - Small -->
                    <div class="bento-card bento-small">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                </svg>
                            </div>
                            <div>
                                <div class="card-title">Market Opportunities</div>
                                <div class="card-subtitle">From audience feedback analysis</div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div style="margin-bottom: 1rem;">
                                <h4 style="color: #ffffff; margin-bottom: 0.5rem; font-size: 0.9rem;">Tutorial Requests</h4>
                                <div style="max-height: 100px; overflow-y: auto; font-size: 0.8rem; color: #cbd5e1;">
                                    ${(data.content_gaps?.tutorial_gaps || ['No tutorial requests found']).slice(0, 5).map(gap => `
                                        <div style="padding: 0.2rem 0;">• ${gap}</div>
                                    `).join('')}
                                </div>
                            </div>
                            
                            <div>
                                <h4 style="color: #ffffff; margin-bottom: 0.5rem; font-size: 0.9rem;">Topic Requests</h4>
                                <div style="max-height: 100px; overflow-y: auto; font-size: 0.8rem; color: #cbd5e1;">
                                    ${(data.content_gaps?.topic_gaps || ['No topic requests found']).slice(0, 5).map(gap => `
                                        <div style="padding: 0.2rem 0;">• ${gap}</div>
                                    `).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Positive Feedback Examples - Medium -->
                    <div class="bento-card bento-medium">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
                                </svg>
                            </div>
                            <div>
                                <div class="card-title">Positive Audience Feedback</div>
                                <div class="card-subtitle">What viewers love about this content</div>
                            </div>
                        </div>
                        <div class="card-content">
                            <div style="max-height: 250px; overflow-y: auto; background: rgba(34, 197, 94, 0.1); padding: 1rem; border-radius: 8px;">
                                ${(data.comment_analysis?.positive_feedback || ['No positive feedback available']).map(feedback => `
                                    <div style="padding: 0.5rem 0; border-bottom: 1px solid rgba(34, 197, 94, 0.2); font-size: 0.85rem; color: #86efac;">
                                        "${feedback}"
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                    
                    <!-- Launch Strategy - Medium -->
                    <div class="bento-card bento-medium">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <div>
                                <div class="card-title">Launch Strategy Blueprint</div>
                                <div class="card-subtitle">Data-driven go-to-market plan</div>
                            </div>
                        </div>
                        <div class="card-content">
                            <ul class="content-list">
                                ${(data.content_gaps?.launch_strategy || ['No strategy available']).map(point => `<li>• ${point}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Quick Stats Grid - 4 Compact Cards -->
                    <div class="bento-card bento-compact">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/>
                                </svg>
                            </div>
                            <div class="card-title">Competition</div>
                        </div>
                        <div class="card-content" style="text-align: center;">
                            <div class="metric-value" style="font-size: 1.5rem;">${(data.competition_level || 'Medium').split('(')[0]}</div>
                            <div class="metric-label">Market Level</div>
                        </div>
                    </div>
                    
                    <div class="bento-card bento-compact">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                            <div class="card-title">Opportunities</div>
                        </div>
                        <div class="card-content" style="text-align: center;">
                            <div class="metric-value" style="font-size: 1.5rem;">${data.video_ideas_count || '0'}</div>
                            <div class="metric-label">Content Ideas</div>
                        </div>
                    </div>
                    
                    <div class="bento-card bento-compact">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M9 11H7v6h2v-6zm4 0h-2v6h2v-6zm4 0h-2v6h2v-6zm2.5-9H19V1h-2v1H7V1H5v1H3.5C2.67 2 2 2.67 2 3.5v15C2 19.33 2.67 20 3.5 20h15c.83 0 1.5-.67 1.5-1.5v-15C20 2.67 19.33 2 18.5 2z"/>
                                </svg>
                            </div>
                            <div class="card-title">ROI Rating</div>
                        </div>
                        <div class="card-content" style="text-align: center;">
                            <div class="metric-value" style="font-size: 1.5rem; color: #22c55e;">${data.roi_rating || 'High'}</div>
                            <div class="metric-label">Revenue Potential</div>
                        </div>
                    </div>
                    
                    <div class="bento-card bento-compact">
                        <div class="card-header">
                            <div class="card-icon">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M16 4l4 4-4 4-1.41-1.41L16.17 9H4V7h12.17l-1.58-1.59L16 4z"/>
                                </svg>
                            </div>
                            <div class="card-title">Timeline</div>
                        </div>
                        <div class="card-content" style="text-align: center;">
                            <div class="metric-value" style="font-size: 1.5rem;">${data.time_to_market || '2-4wks'}</div>
                            <div class="metric-label">Time to Market</div>
                        </div>
                    </div>
                </div>
            `;
        }
    </script>
</body>
</html>
"""

@app.post("/analyze")
async def analyze_channel(request: AnalysisRequest, background_tasks: BackgroundTasks):
    """Start channel analysis"""
    try:
        analysis_id = str(uuid.uuid4())
        
        # Store initial status
        analysis_store[analysis_id] = {
            'status': 'running',
            'data': None,
            'message': 'Analysis in progress...',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        # Start background analysis
        background_tasks.add_task(
            run_background_analysis, 
            analysis_id, 
            request.channel_id,
            request.max_videos,
            request.max_comments
        )
        
        return AnalysisResponse(
            analysis_id=analysis_id,
            status="started",
            message="Analysis started successfully"
        )
        
    except Exception as e:
        logger.error(f"Failed to start analysis: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

async def run_background_analysis(analysis_id: str, channel_id: str, max_videos: int, max_comments: int):
    """Run analysis in background"""
    try:
        crew_ai = get_crewai()
        
        logger.info(f"Starting analysis for {channel_id}")
        result = crew_ai.run_analysis(channel_id, max_videos, max_comments)
        
        if 'error' in result:
            analysis_store[analysis_id] = {
                'status': 'failed',
                'data': None,
                'message': result['error'],
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
        else:
            analysis_store[analysis_id] = {
                'status': 'completed',
                'data': result,
                'message': 'Analysis completed successfully',
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        logger.info(f"Analysis {analysis_id} completed")
        
    except Exception as e:
        logger.error(f"Analysis {analysis_id} failed: {str(e)}")
        analysis_store[analysis_id] = {
            'status': 'failed',
            'data': None,
            'message': str(e),
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

@app.get("/results/{analysis_id}")
async def get_results(analysis_id: str):
    """Get analysis results"""
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    return analysis_store[analysis_id]

@app.get("/health")
async def health():
    """Health check"""
    return {"status": "healthy", "timestamp": datetime.now(timezone.utc).isoformat()}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)