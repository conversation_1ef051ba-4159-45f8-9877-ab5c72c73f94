"""
YouTube Research v2 - Supabase Integration
Complete user management, authentication, and report storage system
"""

import os
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from supabase import create_client, Client
from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import jwt
from pydantic import BaseModel
import logging

logger = logging.getLogger(__name__)

# Supabase Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_ANON_KEY = os.getenv("SUPABASE_ANON_KEY")
SUPABASE_SERVICE_KEY = os.getenv("SUPABASE_SERVICE_KEY")
JWT_SECRET = os.getenv("JWT_SECRET", "your-jwt-secret-key")

if not all([SUPABASE_URL, SUPABASE_ANON_KEY]):
    logger.warning("Supabase credentials not found. User management will be disabled.")
    SUPABASE_ENABLED = False
else:
    SUPABASE_ENABLED = True

# Initialize Supabase clients
supabase_client: Optional[Client] = None
supabase_admin: Optional[Client] = None

if SUPABASE_ENABLED:
    supabase_client = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
    if SUPABASE_SERVICE_KEY:
        supabase_admin = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)

# Security
security = HTTPBearer()

# Pydantic Models
class UserProfile(BaseModel):
    id: str
    email: str
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None
    subscription_tier: str = "free"  # free, pro
    credits_remaining: int = 10
    credits_used: int = 0
    subscription_expires: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

class AnalysisReport(BaseModel):
    id: str
    user_id: str
    report_type: str  # video_analysis, channel_analysis, market_research
    title: str
    youtube_url: str
    analysis_data: Dict[str, Any]
    status: str  # pending, completed, failed
    credits_used: int
    created_at: datetime
    updated_at: datetime

class UsageStats(BaseModel):
    user_id: str
    total_reports: int
    credits_used: int
    credits_remaining: int
    subscription_tier: str
    last_analysis: Optional[datetime] = None

# Database Schema SQL (for Supabase setup)
DATABASE_SCHEMA = """
-- Users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    email TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro')),
    credits_remaining INTEGER DEFAULT 10,
    credits_used INTEGER DEFAULT 0,
    subscription_expires TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Analysis reports table
CREATE TABLE IF NOT EXISTS analysis_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    report_type TEXT NOT NULL CHECK (report_type IN ('video_analysis', 'channel_analysis', 'market_research')),
    title TEXT NOT NULL,
    youtube_url TEXT NOT NULL,
    analysis_data JSONB NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'completed', 'failed')),
    credits_used INTEGER DEFAULT 1,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Usage tracking table
CREATE TABLE IF NOT EXISTS usage_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) NOT NULL,
    action TEXT NOT NULL,
    credits_consumed INTEGER DEFAULT 0,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_analysis_reports_user_id ON analysis_reports(user_id);
CREATE INDEX IF NOT EXISTS idx_analysis_reports_created_at ON analysis_reports(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_usage_logs_user_id ON usage_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_logs_created_at ON usage_logs(created_at DESC);

-- Row Level Security (RLS) Policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE analysis_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_logs ENABLE ROW LEVEL SECURITY;

-- Users can only see their own profile
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid() = id);

-- Users can only see their own reports
CREATE POLICY "Users can view own reports" ON analysis_reports
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own reports" ON analysis_reports
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own reports" ON analysis_reports
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can only see their own usage logs
CREATE POLICY "Users can view own usage" ON usage_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own usage" ON usage_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (id, email, full_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'avatar_url'
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create profile on user signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON user_profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_analysis_reports_updated_at
    BEFORE UPDATE ON analysis_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
"""

class SupabaseManager:
    """Manages all Supabase operations for YouTube Research v2"""
    
    def __init__(self):
        self.client = supabase_client
        self.admin = supabase_admin
        self.enabled = SUPABASE_ENABLED
    
    async def get_current_user(self, credentials: HTTPAuthorizationCredentials = Depends(security)) -> Optional[UserProfile]:
        """Get current authenticated user"""
        if not self.enabled:
            return None
            
        try:
            # Verify JWT token
            token = credentials.credentials
            payload = jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
            user_id = payload.get("sub")
            
            if not user_id:
                raise HTTPException(status_code=401, detail="Invalid token")
            
            # Get user profile from Supabase
            response = self.client.table("user_profiles").select("*").eq("id", user_id).execute()
            
            if not response.data:
                raise HTTPException(status_code=404, detail="User profile not found")
            
            user_data = response.data[0]
            return UserProfile(**user_data)
            
        except jwt.InvalidTokenError:
            raise HTTPException(status_code=401, detail="Invalid token")
        except Exception as e:
            logger.error(f"Error getting current user: {e}")
            raise HTTPException(status_code=500, detail="Authentication error")
    
    async def create_user_profile(self, user_id: str, email: str, full_name: str = None) -> UserProfile:
        """Create a new user profile"""
        if not self.enabled:
            raise HTTPException(status_code=503, detail="User management disabled")
        
        try:
            profile_data = {
                "id": user_id,
                "email": email,
                "full_name": full_name,
                "subscription_tier": "free",
                "credits_remaining": 10,
                "credits_used": 0
            }
            
            response = self.client.table("user_profiles").insert(profile_data).execute()
            
            if response.data:
                return UserProfile(**response.data[0])
            else:
                raise HTTPException(status_code=500, detail="Failed to create user profile")
                
        except Exception as e:
            logger.error(f"Error creating user profile: {e}")
            raise HTTPException(status_code=500, detail="Failed to create user profile")
    
    async def get_user_profile(self, user_id: str) -> Optional[UserProfile]:
        """Get user profile by ID"""
        if not self.enabled:
            return None
            
        try:
            response = self.client.table("user_profiles").select("*").eq("id", user_id).execute()
            
            if response.data:
                return UserProfile(**response.data[0])
            return None
            
        except Exception as e:
            logger.error(f"Error getting user profile: {e}")
            return None
    
    async def update_user_credits(self, user_id: str, credits_used: int) -> bool:
        """Update user credits after analysis"""
        if not self.enabled:
            return True
            
        try:
            # Get current profile
            profile = await self.get_user_profile(user_id)
            if not profile:
                return False
            
            new_credits_remaining = max(0, profile.credits_remaining - credits_used)
            new_credits_used = profile.credits_used + credits_used
            
            response = self.client.table("user_profiles").update({
                "credits_remaining": new_credits_remaining,
                "credits_used": new_credits_used
            }).eq("id", user_id).execute()
            
            return bool(response.data)
            
        except Exception as e:
            logger.error(f"Error updating user credits: {e}")
            return False
    
    async def save_analysis_report(self, user_id: str, report_type: str, title: str, 
                                 youtube_url: str, analysis_data: Dict[str, Any], 
                                 credits_used: int = 1) -> str:
        """Save analysis report to database"""
        if not self.enabled:
            return str(uuid.uuid4())  # Return mock ID
            
        try:
            report_data = {
                "user_id": user_id,
                "report_type": report_type,
                "title": title,
                "youtube_url": youtube_url,
                "analysis_data": analysis_data,
                "status": "completed",
                "credits_used": credits_used
            }
            
            response = self.client.table("analysis_reports").insert(report_data).execute()
            
            if response.data:
                # Update user credits
                await self.update_user_credits(user_id, credits_used)
                return response.data[0]["id"]
            else:
                raise HTTPException(status_code=500, detail="Failed to save report")
                
        except Exception as e:
            logger.error(f"Error saving analysis report: {e}")
            raise HTTPException(status_code=500, detail="Failed to save report")
    
    async def get_user_reports(self, user_id: str, limit: int = 50) -> List[AnalysisReport]:
        """Get user's analysis reports"""
        if not self.enabled:
            return []
            
        try:
            response = self.client.table("analysis_reports").select("*").eq("user_id", user_id).order("created_at", desc=True).limit(limit).execute()
            
            return [AnalysisReport(**report) for report in response.data]
            
        except Exception as e:
            logger.error(f"Error getting user reports: {e}")
            return []
    
    async def get_usage_stats(self, user_id: str) -> UsageStats:
        """Get user usage statistics"""
        if not self.enabled:
            return UsageStats(
                user_id=user_id,
                total_reports=0,
                credits_used=0,
                credits_remaining=10,
                subscription_tier="free"
            )
            
        try:
            # Get user profile
            profile = await self.get_user_profile(user_id)
            if not profile:
                raise HTTPException(status_code=404, detail="User not found")
            
            # Get report count
            reports_response = self.client.table("analysis_reports").select("id", count="exact").eq("user_id", user_id).execute()
            total_reports = reports_response.count or 0
            
            # Get last analysis
            last_report_response = self.client.table("analysis_reports").select("created_at").eq("user_id", user_id).order("created_at", desc=True).limit(1).execute()
            last_analysis = None
            if last_report_response.data:
                last_analysis = datetime.fromisoformat(last_report_response.data[0]["created_at"].replace("Z", "+00:00"))
            
            return UsageStats(
                user_id=user_id,
                total_reports=total_reports,
                credits_used=profile.credits_used,
                credits_remaining=profile.credits_remaining,
                subscription_tier=profile.subscription_tier,
                last_analysis=last_analysis
            )
            
        except Exception as e:
            logger.error(f"Error getting usage stats: {e}")
            raise HTTPException(status_code=500, detail="Failed to get usage stats")

# Global instance
supabase_manager = SupabaseManager()

# Dependency for getting current user
async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> Optional[UserProfile]:
    """FastAPI dependency to get current user"""
    return await supabase_manager.get_current_user(credentials)

# Optional dependency (doesn't require authentication)
async def get_current_user_optional(credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))) -> Optional[UserProfile]:
    """FastAPI dependency to optionally get current user"""
    if not credentials:
        return None
    return await supabase_manager.get_current_user(credentials)
