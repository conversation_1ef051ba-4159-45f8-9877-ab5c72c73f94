<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YoutubePulse - Premium Dashboard</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/static/favicon.svg">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- YouTube Research v2 Design System -->
    <link rel="stylesheet" href="/static/styles/youtube-research-v2-design-system.css?v=2025073006">
</head>
<body>
    <div class="app-layout">
        <!-- Premium Sidebar -->
        <aside class="app-sidebar" id="sidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <a href="/" class="sidebar-logo">
                    <div class="sidebar-logo-icon">YP</div>
                    <span class="sidebar-logo-text">YoutubePulse</span>
                </a>
                <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                    <i data-lucide="sun" class="theme-icon-light"></i>
                    <i data-lucide="moon" class="theme-icon-dark" style="display: none;"></i>
                </button>
            </div>

            <!-- Navigation Menu -->
            <nav class="sidebar-nav">
                <!-- Overview Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Overview</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/" class="nav-item active" data-page="dashboard">
                                <i data-lucide="layout-dashboard" class="nav-icon"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <a href="/analytics" class="nav-item" data-page="analytics">
                                <i data-lucide="bar-chart-3" class="nav-icon"></i>
                                <span>Analytics</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Analysis Tools Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Analysis Tools</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/video-analyzer" class="nav-item" data-page="video-analyzer">
                                <i data-lucide="video" class="nav-icon"></i>
                                <span>Video Analyzer</span>
                                <span class="nav-badge">6 AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/channel-analyzer" class="nav-item" data-page="channel-analyzer">
                                <i data-lucide="users" class="nav-icon"></i>
                                <span>Channel Analyzer</span>
                                <span class="nav-badge">Pro</span>
                            </a>
                        </li>
                        <li>
                            <a href="/market-research" class="nav-item" data-page="market-research">
                                <i data-lucide="trending-up" class="nav-icon"></i>
                                <span>Market Research</span>
                                <span class="nav-badge">New</span>
                            </a>
                        </li>
                        <li>
                            <a href="/agent/comment" class="nav-item" data-page="comment-intelligence">
                                <i data-lucide="message-circle" class="nav-icon"></i>
                                <span>Comment Intelligence</span>
                                <span class="nav-badge">AI</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Research Tools Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Research Tools</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/competitor-analysis" class="nav-item" data-page="competitor-analysis">
                                <i data-lucide="target" class="nav-icon"></i>
                                <span>Competitor Analysis</span>
                            </a>
                        </li>
                        <li>
                            <a href="/trend-tracker" class="nav-item" data-page="trend-tracker">
                                <i data-lucide="activity" class="nav-icon"></i>
                                <span>Trend Tracker</span>
                            </a>
                        </li>
                        <li>
                            <a href="/keyword-research" class="nav-item" data-page="keyword-research">
                                <i data-lucide="search" class="nav-icon"></i>
                                <span>Keyword Research</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Settings Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Settings</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/settings" class="nav-item" data-page="settings">
                                <i data-lucide="settings" class="nav-icon"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li>
                            <a href="/help" class="nav-item" data-page="help">
                                <i data-lucide="help-circle" class="nav-icon"></i>
                                <span>Help & Support</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="user-profile">
                    <div class="user-avatar">
                        <i data-lucide="user" class="user-icon"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-name">Premium User</div>
                        <div class="user-plan">Pro Plan</div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Page Header -->
            <header class="page-header">
                <div class="page-header-content">
                    <div class="page-title-section">
                        <h1 class="page-title">Welcome back, Ardi! 👋</h1>
                        <p class="page-subtitle">Ready to unlock YouTube insights? Your AI agents are standing by.</p>
                    </div>
                    <div class="page-actions">
                        <button class="btn btn-ghost btn-sm">
                            <i data-lucide="bell" class="sidebar-nav-icon"></i>
                        </button>
                        <button class="btn btn-ghost btn-sm">
                            <i data-lucide="settings" class="sidebar-nav-icon"></i>
                        </button>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="page-content">
<!-- Welcome Section -->
<div class="welcome-section">
    <div class="welcome-content">
        <h1 class="welcome-title">Welcome back, Ardi! 👋</h1>
        <p class="welcome-subtitle">Ready to unlock YouTube insights? Your AI agents are standing by.</p>
    </div>
    <div class="welcome-stats">
        <div class="metric-card">
            <div class="metric-value">127</div>
            <div class="metric-label">Analyses this month</div>
            <div class="metric-change positive">
                <i data-lucide="arrow-up" class="metric-icon"></i>
                24% from last month
            </div>
        </div>
        <div class="metric-card">
            <div class="metric-value">4.8</div>
            <div class="metric-label">Avg. video score</div>
            <div class="metric-change positive">
                <i data-lucide="arrow-up" class="metric-icon"></i>
                0.3 points
            </div>
        </div>
        <div class="metric-card">
            <div class="metric-value">$2,847</div>
            <div class="metric-label">Value delivered</div>
            <div class="metric-change positive">
                <i data-lucide="arrow-up" class="metric-icon"></i>
                15% increase
            </div>
        </div>
    </div>
</div>

<!-- Quick Analysis -->
<div class="quick-analysis-section">
    <div class="premium-card premium-card-gradient">
        <div class="premium-card-header">
            <div>
                <h2 class="premium-card-title">Quick Analysis</h2>
                <p class="premium-card-subtitle">Paste any YouTube video URL to start analyzing</p>
            </div>
            <i data-lucide="zap" class="premium-card-icon"></i>
        </div>
        <div class="premium-card-content">
            <div class="quick-analysis-form">
                <div class="form-group">
                    <input type="url" class="form-input" placeholder="Paste YouTube video URL here..." id="quickAnalysisUrl">
                </div>
                <button class="btn btn-primary btn-lg" onclick="startQuickAnalysis()">
                    <i data-lucide="play" class="btn-icon"></i>
                    Analyze Now
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Analysis Tools Grid -->
<div class="tools-grid">
    <div class="premium-card">
        <div class="premium-card-header">
            <div>
                <h3 class="premium-card-title">Video Analyzer</h3>
                <p class="premium-card-subtitle">6 AI agents analyze any YouTube video</p>
            </div>
            <i data-lucide="video" class="premium-card-icon"></i>
        </div>
        <div class="premium-card-content">
            <p class="premium-card-description">Get comprehensive insights on performance, script quality, SEO optimization, and psychological triggers.</p>
            <a href="/video-analyzer" class="btn btn-primary">
                <i data-lucide="play" class="btn-icon"></i>
                Start Analysis
            </a>
        </div>
    </div>

    <div class="premium-card">
        <div class="premium-card-header">
            <div>
                <h3 class="premium-card-title">Channel Analyzer</h3>
                <p class="premium-card-subtitle">Deep dive into any YouTube channel</p>
            </div>
            <i data-lucide="users" class="premium-card-icon"></i>
        </div>
        <div class="premium-card-content">
            <p class="premium-card-description">Analyze channel performance, content strategy, upload patterns, and growth opportunities.</p>
            <a href="/channel-analyzer" class="btn btn-primary">
                <i data-lucide="search" class="btn-icon"></i>
                Analyze Channel
            </a>
        </div>
    </div>

    <div class="premium-card">
        <div class="premium-card-header">
            <div>
                <h3 class="premium-card-title">Market Research Hub</h3>
                <p class="premium-card-subtitle">Discover trending topics and opportunities</p>
            </div>
            <i data-lucide="trending-up" class="premium-card-icon"></i>
        </div>
        <div class="premium-card-content">
            <p class="premium-card-description">Research market trends, analyze competitors, and find content gaps in your niche.</p>
            <a href="/market-research" class="btn btn-primary">
                <i data-lucide="bar-chart-3" class="btn-icon"></i>
                Start Research
            </a>
        </div>
    </div>

    <div class="premium-card">
        <div class="premium-card-header">
            <div>
                <h3 class="premium-card-title">Comment Intelligence</h3>
                <p class="premium-card-subtitle">AI-powered comment analysis</p>
            </div>
            <i data-lucide="message-circle" class="premium-card-icon"></i>
        </div>
        <div class="premium-card-content">
            <p class="premium-card-description">Understand audience sentiment, extract insights, and identify engagement patterns.</p>
            <a href="/agent/comment" class="btn btn-primary">
                <i data-lucide="brain" class="btn-icon"></i>
                Analyze Comments
            </a>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="recent-grid">
    <div class="premium-card">
        <div class="premium-card-header">
            <h3 class="premium-card-title">Recent Analyses</h3>
            <a href="/history" class="btn btn-ghost btn-sm">View All</a>
        </div>
        <div class="premium-card-content">
            <div class="recent-list">
                <div class="recent-item">
                    <div class="recent-thumbnail">
                        <i data-lucide="video" class="recent-icon"></i>
                    </div>
                    <div class="recent-content">
                        <h4 class="recent-title">How to Build a YouTube Channel</h4>
                        <p class="recent-meta">Video Analysis • 2 hours ago</p>
                    </div>
                    <div class="recent-score">8.7</div>
                </div>
                <div class="recent-item">
                    <div class="recent-thumbnail">
                        <i data-lucide="users" class="recent-icon"></i>
                    </div>
                    <div class="recent-content">
                        <h4 class="recent-title">MrBeast Channel Analysis</h4>
                        <p class="recent-meta">Channel Analysis • 1 day ago</p>
                    </div>
                    <div class="recent-score">9.2</div>
                </div>
                <div class="recent-item">
                    <div class="recent-thumbnail">
                        <i data-lucide="trending-up" class="recent-icon"></i>
                    </div>
                    <div class="recent-content">
                        <h4 class="recent-title">Tech Review Market Research</h4>
                        <p class="recent-meta">Market Research • 3 days ago</p>
                    </div>
                    <div class="recent-score">7.9</div>
                </div>
            </div>
        </div>
    </div>

    <div class="premium-card">
        <div class="premium-card-header">
            <h3 class="premium-card-title">Usage Statistics</h3>
        </div>
        <div class="premium-card-content">
            <div class="usage-grid">
                <div class="usage-stat">
                    <div class="usage-value">127</div>
                    <div class="usage-label">Total Analyses</div>
                </div>
                <div class="usage-stat">
                    <div class="usage-value">23</div>
                    <div class="usage-label">This Week</div>
                </div>
                <div class="usage-stat">
                    <div class="usage-value">4.8</div>
                    <div class="usage-label">Avg Score</div>
                </div>
                <div class="usage-stat">
                    <div class="usage-value">89%</div>
                    <div class="usage-label">Success Rate</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Stats -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-header">
            <i data-lucide="eye" class="stat-icon"></i>
            <h4 class="stat-title">Total Views Analyzed</h4>
        </div>
        <div class="stat-value">2.4M</div>
        <div class="stat-change positive">
            <i data-lucide="arrow-up" class="stat-change-icon"></i>
            12% this month
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-header">
            <i data-lucide="thumbs-up" class="stat-icon"></i>
            <h4 class="stat-title">Engagement Rate</h4>
        </div>
        <div class="stat-value">8.7%</div>
        <div class="stat-change positive">
            <i data-lucide="arrow-up" class="stat-change-icon"></i>
            2.1% improvement
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-header">
            <i data-lucide="clock" class="stat-icon"></i>
            <h4 class="stat-title">Avg Watch Time</h4>
        </div>
        <div class="stat-value">4:32</div>
        <div class="stat-change positive">
            <i data-lucide="arrow-up" class="stat-change-icon"></i>
            0:45 increase
        </div>
    </div>

    <div class="stat-card">
        <div class="stat-header">
            <i data-lucide="target" class="stat-icon"></i>
            <h4 class="stat-title">CTR Improvement</h4>
        </div>
        <div class="stat-value">+23%</div>
        <div class="stat-change positive">
            <i data-lucide="arrow-up" class="stat-change-icon"></i>
            From optimizations
        </div>
    </div>
</div>
            </div>
        </main>
    </div>

    <!-- Base JavaScript -->
    <script>
        // Theme Toggle Functionality
        const themeToggle = document.getElementById('themeToggle');
        const html = document.documentElement;
        const lightIcon = themeToggle.querySelector('.theme-icon-light');
        const darkIcon = themeToggle.querySelector('.theme-icon-dark');

        // Load saved theme or default to dark
        const savedTheme = localStorage.getItem('theme') || 'dark';
        html.setAttribute('data-theme', savedTheme);
        updateThemeIcons(savedTheme);

        themeToggle.addEventListener('click', () => {
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcons(newTheme);
        });

        function updateThemeIcons(theme) {
            if (theme === 'dark') {
                lightIcon.style.display = 'block';
                darkIcon.style.display = 'none';
            } else {
                lightIcon.style.display = 'none';
                darkIcon.style.display = 'block';
            }
        }

        // Initialize Lucide Icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        // Navigation Active State
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');

            navItems.forEach(item => {
                const href = item.getAttribute('href');
                if (href === currentPath || (currentPath === '/' && href === '/')) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        });

        function startQuickAnalysis() {
            const url = document.getElementById('quickAnalysisUrl').value;
            if (url) {
                window.location.href = `/video-analyzer?url=${encodeURIComponent(url)}`;
            }
        }

        // Animation observer for premium cards
        document.addEventListener('DOMContentLoaded', function() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-slideIn');
                    }
                });
            }, observerOptions);

            // Observe all premium cards
            document.querySelectorAll('.premium-card').forEach(card => {
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
