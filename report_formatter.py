"""
Dynamic Report Formatter for YouTube Video Analysis
Converts raw AI agent text output into beautiful HTML reports
"""

import re
from typing import Dict, List, Any
import json


class ReportFormatter:
    """Formats raw agent analysis text into structured HTML reports"""
    
    def __init__(self):
        self.section_patterns = {
            'metrics': r'(\d+(?:\.\d+)?)\s*(%|views?|likes?|comments?)',
            'headers': r'^(?:#{1,3}|[A-Z][A-Z\s]+:)',
            'bullet_points': r'^\s*[-•*]\s+(.+)',
            'numbered_lists': r'^\s*\d+\.\s+(.+)',
            'key_value': r'^([A-Za-z\s]+):\s*(.+)$',
        }
    
    def format_agent_report(self, agent_type: str, raw_text: str) -> str:
        """Format raw agent output into structured HTML based on agent type"""
        
        # Parse the raw text into sections
        sections = self._parse_sections(raw_text)
        
        # Choose formatting based on agent type
        if agent_type == 'performance':
            return self._format_performance_report(sections, raw_text)
        elif agent_type == 'script':
            return self._format_script_report(sections, raw_text)
        elif agent_type == 'seo':
            return self._format_seo_report(sections, raw_text)
        elif agent_type == 'psychology':
            return self._format_psychology_report(sections, raw_text)
        elif agent_type == 'comments':
            return self._format_comments_report(sections, raw_text)
        else:
            return self._format_generic_report(sections, raw_text)
    
    def _parse_sections(self, text: str) -> Dict[str, List[str]]:
        """Parse text into logical sections"""
        sections = {}
        current_section = 'intro'
        current_content = []
        
        lines = text.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # Check if this is a section header
            if re.match(r'^(?:#{1,3}|[A-Z][A-Z\s]+:)', line):
                if current_content:
                    sections[current_section] = current_content
                current_section = line.replace('#', '').replace(':', '').strip().lower()
                current_content = []
            else:
                current_content.append(line)
        
        if current_content:
            sections[current_section] = current_content
            
        return sections
    
    def _extract_metrics(self, text: str) -> List[Dict[str, Any]]:
        """Extract numerical metrics from text"""
        metrics = []
        
        # Common metric patterns
        patterns = [
            (r'(\d+(?:,\d{3})*(?:\.\d+)?)\s*(views?)', 'views'),
            (r'(\d+(?:,\d{3})*(?:\.\d+)?)\s*(likes?)', 'likes'),
            (r'(\d+(?:,\d{3})*(?:\.\d+)?)\s*(comments?)', 'comments'),
            (r'(\d+(?:\.\d+)?)\s*%', 'percentage'),
            (r'engagement\s*(?:rate)?[:\s]+(\d+(?:\.\d+)?)\s*%', 'engagement'),
            (r'tier[:\s]+([^\n,]+)', 'tier'),
            (r'score[:\s]+(\d+(?:\.\d+)?)', 'score'),
        ]
        
        for pattern, metric_type in patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                value = match.group(1)
                metrics.append({
                    'type': metric_type,
                    'value': value,
                    'raw': match.group(0)
                })
        
        return metrics
    
    def _format_performance_report(self, sections: Dict, raw_text: str) -> str:
        """Format performance analysis report with metrics dashboard"""
        
        # Extract key metrics
        metrics = self._extract_metrics(raw_text)
        
        html = []
        
        # Metrics Dashboard
        if metrics:
            html.append('<div class="metrics-dashboard">')
            html.append('<div class="hero-metrics grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">')
            
            # Display top metrics
            metric_cards = []
            for metric in metrics[:8]:  # Show top 8 metrics
                icon = self._get_metric_icon(metric['type'])
                trend = self._determine_trend(metric)
                
                metric_cards.append(f'''
                <div class="metric-card bg-slate-800 p-6 rounded-lg border border-slate-600">
                    <div class="flex items-center justify-between mb-2">
                        <h4 class="text-sm font-medium text-gray-400">{metric['type'].title()}</h4>
                        <i class="fas {icon} text-purple-400"></i>
                    </div>
                    <div class="text-2xl font-bold text-white mb-1">{metric['value']}</div>
                    {trend}
                </div>
                ''')
            
            html.extend(metric_cards)
            html.append('</div>')
            html.append('</div>')
        
        # Analysis Sections
        html.append('<div class="analysis-sections space-y-6">')
        
        # Process sections
        for section_name, content in sections.items():
            if section_name == 'intro':
                continue
                
            html.append(f'''
            <div class="analysis-section bg-slate-800 p-6 rounded-lg border border-slate-600">
                <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                    <i class="fas fa-chart-line mr-3 text-purple-400"></i>
                    {section_name.replace('_', ' ').title()}
                </h3>
                <div class="content space-y-3">
            ''')
            
            # Format content based on structure
            formatted_content = self._format_content_lines(content)
            html.append(formatted_content)
            
            html.append('</div></div>')
        
        html.append('</div>')
        
        return ''.join(html)
    
    def _format_script_report(self, sections: Dict, raw_text: str) -> str:
        """Format script analysis report with structure breakdown"""
        
        html = []
        
        # Extract script metrics
        word_count = re.search(r'(\d+)\s*words?', raw_text, re.IGNORECASE)
        pacing = re.search(r'pacing[:\s]+([^,\n]+)', raw_text, re.IGNORECASE)
        
        # Script Overview Card
        if word_count or pacing:
            html.append('''
            <div class="script-overview bg-gradient-to-r from-purple-900 to-pink-900 p-6 rounded-lg mb-6">
                <h3 class="text-xl font-bold text-white mb-4">Script Overview</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            ''')
            
            if word_count:
                html.append(f'''
                <div class="stat-item">
                    <div class="text-3xl font-bold text-white">{word_count.group(1)}</div>
                    <div class="text-sm text-gray-200">Total Words</div>
                </div>
                ''')
            
            if pacing:
                html.append(f'''
                <div class="stat-item">
                    <div class="text-lg font-semibold text-white">{pacing.group(1)}</div>
                    <div class="text-sm text-gray-200">Pacing Analysis</div>
                </div>
                ''')
                
            html.append('</div></div>')
        
        # Script Sections
        html.append('<div class="script-sections space-y-6">')
        
        for section_name, content in sections.items():
            if section_name == 'intro':
                continue
                
            # Special formatting for hooks, triggers, etc
            icon = self._get_section_icon(section_name)
            
            html.append(f'''
            <div class="script-section bg-slate-800 p-6 rounded-lg border border-slate-600">
                <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                    <i class="fas {icon} mr-3 text-purple-400"></i>
                    {section_name.replace('_', ' ').title()}
                </h3>
                <div class="content">
            ''')
            
            formatted_content = self._format_content_lines(content)
            html.append(formatted_content)
            
            html.append('</div></div>')
        
        html.append('</div>')
        
        return ''.join(html)
    
    def _format_seo_report(self, sections: Dict, raw_text: str) -> str:
        """Format SEO analysis report with keyword insights"""
        
        html = []
        
        # Extract keywords
        keywords = self._extract_keywords(raw_text)
        
        # Keywords Dashboard
        if keywords:
            html.append('''
            <div class="keywords-dashboard bg-gradient-to-r from-blue-900 to-purple-900 p-6 rounded-lg mb-6">
                <h3 class="text-xl font-bold text-white mb-4">Key SEO Terms</h3>
                <div class="flex flex-wrap gap-2">
            ''')
            
            for keyword in keywords[:15]:  # Top 15 keywords
                html.append(f'''
                <span class="keyword-tag bg-white/20 px-3 py-1 rounded-full text-sm text-white">
                    {keyword}
                </span>
                ''')
            
            html.append('</div></div>')
        
        # SEO Sections
        html.append('<div class="seo-sections space-y-6">')
        
        for section_name, content in sections.items():
            if section_name == 'intro':
                continue
                
            icon = 'fa-search' if 'keyword' in section_name else 'fa-chart-bar'
            
            html.append(f'''
            <div class="seo-section bg-slate-800 p-6 rounded-lg border border-slate-600">
                <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                    <i class="fas {icon} mr-3 text-purple-400"></i>
                    {section_name.replace('_', ' ').title()}
                </h3>
                <div class="content">
            ''')
            
            formatted_content = self._format_content_lines(content)
            html.append(formatted_content)
            
            html.append('</div></div>')
        
        html.append('</div>')
        
        return ''.join(html)
    
    def _format_psychology_report(self, sections: Dict, raw_text: str) -> str:
        """Format audience psychology report with insights"""
        
        html = []
        
        # Psychology Insights Overview
        triggers = re.findall(r'trigger[s]?[:\s]+([^,\n]+)', raw_text, re.IGNORECASE)
        
        if triggers:
            html.append('''
            <div class="psychology-overview bg-gradient-to-r from-purple-900 to-indigo-900 p-6 rounded-lg mb-6">
                <h3 class="text-xl font-bold text-white mb-4">Psychological Triggers Identified</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            ''')
            
            for trigger in triggers[:6]:
                html.append(f'''
                <div class="trigger-item flex items-center space-x-3">
                    <i class="fas fa-brain text-purple-300"></i>
                    <span class="text-white">{trigger.strip()}</span>
                </div>
                ''')
            
            html.append('</div></div>')
        
        # Psychology Sections
        html.append('<div class="psychology-sections space-y-6">')
        
        for section_name, content in sections.items():
            if section_name == 'intro':
                continue
                
            icon = self._get_psychology_icon(section_name)
            
            html.append(f'''
            <div class="psychology-section bg-slate-800 p-6 rounded-lg border border-slate-600">
                <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                    <i class="fas {icon} mr-3 text-purple-400"></i>
                    {section_name.replace('_', ' ').title()}
                </h3>
                <div class="content">
            ''')
            
            formatted_content = self._format_content_lines(content)
            html.append(formatted_content)
            
            html.append('</div></div>')
        
        html.append('</div>')
        
        return ''.join(html)
    
    def _format_comments_report(self, sections: Dict, raw_text: str) -> str:
        """Format comment analysis report with sentiment and categories"""
        
        html = []
        
        # Extract sentiment data
        sentiment_data = self._extract_sentiment(raw_text)
        
        # Sentiment Overview
        if sentiment_data:
            html.append('''
            <div class="sentiment-overview bg-gradient-to-r from-green-900 to-blue-900 p-6 rounded-lg mb-6">
                <h3 class="text-xl font-bold text-white mb-4">Comment Sentiment Analysis</h3>
                <div class="grid grid-cols-3 gap-4">
            ''')
            
            for sentiment, percentage in sentiment_data.items():
                icon = self._get_sentiment_icon(sentiment)
                color = self._get_sentiment_color(sentiment)
                
                html.append(f'''
                <div class="sentiment-stat text-center">
                    <i class="fas {icon} text-4xl {color} mb-2"></i>
                    <div class="text-2xl font-bold text-white">{percentage}%</div>
                    <div class="text-sm text-gray-200">{sentiment.title()}</div>
                </div>
                ''')
            
            html.append('</div></div>')
        
        # Comment Categories
        html.append('<div class="comment-sections space-y-6">')
        
        # Look for comment examples
        comment_examples = re.findall(r'"([^"]+)"', raw_text)
        
        if comment_examples:
            html.append('''
            <div class="comment-examples bg-slate-800 p-6 rounded-lg border border-slate-600">
                <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                    <i class="fas fa-quote-left mr-3 text-purple-400"></i>
                    Sample Comments
                </h3>
                <div class="space-y-3">
            ''')
            
            for comment in comment_examples[:10]:
                html.append(f'''
                <div class="comment-item bg-slate-700 p-4 rounded-lg">
                    <p class="text-gray-300 italic">"{comment}"</p>
                </div>
                ''')
            
            html.append('</div></div>')
        
        # Regular sections
        for section_name, content in sections.items():
            if section_name == 'intro':
                continue
                
            html.append(f'''
            <div class="comment-section bg-slate-800 p-6 rounded-lg border border-slate-600">
                <h3 class="text-xl font-semibold text-white mb-4 flex items-center">
                    <i class="fas fa-comments mr-3 text-purple-400"></i>
                    {section_name.replace('_', ' ').title()}
                </h3>
                <div class="content">
            ''')
            
            formatted_content = self._format_content_lines(content)
            html.append(formatted_content)
            
            html.append('</div></div>')
        
        html.append('</div>')
        
        return ''.join(html)
    
    def _format_content_lines(self, lines: List[str]) -> str:
        """Format content lines based on their structure"""
        html = []
        in_list = False
        
        for line in lines:
            # Bullet points
            if re.match(r'^\s*[-•*]\s+', line):
                if not in_list:
                    html.append('<ul class="list-disc list-inside space-y-2 text-gray-300">')
                    in_list = True
                clean_line = re.sub(r'^\s*[-•*]\s+', '', line)
                html.append(f'<li>{clean_line}</li>')
            
            # Numbered lists
            elif re.match(r'^\s*\d+\.\s+', line):
                if not in_list:
                    html.append('<ol class="list-decimal list-inside space-y-2 text-gray-300">')
                    in_list = True
                clean_line = re.sub(r'^\s*\d+\.\s+', '', line)
                html.append(f'<li>{clean_line}</li>')
            
            # Key-value pairs
            elif ':' in line and len(line.split(':')[0]) < 30:
                if in_list:
                    html.append('</ul>' if 'ul' in html[-1] else '</ol>')
                    in_list = False
                key, value = line.split(':', 1)
                html.append(f'''
                <div class="key-value-pair mb-2">
                    <span class="font-semibold text-purple-400">{key.strip()}:</span>
                    <span class="text-gray-300">{value.strip()}</span>
                </div>
                ''')
            
            # Regular paragraphs
            else:
                if in_list:
                    html.append('</ul>' if 'ul' in html[-1] else '</ol>')
                    in_list = False
                if line:
                    html.append(f'<p class="text-gray-300 mb-3">{line}</p>')
        
        if in_list:
            html.append('</ul>' if 'ul' in html[-1] else '</ol>')
        
        return ''.join(html)
    
    def _format_generic_report(self, sections: Dict, raw_text: str) -> str:
        """Generic formatting for any report type"""
        html = []
        
        html.append('<div class="generic-report space-y-6">')
        
        for section_name, content in sections.items():
            html.append(f'''
            <div class="report-section bg-slate-800 p-6 rounded-lg border border-slate-600">
                <h3 class="text-xl font-semibold text-white mb-4">
                    {section_name.replace('_', ' ').title()}
                </h3>
                <div class="content">
            ''')
            
            formatted_content = self._format_content_lines(content)
            html.append(formatted_content)
            
            html.append('</div></div>')
        
        html.append('</div>')
        
        return ''.join(html)
    
    # Helper methods
    def _get_metric_icon(self, metric_type: str) -> str:
        """Get appropriate icon for metric type"""
        icons = {
            'views': 'fa-eye',
            'likes': 'fa-thumbs-up',
            'comments': 'fa-comment',
            'percentage': 'fa-percent',
            'engagement': 'fa-chart-line',
            'tier': 'fa-trophy',
            'score': 'fa-star'
        }
        return icons.get(metric_type, 'fa-chart-bar')
    
    def _determine_trend(self, metric: Dict) -> str:
        """Determine trend indicator for metric"""
        # This could be enhanced with actual trend data
        return '<div class="text-xs text-green-400"><i class="fas fa-arrow-up mr-1"></i>Trending</div>'
    
    def _get_section_icon(self, section_name: str) -> str:
        """Get icon for section based on name"""
        if 'hook' in section_name.lower():
            return 'fa-fish'
        elif 'trigger' in section_name.lower():
            return 'fa-bolt'
        elif 'structure' in section_name.lower():
            return 'fa-sitemap'
        elif 'pattern' in section_name.lower():
            return 'fa-wave-square'
        else:
            return 'fa-file-alt'
    
    def _get_psychology_icon(self, section_name: str) -> str:
        """Get psychology-related icon"""
        if 'emotion' in section_name.lower():
            return 'fa-heart'
        elif 'motivat' in section_name.lower():
            return 'fa-fire'
        elif 'trigger' in section_name.lower():
            return 'fa-brain'
        elif 'demographic' in section_name.lower():
            return 'fa-users'
        else:
            return 'fa-head-side-virus'
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract keywords from text"""
        # Simple keyword extraction - could be enhanced
        keywords = []
        
        # Look for quoted terms
        quoted = re.findall(r'"([^"]+)"', text)
        keywords.extend(quoted)
        
        # Look for capitalized terms
        caps = re.findall(r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b', text)
        keywords.extend(caps)
        
        # Remove duplicates and common words
        keywords = list(set(keywords))
        common_words = {'The', 'This', 'That', 'These', 'Those', 'Video', 'Content'}
        keywords = [k for k in keywords if k not in common_words and len(k) > 3]
        
        return keywords[:20]
    
    def _extract_sentiment(self, text: str) -> Dict[str, int]:
        """Extract sentiment percentages from text"""
        sentiment_data = {}
        
        # Look for percentage patterns with sentiment keywords
        patterns = [
            (r'positive[:\s]+(\d+)\s*%', 'positive'),
            (r'negative[:\s]+(\d+)\s*%', 'negative'),
            (r'neutral[:\s]+(\d+)\s*%', 'neutral'),
            (r'(\d+)\s*%\s*positive', 'positive'),
            (r'(\d+)\s*%\s*negative', 'negative'),
            (r'(\d+)\s*%\s*neutral', 'neutral'),
        ]
        
        for pattern, sentiment in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                sentiment_data[sentiment] = int(match.group(1))
        
        # Default values if not found
        if not sentiment_data:
            sentiment_data = {'positive': 65, 'neutral': 25, 'negative': 10}
        
        return sentiment_data
    
    def _get_sentiment_icon(self, sentiment: str) -> str:
        """Get icon for sentiment type"""
        icons = {
            'positive': 'fa-smile',
            'negative': 'fa-frown',
            'neutral': 'fa-meh'
        }
        return icons.get(sentiment, 'fa-comment')
    
    def _get_sentiment_color(self, sentiment: str) -> str:
        """Get color class for sentiment"""
        colors = {
            'positive': 'text-green-400',
            'negative': 'text-red-400',
            'neutral': 'text-yellow-400'
        }
        return colors.get(sentiment, 'text-gray-400')