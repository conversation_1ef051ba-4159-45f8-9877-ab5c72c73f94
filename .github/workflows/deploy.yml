name: Deploy

on:
  push:
    branches: [ main ]
  release:
    types: [ published ]

jobs:
  deploy-staging:
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    environment: staging
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run tests before deploy
      run: |
        pytest -v --ignore=test_archived/ --ignore=test_async_example.py
    
    - name: Deploy to Staging
      run: |
        echo "Deploying to staging environment..."
        # Add your staging deployment commands here
        # Example: docker build, push to registry, deploy to cloud service
    
    - name: Notify deployment
      run: |
        echo "Staging deployment completed: ${{ github.sha }}"

  deploy-production:
    if: github.event_name == 'release'
    runs-on: ubuntu-latest
    environment: production
    needs: [deploy-staging]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Run full test suite
      run: |
        pytest -v --ignore=test_archived/ --ignore=test_async_example.py
    
    - name: Deploy to Production
      run: |
        echo "Deploying to production environment..."
        # Add your production deployment commands here
        echo "Production deployment of ${{ github.event.release.tag_name }} completed"
    
    - name: Create deployment status
      uses: actions/github-script@v6
      with:
        script: |
          github.rest.repos.createDeploymentStatus({
            owner: context.repo.owner,
            repo: context.repo.repo,
            deployment_id: context.payload.deployment.id,
            state: 'success',
            description: 'Deployment completed successfully'
          })
