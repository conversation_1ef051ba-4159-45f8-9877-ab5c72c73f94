"""
Channel DNA Data Models
Defines the structure for storing channel analysis results
"""

from typing import List, Dict, Any, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from enum import Enum


class AnalysisStatus(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class ContentDNA(BaseModel):
    """Content DNA analysis results"""
    content_personality: str = Field(description="Comprehensive content personality profile")
    content_evolution: str = Field(description="How content strategy has evolved over time")
    signature_elements: List[str] = Field(description="Unique elements that define the channel")
    content_strategy: str = Field(description="Strategic breakdown of content approach")
    optimization_opportunities: List[str] = Field(description="Specific areas for improvement")
    competitive_advantages: List[str] = Field(description="What sets this channel apart")


class AudienceDNA(BaseModel):
    """Audience DNA analysis results"""
    demographics: Dict[str, Any] = Field(description="Detailed audience demographics")
    psychographics: str = Field(description="Audience psychology and motivations")
    pain_points: List[str] = Field(description="Key audience challenges and needs")
    engagement_patterns: str = Field(description="How the audience interacts with content")
    community_culture: str = Field(description="The culture within the community")
    growth_opportunities: List[str] = Field(description="Untapped audience segments")


class EngagementDNA(BaseModel):
    """Engagement DNA analysis results"""
    mathematical_patterns: Dict[str, Any] = Field(description="Quantitative engagement metrics")
    upload_strategy: str = Field(description="Strategic analysis of upload patterns")
    performance_insights: str = Field(description="What drives high performance")
    engagement_hooks: List[str] = Field(description="Elements that drive engagement")
    optimization_framework: str = Field(description="Framework for improving engagement")
    viral_factors: List[str] = Field(description="Elements that contribute to viral success")


class VisualDNA(BaseModel):
    """Visual DNA analysis results"""
    visual_identity: str = Field(description="Overall visual brand identity")
    color_psychology: Dict[str, Any] = Field(description="Color schemes and their impact")
    thumbnail_patterns: List[str] = Field(description="Common thumbnail elements")
    design_evolution: str = Field(description="How visual style has evolved")
    visual_hooks: List[str] = Field(description="Visual elements that grab attention")
    brand_consistency: float = Field(description="Visual brand consistency score 0-1")


class StrategyDNA(BaseModel):
    """Strategic synthesis and recommendations"""
    executive_summary: str = Field(description="High-level strategic overview")
    competitive_positioning: str = Field(description="Where the channel stands in market")
    growth_roadmap: List[Dict[str, str]] = Field(description="Phased growth plan")
    content_calendar_strategy: str = Field(description="Recommended content approach")
    monetization_opportunities: List[str] = Field(description="Revenue optimization ideas")
    risk_factors: List[str] = Field(description="Potential challenges to address")
    success_metrics: Dict[str, Any] = Field(description="KPIs to track progress")


class ChannelDNAAnalysis(BaseModel):
    """Complete Channel DNA analysis results"""
    channel_id: str
    channel_name: str
    analysis_id: str
    status: AnalysisStatus
    
    # DNA Components
    content_dna: Optional[ContentDNA] = None
    audience_dna: Optional[AudienceDNA] = None
    engagement_dna: Optional[EngagementDNA] = None
    visual_dna: Optional[VisualDNA] = None
    strategy_dna: Optional[StrategyDNA] = None
    
    # Metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    analysis_duration_seconds: Optional[float] = None
    data_points_analyzed: Dict[str, int] = Field(default_factory=dict)
    
    # Raw data reference
    raw_data_id: Optional[str] = None
    
    class Config:
        use_enum_values = True


class ChannelDataCache(BaseModel):
    """Cached channel data for analysis"""
    channel_id: str
    channel_info: Dict[str, Any]
    videos: List[Dict[str, Any]]
    comments: Dict[str, List[Dict[str, Any]]]
    transcripts: Dict[str, str]
    fetched_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime


class AgentOutput(BaseModel):
    """Individual agent output structure"""
    agent_name: str
    agent_role: str
    analysis: Any
    confidence_score: float = Field(ge=0, le=1)
    processing_time: float
    data_sources_used: List[str]
    warnings: List[str] = Field(default_factory=list)