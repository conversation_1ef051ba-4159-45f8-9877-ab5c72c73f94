#!/usr/bin/env python3
"""
Rome vs Carthage - Writers' Room Test with RLL DNA Edition Prompts
Using the provided golden nuggets for geographic storytelling
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'core_system'))

from writers_room import WritersRoom, WritersRoomInput
import json
import time

def test_rome_carthage():
    """Test Rome vs Carthage with provided golden nuggets"""
    
    print("🏛️ ROME VS CARTHAGE - WRITERS' ROOM GENERATION")
    print("="*80)
    
    # Rome vs Carthage with your golden nuggets
    input_data = WritersRoomInput(
        topic="Rome vs Carthage - The 90-Mile Strait That Decided Two Empires",
        golden_nuggets=[
            "<PERSON> lost 50% of his army (20,000 men) crossing the Alps in 15 days - not from Roman attacks, but from a 70-mile mountain path",
            "Rome built 120 warships in 60 days by reverse-engineering a single shipwrecked Carthaginian vessel - then added a boarding bridge that turned sea battles into land battles",
            "The strait between Sicily and Carthage is only 90 miles wide - controlling it meant controlling 80% of Mediterranean trade",
            "Carthage's harbor could hold 220 warships in covered docks invisible from the sea - the ancient equivalent of a hidden nuclear submarine base",
            "After defeating Carthage, Rome insisted on moving the entire city 10 miles inland - because geography is power, and coastlines create navies"
        ],
        persona={
            "channel": "Frontier",
            "voice": "Awestruck Strategist",
            "focus": "Geography and logistics determining history"
        }
    )
    
    # Generate script
    writers_room = WritersRoom()
    start_time = time.time()
    
    result = writers_room.run_writers_room(input_data)
    
    execution_time = time.time() - start_time
    
    # Display results
    print(f"\n🎬 SCRIPT GENERATION COMPLETE!")
    print(f"⏱️ Execution Time: {execution_time:.1f} seconds")
    print(f"📊 Script Length: {len(result.final_script)} characters")
    print(f"🎭 Vocal Annotations: {result.vocal_annotations}")
    print(f"⏳ Estimated Duration: {result.estimated_duration}")
    
    # Save results
    output_data = {
        "topic": input_data.topic,
        "golden_nuggets": input_data.golden_nuggets,
        "persona": input_data.persona,
        "execution_time": execution_time,
        "final_script": result.final_script,
        "structure": result.structure,
        "hook_variations": result.hook_variations,
        "visual_cues": result.visual_cues,
        "vocal_annotations": result.vocal_annotations,
        "estimated_duration": result.estimated_duration
    }
    
    # Save to outputs directory
    output_dir = "../outputs/tests"
    os.makedirs(output_dir, exist_ok=True)
    
    output_file = f"{output_dir}/rome_carthage_script.json"
    with open(output_file, 'w') as f:
        json.dump(output_data, f, indent=2)
    
    print(f"\n💾 Script saved to: {output_file}")
    
    # Display the final script
    print("\n" + "="*80)
    print("🎯 FINAL SCRIPT PREVIEW:")
    print("="*80)
    print(result.final_script)
    
    return result

if __name__ == "__main__":
    test_rome_carthage()