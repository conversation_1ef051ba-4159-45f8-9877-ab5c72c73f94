<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Functionality Test</title>
    <link rel="stylesheet" href="static/styles/youtube-research-v2-design-system.css">
    <style>
        body {
            margin: 0;
            padding: var(--space-lg);
            background: var(--bg-primary);
            font-family: var(--font-primary);
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: var(--space-2xl);
            padding: var(--space-lg);
            background: var(--bg-elevated);
            border: 1px solid var(--border-light);
            border-radius: var(--radius-lg);
        }
        
        .test-title {
            font-size: var(--text-xl);
            font-weight: var(--font-weight-bold);
            color: var(--text-primary);
            margin-bottom: var(--space-lg);
            padding-bottom: var(--space-sm);
            border-bottom: 2px solid var(--purple-primary);
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-lg);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: var(--space-xs);
        }
        
        .status-pass { background-color: var(--success); }
        .status-fail { background-color: var(--error); }
        .status-pending { background-color: var(--warning); }
        
        .test-result {
            padding: var(--space-sm);
            margin: var(--space-xs) 0;
            border-radius: var(--radius-sm);
            font-size: var(--text-sm);
        }
        
        .result-pass {
            background-color: var(--success-light);
            color: var(--success);
        }
        
        .result-fail {
            background-color: var(--error-light);
            color: var(--error);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="text-align: center; color: var(--text-primary); margin-bottom: var(--space-2xl);">
            Dashboard Functionality Test Suite
        </h1>
        
        <!-- CSS Variables Test -->
        <div class="test-section">
            <h2 class="test-title">CSS Variables & Design System</h2>
            <div id="css-test-results"></div>
        </div>
        
        <!-- Component Styling Test -->
        <div class="test-section">
            <h2 class="test-title">Component Styling Test</h2>
            <div class="test-grid">
                <!-- Metric Card Test -->
                <div class="metric-card">
                    <div class="stat-value">127</div>
                    <div class="stat-label">Test Metric</div>
                    <div class="stat-trend up">↑ 23% improvement</div>
                </div>
                
                <!-- Activity Item Test -->
                <div class="activity-item">
                    <div class="activity-thumbnail"></div>
                    <div class="activity-details">
                        <h4>Test Activity Item</h4>
                        <p style="font-size: var(--text-sm); color: var(--text-secondary); margin: 0;">
                            Hover test • Interactive elements
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Theme Toggle Test -->
        <div class="test-section">
            <h2 class="test-title">Theme System Test</h2>
            <button onclick="toggleTheme()" class="btn btn-primary">Toggle Light/Dark Theme</button>
            <p style="margin-top: var(--space-md); color: var(--text-secondary);">
                Current theme: <span id="current-theme">dark</span>
            </p>
            <div id="theme-test-results"></div>
        </div>
        
        <!-- Responsive Design Test -->
        <div class="test-section">
            <h2 class="test-title">Responsive Design Test</h2>
            <div id="responsive-test-results"></div>
        </div>
        
        <!-- Interactive Elements Test -->
        <div class="test-section">
            <h2 class="test-title">Interactive Elements Test</h2>
            <div style="display: flex; gap: var(--space-md); flex-wrap: wrap;">
                <button class="btn btn-primary" onclick="testButton('primary')">Primary Button</button>
                <button class="btn btn-secondary" onclick="testButton('secondary')">Secondary Button</button>
                <button class="btn btn-ghost" onclick="testButton('ghost')">Ghost Button</button>
            </div>
            <div id="interaction-test-results"></div>
        </div>
    </div>
    
    <script>
        // Test results storage
        const testResults = {
            css: [],
            theme: [],
            responsive: [],
            interaction: []
        };
        
        // Theme toggle functionality
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            html.setAttribute('data-theme', newTheme);
            document.getElementById('current-theme').textContent = newTheme;
            
            // Test theme variables
            testThemeVariables();
        }
        
        // Test CSS variables
        function testCSSVariables() {
            const testVars = [
                '--bg-primary',
                '--bg-elevated',
                '--text-primary',
                '--text-secondary',
                '--purple-primary',
                '--success',
                '--border-light',
                '--shadow-sm',
                '--radius-lg',
                '--space-lg'
            ];
            
            const results = [];
            testVars.forEach(varName => {
                const value = getComputedStyle(document.documentElement).getPropertyValue(varName);
                const status = value.trim() ? 'pass' : 'fail';
                results.push({
                    name: varName,
                    value: value.trim(),
                    status: status
                });
            });
            
            testResults.css = results;
            displayResults('css-test-results', results);
        }
        
        // Test theme variables
        function testThemeVariables() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const bgPrimary = getComputedStyle(document.documentElement).getPropertyValue('--bg-primary');
            const textPrimary = getComputedStyle(document.documentElement).getPropertyValue('--text-primary');
            
            const results = [{
                name: `Theme: ${currentTheme}`,
                value: `bg: ${bgPrimary.trim()}, text: ${textPrimary.trim()}`,
                status: bgPrimary.trim() && textPrimary.trim() ? 'pass' : 'fail'
            }];
            
            testResults.theme = results;
            displayResults('theme-test-results', results);
        }
        
        // Test responsive design
        function testResponsiveDesign() {
            const breakpoints = [
                { name: 'Mobile', width: 375 },
                { name: 'Tablet', width: 768 },
                { name: 'Desktop', width: 1024 }
            ];
            
            const results = breakpoints.map(bp => ({
                name: `${bp.name} (${bp.width}px)`,
                value: window.innerWidth >= bp.width ? 'Supported' : 'Not active',
                status: 'pass'
            }));
            
            testResults.responsive = results;
            displayResults('responsive-test-results', results);
        }
        
        // Test button interactions
        function testButton(type) {
            const result = {
                name: `${type} button`,
                value: 'Clicked successfully',
                status: 'pass'
            };
            
            testResults.interaction.push(result);
            displayResults('interaction-test-results', testResults.interaction);
        }
        
        // Display test results
        function displayResults(containerId, results) {
            const container = document.getElementById(containerId);
            container.innerHTML = results.map(result => `
                <div class="test-result result-${result.status}">
                    <span class="status-indicator status-${result.status}"></span>
                    <strong>${result.name}:</strong> ${result.value}
                </div>
            `).join('');
        }
        
        // Run tests on page load
        window.addEventListener('load', () => {
            testCSSVariables();
            testThemeVariables();
            testResponsiveDesign();
        });
        
        // Run responsive test on resize
        window.addEventListener('resize', testResponsiveDesign);
    </script>
</body>
</html>
