/**
 * Tool Configurations for Professional Layout System
 * Defines capabilities and settings for each analysis tool
 */

const TOOL_CONFIGS = {
    'video-analyzer': {
        title: '🎬 AI Video Analyzer',
        subtitle: 'Deep forensic analysis with 7 specialized AI agents. Uncover performance secrets, script psychology, SEO opportunities, and audience insights.',
        icon: 'video',
        agentCount: '7',
        analysisTime: '3-5 min',
        analyzeButtonText: 'Analyze Video',
        capabilities: [
            {
                id: 'performance-intelligence',
                icon: 'trending-up',
                title: 'Performance Intelligence',
                description: 'Channel-relative metrics analysis with overperformance detection and tier classification'
            },
            {
                id: 'script-forensics',
                icon: 'file-text',
                title: 'Script Forensics',
                description: 'Psychological trigger analysis, pacing optimization, and retention hook identification'
            },
            {
                id: 'seo-discoverability',
                icon: 'search',
                title: 'SEO & Discoverability',
                description: 'Keyword optimization, search intent alignment, and discoverability enhancement'
            },
            {
                id: 'audience-psychology',
                icon: 'brain',
                title: 'Audience Psychology',
                description: 'Emotional trigger mapping, viewer motivation analysis, and engagement driver identification'
            },
            {
                id: 'comment-intelligence',
                icon: 'message-circle',
                title: 'Comment Intelligence',
                description: 'Real-time sentiment analysis with audience feedback mining and content opportunity discovery'
            },
            {
                id: 'strategic-synthesis',
                icon: 'target',
                title: 'Strategic Synthesis',
                description: 'Cross-agent correlation analysis with actionable strategic recommendations'
            },
            {
                id: 'viral-potential',
                icon: 'zap',
                title: 'Viral Potential Predictor',
                description: 'Advanced pattern recognition to predict viral breakthrough potential'
            }
        ],
        formFields: [
            {
                type: 'url',
                id: 'videoUrl',
                label: 'YouTube Video URL',
                placeholder: 'https://www.youtube.com/watch?v=...',
                helpText: 'Paste any YouTube video URL for comprehensive analysis',
                required: true
            },
            {
                type: 'textarea',
                id: 'script',
                label: 'Video Script',
                placeholder: 'Paste the video script here for deeper analysis...',
                helpText: 'Optional: Adding the script enables advanced forensic analysis',
                required: false
            }
        ]
    },

    'channel-analyzer': {
        title: '🧬 Channel DNA Analyzer',
        subtitle: 'Comprehensive channel profiling with 5 specialized AI agents. Decode growth patterns, content strategies, and competitive positioning.',
        icon: 'users',
        agentCount: '5',
        analysisTime: '2-4 min',
        analyzeButtonText: 'Analyze Channel',
        capabilities: [
            {
                id: 'channel-profiling',
                icon: 'user',
                title: 'Channel Profiling',
                description: 'Growth trajectory analysis, subscriber tier classification, and performance benchmarking'
            },
            {
                id: 'content-patterns',
                icon: 'grid-3x3',
                title: 'Content Pattern Analysis',
                description: 'Viral element detection, format optimization, and content strategy decoding'
            },
            {
                id: 'audience-psychology',
                icon: 'heart',
                title: 'Audience Psychology',
                description: 'Community behavior analysis, engagement pattern recognition, and viewer motivation mapping'
            },
            {
                id: 'performance-metrics',
                icon: 'bar-chart-3',
                title: 'Performance Intelligence',
                description: 'Engagement rate analysis, consistency scoring, and outlier performance detection'
            },
            {
                id: 'competitive-positioning',
                icon: 'compass',
                title: 'Competitive Intelligence',
                description: 'Market positioning analysis, competitive benchmarking, and strategic differentiation opportunities'
            }
        ],
        formFields: [
            {
                type: 'url',
                id: 'channelUrl',
                label: 'YouTube Channel URL',
                placeholder: 'https://www.youtube.com/@channelname or channel ID',
                helpText: 'Paste channel URL, @handle, or channel ID for comprehensive analysis',
                required: true
            },
            {
                type: 'number',
                id: 'maxVideos',
                label: 'Videos to Analyze',
                placeholder: '20',
                helpText: 'Number of recent videos to analyze (5-50, default: 20)',
                required: false,
                min: 5,
                max: 50,
                default: 20
            }
        ]
    },

    'market-research': {
        title: '📊 Market Research Hub',
        subtitle: 'Strategic market intelligence with 6 specialized AI agents. Identify opportunities, analyze competition, and develop launch strategies.',
        icon: 'trending-up',
        agentCount: '6',
        analysisTime: '4-6 min',
        analyzeButtonText: 'Research Market',
        capabilities: [
            {
                id: 'opportunity-scouting',
                icon: 'search',
                title: 'Market Opportunity Scouting',
                description: 'Blue ocean identification, content gap analysis, and underserved audience discovery'
            },
            {
                id: 'content-strategy',
                icon: 'layers',
                title: 'Content Strategy Architecture',
                description: 'Viral pattern extraction, format optimization, and replicable success framework development'
            },
            {
                id: 'audience-intelligence',
                icon: 'users',
                title: 'Audience Intelligence',
                description: 'Unmet need mining, demographic analysis, and content request identification from real feedback'
            },
            {
                id: 'competitive-mapping',
                icon: 'map',
                title: 'Competitive Landscape Mapping',
                description: 'Market positioning analysis, competitive gap identification, and strategic differentiation opportunities'
            },
            {
                id: 'production-intelligence',
                icon: 'cog',
                title: 'AI Production Intelligence',
                description: 'Automation feasibility assessment, ROI calculation, and production workflow optimization'
            },
            {
                id: 'launch-strategy',
                icon: 'rocket',
                title: 'Launch Strategy Synthesis',
                description: 'Go-to-market planning, strategic roadmap development, and comprehensive launch blueprint creation'
            }
        ],
        formFields: [
            {
                type: 'url',
                id: 'competitorChannel',
                label: 'Competitor Channel URL',
                placeholder: 'https://www.youtube.com/@competitor',
                helpText: 'Primary competitor or successful channel in your target niche',
                required: true
            },
            {
                type: 'text',
                id: 'targetNiche',
                label: 'Target Niche',
                placeholder: 'e.g., AI tutorials, productivity, finance',
                helpText: 'Describe your target content niche or market segment',
                required: true
            },
            {
                type: 'textarea',
                id: 'businessGoals',
                label: 'Business Goals',
                placeholder: 'Describe your channel goals, target audience, and success metrics...',
                helpText: 'Optional: Provide context for more targeted strategic recommendations',
                required: false
            }
        ]
    },

    'comment-intelligence': {
        title: '💬 Comment Intelligence Agent',
        subtitle: 'Extract strategic insights from real audience feedback with advanced sentiment analysis and opportunity mining.',
        icon: 'message-circle',
        agentCount: '1',
        analysisTime: '1-2 min',
        analyzeButtonText: 'Analyze Comments',
        capabilities: [
            {
                id: 'sentiment-analysis',
                icon: 'heart',
                title: 'Sentiment Analysis',
                description: 'Real comment sentiment analysis with evidence categorization and emotional tone mapping'
            },
            {
                id: 'audience-validation',
                icon: 'users',
                title: 'Audience Validation',
                description: 'Cross-reference viewer feedback with performance predictions and content strategy validation'
            },
            {
                id: 'content-opportunities',
                icon: 'lightbulb',
                title: 'Content Opportunity Mining',
                description: 'Extract viewer requests, identify content gaps, and discover high-demand topic opportunities'
            },
            {
                id: 'community-intelligence',
                icon: 'target',
                title: 'Community Intelligence',
                description: 'Engagement pattern analysis, community behavior insights, and audience interaction optimization'
            }
        ],
        formFields: [
            {
                type: 'url',
                id: 'videoUrl',
                label: 'YouTube Video URL',
                placeholder: 'https://www.youtube.com/watch?v=...',
                helpText: 'Analyze comments from any YouTube video for strategic insights',
                required: true
            }
        ]
    }
};

/**
 * Generate tool configuration HTML
 */
function generateToolHTML(toolId) {
    const config = TOOL_CONFIGS[toolId];
    if (!config) {
        console.error(`Tool configuration not found for: ${toolId}`);
        return '';
    }

    // Generate capabilities HTML
    const capabilitiesHTML = config.capabilities.map(capability => `
        <div class="capability-item" data-capability="${capability.id}">
            <div class="capability-icon-wrapper">
                <i data-lucide="${capability.icon}" class="capability-icon"></i>
            </div>
            <div class="capability-content">
                <h4 class="capability-title">${capability.title}</h4>
                <p class="capability-description">${capability.description}</p>
            </div>
        </div>
    `).join('');

    // Generate form fields HTML
    const formFieldsHTML = config.formFields.map(field => {
        switch (field.type) {
            case 'url':
            case 'text':
                return `
                    <div class="form-field">
                        <label class="field-label">
                            <span class="label-text">${field.label}</span>
                            ${field.required ? '<span class="label-required">*</span>' : '<span class="label-optional">Optional</span>'}
                        </label>
                        <div class="field-input-wrapper">
                            <div class="input-icon">
                                <i data-lucide="${field.type === 'url' ? 'link' : 'type'}" class="input-icon-svg"></i>
                            </div>
                            <input 
                                type="${field.type}" 
                                class="field-input" 
                                id="${field.id}"
                                placeholder="${field.placeholder}"
                                ${field.required ? 'required' : ''}
                                ${field.min ? `min="${field.min}"` : ''}
                                ${field.max ? `max="${field.max}"` : ''}
                                ${field.default ? `value="${field.default}"` : ''}
                                autocomplete="off"
                            >
                        </div>
                        <div class="field-help">${field.helpText}</div>
                    </div>
                `;
            case 'number':
                return `
                    <div class="form-field">
                        <label class="field-label">
                            <span class="label-text">${field.label}</span>
                            ${field.required ? '<span class="label-required">*</span>' : '<span class="label-optional">Optional</span>'}
                        </label>
                        <div class="field-input-wrapper">
                            <div class="input-icon">
                                <i data-lucide="hash" class="input-icon-svg"></i>
                            </div>
                            <input 
                                type="number" 
                                class="field-input" 
                                id="${field.id}"
                                placeholder="${field.placeholder}"
                                ${field.required ? 'required' : ''}
                                ${field.min ? `min="${field.min}"` : ''}
                                ${field.max ? `max="${field.max}"` : ''}
                                ${field.default ? `value="${field.default}"` : ''}
                            >
                        </div>
                        <div class="field-help">${field.helpText}</div>
                    </div>
                `;
            case 'textarea':
                return `
                    <div class="form-field">
                        <label class="field-label">
                            <span class="label-text">${field.label}</span>
                            ${field.required ? '<span class="label-required">*</span>' : '<span class="label-optional">Optional</span>'}
                        </label>
                        <div class="field-input-wrapper">
                            <textarea 
                                class="field-textarea" 
                                id="${field.id}"
                                placeholder="${field.placeholder}"
                                rows="4"
                                ${field.required ? 'required' : ''}
                            ></textarea>
                        </div>
                        <div class="field-help">${field.helpText}</div>
                    </div>
                `;
            default:
                return '';
        }
    }).join('');

    // Generate complete tool HTML
    return `
        <!-- Tool Introduction Section -->
        <div class="tool-intro-section">
            <div class="tool-hero">
                <div class="tool-hero-content">
                    <div class="tool-header">
                        <div class="tool-icon-wrapper">
                            <i data-lucide="${config.icon}" class="tool-icon"></i>
                        </div>
                        <div class="tool-info">
                            <h1 class="tool-title">${config.title}</h1>
                            <p class="tool-subtitle">${config.subtitle}</p>
                            <div class="tool-badges">
                                <span class="tool-badge" data-badge-type="agents">${config.agentCount} AI Agents</span>
                                <span class="tool-badge" data-badge-type="time">~${config.analysisTime} Analysis</span>
                                <span class="tool-badge" data-badge-type="enterprise">Enterprise Grade</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Capabilities Grid -->
        <div class="capabilities-section">
            <div class="capabilities-container">
                <div class="capabilities-header">
                    <h2 class="capabilities-title">What This Tool Delivers</h2>
                    <p class="capabilities-subtitle">Professional-grade analysis powered by specialized AI agents</p>
                </div>
                <div class="capabilities-grid">
                    ${capabilitiesHTML}
                </div>
            </div>
        </div>

        <!-- Professional Analysis Form -->
        <div class="analysis-form-section">
            <div class="analysis-form-container">
                <div class="form-header">
                    <div class="form-header-content">
                        <h3 class="form-title">Start Analysis</h3>
                        <div class="form-status">
                            <div class="status-indicator ready">
                                <div class="status-dot"></div>
                                <span class="status-text">Ready</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-content">
                    <form class="professional-form" id="analysisForm">
                        ${formFieldsHTML}
                        <div class="form-actions">
                            <button type="submit" class="btn-analyze" id="analyzeBtn">
                                <div class="btn-content">
                                    <i data-lucide="play" class="btn-icon"></i>
                                    <span class="btn-text">${config.analyzeButtonText}</span>
                                </div>
                                <div class="btn-loading" style="display: none;">
                                    <div class="loading-spinner"></div>
                                    <span class="loading-text">Analyzing...</span>
                                </div>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Progress Indicator (shown during analysis) -->
        <div class="analysis-progress" id="analysisProgress" style="display: none;">
            <div class="progress-container">
                <div class="progress-header">
                    <h3 class="progress-title">Analysis in Progress</h3>
                    <div class="progress-time" id="progressTime">Estimated: ${config.analysisTime}</div>
                </div>
                <div class="progress-steps">
                    <div class="progress-step active" data-step="1">
                        <div class="step-icon">
                            <i data-lucide="download" class="step-icon-svg"></i>
                        </div>
                        <div class="step-content">
                            <div class="step-title">Data Collection</div>
                            <div class="step-status">Fetching video data...</div>
                        </div>
                    </div>
                    <div class="progress-step" data-step="2">
                        <div class="step-icon">
                            <i data-lucide="brain" class="step-icon-svg"></i>
                        </div>
                        <div class="step-content">
                            <div class="step-title">AI Analysis</div>
                            <div class="step-status">Running ${config.agentCount} specialized agents...</div>
                        </div>
                    </div>
                    <div class="progress-step" data-step="3">
                        <div class="step-icon">
                            <i data-lucide="file-text" class="step-icon-svg"></i>
                        </div>
                        <div class="step-content">
                            <div class="step-title">Report Generation</div>
                            <div class="step-status">Compiling insights...</div>
                        </div>
                    </div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
            </div>
        </div>
    `;
}

// Export for use in templates
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { TOOL_CONFIGS, generateToolHTML };
}
