#!/usr/bin/env python3
"""
Test script for the enhanced 6-agent Writers' Room with Emotional Arc Specialist
"""

import os
import sys
import time
from pathlib import Path

# Add the writers_room directory to the path
writers_room_path = Path(__file__).parent.parent
sys.path.insert(0, str(writers_room_path))

from core_system.real_autogen_writers_room import RealAutoGenWritersRoom, WritersRoomInput


def test_6_agent_writers_room():
    """Test the enhanced Writers' Room with Emotional Arc Specialist"""
    
    print("🎭 TESTING 6-AGENT WRITERS' ROOM WITH EMOTIONAL ARC SPECIALIST")
    print("=" * 80)
    
    # Test input - emotionally charged topic
    input_data = WritersRoomInput(
        topic="The 30-Second Phone Call That Prevented Nuclear War",
        golden_nuggets=[
            "On September 26, 1983, Soviet officer <PERSON><PERSON> prevented World War III",
            "A computer glitch showed 5 incoming US missiles - but <PERSON><PERSON> didn't report it",
            "He had 30 seconds to decide the fate of humanity",
            "His gut feeling saved 6 billion lives",
            "The Soviet system wanted him to launch immediate nuclear retaliation",
            "He was later fired and lived in poverty, unrecognized for decades"
        ],
        persona={
            "channel": "Crisis Moments",
            "voice": "Intense documentary narrator",
            "focus": "Split-second decisions that changed history"
        },
        quality_threshold=8.5,  # High threshold to test emotional quality
        max_rounds=30  # Reduced for testing
    )
    
    print(f"📍 Topic: {input_data.topic}")
    print(f"💎 Golden Nuggets: {len(input_data.golden_nuggets)}")
    print(f"🎯 Quality Threshold: {input_data.quality_threshold}/10")
    print(f"🎭 NEW: Emotional Arc Specialist integrated!")
    print("=" * 80)
    
    try:
        # Initialize 6-agent system
        writers_room = RealAutoGenWritersRoom(model="gpt-4")
        
        # Verify all 6 agents exist
        expected_agents = ['director', 'hooksmith', 'story_weaver', 'closer', 'emotional_architect', 'producer']
        print(f"\n🤖 AGENT VERIFICATION:")
        for agent_name in expected_agents:
            if agent_name in writers_room.agents:
                print(f"✅ {agent_name.replace('_', ' ').title()}: Ready")
            else:
                print(f"❌ {agent_name.replace('_', ' ').title()}: Missing!")
                return False
        
        print(f"\n🎬 STARTING 6-AGENT COLLABORATION...")
        start_time = time.time()
        
        # Run collaborative session
        output = writers_room.run_collaborative_session(input_data)
        
        # Display results
        print(f"\n🎯 COLLABORATION RESULTS:")
        print(f"⏱️ Total Time: {output.execution_time:.1f} seconds")
        print(f"🔄 Total Rounds: {output.collaboration_metrics.total_rounds}")
        print(f"❌ Rejections: {output.collaboration_metrics.rejections}")
        print(f"🔄 Revisions: {output.collaboration_metrics.revisions}")
        print(f"🏆 Final Quality: {output.collaboration_metrics.final_quality_score:.1f}/10")
        
        # Check if emotional architect contributed
        emotional_contributions = 0
        for msg in output.conversation_transcript:
            if msg.get('name') == 'EmotionalArchitect':
                emotional_contributions += 1
        
        print(f"🎭 Emotional Architect Contributions: {emotional_contributions}")
        
        # Save output
        output_dir = writers_room_path / "outputs" / "6_agent_tests"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        output_file = output_dir / "nuclear_war_prevention_6_agents.json"
        writers_room.save_output(output, str(output_file))
        
        # Success criteria
        success = (
            output.collaboration_metrics.final_quality_score >= input_data.quality_threshold and
            emotional_contributions > 0 and
            len(output.final_script) > 1000  # Substantial script
        )
        
        if success:
            print(f"\n✅ 6-AGENT SYSTEM TEST: SUCCESS!")
            print(f"🎭 Emotional Arc Specialist successfully integrated!")
            print(f"📊 Quality threshold met: {output.collaboration_metrics.final_quality_score:.1f}/10")
            print(f"💬 Emotional contributions: {emotional_contributions}")
            
            # Show snippet of final script
            script_preview = output.final_script[:300] + "..." if len(output.final_script) > 300 else output.final_script
            print(f"\n📜 SCRIPT PREVIEW:")
            print(f"{script_preview}")
            
        else:
            print(f"\n❌ 6-AGENT SYSTEM TEST: FAILED")
            print(f"Quality: {output.collaboration_metrics.final_quality_score:.1f} (needed {input_data.quality_threshold})")
            print(f"Emotional contributions: {emotional_contributions}")
            print(f"Script length: {len(output.final_script)}")
        
        return success
        
    except Exception as e:
        print(f"\n💥 ERROR: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def quick_agent_check():
    """Quick check that all 6 agents are properly configured"""
    
    print("🔍 QUICK AGENT CONFIGURATION CHECK")
    print("-" * 40)
    
    try:
        writers_room = RealAutoGenWritersRoom(model="gpt-4")
        
        expected_agents = {
            'director': "VIRAL STRUCTURE DIRECTOR",
            'hooksmith': "VIRAL HOOK SPECIALIST", 
            'story_weaver': "NARRATIVE ADDICTION SPECIALIST",
            'closer': "VIRAL ENDING SPECIALIST",
            'emotional_architect': "EMOTIONAL MANIPULATION SPECIALIST",
            'producer': "PRODUCTION REALITY CONTROLLER"
        }
        
        all_good = True
        for agent_key, expected_role in expected_agents.items():
            if agent_key in writers_room.agents:
                agent = writers_room.agents[agent_key]
                if expected_role in agent.system_message:
                    print(f"✅ {agent_key}: {expected_role}")
                else:
                    print(f"⚠️ {agent_key}: Configuration issue")
                    all_good = False
            else:
                print(f"❌ {agent_key}: Missing")
                all_good = False
        
        if all_good:
            print("\n✅ All 6 agents properly configured!")
        else:
            print("\n❌ Agent configuration issues detected")
            
        return all_good
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False


if __name__ == "__main__":
    print("🎭 6-AGENT WRITERS' ROOM TEST SUITE")
    print("=" * 50)
    
    # Check if OpenAI API key is available
    if not os.environ.get("OPENAI_API_KEY"):
        print("❌ OPENAI_API_KEY not found in environment")
        print("Set your API key: export OPENAI_API_KEY='your-key-here'")
        exit(1)
    
    # Quick configuration check
    print("\n1️⃣ AGENT CONFIGURATION CHECK")
    config_ok = quick_agent_check()
    
    if config_ok:
        print("\n2️⃣ FULL 6-AGENT COLLABORATION TEST")
        test_success = test_6_agent_writers_room()
        
        if test_success:
            print("\n🎉 ALL TESTS PASSED!")
            print("🎭 Emotional Arc Specialist successfully integrated!")
        else:
            print("\n❌ Tests failed - check logs above")
    else:
        print("\n❌ Configuration check failed - skipping collaboration test")