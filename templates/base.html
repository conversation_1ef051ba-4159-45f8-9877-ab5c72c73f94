<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}YouTube Research v2{% endblock %}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/static/favicon.svg">
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    
    <!-- YouTube Research v2 Design System -->
    <link rel="stylesheet" href="/static/styles/youtube-research-v2-design-system.css?v=2025080301">
    
    <!-- Enhanced Theme Toggle System -->
    <script src="/static/js/theme-toggle.js" defer></script>
    
    <!-- Analytics & Metrics Tracking -->
    <meta name="mixpanel-token" content="{{ MIXPANEL_TOKEN or '' }}">
    <script src="/static/js/analytics-tracker.js" defer></script>
    
    {% block extra_head %}{% endblock %}
</head>
<body>
    <div class="app-layout">
        <!-- Premium Sidebar -->
        <aside class="app-sidebar" id="sidebar">
            <!-- Sidebar Header -->
            <div class="sidebar-header">
                <a href="/" class="sidebar-logo">
                    <div class="sidebar-logo-icon">{% block logo_icon %}YR{% endblock %}</div>
                    <span class="sidebar-logo-text">{% block logo_text %}YouTube Research{% endblock %}</span>
                </a>
                <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                    <i data-lucide="sun" class="theme-icon-light"></i>
                    <i data-lucide="moon" class="theme-icon-dark" style="display: none;"></i>
                </button>
            </div>

            <!-- Navigation Menu -->
            <nav class="sidebar-nav">
                <!-- Overview Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Overview</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/" class="nav-item{% if request.url.path == '/' %} active{% endif %}" data-page="dashboard" data-track='{"feature_name": "dashboard", "action": "dashboard_viewed"}'>
                                <i data-lucide="layout-dashboard" class="nav-icon"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>
                        <li>
                            <a href="/analytics" class="nav-item{% if request.url.path == '/analytics' %} active{% endif %}" data-page="analytics">
                                <i data-lucide="bar-chart-3" class="nav-icon"></i>
                                <span>Analytics</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Analysis Tools Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Analysis Tools</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/video-analyzer" class="nav-item{% if request.url.path == '/video-analyzer' %} active{% endif %}" data-page="video-analyzer">
                                <i data-lucide="video" class="nav-icon"></i>
                                <span>Video Analyzer</span>
                                <span class="nav-badge">6 AI</span>
                            </a>
                        </li>
                        <li>
                            <a href="/channel-analyzer" class="nav-item{% if request.url.path == '/channel-analyzer' %} active{% endif %}" data-page="channel-analyzer">
                                <i data-lucide="users" class="nav-icon"></i>
                                <span>Channel Analyzer</span>
                                <span class="nav-badge">Pro</span>
                            </a>
                        </li>
                        <li>
                            <a href="/market-research" class="nav-item{% if request.url.path == '/market-research' %} active{% endif %}" data-page="market-research">
                                <i data-lucide="trending-up" class="nav-icon"></i>
                                <span>Market Research</span>
                                <span class="nav-badge">New</span>
                            </a>
                        </li>
                        <li>
                            <a href="/agent/comment" class="nav-item{% if request.url.path == '/agent/comment' %} active{% endif %}" data-page="comment-intelligence">
                                <i data-lucide="message-circle" class="nav-icon"></i>
                                <span>Comment Intelligence</span>
                                <span class="nav-badge">AI</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Research Tools Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Research Tools</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/competitor-analysis" class="nav-item{% if request.url.path == '/competitor-analysis' %} active{% endif %}" data-page="competitor-analysis">
                                <i data-lucide="target" class="nav-icon"></i>
                                <span>Competitor Analysis</span>
                            </a>
                        </li>
                        <li>
                            <a href="/trend-tracker" class="nav-item{% if request.url.path == '/trend-tracker' %} active{% endif %}" data-page="trend-tracker">
                                <i data-lucide="activity" class="nav-icon"></i>
                                <span>Trend Tracker</span>
                            </a>
                        </li>
                        <li>
                            <a href="/keyword-research" class="nav-item{% if request.url.path == '/keyword-research' %} active{% endif %}" data-page="keyword-research">
                                <i data-lucide="search" class="nav-icon"></i>
                                <span>Keyword Research</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <!-- Settings Section -->
                <div class="nav-section">
                    <h3 class="nav-section-title">Settings</h3>
                    <ul class="nav-items">
                        <li>
                            <a href="/settings" class="nav-item{% if request.url.path == '/settings' %} active{% endif %}" data-page="settings">
                                <i data-lucide="settings" class="nav-icon"></i>
                                <span>Settings</span>
                            </a>
                        </li>
                        <li>
                            <a href="/help" class="nav-item{% if request.url.path == '/help' %} active{% endif %}" data-page="help">
                                <i data-lucide="help-circle" class="nav-icon"></i>
                                <span>Help & Support</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Sidebar Footer -->
            <div class="sidebar-footer">
                <div class="user-profile">
                    <div class="user-avatar">
                        <i data-lucide="user" class="user-icon"></i>
                    </div>
                    <div class="user-info">
                        <div class="user-name">Premium User</div>
                        <div class="user-plan">Pro Plan</div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Page Header -->
            <header class="page-header">
                <div class="page-header-content">
                    <div class="page-title-section">
                        <h1 class="page-title">{% block page_title %}{% endblock %}</h1>
                        <p class="page-subtitle">{% block page_subtitle %}{% endblock %}</p>
                    </div>
                    <div class="page-actions">
                        {% block page_actions %}{% endblock %}
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="page-content">
                {% block content %}{% endblock %}
            </div>
        </main>
    </div>

    <!-- Base JavaScript -->
    <script>
        // Theme Toggle Functionality
        const themeToggle = document.getElementById('themeToggle');
        const html = document.documentElement;
        const lightIcon = themeToggle.querySelector('.theme-icon-light');
        const darkIcon = themeToggle.querySelector('.theme-icon-dark');

        // Load saved theme or default to dark
        const savedTheme = localStorage.getItem('theme') || 'dark';
        html.setAttribute('data-theme', savedTheme);
        updateThemeIcons(savedTheme);

        themeToggle.addEventListener('click', () => {
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcons(newTheme);
        });

        function updateThemeIcons(theme) {
            if (theme === 'dark') {
                lightIcon.style.display = 'block';
                darkIcon.style.display = 'none';
            } else {
                lightIcon.style.display = 'none';
                darkIcon.style.display = 'block';
            }
        }

        // Initialize Lucide Icons
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }

        // Navigation Active State
        document.addEventListener('DOMContentLoaded', function() {
            const currentPath = window.location.pathname;
            const navItems = document.querySelectorAll('.nav-item');
            
            navItems.forEach(item => {
                const href = item.getAttribute('href');
                if (href === currentPath || (currentPath === '/' && href === '/')) {
                    item.classList.add('active');
                } else {
                    item.classList.remove('active');
                }
            });
        });
    </script>

    {% block extra_scripts %}{% endblock %}
</body>
</html>
