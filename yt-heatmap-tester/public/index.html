<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>YouTube Heatmap Tester</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 560px;
        margin: 40px auto;
        padding: 0 20px;
      }
      input {
        width: 100%;
        padding: 8px;
        margin-bottom: 12px;
      }
      pre {
        background: #111;
        color: #0f0;
        padding: 12px;
        overflow-x: auto;
      }
    </style>
  </head>
  <body>
    <h1>YouTube Heat‑Map Tester</h1>
    <p>Paste a YouTube URL or video ID:</p>
    <input id="vid" placeholder="https://www.youtube.com/watch?v=dQw4w9WgXcQ" />
    <button onclick="fetchHeatmap()">Fetch Heatmap</button>
    <h3>Result:</h3>
    <pre id="out">(waiting…)</pre>

    <script>
      async function fetchHeatmap() {
        const v = document.getElementById("vid").value.trim();
        if (!v) return alert("Enter a video URL or ID!");
        document.getElementById("out").textContent = "Fetching…";
        const r = await fetch(`/api/heatmap?v=${encodeURIComponent(v)}`);
        const json = await r.json();
        document.getElementById("out").textContent = JSON.stringify(json, null, 2);
      }
    </script>
  </body>
</html>