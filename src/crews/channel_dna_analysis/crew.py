from crewai import Agent, Crew, Task
from crewai.project import CrewBase, agent, crew, task
import yaml
from pathlib import Path

@CrewBase
class ChannelDNAAnalysisCrew():
    """Channel DNA Analysis Crew for comprehensive YouTube channel analysis"""
    
    agents_config = 'config/channel_dna_analysis/agents.yaml'
    tasks_config = 'config/channel_dna_analysis/tasks.yaml'
    
    @agent
    def channel_profiler(self) -> Agent:
        return Agent(
            config=self.agents_config['channel_profiler'],
            tools=[],  # Add YouTube API tools here
            verbose=True
        )
    
    @agent
    def content_pattern_analyzer(self) -> Agent:
        return Agent(
            config=self.agents_config['content_pattern_analyzer'],
            tools=[],  # Add pattern analysis tools here
            verbose=True
        )
    
    @agent
    def audience_psychologist(self) -> Agent:
        return Agent(
            config=self.agents_config['audience_psychologist'],
            tools=[],  # Add sentiment analysis tools here
            verbose=True
        )
    
    @agent
    def performance_analyst(self) -> Agent:
        return Agent(
            config=self.agents_config['performance_analyst'],
            tools=[],  # Add analytics tools here
            verbose=True
        )
    
    @agent
    def competitor_analyst(self) -> Agent:
        return Agent(
            config=self.agents_config['competitor_analyst'],
            tools=[],  # Add competitive analysis tools here
            verbose=True
        )
    
    @task
    def extract_channel_profile_task(self) -> Task:
        return Task(
            config=self.tasks_config['extract_channel_profile'],
            agent=self.channel_profiler()
        )
    
    @task
    def analyze_content_patterns_task(self) -> Task:
        return Task(
            config=self.tasks_config['analyze_content_patterns'],
            agent=self.content_pattern_analyzer()
        )
    
    @task
    def analyze_audience_psychology_task(self) -> Task:
        return Task(
            config=self.tasks_config['analyze_audience_psychology'],
            agent=self.audience_psychologist()
        )
    
    @task
    def evaluate_performance_metrics_task(self) -> Task:
        return Task(
            config=self.tasks_config['evaluate_performance_metrics'],
            agent=self.performance_analyst()
        )
    
    @task
    def assess_competitive_position_task(self) -> Task:
        return Task(
            config=self.tasks_config['assess_competitive_position'],
            agent=self.competitor_analyst()
        )
    
    @crew
    def crew(self) -> Crew:
        """Creates the Channel DNA Analysis crew"""
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True
        )