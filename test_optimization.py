#!/usr/bin/env python3
"""
YouTube Research v2 - Optimization Testing Protocol
Tests core functionality after each optimization change
"""

import requests
import time
import subprocess
import sys
import os
import signal
from datetime import datetime

class OptimizationTester:
    def __init__(self):
        self.base_url = "http://localhost:8003"
        self.server_process = None
        self.endpoints = [
            ("/", "Dashboard"),
            ("/video-analyzer", "Video Analyzer"),
            ("/channel-analyzer", "Channel Analyzer"),
            ("/market-research", "Market Research Hub"),
            ("/agent/comment", "Comment Intelligence")
        ]
    
    def start_server(self, timeout=30):
        """Start the main application server"""
        print("🚀 Starting server...")
        try:
            # Start server in background
            self.server_process = subprocess.Popen(
                [sys.executable, "working_crewai_app_tabbed.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )
            
            # Wait for server to be ready
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=5)
                    if response.status_code == 200:
                        print("✅ Server started successfully")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)
                    continue
            
            print("❌ Server failed to start within timeout")
            return False
            
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return False
    
    def stop_server(self):
        """Stop the server process"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                print("🛑 Server stopped")
            except subprocess.TimeoutExpired:
                self.server_process.kill()
                print("🛑 Server force killed")
            except Exception as e:
                print(f"⚠️ Error stopping server: {e}")
    
    def test_endpoint(self, endpoint, name):
        """Test a single endpoint"""
        try:
            response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
            success = response.status_code == 200
            
            result = {
                'endpoint': endpoint,
                'name': name,
                'status_code': response.status_code,
                'success': success,
                'response_size': len(response.content),
                'response_time': response.elapsed.total_seconds()
            }
            
            if success:
                # Check for basic HTML structure
                content = response.text.lower()
                has_html = '<html' in content and '</html>' in content
                has_css = 'youtube-research-v2-design-system.css' in content
                
                result['has_html_structure'] = has_html
                result['has_design_system'] = has_css
                
                if not has_html:
                    result['success'] = False
                    result['error'] = 'Missing HTML structure'
                elif not has_css:
                    result['success'] = False
                    result['error'] = 'Missing design system CSS'
            
            return result
            
        except requests.exceptions.RequestException as e:
            return {
                'endpoint': endpoint,
                'name': name,
                'success': False,
                'error': str(e)
            }
    
    def test_all_endpoints(self):
        """Test all critical endpoints"""
        print("🧪 Testing all endpoints...")
        results = []
        
        for endpoint, name in self.endpoints:
            print(f"   Testing {name}...")
            result = self.test_endpoint(endpoint, name)
            results.append(result)
            
            status = "✅" if result['success'] else "❌"
            if result['success']:
                print(f"   {status} {name}: {result['status_code']} ({result['response_size']} bytes)")
            else:
                error = result.get('error', 'Unknown error')
                print(f"   {status} {name}: {error}")
        
        return results
    
    def test_css_loading(self):
        """Test that CSS design system is loading properly"""
        print("🎨 Testing CSS design system...")
        try:
            css_url = f"{self.base_url}/static/styles/youtube-research-v2-design-system.css"
            response = requests.get(css_url, timeout=10)
            
            if response.status_code == 200:
                css_size = len(response.content)
                print(f"✅ Design system CSS loaded ({css_size} bytes)")
                return True
            else:
                print(f"❌ CSS failed to load: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ CSS loading error: {e}")
            return False
    
    def run_full_test_suite(self):
        """Run complete test suite"""
        print("=" * 60)
        print("🧪 YouTube Research v2 - Optimization Test Suite")
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # Start server
        if not self.start_server():
            print("❌ Cannot run tests - server failed to start")
            return False
        
        try:
            # Test endpoints
            endpoint_results = self.test_all_endpoints()
            
            # Test CSS loading
            css_result = self.test_css_loading()
            
            # Calculate overall success
            endpoint_success = all(r['success'] for r in endpoint_results)
            overall_success = endpoint_success and css_result
            
            # Report summary
            print("\n" + "=" * 60)
            print("📊 TEST SUMMARY")
            print("=" * 60)
            
            passed = sum(1 for r in endpoint_results if r['success'])
            total = len(endpoint_results)
            
            print(f"Endpoints: {passed}/{total} passed")
            print(f"CSS Loading: {'✅ PASS' if css_result else '❌ FAIL'}")
            print(f"Overall: {'✅ ALL TESTS PASSED' if overall_success else '❌ SOME TESTS FAILED'}")
            
            if not overall_success:
                print("\n🚨 FAILED TESTS:")
                for result in endpoint_results:
                    if not result['success']:
                        error = result.get('error', 'Unknown error')
                        print(f"   - {result['name']}: {error}")
            
            return overall_success
            
        finally:
            self.stop_server()
    
    def quick_test(self):
        """Quick test for rapid validation during optimization"""
        print("⚡ Quick validation test...")
        
        if not self.start_server(timeout=15):
            return False
        
        try:
            # Test just the dashboard
            result = self.test_endpoint("/", "Dashboard")
            success = result['success']
            
            status = "✅ PASS" if success else "❌ FAIL"
            print(f"   Dashboard: {status}")
            
            return success
            
        finally:
            self.stop_server()

def main():
    """Main test runner"""
    tester = OptimizationTester()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        success = tester.quick_test()
    else:
        success = tester.run_full_test_suite()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
