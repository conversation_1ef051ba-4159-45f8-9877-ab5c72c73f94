#!/usr/bin/env python3
"""
Test Layout Spacing Improvements
"""

import requests
import time
import subprocess
import sys
import os
from datetime import datetime

class LayoutSpacingTester:
    def __init__(self):
        self.base_url = "http://localhost:8003"
        self.server_process = None
        self.test_pages = [
            ("/", "Dashboard"),
            ("/video-analyzer", "Video Analyzer"),
            ("/channel-analyzer", "Channel Analyzer"),
            ("/market-research", "Market Research Hub"),
            ("/agent/comment", "Comment Intelligence")
        ]
    
    def start_server(self, timeout=30):
        """Start the main application server"""
        print("🚀 Starting server...")
        try:
            self.server_process = subprocess.Popen(
                [sys.executable, "working_crewai_app_tabbed.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=5)
                    if response.status_code == 200:
                        print("✅ Server started successfully")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)
                    continue
            
            print("❌ Server failed to start within timeout")
            return False
            
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return False
    
    def stop_server(self):
        """Stop the server"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                print("🛑 Server stopped")
            except subprocess.TimeoutExpired:
                self.server_process.kill()
                print("🛑 Server force killed")
            except Exception as e:
                print(f"⚠️ Error stopping server: {e}")
    
    def test_css_spacing_classes(self):
        """Test if the CSS file contains proper spacing classes"""
        print("\n🎨 Testing CSS Spacing Classes...")
        try:
            response = requests.get(f"{self.base_url}/static/styles/youtube-research-v2-design-system.css", timeout=10)
            if response.status_code == 200:
                print("✅ CSS file is accessible")
                
                required_classes = [
                    '.premium-content',
                    '.analyzer-form-section',
                    'padding: var(--space-2xl)',
                    'margin-bottom: var(--space-2xl)'
                ]
                
                css_content = response.text
                missing_classes = []
                
                for css_class in required_classes:
                    if css_class not in css_content:
                        missing_classes.append(css_class)
                
                if missing_classes:
                    print(f"❌ Missing CSS spacing rules: {missing_classes}")
                    return False
                else:
                    print("✅ All required spacing CSS rules found")
                    return True
            else:
                print(f"❌ CSS file not accessible: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error accessing CSS file: {e}")
            return False
    
    def test_page_layout_structure(self):
        """Test if pages have proper layout structure"""
        print("\n📄 Testing Page Layout Structure...")
        
        all_pages_pass = True
        
        for url, name in self.test_pages:
            try:
                response = requests.get(f"{self.base_url}{url}", timeout=10)
                if response.status_code == 200:
                    html_content = response.text
                    
                    # Check for layout classes
                    layout_classes = [
                        'app-layout',
                        'app-main',
                        'premium-content'
                    ]
                    
                    missing_classes = []
                    for layout_class in layout_classes:
                        if layout_class not in html_content:
                            missing_classes.append(layout_class)
                    
                    if missing_classes:
                        print(f"   ❌ {name}: Missing layout classes: {missing_classes}")
                        all_pages_pass = False
                    else:
                        print(f"   ✅ {name}: All layout classes present")
                else:
                    print(f"   ❌ {name}: Failed to load (status: {response.status_code})")
                    all_pages_pass = False
                    
            except Exception as e:
                print(f"   ❌ {name}: Error loading page: {e}")
                all_pages_pass = False
        
        return all_pages_pass
    
    def run_tests(self):
        """Run all tests"""
        print("🧪 Layout Spacing Test")
        print("=" * 50)
        
        # Start server
        if not self.start_server():
            return False
        
        try:
            # Test CSS spacing classes
            css_test = self.test_css_spacing_classes()
            
            # Test page layout structure
            layout_test = self.test_page_layout_structure()
            
            # Summary
            print(f"\n📊 Test Results:")
            print(f"   CSS Spacing Classes: {'✅ PASS' if css_test else '❌ FAIL'}")
            print(f"   Page Layout Structure: {'✅ PASS' if layout_test else '❌ FAIL'}")
            
            if css_test and layout_test:
                print(f"\n🎉 All tests passed! Layout spacing is working correctly.")
                print(f"   ✨ Professional spacing between sidebar and main content")
                print(f"   ✨ Proper padding in content areas")
                print(f"   ✨ Consistent spacing across all pages")
                return True
            else:
                print(f"\n⚠️ Some tests failed. Layout spacing needs attention.")
                return False
                
        finally:
            self.stop_server()

if __name__ == "__main__":
    tester = LayoutSpacingTester()
    success = tester.run_tests()
    sys.exit(0 if success else 1)
