#!/usr/bin/env python3
"""
Rome vs Carthage - Real AI Writers' Room Test
Uses actual CrewAI agents with RLL DNA Edition prompts
"""

import os
import sys
from datetime import datetime
import json

# Add parent directories to Python path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
grandparent_dir = os.path.dirname(parent_dir)
sys.path.extend([current_dir, parent_dir, grandparent_dir])

try:
    from crewai import Agent, Task, Crew
    from litellm import LLM
    print("✅ CrewAI imports successful")
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure CrewAI is installed: pip install crewai")
    sys.exit(1)

class RealWritersRoom:
    """Real AI-powered Writers' Room using CrewAI agents"""
    
    def __init__(self):
        # Initialize LLM (using same config as main system)
        self.llm = LLM(model="gemini/gemini-2.5-flash")
        print("✅ LLM initialized: Gemini 2.5 Flash")
    
    def create_director_agent(self):
        """Create the Director agent with RLL DNA prompts"""
        return Agent(
            role="Director/Architect - Master Orchestrator",
            goal="Find the ONE geographic question that anchors the entire video and create the 5-beat structure",
            backstory="""You've studied RealLifeLore's success and understand that complexity must be hidden behind simplicity. You know that viewers don't care about empires - they care about shocking geographic facts that explain why the world works the way it does. Your gift is finding the 'WTF Geography' moment in any historical topic.""",
            llm=self.llm,
            verbose=True
        )
    
    def create_hooksmith_agent(self):
        """Create the Hooksmith agent with RLL DNA prompts"""
        return Agent(
            role="Hooksmith - Master of the First 30 Seconds",
            goal="Create 3 hook variations that make history irresistible to people who think they hate history",
            backstory="""You've analyzed thousands of viral videos and know that ancient names and dates are instant scroll-aways. Your specialty is using modern comparisons and simple language to make 2000-year-old events feel like breaking news. You think in TikTok clips, not textbook chapters.""",
            llm=self.llm,
            verbose=True
        )
    
    def create_story_weaver_agent(self):
        """Create the Story-Weaver agent with RLL DNA prompts"""
        return Agent(
            role="Story-Weaver - Narrative Architect",
            goal="Write narrative sections that make geographic facts feel like thriller plot twists",
            backstory="""You understand that RLL's power comes from relentless clarity and efficiency. Every sentence either delivers new information or connects to the geographic anchor. But you also know Frontier's edge: showing how geography forced impossible human choices. You're basically writing geographic horror stories.""",
            llm=self.llm,
            verbose=True
        )
    
    def create_sensory_director_agent(self):
        """Create the Sensory Director agent with RLL DNA prompts"""
        return Agent(
            role="Sensory Director - Visual and Vocal Choreographer",
            goal="Add vocal performance and visual cues that maintain 145-155 WPM pace with strategic variations",
            backstory="""You know RLL's clean, modern aesthetic works for contemporary topics, but historical content needs texture. Your job is to make ancient maps feel as compelling as satellite imagery. You also understand that the 'Awestruck Strategist' voice needs subtle shifts from RLL's neutral tone.""",
            llm=self.llm,
            verbose=True
        )
    
    def create_closer_agent(self):
        """Create The Closer agent with RLL DNA prompts"""
        return Agent(
            role="The Closer - Master of Modern Relevance",
            goal="Make ancient geography urgently modern and build Frontier's strategic community",
            backstory="""You understand that Frontier's superpower is making ancient strategic patterns feel like urgent current events. Your job isn't just to end videos - it's to make viewers see the ancient world in every modern headline. You know that the best historical education happens when people realize the past never ended.""",
            llm=self.llm,
            verbose=True
        )
    
    def run_rome_carthage_analysis(self):
        """Run the complete Rome vs Carthage analysis"""
        
        topic = "Rome vs Carthage - The 90-Mile Strait That Decided Two Empires"
        golden_nuggets = [
            "Hannibal lost 50% of his army (20,000 men) crossing the Alps in 15 days - not from Roman attacks, but from a 70-mile mountain path",
            "Rome built 120 warships in 60 days by reverse-engineering a single shipwrecked Carthaginian vessel - then added a boarding bridge that turned sea battles into land battles",
            "The strait between Sicily and Carthage is only 90 miles wide - controlling it meant controlling 80% of Mediterranean trade",
            "Carthage's harbor could hold 220 warships in covered docks invisible from the sea - the ancient equivalent of a hidden nuclear submarine base",
            "After defeating Carthage, Rome insisted on moving the entire city 10 miles inland - because geography is power, and coastlines create navies"
        ]
        
        print(f"\n🏛️ STARTING ROME VS CARTHAGE ANALYSIS")
        print(f"📝 Topic: {topic}")
        print(f"💎 Golden Nuggets: {len(golden_nuggets)}")
        
        # PHASE 1: Director creates structure
        print(f"\n🎯 PHASE 1: DIRECTOR - STRUCTURAL FOUNDATION")
        director = self.create_director_agent()
        director_task = Task(
            description=f"""You are the Master Orchestrator of Frontier channel, responsible for finding the ONE geographic question that will anchor the entire video about {topic}.

CRITICAL MISSION: Find the ONE simple geographic question that would shock a modern viewer about Rome vs Carthage.

Think like this: "What geographic fact would make someone stop mid-scroll and say 'No way that's real'?"

Examples of PERFECT geographic anchors:
- "Why did 50,000 Romans die trying to cross this 3-mile valley?"
- "How did controlling this river make one city richer than all of Europe?"
- "Why did every empire that conquered this desert collapse within 50 years?"

BAD anchors (too complex/academic):
- "The sociopolitical implications of Mesopotamian geography"
- "How the Sasanids leveraged terrain advantages"

Your 5-beat structure for {topic}:
1. HOOK (0:00-0:30): The geographic WTF moment
2. SETUP (0:30-3:00): Why this geography matters (modern comparison)
3. ESCALATION (3:00-8:00): How geography created an impossible problem
4. HUMAN DRAMA (8:00-12:00): Strategic decisions constrained by geography
5. PAYOFF (12:00-end): The geographic punchline + modern parallel

Golden nuggets to weave in: {golden_nuggets}

Success test: If a 16-year-old wouldn't text this fact to a friend, start over.

Return structure with geographic anchor clearly identified.""",
            agent=director,
            expected_output="5-beat structure with clear geographic anchor and timing breakdown"
        )
        
        # PHASE 2: Hooksmith creates hooks
        print(f"\n🎯 PHASE 2: HOOKSMITH - HOOK CREATION")
        hooksmith = self.create_hooksmith_agent()
        hooksmith_task = Task(
            description=f"""You are the Master of the First 30 Seconds, responsible for creating hooks that make Rome vs Carthage irresistible to people who think they hate history.

CRITICAL MISSION: Create 3 hook variations for {topic} (20-30 seconds each).

Your formula: [Modern relatable thing] + [Shocking number] + [Geographic twist] = Hook

PERFECT HOOKS:
- "This mountain pass generated more wealth than Apple and Microsoft combined. And it's only 3 miles wide."
- "Every iPhone in the world travels through this strait. The Romans killed 100,000 people trying to control the ancient version."
- "This desert is smaller than Texas. It's also destroyed more empires than any battlefield in history."

TERRIBLE HOOKS (NEVER DO THIS):
- "In 264 BCE, Rome faced Carthage at Sicily..."
- "The geopolitical significance of the Mediterranean..."
- "Let me tell you about the Punic Wars..."

REQUIREMENTS:
1. Start with modern comparison or shocking statistic
2. No ancient names/dates in first sentence
3. Build to the geographic revelation
4. Make it shareable - would someone screenshot this?
5. Use these golden nuggets: {golden_nuggets}

Create 3 variations:
1. Primary hook (most compelling)
2. Alternative hook (different angle)
3. Backup hook (simpler approach)

Test: Would a teenager share this? Would Joe Rogan say "Jamie, pull that up"?""",
            agent=hooksmith,
            expected_output="3 hook variations with timing and shareability focus"
        )
        
        # PHASE 3: Story-Weaver creates narrative
        print(f"\n🎯 PHASE 3: STORY-WEAVER - NARRATIVE ARCHITECTURE")
        story_weaver = self.create_story_weaver_agent()
        story_weaver_task = Task(
            description=f"""You are the Narrative Architect who makes geographic facts feel like thriller plot twists for {topic}.

CRITICAL MISSION: Write narrative sections that always return to the map.

Your narrative technique:
- Geography states the problem: "The strait was only 90 miles wide"
- Logistics make it worse: "Meaning whoever controlled it controlled 80% of trade"
- Human drama emerges: "Both empires had to choose: control the strait or die"
- Modern parallel: "Like trying to control the Suez Canal today"

KEY PHRASES THAT WORK:
- "The geography said no, but they tried anyway"
- "The map made the choice for them"
- "This is where logistics becomes destiny"
- "To understand why this mattered, look at the map"
- "The distance that broke empires"

REQUIREMENTS:
1. Write narrative sections that always return to the map
2. Use "To understand why..." transitions for context zooms
3. Information payoff every 20-30 seconds
4. Make geography the villain, humans the trapped protagonists
5. Use modern analogies to make ancient logistics relatable
6. Integrate these golden nuggets: {golden_nuggets}

AVOID:
- Long character backstories
- Political complexity without geographic relevance
- Any sentence that doesn't relate to the map

Write core narrative following geographic anchor structure.""",
            agent=story_weaver,
            expected_output="Geographic-focused narrative with modern analogies"
        )
        
        # PHASE 4: Sensory Director adds annotations
        print(f"\n🎯 PHASE 4: SENSORY DIRECTOR - VISUAL & VOCAL CHOREOGRAPHY")
        sensory_director = self.create_sensory_director_agent()
        sensory_director_task = Task(
            description=f"""You are the Visual and Vocal Choreographer, ensuring every second reinforces Frontier's identity as "historical RealLifeLore" for {topic}.

CRITICAL MISSION: Add vocal performance and visual cues that maintain 145-155 WPM pace with strategic variations.

VOCAL CUES (Frontier-specific):
- [TONE: Strategic] - When revealing geographic constraints
- [TONE: Inevitable] - When geography determines outcome
- [WONDER] - On specific words that reveal scale
- [PAUSE: Let it sink in] - After WTF geographic facts
- [PACE: Building tension] - During impossible choices
- [EMPHASIS: word] - Key geographic terms

VISUAL CUES (Your unique "historical RLL" style):
- [MAP: Parchment-style elevation view]
- [ANIMATE: Army movements with supply line strain]
- [OVERLAY: Modern city for scale comparison]
- [TRANSITION: Zoom from empire scale to strait width]
- [VISUAL: "Cinematic oil painting" of strategic moment]
- [SATELLITE: Modern view of same geographic features]

THE RULE: 70% maps, 20% strategic visualizations, 10% atmospheric art

REQUIREMENTS:
1. Mark visual cues every 20-30 seconds (map-focused)
2. Design the "historical RLL" aesthetic
3. Maintain pacing that emphasizes strategic drama
4. Never use character portraits or political ceremonies
5. Everything must relate to geography

Use golden nuggets: {golden_nuggets}

AVOID:
- Character portraits
- Political ceremonies  
- Anything not geographic""",
            agent=sensory_director,
            expected_output="Script with vocal and visual annotations for historical RLL style"
        )
        
        # PHASE 5: The Closer creates ending
        print(f"\n🎯 PHASE 5: THE CLOSER - MODERN RELEVANCE")
        closer = self.create_closer_agent()
        closer_task = Task(
            description=f"""You are the Master of Modern Relevance, responsible for making Rome vs Carthage urgently modern and building Frontier's strategic community.

CRITICAL MISSION: End {topic} by proving that geographic lessons from the past are MORE relevant today, not less.

Your closing formula: [Ancient geographic lesson] + [Modern parallel that's BETTER] + [Community debate starter] = Engaged audience

PERFECT CLOSERS:
- "Rome controlled the Mediterranean with 120 ships. Today, China's building 400 vessels to control the South China Sea. Which chokepoints matter most now?"
- "Carthage's hidden harbor gave them naval supremacy for 300 years. Now submarine bases are invisible under Arctic ice. Where are tomorrow's surprise attacks coming from?"
- "The 90-mile strait between Sicily and Carthage controlled ancient trade. Three underwater cables carry 95% of internet traffic. What happens when someone cuts them?"

TERRIBLE CLOSERS (AVOID):
- "So that's the story of Rome and Carthage..."
- "History teaches us important lessons..."
- "Like and subscribe for more ancient content..."

YOUR REQUIREMENTS:
1. Ancient lesson clearly stated in ONE sentence
2. Modern parallel that's MORE dramatic than the historical example
3. Question that forces viewers to think about current geography/strategy
4. Community-building element that makes people want to contribute knowledge
5. Use golden nuggets: {golden_nuggets}

ENGAGEMENT PATTERNS THAT WORK:
- "Which modern [geographic feature] do you think controls [strategic outcome]?"
- "What other [current situations] follow this same geographic pattern?"
- "Where do you see [ancient pattern] playing out in [modern context]?"

SUCCESS TEST: Would someone pause their day to research and comment on your question?

Keep to 30-40 seconds. Make ancient geography feel like breaking news.""",
            agent=closer,
            expected_output="Modern relevance conclusion with engaging CTA"
        )
        
        # Execute all tasks
        crew = Crew(
            agents=[director, hooksmith, story_weaver, sensory_director, closer],
            tasks=[director_task, hooksmith_task, story_weaver_task, sensory_director_task, closer_task],
            verbose=True
        )
        
        print(f"\n🚀 EXECUTING COMPLETE WRITERS' ROOM CREW...")
        result = crew.kickoff()
        
        return result

def main():
    """Main execution function"""
    print("🎬 ROME VS CARTHAGE - REAL AI WRITERS' ROOM")
    print("="*80)
    
    try:
        writers_room = RealWritersRoom()
        result = writers_room.run_rome_carthage_analysis()
        
        print("\n" + "="*80)
        print("🏆 COMPLETE ROME VS CARTHAGE SCRIPT")
        print("="*80)
        print(str(result))
        
        # Save results
        output_dir = "../outputs/tests"
        os.makedirs(output_dir, exist_ok=True)
        
        output_data = {
            "topic": "Rome vs Carthage - The 90-Mile Strait That Decided Two Empires",
            "generated_at": datetime.now().isoformat(),
            "model": "gemini-2.5-flash",
            "script": str(result),
            "agents_used": ["Director", "Hooksmith", "Story-Weaver", "Sensory Director", "The Closer"]
        }
        
        output_file = f"{output_dir}/rome_carthage_real_ai.json"
        with open(output_file, 'w') as f:
            json.dump(output_data, f, indent=2)
        
        print(f"\n💾 Results saved to: {output_file}")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()