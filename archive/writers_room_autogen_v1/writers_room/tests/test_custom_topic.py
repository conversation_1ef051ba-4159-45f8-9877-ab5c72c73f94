#!/usr/bin/env python3
"""
Custom topic testing for Writers' Room system
"""

from writers_room import WritersRoom, WritersRoomInput
from writers_room_enhanced import EnhancedWriters<PERSON>oom

def test_custom_topic():
    """Test with a different historical topic"""
    
    # Example: The D-Day Landings
    test_input = WritersRoomInput(
        topic="The D-Day Landings - Operation Overlord 1944",
        golden_nuggets=[
            "Normandy beaches were chosen over Calais due to German expectations",
            "Operation Fortitude deceived <PERSON> about landing location",
            "Tide charts determined the exact timing of June 6th",
            "Mulberry harbors were towed across the English Channel",
            "Weather delayed the invasion by 24 crucial hours"
        ],
        persona={
            "channel": "Frontier", 
            "voice": "Awestruck Strategist",
            "focus": "Geography and logistics determining history"
        }
    )
    
    print("🎬 TESTING CUSTOM TOPIC: D-Day Landings")
    print("="*60)
    
    # Test basic system
    writers_room = WritersRoom()
    result = writers_room.run_writers_room(test_input)
    
    print(f"\n📊 Results:")
    print(f"Script Length: {len(result.final_script)} characters")
    print(f"Estimated Duration: {result.estimated_duration}")
    print(f"Vocal Annotations: {result.vocal_annotations}")
    
    # Save results
    with open('dday_script_output.json', 'w') as f:
        import json
        output_dict = {
            "final_script": result.final_script,
            "structure": result.structure,
            "hook_variations": result.hook_variations,
            "visual_cues": result.visual_cues,
            "vocal_annotations": result.vocal_annotations,
            "estimated_duration": result.estimated_duration
        }
        json.dump(output_dict, f, indent=2)
    
    print(f"\n💾 D-Day script saved to: dday_script_output.json")
    
    return result

def test_modern_topic():
    """Test with a modern technology topic"""
    
    test_input = WritersRoomInput(
        topic="The Rise and Fall of Theranos - Silicon Valley's Greatest Fraud",
        golden_nuggets=[
            "Elizabeth Holmes dropped out of Stanford at 19",
            "Theranos was valued at $9 billion at its peak",
            "The Edison machines could only run 12 real tests",
            "Walgreens partnership put fake results in real pharmacies",
            "One drop of blood was supposed to run 200+ tests"
        ],
        persona={
            "channel": "Tech Investigative",
            "voice": "Analytical Detective", 
            "focus": "How technology promises can deceive even experts"
        }
    )
    
    print("\n🎬 TESTING MODERN TOPIC: Theranos Fraud")
    print("="*60)
    
    writers_room = WritersRoom()
    result = writers_room.run_writers_room(test_input)
    
    print(f"\n📊 Results:")
    print(f"Script Length: {len(result.final_script)} characters")
    print(f"Hook Quality: {'Strong' if 'billion' in result.final_script else 'Needs work'}")
    
    # Show just the primary hook
    if "PRIMARY HOOK" in result.final_script:
        hook_start = result.final_script.find("PRIMARY HOOK")
        hook_end = result.final_script.find("ALTERNATIVE HOOK")
        primary_hook = result.final_script[hook_start:hook_end].strip()
        print(f"\n🎣 Generated Hook:")
        print(primary_hook)
    
    return result

def test_multiple_personas():
    """Test the same topic with different channel personas"""
    
    base_topic = "The Bitcoin Pizza Day - 10,000 BTC for Two Pizzas"
    golden_nuggets = [
        "Laszlo Hanyecz paid 10,000 Bitcoin for two Papa John's pizzas",
        "May 22, 2010 was the first real-world Bitcoin transaction", 
        "Those pizzas would be worth $400+ million today",
        "Bitcoin was worth $0.0025 per coin at the time",
        "The forum post took 4 days to find someone willing to trade"
    ]
    
    personas = [
        {
            "channel": "Tech History",
            "voice": "Excited Educator",
            "focus": "How small moments change technology forever"
        },
        {
            "channel": "Financial Analysis", 
            "voice": "Analytical Expert",
            "focus": "Economic implications and market psychology"
        },
        {
            "channel": "Human Stories",
            "voice": "Empathetic Narrator",
            "focus": "Personal stories behind big moments"
        }
    ]
    
    print("\n🎬 TESTING MULTIPLE PERSONAS: Bitcoin Pizza Day")
    print("="*60)
    
    writers_room = WritersRoom()
    results = {}
    
    for i, persona in enumerate(personas, 1):
        print(f"\n🎭 PERSONA {i}: {persona['channel']}")
        
        test_input = WritersRoomInput(
            topic=base_topic,
            golden_nuggets=golden_nuggets,
            persona=persona
        )
        
        result = writers_room.run_writers_room(test_input)
        results[persona['channel']] = result
        
        # Show hook comparison
        if "PRIMARY HOOK" in result.final_script:
            hook_start = result.final_script.find("PRIMARY HOOK")
            hook_end = result.final_script.find("ALTERNATIVE HOOK")
            primary_hook = result.final_script[hook_start:hook_end].strip()
            print(f"Hook Style: {primary_hook[:100]}...")
    
    return results

if __name__ == "__main__":
    print("🧪 WRITERS' ROOM CUSTOM TESTING SUITE")
    print("="*80)
    
    # Run all tests
    print("\n1️⃣ HISTORICAL TOPIC TEST")
    test_custom_topic()
    
    print("\n2️⃣ MODERN TOPIC TEST") 
    test_modern_topic()
    
    print("\n3️⃣ MULTIPLE PERSONA TEST")
    test_multiple_personas()
    
    print("\n✅ ALL TESTS COMPLETE!")
    print("Check the generated JSON files for full results.")