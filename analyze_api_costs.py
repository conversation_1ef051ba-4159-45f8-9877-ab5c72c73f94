#!/usr/bin/env python3
"""
API Cost Analysis for YouTube Research v2
Calculate exact costs per video analysis
"""

import json

def calculate_api_costs():
    """Calculate API costs for a 20-minute video analysis"""
    
    print("💰 API Cost Analysis - YouTube Research v2")
    print("=" * 55)
    
    # Gemini 2.5 Flash Pricing (as of June 2025)
    GEMINI_PRICING = {
        'input_tokens_per_million': 0.075,    # $0.075 per 1M input tokens
        'output_tokens_per_million': 0.30,    # $0.30 per 1M output tokens
        'context_window': 1000000,            # 1M token context
        'max_output': 65000                   # 65K output tokens
    }
    
    # YouTube API v3 Pricing
    YOUTUBE_API_PRICING = {
        'cost_per_quota_unit': 0.0001,        # Roughly $0.0001 per quota unit
        'video_details': 1,                   # 1 quota unit
        'comments_list': 1,                   # 1 quota unit per request
        'free_quota_daily': 10000             # 10,000 units free per day
    }
    
    print("📊 Pricing Models Used:")
    print(f"   • Gemini 2.5 Flash: ${GEMINI_PRICING['input_tokens_per_million']}/M input, ${GEMINI_PRICING['output_tokens_per_million']}/M output")
    print(f"   • YouTube API v3: ${YOUTUBE_API_PRICING['cost_per_quota_unit']} per quota unit")
    
    # Estimate token usage for 20-minute video
    VIDEO_ANALYSIS_BREAKDOWN = {
        'video_metadata': {
            'input_tokens': 500,
            'output_tokens': 0
        },
        'transcript_processing': {
            'input_tokens': 8000,  # ~20 min video = ~8K tokens
            'output_tokens': 0
        },
        'comments_data': {
            'input_tokens': 3000,  # 100 comments = ~3K tokens
            'output_tokens': 0
        }
    }
    
    # Agent token usage estimates
    AGENT_COSTS = {
        'Agent 1: Performance Intelligence': {
            'input_tokens': 12000,  # Video data + prompts + context
            'output_tokens': 3000,  # Comprehensive analysis
            'thinking_tokens': 2000  # Sequential thinking overhead
        },
        'Agent 2: Script Forensics': {
            'input_tokens': 15000,  # Script + video data + agent 1 results
            'output_tokens': 4000,  # Detailed script analysis
            'thinking_tokens': 2500
        },
        'Agent 3: SEO Analysis': {
            'input_tokens': 14000,  # All data + agent 1 results
            'output_tokens': 3500,  # SEO insights
            'thinking_tokens': 2000
        },
        'Agent 4: Psychology Analysis': {
            'input_tokens': 14000,  # All data + agent 1 results
            'output_tokens': 3500,  # Psychology insights
            'thinking_tokens': 2000
        },
        'Agent 5: Comment Intelligence': {
            'input_tokens': 18000,  # All previous results + comments
            'output_tokens': 4000,  # Comment analysis
            'thinking_tokens': 2500
        }
    }
    
    # Styling agents (if using 5 separate agents)
    STYLING_AGENTS_SEPARATE = {
        'Performance Styling': {'input_tokens': 8000, 'output_tokens': 2000},
        'Script Styling': {'input_tokens': 10000, 'output_tokens': 2500},
        'SEO Styling': {'input_tokens': 9000, 'output_tokens': 2200},
        'Psychology Styling': {'input_tokens': 9000, 'output_tokens': 2200},
        'Comment Styling': {'input_tokens': 10000, 'output_tokens': 2500}
    }
    
    # Unified styling agent (optimized)
    STYLING_AGENT_UNIFIED = {
        'Unified Styling': {'input_tokens': 25000, 'output_tokens': 8000}
    }
    
    print("\n🔍 Token Usage Analysis for 20-Minute Video:")
    
    # Calculate total tokens for analysis agents
    total_input_tokens = sum(VIDEO_ANALYSIS_BREAKDOWN[key]['input_tokens'] for key in VIDEO_ANALYSIS_BREAKDOWN)
    total_output_tokens = 0
    
    print(f"\n📝 Data Processing:")
    for component, tokens in VIDEO_ANALYSIS_BREAKDOWN.items():
        print(f"   • {component}: {tokens['input_tokens']:,} input tokens")
        
    print(f"\n🤖 Analysis Agents:")
    for agent_name, tokens in AGENT_COSTS.items():
        agent_total_input = tokens['input_tokens'] + tokens['thinking_tokens']
        total_input_tokens += agent_total_input
        total_output_tokens += tokens['output_tokens']
        print(f"   • {agent_name}:")
        print(f"     - Input: {agent_total_input:,} tokens")
        print(f"     - Output: {tokens['output_tokens']:,} tokens")
    
    print(f"\n🎨 Styling Options:")
    
    # Option 1: Separate styling agents
    separate_styling_input = sum(agent['input_tokens'] for agent in STYLING_AGENTS_SEPARATE.values())
    separate_styling_output = sum(agent['output_tokens'] for agent in STYLING_AGENTS_SEPARATE.values())
    
    print(f"   Option A - 5 Separate Styling Agents:")
    print(f"     - Input: {separate_styling_input:,} tokens")
    print(f"     - Output: {separate_styling_output:,} tokens")
    
    # Option 2: Unified styling agent
    unified_styling_input = STYLING_AGENT_UNIFIED['Unified Styling']['input_tokens']
    unified_styling_output = STYLING_AGENT_UNIFIED['Unified Styling']['output_tokens']
    
    print(f"   Option B - Unified Styling Agent (OPTIMIZED):")
    print(f"     - Input: {unified_styling_input:,} tokens")
    print(f"     - Output: {unified_styling_output:,} tokens")
    
    # Calculate costs for both scenarios
    def calculate_gemini_cost(input_tokens, output_tokens):
        input_cost = (input_tokens / 1_000_000) * GEMINI_PRICING['input_tokens_per_million']
        output_cost = (output_tokens / 1_000_000) * GEMINI_PRICING['output_tokens_per_million']
        return input_cost + output_cost
    
    # YouTube API costs
    youtube_quota_units = 2  # video details + comments
    youtube_cost = youtube_quota_units * YOUTUBE_API_PRICING['cost_per_quota_unit']
    
    # Scenario A: Current system with separate styling agents
    scenario_a_input = total_input_tokens + separate_styling_input
    scenario_a_output = total_output_tokens + separate_styling_output
    scenario_a_gemini_cost = calculate_gemini_cost(scenario_a_input, scenario_a_output)
    scenario_a_total = scenario_a_gemini_cost + youtube_cost
    
    # Scenario B: Optimized with unified styling agent
    scenario_b_input = total_input_tokens + unified_styling_input
    scenario_b_output = total_output_tokens + unified_styling_output
    scenario_b_gemini_cost = calculate_gemini_cost(scenario_b_input, scenario_b_output)
    scenario_b_total = scenario_b_gemini_cost + youtube_cost
    
    print(f"\n💸 COST BREAKDOWN PER 20-MINUTE VIDEO:")
    print("=" * 55)
    
    print(f"\nScenario A - CURRENT SYSTEM (5 Separate Styling Agents):")
    print(f"   📊 Total Input Tokens: {scenario_a_input:,}")
    print(f"   📊 Total Output Tokens: {scenario_a_output:,}")
    print(f"   💎 Gemini 2.5 Flash Cost: ${scenario_a_gemini_cost:.4f}")
    print(f"   📺 YouTube API Cost: ${youtube_cost:.4f}")
    print(f"   🔥 TOTAL COST PER VIDEO: ${scenario_a_total:.4f}")
    
    print(f"\nScenario B - OPTIMIZED SYSTEM (Unified Styling Agent):")
    print(f"   📊 Total Input Tokens: {scenario_b_input:,}")
    print(f"   📊 Total Output Tokens: {scenario_b_output:,}")
    print(f"   💎 Gemini 2.5 Flash Cost: ${scenario_b_gemini_cost:.4f}")
    print(f"   📺 YouTube API Cost: ${youtube_cost:.4f}")
    print(f"   💚 TOTAL COST PER VIDEO: ${scenario_b_total:.4f}")
    
    savings = scenario_a_total - scenario_b_total
    savings_percent = (savings / scenario_a_total) * 100
    
    print(f"\n💰 OPTIMIZATION SAVINGS:")
    print(f"   💸 Cost Reduction: ${savings:.4f} per video")
    print(f"   📈 Percentage Savings: {savings_percent:.1f}%")
    
    # Volume calculations
    volumes = [10, 50, 100, 500, 1000]
    print(f"\n📊 MONTHLY COST PROJECTIONS:")
    print("   Videos/Month | Current Cost | Optimized Cost | Monthly Savings")
    print("   " + "-" * 60)
    for volume in volumes:
        current_monthly = volume * scenario_a_total
        optimized_monthly = volume * scenario_b_total
        monthly_savings = current_monthly - optimized_monthly
        print(f"   {volume:11d} | ${current_monthly:11.2f} | ${optimized_monthly:13.2f} | ${monthly_savings:14.2f}")
    
    # Break-even analysis for $99/month pricing
    pricing_tier = 99
    max_videos_current = pricing_tier / scenario_a_total
    max_videos_optimized = pricing_tier / scenario_b_total
    
    print(f"\n🎯 BUSINESS MODEL ANALYSIS ($99/month tier):")
    print(f"   Current System: {max_videos_current:.0f} videos max before losing money")
    print(f"   Optimized System: {max_videos_optimized:.0f} videos max before losing money")
    print(f"   Additional Video Capacity: {max_videos_optimized - max_videos_current:.0f} videos")
    
    # Recommendations
    print(f"\n🚀 RECOMMENDATIONS:")
    print(f"   1. 🔥 IMMEDIATE: Deploy unified styling agent for {savings_percent:.1f}% cost reduction")
    print(f"   2. ⚡ PRIORITY: Implement parallel processing for agents 2-4")
    print(f"   3. 💡 FUTURE: Add usage-based pricing tiers above base cost")
    print(f"   4. 📊 MONITORING: Track token usage per video to optimize further")
    
    return {
        'current_cost_per_video': scenario_a_total,
        'optimized_cost_per_video': scenario_b_total,
        'savings_per_video': savings,
        'savings_percentage': savings_percent
    }

if __name__ == "__main__":
    calculate_api_costs()