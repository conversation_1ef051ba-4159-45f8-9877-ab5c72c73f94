#!/usr/bin/env python3
"""
Quick Rome vs Sasanid test with reduced rounds
"""

import sys
import os
sys.path.append('/Users/<USER>/Documents/CrewAI/youtube-research-v2/writers_room/core_system')

from working_autogen_writers_room import WorkingAutoGenWritersRoom, WritersRoomInput

def quick_rome_sasanid_test():
    """Quick test with fewer rounds"""
    
    print("🏛️ QUICK ROME-SASANID TEST")
    print("=" * 50)
    
    # Shortened version for speed
    input_data = WritersRoomInput(
        topic="The 400-Year Water War That Decided Two Empires",
        golden_nuggets=[
            "Rome and Sasanid Persia fought 400 years over Mesopotamian water sources feeding 50 million people",
            "Euphrates/Tigris were ancient Colorado River - control them, control Middle East",
            "Sasanid irrigation system 1000x bigger than Roman aqueducts but had fatal weakness",
            "Roman general cut water supplies, destroyed 500-year empire in 3 months",
            "Same tactics used today: Colorado River deal creating Phoenix crisis based on <PERSON><PERSON><PERSON><PERSON>'s laws"
        ],
        persona={
            "channel": "Frontier",
            "voice": "Shocked investigator", 
            "focus": "Ancient water wars → Modern crisis"
        },
        quality_threshold=7.5,  # Lower threshold
        max_rounds=15  # Fewer rounds for speed
    )
    
    try:
        writers_room = WorkingAutoGenWritersRoom(model="gpt-4", api_key="********************************************************************************************************************************************************************")
        
        print("🚀 Running quick collaboration...")
        
        # Just test Phase 1 to see it working
        result = writers_room._structure_collaboration(input_data)
        
        print(f"\n✅ STRUCTURE COLLABORATION COMPLETE")
        print(f"🔄 Rounds: {result['rounds']}")
        print(f"💬 Messages: {len(result['conversation'])}")
        
        # Show final structure
        print(f"\n📜 FINAL STRUCTURE SAMPLE:")
        for msg in result['conversation'][-3:]:
            name = msg.get('name', 'Unknown')
            content = msg.get('content', '')[:300] + "..." if len(msg.get('content', '')) > 300 else msg.get('content', '')
            print(f"\n{name}:")
            print(f"{content}")
            print("-" * 40)
        
        # Save result
        import json
        output_dir = "/Users/<USER>/Documents/CrewAI/youtube-research-v2/writers_room/outputs/quick_tests"
        os.makedirs(output_dir, exist_ok=True)
        
        with open(f"{output_dir}/rome_sasanid_structure.json", 'w') as f:
            json.dump(result, f, indent=2)
        
        print(f"\n💾 Structure saved to: {output_dir}/rome_sasanid_structure.json")
        
        return result
        
    except Exception as e:
        print(f"❌ ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_rome_sasanid_test()