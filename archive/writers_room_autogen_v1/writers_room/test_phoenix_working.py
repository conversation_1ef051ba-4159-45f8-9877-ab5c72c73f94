#!/usr/bin/env python3
"""
Test the ACTUAL working Writers' Room with Phoenix topic
"""

import sys
sys.path.append('/Users/<USER>/Documents/CrewAI/youtube-research-v2/writers_room/core_system')

from writers_room import WritersRoom, WritersRoomInput

def test_phoenix_with_working_system():
    """Test Phoenix script with the actual working Writers' Room"""
    
    input_data = WritersRoomInput(
        topic="The $60 Billion Gamble That Could Save America's Hottest City",
        golden_nuggets=[
            "Phoenix consumes TWICE the water of NYC despite having half the population",
            "Arizona's 1922 water deal was based on 'abnormally high' rainfall data - they've been stealing water that doesn't exist for 100 years", 
            "Taiwan makes 90% of advanced chips, China threatens invasion - so America bet $60 BILLION on Phoenix as backup",
            "Phoenix hit 113°F for 54 straight days in 2023, breaking every heat record",
            "Saudi Arabia was secretly pumping unlimited Arizona groundwater for FREE until the state caught on"
        ],
        persona={
            "channel": "Frontier",
            "voice": "Shocked investigator revealing a conspiracy",
            "focus": "How bad decisions 100 years ago created today's crisis"
        }
    )
    
    print("🔥 TESTING WORKING WRITERS' ROOM WITH PHOENIX")
    print("=" * 60)
    
    writers_room = WritersRoom()
    result = writers_room.run_writers_room(input_data)
    
    print(f"\n📜 PHOENIX SCRIPT PREVIEW:")
    preview = result.final_script[:500] + "..." if len(result.final_script) > 500 else result.final_script
    print(preview)
    
    return result

if __name__ == "__main__":
    test_phoenix_with_working_system()