#!/usr/bin/env python3
"""
Measure ONLY the styling performance improvement
"""

import time
import sys
import os
from dotenv import load_dotenv
load_dotenv()

sys.path.append('/Users/<USER>/Documents/CrewAI/youtube-research-v2')

def test_styling_performance():
    """Test styling performance in isolation"""
    
    print("🎨 STYLING PERFORMANCE COMPARISON")
    print("=" * 50)
    
    # Sample analysis data (realistic size)
    sample_analysis = {
        'performance_analysis': {
            'analysis': """
            Performance Analysis Report:
            Views: 150M+ views (Exceptional viral success)
            Likes: 12M+ likes (8% engagement rate - excellent)
            Comments: 2.8M+ comments (Highly engaged audience)
            Duration: 3:34 minutes (Perfect length for retention)
            Published: 2007 (15+ years of sustained popularity)
            
            Performance Tier: S-Grade (Top 0.1% viral phenomenon)
            Engagement Excellence: This video demonstrates perfect engagement optimization
            with consistent interaction patterns across demographics and time periods.
            
            Key Success Factors:
            1. Timeless nostalgic appeal
            2. Meme-worthy content structure
            3. High replay value and shareability
            4. Cross-generational humor appeal
            
            Competitive Analysis: This video outperforms 99.9% of content on YouTube.
            """
        },
        'script_analysis': {
            'analysis': """
            Script Forensics Analysis:
            Word Count: 245 words (Perfect for music video format)
            Emotional Impact: 10/10 (Creates strong nostalgic response)
            Hook Effectiveness: 9/10 (Immediate musical recognition)
            
            Psychological Triggers:
            1. Nostalgia activation (0:00-0:05)
            2. Musical familiarity (throughout)
            3. Emotional commitment themes (lyrics)
            4. Surprise element (Rick Roll context)
            
            Content Structure: Classic music video format with narrative elements
            that create emotional connection and memorable experience.
            """
        },
        'seo_analysis': {
            'analysis': """
            SEO Analysis:
            Title Optimization: 9/10 (Clear, recognizable, searchable)
            Keywords: "Rick Astley", "Never Gonna Give You Up", "Official Video"
            Search Volume: 100M+ monthly searches
            
            Keyword Performance:
            - "Rick Astley" (50M monthly searches)
            - "Never Gonna Give You Up" (25M monthly searches)
            - "Rick Roll" (15M monthly searches)
            
            SEO Excellence: Perfect example of brand + song title optimization
            with strong search intent fulfillment.
            """
        },
        'psychology_analysis': {
            'analysis': """
            Audience Psychology Analysis:
            Primary Motivation: Nostalgia + Entertainment
            Emotional Profile: Joy, nostalgia, humor, surprise
            
            Engagement Psychology:
            1. Nostalgia triggers (Strong): 1980s music revival
            2. Humor appreciation (High): Meme culture participation
            3. Social sharing (Very High): Rick Roll phenomenon
            
            Demographics: Cross-generational appeal from Gen X to Gen Alpha
            Global reach with cultural significance transcending language barriers.
            """
        },
        'comment_analysis': {
            'analysis': """
            Comment Intelligence Analysis:
            Total Comments: 2.8M+ analyzed
            Sentiment: 95% positive (Extremely high satisfaction)
            
            Comment Categories:
            1. Nostalgia (45%): "Takes me back to..."
            2. Meme appreciation (30%): "Got Rick Rolled again!"
            3. Music appreciation (20%): "What a voice!"
            4. Cultural impact (5%): "Internet history"
            
            Top comment themes consistently show appreciation for the song's
            quality, nostalgia value, and cultural significance.
            """
        }
    }
    
    video_data = {
        'title': 'Rick Astley - Never Gonna Give You Up (Official Video)',
        'statistics': {
            'viewCount': '1500000000',
            'likeCount': '12000000', 
            'commentCount': '2800000'
        }
    }
    
    # Test 1: Unified styling agent
    print("🚀 Testing UNIFIED styling agent...")
    unified_start = time.time()
    
    try:
        from unified_styling_agent import UnifiedVideoStylingAgent
        from crewai.llm import LLM
        
        llm = LLM(model="gemini/gemini-2.5-flash")
        unified_agent = UnifiedVideoStylingAgent(llm)
        unified_result = unified_agent.format_all_analyses(sample_analysis, video_data)
        
        unified_time = time.time() - unified_start
        
        print(f"✅ Unified styling: {unified_time:.1f}s")
        print(f"💰 Token savings: {unified_result['token_savings']['savings_percentage']:.0f}%")
        
        # Verify output quality
        sections = unified_result['styled_sections']
        for key in ['performance_html', 'script_html', 'seo_html', 'psychology_html', 'comments_html']:
            if key in sections and len(sections[key]) > 100:
                print(f"✅ {key}: {len(sections[key])} chars")
            else:
                print(f"❌ {key}: Missing or too short")
        
        print()
        return unified_time
        
    except Exception as e:
        print(f"❌ Unified styling failed: {e}")
        return None

def estimate_original_performance():
    """Estimate original 5-agent styling performance"""
    
    # Based on our previous measurements:
    # - 5 agents × ~30-45 seconds each = 150-225 seconds
    # - Plus token overhead = ~180-300 seconds total
    
    estimated_time = 210  # 3.5 minutes average
    print(f"📊 Original 5-agent styling (estimated): {estimated_time}s")
    return estimated_time

if __name__ == "__main__":
    print("🎯 MEASURING STYLING PERFORMANCE ONLY")
    print("(This tests styling optimization, not analysis agents)")
    print()
    
    # Test unified performance
    unified_time = test_styling_performance()
    
    if unified_time:
        # Compare with estimated original
        original_time = estimate_original_performance()
        
        improvement = ((original_time - unified_time) / original_time) * 100
        time_saved = original_time - unified_time
        
        print(f"📈 PERFORMANCE COMPARISON:")
        print(f"   Original (5 agents): ~{original_time}s")
        print(f"   Unified (1 agent):   {unified_time:.1f}s")
        print(f"   Time saved:          {time_saved:.1f}s")
        print(f"   Improvement:         {improvement:.1f}% faster")
        
        if improvement > 60:
            print("\n🎉 SUCCESS: >60% styling performance improvement achieved!")
        else:
            print(f"\n⚠️  Moderate improvement: {improvement:.1f}% (target was 60%+)")
    else:
        print("\n❌ Could not measure unified performance")