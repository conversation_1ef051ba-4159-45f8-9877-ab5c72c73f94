#!/usr/bin/env python
"""
YouTube Research v2 - Main Entry Point
Orchestrates the execution of different CrewAI crews for YouTube analysis
"""

import os
import sys
from typing import Dict, Any, Optional
from datetime import datetime
import json

from crews import (
    ChannelDNAAnalysisCrew,
    VideoAnalyzerCrew,
    ResearchTopicsCrew
)

class YouTubeResearchOrchestrator:
    """Main orchestrator for YouTube Research crews"""
    
    def __init__(self):
        self.channel_crew = ChannelDNAAnalysisCrew()
        self.video_crew = VideoAnalyzerCrew()
        self.research_crew = ResearchTopicsCrew()
    
    def analyze_channel(self, channel_url: str, depth: str = "standard") -> Dict[str, Any]:
        """
        Perform comprehensive channel analysis
        
        Args:
            channel_url: YouTube channel URL
            depth: Analysis depth ('standard', 'deep', 'comprehensive')
        
        Returns:
            Dictionary containing analysis results
        """
        print(f"Starting channel analysis for: {channel_url}")
        print(f"Analysis depth: {depth}")
        
        inputs = {
            'channel_url': channel_url,
            'analysis_depth': depth,
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            result = self.channel_crew.crew().kickoff(inputs=inputs)
            return {
                'status': 'success',
                'channel_url': channel_url,
                'analysis': result,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'status': 'error',
                'channel_url': channel_url,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def analyze_video(self, video_url: str, include_comments: bool = False) -> Dict[str, Any]:
        """
        Perform deep video analysis
        
        Args:
            video_url: YouTube video URL
            include_comments: Whether to analyze comments
        
        Returns:
            Dictionary containing analysis results
        """
        print(f"Starting video analysis for: {video_url}")
        
        inputs = {
            'video_url': video_url,
            'include_comments': include_comments,
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            result = self.video_crew.crew().kickoff(inputs=inputs)
            return {
                'status': 'success',
                'video_url': video_url,
                'analysis': result,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'status': 'error',
                'video_url': video_url,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def research_topics(self, niche: str, competitor_channels: Optional[list] = None) -> Dict[str, Any]:
        """
        Research content topics and opportunities
        
        Args:
            niche: Target niche or topic area
            competitor_channels: List of competitor channel URLs
        
        Returns:
            Dictionary containing research results
        """
        print(f"Starting topic research for niche: {niche}")
        
        inputs = {
            'niche': niche,
            'competitor_channels': competitor_channels or [],
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            result = self.research_crew.crew().kickoff(inputs=inputs)
            return {
                'status': 'success',
                'niche': niche,
                'research': result,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'status': 'error',
                'niche': niche,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

def main():
    """Main function for command-line usage"""
    orchestrator = YouTubeResearchOrchestrator()
    
    # Example usage
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "channel" and len(sys.argv) > 2:
            channel_url = sys.argv[2]
            depth = sys.argv[3] if len(sys.argv) > 3 else "standard"
            result = orchestrator.analyze_channel(channel_url, depth)
            print(json.dumps(result, indent=2))
        
        elif command == "video" and len(sys.argv) > 2:
            video_url = sys.argv[2]
            result = orchestrator.analyze_video(video_url)
            print(json.dumps(result, indent=2))
        
        elif command == "research" and len(sys.argv) > 2:
            niche = sys.argv[2]
            result = orchestrator.research_topics(niche)
            print(json.dumps(result, indent=2))
        
        else:
            print("Usage:")
            print("  python main.py channel <channel_url> [depth]")
            print("  python main.py video <video_url>")
            print("  python main.py research <niche>")
    else:
        # Interactive mode
        print("YouTube Research v2 - Interactive Mode")
        print("Commands:")
        print("  1. Analyze Channel")
        print("  2. Analyze Video")
        print("  3. Research Topics")
        print("  4. Exit")
        
        while True:
            choice = input("\nSelect option (1-4): ")
            
            if choice == "1":
                channel_url = input("Enter channel URL: ")
                depth = input("Analysis depth (standard/deep/comprehensive) [standard]: ") or "standard"
                result = orchestrator.analyze_channel(channel_url, depth)
                print(json.dumps(result, indent=2))
            
            elif choice == "2":
                video_url = input("Enter video URL: ")
                include_comments = input("Include comments analysis? (y/n) [n]: ").lower() == 'y'
                result = orchestrator.analyze_video(video_url, include_comments)
                print(json.dumps(result, indent=2))
            
            elif choice == "3":
                niche = input("Enter niche/topic: ")
                result = orchestrator.research_topics(niche)
                print(json.dumps(result, indent=2))
            
            elif choice == "4":
                print("Exiting...")
                break
            
            else:
                print("Invalid option. Please try again.")

if __name__ == "__main__":
    main()