#!/usr/bin/env python3
"""
Debug script to test recommendations LLM call directly
"""

import os
import sys
from litellm import completion
import json

def test_recommendations_llm():
    """Test the recommendations LLM call directly"""
    print("🔍 Testing Recommendations LLM Call")
    print("=" * 50)
    
    # Set up LiteLLM
    os.environ["GEMINI_API_KEY"] = "AIzaSyB64ePEL3d95ANi8hrdGj2TD09CcRsUkJ8"
    
    # Sample context similar to what the app uses
    test_prompt = """Generate specific, prioritized recommendations for success in the python tutorial market:

MARKET ANALYSIS SUMMARY:
The python tutorial market shows strong engagement with diverse content formats.

SUCCESS PATTERNS:
Top performers focus on practical, actionable content with clear value propositions.

OPPORTUNITIES & RISKS:
OPPORTUNITIES: Growing demand for beginner-friendly content

RISKS: High competition in basic tutorial space

Generate 4-5 specific, actionable recommendations in this exact JSON format:
[
    {
        "title": "Specific recommendation title",
        "description": "Detailed description of the recommendation and how to implement it",
        "priority": "High/Medium/Low"
    }
]"""
    
    try:
        print("📝 Making LLM call...")
        response = completion(
            model="gemini-1.5-flash",
            messages=[{"role": "user", "content": test_prompt}],
            temperature=0.7
        )
        
        # Extract response text
        if hasattr(response, 'choices') and response.choices:
            response_text = response.choices[0].message.content
        else:
            response_text = str(response)
        
        print(f"✅ LLM Response received ({len(response_text)} chars)")
        print("-" * 30)
        print("📄 Raw Response:")
        print(repr(response_text))
        print("-" * 30)
        print("📄 Formatted Response:")
        print(response_text)
        print("-" * 30)
        
        # Test JSON parsing
        print("🔍 Testing JSON Parsing:")
        
        # Clean up the response
        cleaned = response_text.strip()
        
        # Remove markdown code blocks
        if cleaned.startswith('```json'):
            cleaned = cleaned[7:].strip()
        elif cleaned.startswith('```'):
            cleaned = cleaned[3:].strip()
        
        if cleaned.endswith('```'):
            cleaned = cleaned[:-3].strip()
        
        print(f"Cleaned text: {repr(cleaned[:100])}...")
        
        # Try to parse as JSON
        try:
            parsed = json.loads(cleaned)
            print(f"✅ JSON parsing successful: {len(parsed)} items")
            for i, item in enumerate(parsed):
                print(f"  {i+1}. {item.get('title', 'No title')}")
        except json.JSONDecodeError as e:
            print(f"❌ JSON parsing failed: {e}")
            print(f"Error position: {e.pos if hasattr(e, 'pos') else 'unknown'}")
            
            # Try to find the problematic part
            if hasattr(e, 'pos') and e.pos < len(cleaned):
                start = max(0, e.pos - 20)
                end = min(len(cleaned), e.pos + 20)
                print(f"Context around error: {repr(cleaned[start:end])}")
        
    except Exception as e:
        print(f"❌ LLM call failed: {e}")
        print(f"Error type: {type(e).__name__}")
    
    print("\n" + "=" * 50)
    print("🏁 Recommendations Debug Complete")

if __name__ == "__main__":
    test_recommendations_llm()
