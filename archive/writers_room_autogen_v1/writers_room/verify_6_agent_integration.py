#!/usr/bin/env python3
"""
Verify that Emotional Arc Specialist has been properly integrated
without requiring AutoGen dependency
"""

import re


def verify_emotional_architect_integration():
    """Verify the 6-agent integration by checking source code"""
    
    print("🎭 VERIFYING EMOTIONAL ARC SPECIALIST INTEGRATION")
    print("=" * 60)
    
    # Read the source file
    file_path = "core_system/real_autogen_writers_room.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check 1: Agent definition
        print("\n1️⃣ AGENT DEFINITION CHECK:")
        if "agents['emotional_architect']" in content:
            print("✅ Emotional Architect agent defined")
        else:
            print("❌ Emotional Architect agent missing")
            return False
            
        # Check 2: Agent role and personality
        print("\n2️⃣ AGENT ROLE CHECK:")
        emotional_keywords = [
            "EMOTIONAL MANIPULATION SPECIALIST",
            "dopamine curves",
            "emotional peaks",
            "psychological triggers",
            "memory formation"
        ]
        
        found_keywords = 0
        for keyword in emotional_keywords:
            if keyword in content:
                print(f"✅ {keyword}")
                found_keywords += 1
            else:
                print(f"❌ {keyword}")
        
        if found_keywords < 4:
            print(f"❌ Insufficient emotional architect capabilities ({found_keywords}/5)")
            return False
        
        # Check 3: Phase integration
        print("\n3️⃣ PHASE INTEGRATION CHECK:")
        phase_patterns = [
            (r"structure_agents.*emotional_architect", "Phase 1: Structure"),
            (r"content_agents.*emotional_architect", "Phase 2: Content"),
            (r"production_agents.*emotional_architect", "Phase 3: Production"),
            (r"quality_agents.*emotional_architect", "Phase 4: Quality")
        ]
        
        integrated_phases = 0
        for pattern, phase_name in phase_patterns:
            if re.search(pattern, content, re.DOTALL):
                print(f"✅ {phase_name}: Integrated")
                integrated_phases += 1
            else:
                print(f"❌ {phase_name}: Not integrated")
        
        if integrated_phases < 4:
            print(f"❌ Incomplete phase integration ({integrated_phases}/4)")
            return False
        
        # Check 4: Agent count updates
        print("\n4️⃣ DOCUMENTATION UPDATES:")
        doc_updates = [
            ("6 AGENTS", "Agent count updated"),
            ("NOW WITH 6 AGENTS", "Class description updated"),
            ("Create 6 specialized agents", "Method description updated")
        ]
        
        doc_score = 0
        for pattern, description in doc_updates:
            if pattern in content:
                print(f"✅ {description}")
                doc_score += 1
            else:
                print(f"❌ {description}")
        
        # Check 5: Validation questions
        print("\n5️⃣ VALIDATION INTEGRATION:")
        if "EmotionalArchitect ONLY: Is the emotional journey memorable?" in content:
            print("✅ Specialized validation question added")
        else:
            print("❌ Specialized validation question missing")
            doc_score -= 1
        
        # Final assessment
        print("\n" + "=" * 60)
        if found_keywords >= 4 and integrated_phases == 4 and doc_score >= 2:
            print("🎉 EMOTIONAL ARC SPECIALIST SUCCESSFULLY INTEGRATED!")
            print("✅ All checks passed - 6-agent system ready!")
            
            # Show integration summary
            print(f"\n📊 INTEGRATION SUMMARY:")
            print(f"🤖 Agent Definition: ✅ Complete")
            print(f"🧠 Capabilities: ✅ {found_keywords}/5 keywords found")
            print(f"🔄 Phase Integration: ✅ {integrated_phases}/4 phases")
            print(f"📝 Documentation: ✅ {doc_score}/3 updates")
            print(f"🎭 Validation: ✅ Specialized questions added")
            
            return True
        else:
            print("❌ INTEGRATION INCOMPLETE")
            print(f"Capabilities: {found_keywords}/5")
            print(f"Phases: {integrated_phases}/4") 
            print(f"Documentation: {doc_score}/3")
            return False
            
    except FileNotFoundError:
        print(f"❌ File not found: {file_path}")
        return False
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False


def show_agent_summary():
    """Show summary of all 6 agents"""
    
    print("\n🤖 6-AGENT WRITERS' ROOM SUMMARY")
    print("=" * 50)
    
    agents = [
        ("1. Director", "Viral Structure Architect", "Creates retention-optimized 5-beat structure"),
        ("2. Hooksmith", "First 30-Second Specialist", "Crafts impossible-to-ignore openings"),
        ("3. Story-Weaver", "Narrative Addiction Expert", "Builds tension loops and cliffhangers"),
        ("4. Closer", "Engagement Ending Master", "Creates debate-triggering conclusions"),
        ("5. Emotional Architect", "Dopamine Engineer", "Maps emotional peaks and valleys"),
        ("6. Producer", "Production Reality Check", "Ensures filmable, professional output")
    ]
    
    for name, specialty, function in agents:
        print(f"{name:<20} | {specialty:<25} | {function}")
    
    print(f"\n🎯 COLLABORATION WORKFLOW:")
    print(f"Phase 1: Structure Wars (All 6 agents debate structure)")
    print(f"Phase 2: Content Battle (Specialists create content)")
    print(f"Phase 3: Production Polish (Producer + Emotional Architect)")
    print(f"Phase 4: Quality Gate (All 6 agents validate)")


if __name__ == "__main__":
    success = verify_emotional_architect_integration()
    
    if success:
        show_agent_summary()
        print(f"\n🚀 Ready for testing with OpenAI API!")
    else:
        print(f"\n🔧 Integration needs fixes before testing")