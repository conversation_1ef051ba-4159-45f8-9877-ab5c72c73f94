#!/usr/bin/env python3
"""
Test Writers' Room with Rome vs Sasanid Empire topic
"""

from writers_room import WritersRoom, WritersRoomInput
from writers_room_enhanced import EnhancedWritersRoom
import json

def test_rome_vs_sasanid():
    """Test the epic Rome vs Sasanid Empire rivalry"""
    
    test_input = WritersRoomInput(
        topic="Rome vs the Sasanid Empire - The 400-Year War That Broke Two Superpowers",
        golden_nuggets=[
            "The conflict lasted over 400 years (224-651 CE)",
            "Both empires spent 25% of their treasuries fighting each other",
            "The Zagros Mountains created a natural fortress line",
            "Ctesiphon changed hands 5 times in 50 years",
            "The war bankrupted both empires, leaving them vulnerable to Arab conquest",
            "<PERSON> the Apostate died trying to capture <PERSON><PERSON><PERSON><PERSON> in 363 CE",
            "<PERSON><PERSON><PERSON> I and <PERSON><PERSON> fought a 20-year proxy war"
        ],
        persona={
            "channel": "Frontier",
            "voice": "Awestruck Strategist", 
            "focus": "Geography and logistics determining history"
        }
    )
    
    print("🏛️ TESTING: ROME VS SASANID EMPIRE")
    print("="*60)
    print("📍 Geographic Focus: How terrain shaped a 400-year superpower rivalry")
    print("💰 Economic Impact: Two empires bankrupting each other")
    print("⚔️ Strategic Stakes: Control of the Ancient World's crossroads")
    
    # Run basic system
    print("\n🎬 RUNNING WRITERS' ROOM...")
    writers_room = WritersRoom()
    result = writers_room.run_writers_room(test_input)
    
    print(f"\n📊 RESULTS:")
    print(f"   Script Length: {len(result.final_script)} characters")
    print(f"   Estimated Duration: {result.estimated_duration}")
    print(f"   Vocal Annotations: {result.vocal_annotations}")
    
    # Extract and show the primary hook
    if "PRIMARY HOOK" in result.final_script:
        hook_start = result.final_script.find("PRIMARY HOOK")
        hook_end = result.final_script.find("ALTERNATIVE HOOK")
        primary_hook = result.final_script[hook_start:hook_end].strip()
        
        print(f"\n🎣 GENERATED HOOK:")
        print("-" * 40)
        print(primary_hook)
        print("-" * 40)
    
    # Extract structure
    if "DIRECTOR'S 5-BEAT STRUCTURE" in result.structure:
        print(f"\n🎬 DIRECTOR'S STRUCTURE:")
        print("-" * 40)
        structure_lines = result.structure.split('\n')[:15]  # First 15 lines
        for line in structure_lines:
            if line.strip():
                print(line)
        print("...")
        print("-" * 40)
    
    # Show conclusion preview
    if "CONCLUSION & CTA" in result.final_script:
        conclusion_start = result.final_script.find("THE CLOSER'S CONCLUSION")
        if conclusion_start != -1:
            conclusion_section = result.final_script[conclusion_start:conclusion_start+500]
            print(f"\n🎪 CONCLUSION PREVIEW:")
            print("-" * 40)
            print(conclusion_section + "...")
            print("-" * 40)
    
    # Save results
    output_dict = {
        "topic": test_input.topic,
        "final_script": result.final_script,
        "structure": result.structure,
        "hook_variations": result.hook_variations,
        "visual_cues": result.visual_cues,
        "vocal_annotations": result.vocal_annotations,
        "estimated_duration": result.estimated_duration,
        "golden_nuggets_used": test_input.golden_nuggets
    }
    
    with open('rome_sasanid_script.json', 'w') as f:
        json.dump(output_dict, f, indent=2)
    
    print(f"\n💾 Full Rome vs Sasanid script saved to: rome_sasanid_script.json")
    
    return result

def test_enhanced_rome_sasanid():
    """Test the same topic with enhanced quality validation"""
    
    input_data = {
        "topic": "Rome vs the Sasanid Empire - The 400-Year War That Broke Two Superpowers",
        "golden_nuggets": [
            "The conflict lasted over 400 years (224-651 CE)",
            "Both empires spent 25% of their treasuries fighting each other",
            "The Zagros Mountains created a natural fortress line",
            "Ctesiphon changed hands 5 times in 50 years",
            "The war bankrupted both empires, leaving them vulnerable to Arab conquest",
            "Julian the Apostate died trying to capture Ctesiphon in 363 CE",
            "Khosrow I and Justinian fought a 20-year proxy war"
        ],
        "persona": {
            "channel": "Frontier",
            "voice": "Awestruck Strategist",
            "focus": "Geography and logistics determining history"
        }
    }
    
    print("\n🏆 TESTING ENHANCED SYSTEM WITH QUALITY VALIDATION")
    print("="*60)
    
    enhanced_room = EnhancedWritersRoom()
    result = enhanced_room.run_enhanced_writers_room(input_data)
    
    print(f"\n📊 ENHANCED RESULTS:")
    print(f"   Quality Score: {result['final_score']}/100")
    print(f"   Execution Time: {result['execution_time']}")
    print(f"   Revisions Made: {result['revisions_made']}")
    print(f"   Script Length: {len(result['script'])} characters")
    
    # Show quality breakdown
    if 'quality_report' in result:
        checks = result['quality_report']['checks']
        print(f"\n🔍 QUALITY BREAKDOWN:")
        print(f"   Hook Present: {'✅' if checks['hook_present'] else '❌'}")
        print(f"   Golden Nuggets Used: {checks['golden_nuggets_used']}/7")
        print(f"   Vocal Annotations: {checks['vocal_annotations']}")
        print(f"   Visual Cues: {checks['visual_cues']}")
        print(f"   CTA Present: {'✅' if checks['cta_present'] else '❌'}")
        
        if result['quality_report']['recommendations']:
            print(f"\n💡 RECOMMENDATIONS:")
            for rec in result['quality_report']['recommendations']:
                print(f"   • {rec}")
    
    # Save enhanced results
    with open('rome_sasanid_enhanced.json', 'w') as f:
        json.dump(result, f, indent=2)
    
    print(f"\n💾 Enhanced results saved to: rome_sasanid_enhanced.json")
    
    return result

if __name__ == "__main__":
    print("🏛️ ROME VS SASANID EMPIRE - WRITERS' ROOM TEST")
    print("="*80)
    print("🌍 Topic: The 400-year superpower rivalry that shaped the ancient world")
    print("🎯 Focus: Geographic factors that determined the longest war in history")
    
    # Test basic system
    basic_result = test_rome_vs_sasanid()
    
    # Test enhanced system
    enhanced_result = test_enhanced_rome_sasanid()
    
    print("\n" + "="*80)
    print("🏆 ROME VS SASANID TEST COMPLETE!")
    print("="*80)
    print("📁 Files Generated:")
    print("   • rome_sasanid_script.json (Basic system)")
    print("   • rome_sasanid_enhanced.json (Enhanced system)")
    print("\n🎬 The Writers' Room successfully tackled this epic historical rivalry!")