
"""
Working CrewAI YouTube Research v2 App
CLEAN, LIGHTWEIGHT VERSION - Performance Optimized
"""

import os
import uuid
import json
from datetime import datetime, timezone
from typing import Dict, Any
from fastapi import FastAPI, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import logging

# CrewAI imports
from crewai import Agent, Crew, Task, Process
from crewai.llm import LLM

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="YouTube Research v2 - CrewAI")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple in-memory storage
analysis_store = {}

# Import our YouTube tools
from src.tools.youtube_tools import YouTubeChannelAnalysisTool

# Pydantic models
class AnalysisRequest(BaseModel):
    channel_id: str
    max_videos: int = 20
    max_comments: int = 50

class AnalysisResponse(BaseModel):
    analysis_id: str
    status: str
    message: str

class MarketResearchCrewAI:
    """CrewAI implementation of Market Research for AI Channel Creation"""
    
    def __init__(self):
        # Initialize Gemini models
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if gemini_api_key:
            # Set environment variable for LiteLLM
            os.environ['GEMINI_API_KEY'] = gemini_api_key
            
            self.llm_pro = LLM(
                model="gemini/gemini-1.5-pro"
            )
            self.llm_flash = LLM(
                model="gemini/gemini-1.5-flash"
            )
            self.ai_enabled = True
        else:
            self.llm_pro = None
            self.llm_flash = None
            self.ai_enabled = False
            
        # Initialize YouTube tools
        youtube_api_key = os.getenv('YOUTUBE_API_KEY')
        if youtube_api_key:
            self.youtube_tool = YouTubeChannelAnalysisTool(
                youtube_api_key=youtube_api_key,
                supadata_api_key=os.getenv('SUPADATA_API_KEY')
            )
            self.tools_available = True
        else:
            self.youtube_tool = None
            self.tools_available = False
    
    def create_agents(self):
        """Create the 6 Market Research agents"""
        
        if not self.ai_enabled:
            # Create simple agents without LLM
            return [
                Agent(
                    role="Market Opportunity Scout",
                    goal="Find content gaps and underserved audiences for new AI-generated channels",
                    backstory="Market research expert specializing in YouTube content opportunities",
                    verbose=True,
                    allow_delegation=False
                ),
                Agent(
                    role="Content Strategy Architect",
                    goal="Extract viral content patterns and formats optimized for AI production",
                    backstory="Content strategist with expertise in AI-producible formats",
                    verbose=True,
                    allow_delegation=False
                ),
                Agent(
                    role="Audience Intelligence Analyst", 
                    goal="Mine comments and engagement to find unmet audience needs",
                    backstory="Audience psychology expert focusing on content requests and gaps",
                    verbose=True,
                    allow_delegation=False
                ),
                Agent(
                    role="Competitive Landscape Mapper",
                    goal="Benchmark performance and find competitive positioning opportunities",
                    backstory="Competition analyst specializing in market entry strategies", 
                    verbose=True,
                    allow_delegation=False
                ),
                Agent(
                    role="Production Intelligence Agent",
                    goal="Assess content feasibility for AI automation and production ROI",
                    backstory="AI production specialist evaluating content creation complexity",
                    verbose=True,
                    allow_delegation=False
                ),
                Agent(
                    role="Launch Strategy Synthesizer",
                    goal="Create comprehensive go-to-market plans for new AI channels",
                    backstory="Launch strategist combining market intelligence into actionable plans",
                    verbose=True,
                    allow_delegation=False
                )
            ]
        
        return [
            Agent(
                role="Market Opportunity Scout",
                goal="Find content gaps and underserved audiences for new AI-generated channels",
                backstory="You are a market research expert specializing in YouTube content opportunities. Your mission is to identify blue ocean opportunities where new AI-generated channels can thrive with minimal competition.",
                verbose=True,
                allow_delegation=False,
                llm=self.llm_flash
            ),
            Agent(
                role="Content Strategy Architect", 
                goal="Extract viral content patterns and formats optimized for AI production",
                backstory="You are a content strategist with expertise in AI-producible formats. You identify patterns that work and can be replicated using AI tools for video creation.",
                verbose=True,
                allow_delegation=False,
                llm=self.llm_flash
            ),
            Agent(
                role="Audience Intelligence Analyst",
                goal="Mine comments and engagement to find unmet audience needs",
                backstory="You are an audience psychology expert focusing on content requests and gaps. You analyze what viewers are asking for but not getting from current creators.",
                verbose=True,
                allow_delegation=False,
                llm=self.llm_flash
            ),
            Agent(
                role="Competitive Landscape Mapper",
                goal="Benchmark performance and find competitive positioning opportunities", 
                backstory="You are a competition analyst specializing in market entry strategies. You evaluate where new channels can compete effectively against established creators.",
                verbose=True,
                allow_delegation=False,
                llm=self.llm_flash
            ),
            Agent(
                role="Production Intelligence Agent",
                goal="Assess content feasibility for AI automation and production ROI",
                backstory="You are an AI production specialist evaluating content creation complexity. You determine what content can be efficiently produced using AI tools and estimate ROI potential.",
                verbose=True,
                allow_delegation=False,
                llm=self.llm_pro  # Use Pro for complex analysis
            ),
            Agent(
                role="Launch Strategy Synthesizer",
                goal="Create comprehensive go-to-market plans for new AI channels",
                backstory="You are a launch strategist who combines market intelligence into actionable plans. You synthesize all research into step-by-step launch strategies for new AI-generated YouTube channels.",
                verbose=True,
                allow_delegation=False,
                llm=self.llm_pro  # Use Pro for synthesis
            )
        ]
    
    def create_tasks(self, agents, channel_data):
        """Create analysis tasks for the 6-agent system"""
        
        # Extract key data for context
        channel_info = channel_data.get('channel', {})
        videos = channel_data.get('videos', [])
        comments = channel_data.get('comments', {})
        
        channel_name = channel_info.get('title', 'Unknown')
        total_videos = len(videos)
        total_comments = sum(len(c) for c in comments.values())
        
        # Context for all agents
        context = f'''
MARKET RESEARCH TARGET: {channel_name}
DATA COLLECTED: {total_videos} videos, {total_comments} comments
OBJECTIVE: Find opportunities for NEW AI-generated YouTube channels

CHANNEL DATA SUMMARY:
{json.dumps(channel_data, indent=2)[:2000]}...
'''

        tasks = []
        
        # Task 1: Market Opportunity Scout
        tasks.append(Task(
            description=f'''
Analyze the target channel data to identify MARKET OPPORTUNITIES for new AI-generated channels.

{context}

FOCUS ON:
1. Content gaps - what topics/angles are missing?
2. Underserved audience segments 
3. Low competition niches within this space
4. Seasonal or trending opportunities
5. Format innovations that could work better

OUTPUT as JSON:
{{
    "content_gaps": ["gap1", "gap2", "gap3"],
    "underserved_audiences": ["audience1", "audience2"],
    "opportunity_score": "1-10",
    "recommended_niches": ["niche1", "niche2"],
    "market_size_estimate": "small/medium/large"
}}
''',
            agent=agents[0],
            expected_output="JSON analysis of market opportunities"
        ))
        
        # Task 2: Content Strategy Architect
        tasks.append(Task(
            description=f'''
Extract VIRAL CONTENT PATTERNS and formats that can be replicated with AI tools.

{context}

ANALYZE:
1. Most successful video formats and structures
2. Title patterns that drive clicks
3. Thumbnail styles that work
4. Content length and pacing
5. AI-friendly production elements

OUTPUT as JSON:
{{
    "viral_patterns": ["pattern1", "pattern2"],
    "optimal_formats": ["format1", "format2"],
    "title_formulas": ["formula1", "formula2"],
    "ai_production_score": "1-10",
    "recommended_video_length": "X minutes"
}}
''',
            agent=agents[1],
            expected_output="JSON analysis of content strategy patterns"
        ))
        
        # Task 3: Audience Intelligence Analyst
        tasks.append(Task(
            description=f'''
Mine comments and engagement data to find UNMET AUDIENCE NEEDS.

{context}

LOOK FOR:
1. "I wish someone would make..." comments
2. Questions that aren't being answered
3. Complaints about current content
4. Requests for specific topics/angles
5. Audience pain points and desires

OUTPUT as JSON:
{{
    "unmet_needs": ["need1", "need2", "need3"],
    "audience_requests": ["request1", "request2"],
    "pain_points": ["pain1", "pain2"],
    "content_demand": "high/medium/low",
    "audience_sentiment": "positive/neutral/negative"
}}
''',
            agent=agents[2],
            expected_output="JSON analysis of audience intelligence"
        ))
        
        # Task 4: Competitive Landscape Mapper
        tasks.append(Task(
            description=f'''
Map the COMPETITIVE LANDSCAPE and find positioning opportunities for new entrants.

{context}

EVALUATE:
1. Channel size vs performance ratios
2. Content saturation levels
3. Competitive gaps and weaknesses
4. Barriers to entry
5. Success benchmarks for new channels

OUTPUT as JSON:
{{
    "competition_level": "low/medium/high",
    "entry_barriers": ["barrier1", "barrier2"],
    "positioning_opportunities": ["opportunity1", "opportunity2"],
    "performance_benchmarks": {{"views": "X", "subs": "Y"}},
    "competitive_advantages": ["advantage1", "advantage2"]
}}
''',
            agent=agents[3],
            expected_output="JSON analysis of competitive landscape"
        ))
        
        # Task 5: Production Intelligence Agent  
        tasks.append(Task(
            description=f'''
Assess AI PRODUCTION FEASIBILITY and ROI potential for identified opportunities.

{context}

ANALYZE:
1. Content complexity vs AI capabilities
2. Production time and cost estimates
3. Required tools and resources
4. Quality vs effort ratios
5. Scalability potential

OUTPUT as JSON:
{{
    "ai_feasibility_score": "1-10",
    "production_complexity": "low/medium/high",
    "estimated_cost_per_video": "$X",
    "recommended_ai_tools": ["tool1", "tool2"],
    "roi_potential": "low/medium/high"
}}
''',
            agent=agents[4],
            expected_output="JSON analysis of AI production intelligence"
        ))
        
        # Task 6: Launch Strategy Synthesizer
        tasks.append(Task(
            description=f'''
Create a comprehensive LAUNCH STRATEGY for a new AI-generated YouTube channel in this space.

{context}

SYNTHESIZE ALL RESEARCH INTO:
1. Step-by-step launch plan
2. Content calendar recommendations
3. Growth strategy and milestones
4. Risk mitigation strategies
5. Success metrics and KPIs

OUTPUT as JSON:
{{
    "launch_plan": ["step1", "step2", "step3"],
    "content_calendar": ["week1", "week2", "week3"],
    "growth_strategy": ["strategy1", "strategy2"],
    "success_metrics": ["metric1", "metric2"],
    "timeline": "X months to Y subscribers"
}}
''',
            agent=agents[5],
            expected_output="JSON comprehensive launch strategy"
        ))
        
        return tasks

    def analyze_channel(self, channel_id: str, max_videos: int = 20, max_comments: int = 50):
        """Main analysis method"""
        
        # Step 1: Get YouTube data
        if not self.tools_available:
            logger.warning("YouTube tools not available - using mock data")
            channel_data = self._get_mock_data(channel_id)
        else:
            logger.info(f"Collecting YouTube data for {channel_id}...")
            channel_data = self.youtube_tool.get_comprehensive_analysis(
                channel_id, max_videos, max_comments
            )
        
        # Step 2: Process with or without AI
        if self.ai_enabled:
            return self._process_data_with_ai(channel_data)
        else:
            return self._process_data_without_ai(channel_data)
    
    def _process_data_with_ai(self, channel_data):
        """Process channel data using CrewAI"""
        
        logger.info("Creating market research agents...")
        agents = self.create_agents()
        
        logger.info("Creating analysis tasks...")
        tasks = self.create_tasks(agents, channel_data)
        
        # Step 3: Create and run the crew
        logger.info("Running CrewAI crew...")
        crew = Crew(
            agents=agents,
            tasks=tasks,
            process=Process.sequential,
            verbose=True,
            memory=False,  # Disable memory for now to avoid issues
            cache=False    # Disable cache for now
        )
        
        # Run the crew and store it for later access to task outputs
        result = crew.kickoff()
        self.last_crew = crew  # Store the crew to access task outputs later
        
        # Step 4: Process and structure the results
        logger.info("Processing crew results...")
        logger.info(f"CrewAI result type: {type(result)}")
        logger.info(f"CrewAI result preview: {str(result)[:200]}")
        structured_results = self._process_crew_results(result, channel_data)
        
        return structured_results
    
    def _process_data_without_ai(self, channel_data):
        """Process channel data without AI - just structure the YouTube data"""
        
        # Extract channel info for metadata
        channel_info = channel_data.get('channel', {})
        channel_name = channel_info.get('title', 'Unknown Channel')
        videos = channel_data.get('videos', [])
        comments = channel_data.get('comments', {})
        
        # Basic analytics without AI
        total_views = sum(int(v.get('statistics', {}).get('viewCount', 0)) for v in videos)
        total_videos = len(videos)
        total_comments = sum(len(c) for c in comments.values())
        avg_views = total_views / total_videos if total_videos > 0 else 0
        
        # Top performing videos
        top_videos = sorted(videos, key=lambda x: int(x.get('statistics', {}).get('viewCount', 0)), reverse=True)[:5]
        
        results = {
            'market_opportunities': {
                'channel_name': channel_name,
                'channel_id': channel_info.get('id', ''),
                'analysis_agent': 'Market Opportunity Scout',
                'crew_analysis': f'Basic analysis: {total_videos} videos, {total_comments} comments analyzed',
                'data_summary': {
                    'videos_analyzed': total_videos,
                    'comments_analyzed': total_comments,
                    'total_views': total_views,
                    'average_views': int(avg_views)
                }
            },
            'content_strategy': {
                'analysis_agent': 'Content Strategy Architect',
                'crew_analysis': f'Top videos by views: {[v.get("snippet", {}).get("title", "")[:50] for v in top_videos[:3]]}'
            },
            'audience_intelligence': {
                'analysis_agent': 'Audience Intelligence Analyst', 
                'crew_analysis': f'Comments analyzed from {len(comments)} videos'
            },
            'competitive_landscape': {
                'analysis_agent': 'Competitive Landscape Mapper',
                'crew_analysis': f'Channel metrics: {total_views:,} total views across {total_videos} videos'
            },
            'production_intelligence': {
                'analysis_agent': 'Production Intelligence Agent',
                'crew_analysis': 'AI feasibility assessment completed'
            },
            'launch_strategy': {
                'analysis_agent': 'Launch Strategy Synthesizer',
                'crew_analysis': 'Market entry strategy formulated'
            },
            'crewai_metadata': {
                'agents_used': 6,
                'ai_enabled': self.ai_enabled,
                'tools_available': self.tools_available,
                'analysis_focus': 'Market Research for New AI Channel Creation'
            }
        }
        
        return results
    
    def _process_crew_results(self, crew_result, channel_data):
        """Process CrewAI results into structured format"""
        
        # Extract channel info for metadata
        channel_info = channel_data.get('channel', {})
        channel_name = channel_info.get('title', 'Unknown Channel') 
        videos = channel_data.get('videos', [])
        comments = channel_data.get('comments', {})
        
        # Get task outputs from the stored crew
        task_outputs = []
        if hasattr(self, 'last_crew') and hasattr(self.last_crew, 'tasks'):
            for task in self.last_crew.tasks:
                if hasattr(task, 'output'):
                    task_outputs.append({
                        'agent': task.agent.role if hasattr(task, 'agent') else 'Unknown',
                        'output': str(task.output)
                    })
        
        logger.info(f"Extracted {len(task_outputs)} task outputs")
        for i, output in enumerate(task_outputs):
            logger.info(f"Task {i+1} ({output['agent']}): {output['output'][:100]}...")
        
        # Structure results by agent
        results = {
            'market_opportunities': {
                'channel_name': channel_name,
                'channel_id': channel_info.get('id', ''),
                'analysis_agent': 'Market Opportunity Scout',
                'crew_analysis': task_outputs[0]['output'] if len(task_outputs) > 0 else str(crew_result),
                'data_summary': {
                    'videos_analyzed': len(videos),
                    'comments_analyzed': sum(len(c) for c in comments.values())
                }
            },
            'content_strategy': {
                'analysis_agent': 'Content Strategy Architect',
                'crew_analysis': task_outputs[1]['output'] if len(task_outputs) > 1 else 'Content strategy analysis completed'
            },
            'audience_intelligence': {
                'analysis_agent': 'Audience Intelligence Analyst',
                'crew_analysis': task_outputs[2]['output'] if len(task_outputs) > 2 else 'Audience intelligence completed'
            },
            'competitive_landscape': {
                'analysis_agent': 'Competitive Landscape Mapper', 
                'crew_analysis': task_outputs[3]['output'] if len(task_outputs) > 3 else 'Competitive analysis completed'
            },
            'production_intelligence': {
                'analysis_agent': 'Production Intelligence Agent',
                'crew_analysis': task_outputs[4]['output'] if len(task_outputs) > 4 else 'Production intelligence completed'
            },
            'launch_strategy': {
                'analysis_agent': 'Launch Strategy Synthesizer',
                'crew_analysis': task_outputs[5]['output'] if len(task_outputs) > 5 else 'Launch strategy completed'
            },
            'crewai_metadata': {
                'agents_used': len(task_outputs),
                'ai_enabled': self.ai_enabled,
                'tools_available': self.tools_available,
                'analysis_focus': 'Market Research for New AI Channel Creation',
                'raw_result': str(crew_result) if crew_result else None
            }
        }
        
        return results

    def _get_mock_data(self, channel_id):
        """Generate mock data when tools aren't available"""
        return {
            'channel': {
                'id': channel_id,
                'title': f'Mock Channel {channel_id}',
                'description': 'Mock channel for testing',
                'subscriberCount': '100000'
            },
            'videos': [
                {
                    'id': f'video_{i}',
                    'snippet': {'title': f'Mock Video {i}', 'publishedAt': '2024-01-01'},
                    'statistics': {'viewCount': str(1000 * i), 'likeCount': str(10 * i)}
                } for i in range(1, 6)
            ],
            'comments': {
                'video_1': ['Great video!', 'Very helpful'],
                'video_2': ['Love this content', 'More please!']
            }
        }

# Initialize CrewAI instance
crew_ai = None

def get_crewai():
    """Get or create CrewAI instance"""
    global crew_ai
    if crew_ai is None:
        crew_ai = MarketResearchCrewAI()
    return crew_ai

# Routes
@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve web interface"""
    return '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>YoutubePulse - AI Market Research</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif; 
            background: #0f172a;
            color: #f1f5f9; 
            line-height: 1.6;
        }
        
        /* CLEAN HEADER */
        .header {
            background: #1e293b;
            border-bottom: 1px solid #334155;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .logo-icon {
            width: 32px;
            height: 32px;
            background: #8b5cf6;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        .logo-text {
            font-size: 18px;
            font-weight: 600;
            color: #f1f5f9;
        }
        
        .header-search {
            flex: 1;
            max-width: 400px;
            margin: 0 24px;
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 8px 12px;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 6px;
            color: #f1f5f9;
            font-size: 14px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #8b5cf6;
        }
        
        /* RIGHT SIDEBAR NAVIGATION */
        .layout {
            display: flex;
            min-height: calc(100vh - 65px);
        }
        
        .main-content {
            flex: 1;
            padding: 24px;
        }
        
        .sidebar {
            width: 280px;
            background: #1e293b;
            border-left: 1px solid #334155;
            padding: 24px 16px;
        }
        
        .sidebar-section {
            margin-bottom: 32px;
        }
        
        .sidebar-title {
            font-size: 12px;
            font-weight: 600;
            color: #64748b;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 12px;
        }
        
        .nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            border-radius: 6px;
            color: #94a3b8;
            text-decoration: none;
            transition: all 0.2s ease;
            margin-bottom: 4px;
        }
        
        .nav-item:hover {
            background: #334155;
            color: #f1f5f9;
        }
        
        .nav-item.active {
            background: #8b5cf6;
            color: white;
        }
        
        .nav-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        /* MAIN CONTENT AREA */
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .page-title {
            font-size: 32px;
            font-weight: 700;
            color: #f1f5f9;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            font-size: 18px;
            color: #64748b;
            margin-bottom: 32px;
        }
        
        /* CARDS */
        .card {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 24px;
        }
        
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid #334155;
        }
        
        .card-icon {
            width: 40px;
            height: 40px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 16px;
            background: #8b5cf6;
            color: white;
        }
        
        .card-title h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #f1f5f9;
        }
        
        .card-title p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #64748b;
        }
        
        /* FORM ELEMENTS */
        .form-row {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            align-items: end;
            margin-bottom: 20px;
        }
        
        .form-group {
            flex: 1;
            min-width: 200px;
        }
        
        .form-group.auto {
            flex: none;
        }
        
        label {
            display: block;
            margin-bottom: 6px;
            color: #94a3b8;
            font-weight: 500;
            font-size: 14px;
        }
        
        input {
            width: 100%;
            padding: 10px 12px;
            background: #334155;
            border: 1px solid #475569;
            border-radius: 6px;
            color: #f1f5f9;
            font-size: 14px;
        }
        
        input:focus {
            outline: none;
            border-color: #8b5cf6;
        }
        
        button {
            background: #8b5cf6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.2s ease;
        }
        
        button:hover {
            background: #7c3aed;
        }
        
        button:disabled {
            background: #475569;
            cursor: not-allowed;
        }
        
        /* RESULTS LAYOUT - 3 COLUMNS */
        .results-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 24px;
            margin-top: 32px;
        }
        
        @media (max-width: 1200px) {
            .results-grid {
                grid-template-columns: 1fr 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .results-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .result-column {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 20px;
            height: fit-content;
        }
        
        .column-header {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #334155;
        }
        
        .column-icon {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            margin-right: 12px;
            background: #8b5cf6;
            color: white;
        }
        
        .column-title {
            font-size: 16px;
            font-weight: 600;
            color: #f1f5f9;
        }
        
        /* METRICS */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 16px;
            margin: 24px 0;
        }
        
        .metric-card {
            background: #334155;
            border: 1px solid #475569;
            border-radius: 6px;
            padding: 16px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: 700;
            color: #8b5cf6;
            margin-bottom: 4px;
        }
        
        .metric-label {
            font-size: 12px;
            color: #94a3b8;
            text-transform: uppercase;
            font-weight: 500;
        }
        
        /* STATUS MESSAGES */
        .status {
            padding: 12px 16px;
            border-radius: 6px;
            margin: 16px 0;
            font-size: 14px;
        }
        
        .status.success {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            color: #4ade80;
        }
        
        .status.error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            color: #f87171;
        }
        
        .status.info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.2);
            color: #60a5fa;
        }
        
        /* PROGRESS MODAL - SIMPLIFIED */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .modal.show {
            display: flex;
        }
        
        .modal-content {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 32px;
            max-width: 500px;
            width: 90%;
            text-align: center;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #334155;
            border-radius: 4px;
            overflow: hidden;
            margin: 16px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #8b5cf6;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .agent-list {
            margin: 24px 0;
            text-align: left;
        }
        
        .agent-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            font-size: 14px;
        }
        
        .agent-status {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .agent-pending {
            background: #475569;
            color: #94a3b8;
        }
        
        .agent-active {
            background: #8b5cf6;
            color: white;
        }
        
        .agent-completed {
            background: #10b981;
            color: white;
        }
        
        /* RESPONSIVE */
        @media (max-width: 768px) {
            .layout {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                border-left: none;
                border-top: 1px solid #334155;
            }
            
            .header {
                flex-direction: column;
                gap: 16px;
            }
            
            .header-search {
                margin: 0;
                max-width: none;
            }
        }
    </style>
</head>
<body>
    <!-- HEADER -->
    <div class="header">
        <div class="logo">
            <div class="logo-icon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                </svg>
            </div>
            <div class="logo-text">YoutubePulse</div>
        </div>
        <div class="header-search">
            <input type="text" class="search-input" placeholder="Search channels, tools, or insights...">
        </div>
        <div style="font-size: 14px; color: #64748b;">
            v2.0 • Market Research
        </div>
    </div>

    <div class="layout">
        <!-- MAIN CONTENT -->
        <div class="main-content">
            <div class="container">
                <h1 class="page-title">YouTube Market Research</h1>
                <p class="page-subtitle">Analyze competitor channels to find opportunities for new AI-generated content</p>
                
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                            </svg>
                        </div>
                        <div class="card-title">
                            <h3>Competitive Intelligence Analysis</h3>
                            <p>6-agent AI pipeline for market opportunities and content strategy</p>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label>Channel to Analyze</label>
                            <input type="text" id="channelId" placeholder="Channel ID or @handle (e.g. @MrBeast)" />
                        </div>
                        <div class="form-group auto">
                            <label>Videos</label>
                            <input type="number" id="maxVideos" value="20" min="5" max="50" style="width: 80px;" />
                        </div>
                        <div class="form-group auto">
                            <label>Comments</label>
                            <input type="number" id="maxComments" value="50" min="10" max="100" style="width: 80px;" />
                        </div>
                        <div class="form-group auto">
                            <button onclick="startAnalysis()" id="analyzeBtn">Start Analysis</button>
                        </div>
                    </div>
                    
                    <div id="status"></div>
                </div>
                
                <!-- RESULTS WILL GO HERE -->
                <div id="results"></div>
                
                <div class="card">
                    <div class="card-header">
                        <div class="card-icon">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                            </svg>
                        </div>
                        <div class="card-title">
                            <h3>Recent Analyses</h3>
                            <p>Previously analyzed channels and their insights</p>
                        </div>
                    </div>
                    <div id="recent">Loading recent analyses...</div>
                    <button onclick="loadRecent()" style="margin-top: 16px;">Refresh</button>
                </div>
            </div>
        </div>
        
        <!-- RIGHT SIDEBAR -->
        <div class="sidebar">
            <div class="sidebar-section">
                <div class="sidebar-title">Analysis Tools</div>
                <a href="#" class="nav-item active">
                    <div class="nav-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <span>Market Research</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14.828 14.828a4 4 0 0 1-5.656 0A4 4 0 0 1 12 6a4 4 0 0 1 2.828 8.828z"/>
                            <path d="M21 21l-6-6m2-5a7 7 0 1 1-14 0 7 7 0 0 1 14 0z"/>
                        </svg>
                    </div>
                    <span>Video Analyzer</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
                            <path d="M14 2v6h6M16 13H8M16 17H8M10 9H8"/>
                        </svg>
                    </div>
                    <span>Script Analyzer</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <span>Channel DNA</span>
                </a>
            </div>
            
            <div class="sidebar-section">
                <div class="sidebar-title">Tracking & Monitoring</div>
                <a href="#" class="nav-item">
                    <div class="nav-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 3v18h18V3H3zm8 16H5v-6h6v6zm0-8H5V5h6v6zm8 8h-6v-6h6v6zm0-8h-6V5h6v6z"/>
                        </svg>
                    </div>
                    <span>Channel Tracking</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z"/>
                        </svg>
                    </div>
                    <span>Niche Tracking</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                        </svg>
                    </div>
                    <span>Growth Tracker</span>
                </a>
            </div>
            
            <div class="sidebar-section">
                <div class="sidebar-title">AI Production</div>
                <a href="#" class="nav-item">
                    <div class="nav-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                        </svg>
                    </div>
                    <span>AI Content Planner</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <span>ROI Calculator</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9 11H7v9h2v-9zm4 0h-2v9h2v-9zm4 0h-2v9h2v-9zm2-7H3v2h18V4zM5 20h14v2H5v-2z"/>
                        </svg>
                    </div>
                    <span>Launch Strategy</span>
                </a>
            </div>
            
            <div class="sidebar-section">
                <div class="sidebar-title">Settings</div>
                <a href="#" class="nav-item">
                    <div class="nav-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/>
                        </svg>
                    </div>
                    <span>API Keys</span>
                </a>
                <a href="#" class="nav-item">
                    <div class="nav-icon">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                    </div>
                    <span>Export Data</span>
                </a>
            </div>
        </div>
    </div>

    <!-- PROGRESS MODAL -->
    <div id="analysisModal" class="modal">
        <div class="modal-content">
            <h2 id="modalTitle">Analyzing Market Intelligence</h2>
            <div id="modalStatus" style="color: #64748b; margin: 16px 0;">
                Initializing 6-agent analysis pipeline...
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            
            <div id="modalProgress" style="color: #8b5cf6; font-weight: 600; margin-bottom: 24px;">
                0% Complete
            </div>
            
            <div class="agent-list" id="agentList">
                <div class="agent-item">
                    <div class="agent-status agent-pending" id="agent-0">1</div>
                    <span>Market Opportunity Scout</span>
                </div>
                <div class="agent-item">
                    <div class="agent-status agent-pending" id="agent-1">2</div>
                    <span>Content Strategy Architect</span>
                </div>
                <div class="agent-item">
                    <div class="agent-status agent-pending" id="agent-2">3</div>
                    <span>Audience Intelligence Analyst</span>
                </div>
                <div class="agent-item">
                    <div class="agent-status agent-pending" id="agent-3">4</div>
                    <span>Competitive Landscape Mapper</span>
                </div>
                <div class="agent-item">
                    <div class="agent-status agent-pending" id="agent-4">5</div>
                    <span>Production Intelligence Agent</span>
                </div>
                <div class="agent-item">
                    <div class="agent-status agent-pending" id="agent-5">6</div>
                    <span>Launch Strategy Synthesizer</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentAnalysisId = null;
        let statusInterval = null;
        
        // FORMATTING FUNCTIONS
        function formatAnalysisData(data, title, icon) {
            if (!data) return '<div style="color: #64748b; text-align: center; padding: 20px;">No data available</div>';
            
            const content = data.crew_analysis || data.parsed_data || 'Analysis completed';
            
            return `
                <div style="margin-bottom: 16px;">
                    <div style="color: #94a3b8; font-size: 12px; margin-bottom: 8px; text-transform: uppercase; font-weight: 500;">
                        ${data.analysis_agent || title}
                    </div>
                    <div style="color: #f1f5f9; line-height: 1.6;">
                        ${formatContent(content)}
                    </div>
                </div>
            `;
        }
        
        function formatContent(content) {
            if (!content) return 'No content available';
            
            // Try to extract JSON if wrapped in code blocks
            const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
            if (jsonMatch) {
                try {
                    const parsed = JSON.parse(jsonMatch[1]);
                    return formatJSON(parsed);
                } catch (e) {
                    return formatText(content);
                }
            }
            
            return formatText(content);
        }
        
        function formatJSON(obj) {
            if (!obj || typeof obj !== 'object') return String(obj);
            
            return Object.entries(obj).map(([key, value]) => {
                const cleanKey = key.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1');
                const titleKey = cleanKey.charAt(0).toUpperCase() + cleanKey.slice(1);
                
                if (Array.isArray(value)) {
                    return `
                        <div style="margin: 12px 0;">
                            <div style="color: #8b5cf6; font-weight: 600; margin-bottom: 6px;">${titleKey}:</div>
                            <ul style="margin: 0; padding-left: 20px; color: #94a3b8;">
                                ${value.map(item => `<li style="margin: 4px 0;">${item}</li>`).join('')}
                            </ul>
                        </div>
                    `;
                } else if (typeof value === 'object' && value !== null) {
                    return `
                        <div style="margin: 12px 0;">
                            <div style="color: #8b5cf6; font-weight: 600; margin-bottom: 6px;">${titleKey}:</div>
                            <div style="margin-left: 16px;">${formatJSON(value)}</div>
                        </div>
                    `;
                } else {
                    return `
                        <div style="margin: 8px 0;">
                            <span style="color: #8b5cf6; font-weight: 500;">${titleKey}:</span>
                            <span style="color: #f1f5f9; margin-left: 8px;">${value}</span>
                        </div>
                    `;
                }
            }).join('');
        }
        
        function formatText(text) {
            return text.split('\n').filter(line => line.trim()).map(line => 
                `<div style="margin: 4px 0; color: #e2e8f0;">${line.trim()}</div>`
            ).join('');
        }
        
        // MODAL FUNCTIONS
        function showModal() {
            document.getElementById('analysisModal').classList.add('show');
        }
        
        function hideModal() {
            document.getElementById('analysisModal').classList.remove('show');
        }
        
        function updateModal(status) {
            const progress = status.progress || 0;
            const step = status.current_step || 'Processing...';
            
            document.getElementById('modalStatus').textContent = step;
            document.getElementById('modalProgress').textContent = `${progress}% Complete`;
            document.getElementById('progressFill').style.width = `${progress}%`;
            
            // Update agent statuses
            const completedAgents = Math.floor(progress / 16.67); // 6 agents, ~16.67% each
            for (let i = 0; i < 6; i++) {
                const agent = document.getElementById(`agent-${i}`);
                if (i < completedAgents) {
                    agent.className = 'agent-status agent-completed';
                    agent.textContent = '✓';
                } else if (i === completedAgents && progress < 100) {
                    agent.className = 'agent-status agent-active';
                    agent.textContent = '⚡';
                } else {
                    agent.className = 'agent-status agent-pending';
                    agent.textContent = (i + 1).toString();
                }
            }
        }
        
        // MAIN FUNCTIONS
        async function startAnalysis() {
            const channelId = document.getElementById('channelId').value.trim();
            const maxVideos = document.getElementById('maxVideos').value;
            const maxComments = document.getElementById('maxComments').value;
            
            if (!channelId) {
                showMessage('Please enter a channel ID', 'error');
                return;
            }
            
            try {
                document.getElementById('analyzeBtn').disabled = true;
                document.getElementById('analyzeBtn').textContent = 'Starting Analysis...';
                
                showModal();
                
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        channel_id: channelId,
                        max_videos: parseInt(maxVideos),
                        max_comments: parseInt(maxComments)
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    currentAnalysisId = data.analysis_id;
                    showMessage(`Analysis started! ID: ${data.analysis_id}`, 'success');
                    startPolling();
                } else {
                    hideModal();
                    throw new Error(data.detail);
                }
                
            } catch (error) {
                hideModal();
                showMessage(`Error: ${error.message}`, 'error');
                document.getElementById('analyzeBtn').disabled = false;
                document.getElementById('analyzeBtn').textContent = 'Start Analysis';
            }
        }
        
        function startPolling() {
            statusInterval = setInterval(async () => {
                if (!currentAnalysisId) return;
                
                try {
                    const response = await fetch(`/api/status/${currentAnalysisId}`);
                    const status = await response.json();
                    
                    updateModal(status);
                    
                    if (status.status === 'completed') {
                        setTimeout(() => updateModal({progress: 100, current_step: 'Analysis complete!'}), 500);
                        setTimeout(hideModal, 2000);
                        
                        clearInterval(statusInterval);
                        await loadResults(currentAnalysisId);
                        loadRecent();
                        document.getElementById('analyzeBtn').disabled = false;
                        document.getElementById('analyzeBtn').textContent = 'Start Analysis';
                        
                    } else if (status.status === 'failed') {
                        clearInterval(statusInterval);
                        hideModal();
                        showMessage('Analysis failed', 'error');
                        document.getElementById('analyzeBtn').disabled = false;
                        document.getElementById('analyzeBtn').textContent = 'Start Analysis';
                    }
                    
                } catch (error) {
                    console.error('Status error:', error);
                }
            }, 2000);
        }
        
        async function loadResults(analysisId) {
            try {
                const response = await fetch(`/api/results/${analysisId}`);
                const results = await response.json();
                
                // Show metrics
                const metricsHtml = `
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-value">${results.market_opportunities?.channel_name || 'Unknown'}</div>
                            <div class="metric-label">Channel</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${results.market_opportunities?.data_summary?.videos_analyzed || 0}</div>
                            <div class="metric-label">Videos</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${results.market_opportunities?.data_summary?.comments_analyzed || 0}</div>
                            <div class="metric-label">Comments</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${results.crewai_metadata?.agents_used || 6}</div>
                            <div class="metric-label">AI Agents</div>
                        </div>
                    </div>
                `;
                
                // Show results in 3 columns
                const resultsHtml = `
                    <div style="margin-top: 32px;">
                        <div style="text-align: center; margin-bottom: 32px;">
                            <h2 style="color: #f1f5f9; margin-bottom: 8px;">✅ Analysis Complete</h2>
                            <p style="color: #64748b;">Market research and competitive intelligence results</p>
                        </div>
                        
                        ${metricsHtml}
                        
                        <div class="results-grid">
                            <!-- Column 1: Market Intelligence -->
                            <div class="result-column">
                                <div class="column-header">
                                    <div class="column-icon">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                        </svg>
                                    </div>
                                    <div class="column-title">Market Intelligence</div>
                                </div>
                                ${formatAnalysisData(results.market_opportunities, 'Market Opportunities', '🎯')}
                                ${formatAnalysisData(results.competitive_landscape, 'Competitive Landscape', '🗺️')}
                            </div>
                            
                            <!-- Column 2: Content Strategy -->
                            <div class="result-column">
                                <div class="column-header">
                                    <div class="column-icon">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z"/>
                                        </svg>
                                    </div>
                                    <div class="column-title">Content Strategy</div>
                                </div>
                                ${formatAnalysisData(results.content_strategy, 'Content Strategy', '📈')}
                                ${formatAnalysisData(results.audience_intelligence, 'Audience Intelligence', '🧠')}
                            </div>
                            
                            <!-- Column 3: Production & Launch -->
                            <div class="result-column">
                                <div class="column-header">
                                    <div class="column-icon">
                                        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
                                        </svg>
                                    </div>
                                    <div class="column-title">Production & Launch</div>
                                </div>
                                ${formatAnalysisData(results.production_intelligence, 'Production Intelligence', '🤖')}
                                ${formatAnalysisData(results.launch_strategy, 'Launch Strategy', '🚀')}
                            </div>
                        </div>
                    </div>
                `;
                
                document.getElementById('results').innerHTML = resultsHtml;
                
            } catch (error) {
                showMessage(`Failed to load results: ${error.message}`, 'error');
            }
        }
        
        async function loadRecent() {
            try {
                const response = await fetch('/api/recent');
                const analyses = await response.json();
                
                let html = '';
                analyses.forEach(analysis => {
                    html += `
                        <div style="padding: 12px; border: 1px solid #334155; border-radius: 6px; margin-bottom: 8px;">
                            <div style="font-weight: 500; color: #f1f5f9;">${analysis.channel_name}</div>
                            <div style="font-size: 12px; color: #64748b; margin-top: 4px;">
                                ${analysis.status} • ${new Date(analysis.created_at).toLocaleString()}
                            </div>
                        </div>
                    `;
                });
                
                document.getElementById('recent').innerHTML = html || 'No recent analyses';
                
            } catch (error) {
                document.getElementById('recent').innerHTML = 'Error loading recent analyses';
            }
        }
        
        function showMessage(message, type) {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            document.getElementById('status').innerHTML = '';
            document.getElementById('status').appendChild(div);
        }
        
        // Load recent analyses on page load
        window.onload = loadRecent;
    </script>
</body>
</html>
'''

@app.get("/health")
async def health():
    """Health check"""
    crew = get_crewai()
    return {
        "status": "healthy",
        "crewai_available": True,
        "ai_enabled": crew.ai_enabled,
        "tools_available": crew.tools_available,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

@app.post("/api/analyze", response_model=AnalysisResponse)
async def analyze(request: AnalysisRequest, background_tasks: BackgroundTasks):
    """Start CrewAI analysis"""
    analysis_id = str(uuid.uuid4())
    
    analysis_store[analysis_id] = {
        "analysis_id": analysis_id,
        "channel_id": request.channel_id,
        "status": "pending",
        "created_at": datetime.now(timezone.utc),
        "progress": 0,
        "current_step": "Initializing CrewAI...",
        "results": None,
        "channel_name": f"Channel {request.channel_id}"
    }
    
    background_tasks.add_task(run_crewai_analysis, analysis_id, request)
    
    return AnalysisResponse(
        analysis_id=analysis_id,
        status="pending", 
        message=f"CrewAI analysis started for {request.channel_id}"
    )

@app.get("/api/status/{analysis_id}")
async def get_status(analysis_id: str):
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    return analysis_store[analysis_id]

@app.get("/api/results/{analysis_id}")
async def get_results(analysis_id: str):
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    analysis = analysis_store[analysis_id]
    if analysis["status"] != "completed":
        raise HTTPException(status_code=400, detail="Analysis not completed")
    
    return analysis["results"]

@app.get("/api/recent")
async def get_recent():
    recent = sorted(analysis_store.values(), key=lambda x: x["created_at"], reverse=True)[:10]
    return [{
        "analysis_id": r["analysis_id"],
        "channel_id": r["channel_id"],
        "channel_name": r.get("channel_name", "Unknown"),
        "status": r["status"],
        "created_at": r["created_at"]
    } for r in recent]

async def run_crewai_analysis(analysis_id: str, request: AnalysisRequest):
    """Run CrewAI analysis in background with progress tracking"""
    import asyncio
    
    try:
        analysis_store[analysis_id]["status"] = "in_progress"
        analysis_store[analysis_id]["current_step"] = "Initializing market research agents..."
        analysis_store[analysis_id]["progress"] = 5
        await asyncio.sleep(1)
        
        # Get CrewAI instance
        crew = get_crewai()
        
        analysis_store[analysis_id]["current_step"] = "Collecting YouTube data..."
        analysis_store[analysis_id]["progress"] = 15
        await asyncio.sleep(0.5)
        
        analysis_store[analysis_id]["current_step"] = "Market Opportunity Scout analyzing..."
        analysis_store[analysis_id]["progress"] = 25
        await asyncio.sleep(0.5)
        
        analysis_store[analysis_id]["current_step"] = "Content Strategy Architect working..."
        analysis_store[analysis_id]["progress"] = 40
        await asyncio.sleep(0.5)
        
        analysis_store[analysis_id]["current_step"] = "Audience Intelligence Analyst mining comments..."
        analysis_store[analysis_id]["progress"] = 55
        await asyncio.sleep(0.5)
        
        analysis_store[analysis_id]["current_step"] = "Competitive Landscape Mapper benchmarking..."
        analysis_store[analysis_id]["progress"] = 70
        await asyncio.sleep(0.5)
        
        analysis_store[analysis_id]["current_step"] = "Production Intelligence Agent assessing..."
        analysis_store[analysis_id]["progress"] = 85
        await asyncio.sleep(0.5)
        
        analysis_store[analysis_id]["current_step"] = "Launch Strategy Synthesizer finalizing..."
        analysis_store[analysis_id]["progress"] = 95
        
        # Run the analysis
        results = crew.analyze_channel(request.channel_id, request.max_videos, request.max_comments)
        
        # Extract channel name
        channel_name = results.get('market_opportunities', {}).get('channel_name', f"Channel {request.channel_id}")
        
        analysis_store[analysis_id]["current_step"] = "Analysis complete!"
        analysis_store[analysis_id]["progress"] = 100
        analysis_store[analysis_id]["status"] = "completed"
        analysis_store[analysis_id]["channel_name"] = channel_name
        analysis_store[analysis_id]["results"] = results
        
        logger.info(f"Analysis completed for {analysis_id}")
        
    except Exception as e:
        logger.error(f"Analysis failed for {analysis_id}: {e}")
        analysis_store[analysis_id]["status"] = "failed"
        analysis_store[analysis_id]["current_step"] = f"Error: {str(e)}"

if __name__ == "__main__":
    import uvicorn
    
    print("🔴 YoutubePulse - AI Creator Intelligence Platform")
    print("=" * 50)
    
    # Check environment
    youtube_key = os.getenv('YOUTUBE_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY')
    
    print(f"🔑 YouTube API: {'✅' if youtube_key else '❌'}")
    print(f"🤖 Gemini API: {'✅' if gemini_key else '❌'}")
    print(f"🚀 CrewAI: ✅ 6-Agent Market Research Pipeline")
    
    if not youtube_key:
        print("\n⚠️  Need YouTube API key for data collection")
    
    print("\n🚀 Starting server...")
    print("🌐 Open http://localhost:8001")
    print("⌨️  Press Ctrl+C to stop")
    print("-" * 50)
    
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")
