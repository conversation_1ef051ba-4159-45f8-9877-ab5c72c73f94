#!/usr/bin/env python3
"""
Test Hero Section Design Enhancements
"""

import requests
import time
import subprocess
import sys
import os
from datetime import datetime

class HeroSectionTester:
    def __init__(self):
        self.base_url = "http://localhost:8003"
        self.server_process = None
        self.test_pages = [
            ("/", "Dashboard"),
            ("/video-analyzer", "Video Analyzer"),
            ("/channel-analyzer", "Channel Analyzer"),
            ("/market-research", "Market Research Hub"),
            ("/agent/comment", "Comment Intelligence")
        ]
    
    def start_server(self, timeout=30):
        """Start the main application server"""
        print("🚀 Starting server...")
        try:
            self.server_process = subprocess.Popen(
                [sys.executable, "working_crewai_app_tabbed.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )
            
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    response = requests.get(f"{self.base_url}/health", timeout=5)
                    if response.status_code == 200:
                        print("✅ Server started successfully")
                        return True
                except requests.exceptions.RequestException:
                    time.sleep(1)
                    continue
            
            print("❌ Server failed to start within timeout")
            return False
            
        except Exception as e:
            print(f"❌ Failed to start server: {e}")
            return False
    
    def stop_server(self):
        """Stop the server"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                print("🛑 Server stopped")
            except subprocess.TimeoutExpired:
                self.server_process.kill()
                print("🛑 Server force killed")
            except Exception as e:
                print(f"⚠️ Error stopping server: {e}")
    
    def test_hero_section_css_styles(self):
        """Test if the CSS file contains enhanced hero section styles"""
        print("\n🎨 Testing Hero Section CSS Styles...")
        try:
            response = requests.get(f"{self.base_url}/static/styles/youtube-research-v2-design-system.css", timeout=10)
            if response.status_code == 200:
                print("✅ CSS file is accessible")
                
                css_content = response.text
                
                # Check for enhanced hero section styles
                required_styles = [
                    'background: linear-gradient(135deg,',  # Welcome section gradient
                    'radial-gradient(circle at 20% 20%',  # Radial gradient patterns
                    'animation: welcomeGlow',  # Animation for welcome section
                    '@keyframes welcomeGlow',  # Keyframe animation
                    '.premium-card-gradient::before',  # Premium card enhancements
                    'repeating-linear-gradient(',  # Subtle pattern overlay
                    '.premium-card-header::before',  # Header background patterns
                    'rgba(139, 92, 246, 0.03)',  # Purple accent colors
                    'position: relative',  # Proper positioning for overlays
                    'overflow: hidden'  # Proper overflow handling
                ]
                
                missing_styles = []
                for style in required_styles:
                    if style not in css_content:
                        missing_styles.append(style)
                
                if missing_styles:
                    print(f"❌ Missing hero section styles: {missing_styles}")
                    return False
                else:
                    print("✅ All enhanced hero section styles found")
                    
                    # Check for animation keyframes
                    if 'welcomeGlow' in css_content and '@keyframes' in css_content:
                        print("✅ Animation keyframes properly defined")
                    else:
                        print("⚠️ Animation keyframes may be missing")
                    
                    return True
            else:
                print(f"❌ CSS file not accessible: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error accessing CSS file: {e}")
            return False
    
    def test_hero_elements_in_pages(self):
        """Test if pages contain hero elements that will use the enhanced styles"""
        print("\n📄 Testing Hero Elements in Pages...")
        
        all_pages_pass = True
        
        for url, name in self.test_pages:
            try:
                response = requests.get(f"{self.base_url}{url}", timeout=10)
                if response.status_code == 200:
                    html_content = response.text
                    
                    # Check for hero section classes
                    hero_classes = [
                        'welcome-section',  # Dashboard welcome section
                        'premium-card-header',  # Card headers acting as hero sections
                        'premium-card-gradient',  # Gradient cards
                        'analyzer-form-section'  # Form sections with headers
                    ]
                    
                    found_heroes = []
                    for hero_class in hero_classes:
                        if hero_class in html_content:
                            found_heroes.append(hero_class)
                    
                    if found_heroes:
                        print(f"   ✅ {name}: Found hero elements: {', '.join(found_heroes)}")
                    else:
                        print(f"   ⚠️ {name}: No hero elements found")
                        
                else:
                    print(f"   ❌ {name}: Failed to load (status: {response.status_code})")
                    all_pages_pass = False
                    
            except Exception as e:
                print(f"   ❌ {name}: Error loading page: {e}")
                all_pages_pass = False
        
        return all_pages_pass
    
    def test_visual_consistency(self):
        """Test that all pages load without errors and maintain visual consistency"""
        print("\n🔍 Testing Visual Consistency...")
        
        all_consistent = True
        
        for url, name in self.test_pages:
            try:
                response = requests.get(f"{self.base_url}{url}", timeout=10)
                if response.status_code == 200:
                    html_content = response.text
                    
                    # Check for design system CSS reference
                    if 'youtube-research-v2-design-system.css' in html_content:
                        print(f"   ✅ {name}: Design system CSS properly linked")
                    else:
                        print(f"   ❌ {name}: Design system CSS not found")
                        all_consistent = False
                        
                else:
                    print(f"   ❌ {name}: Failed to load (status: {response.status_code})")
                    all_consistent = False
                    
            except Exception as e:
                print(f"   ❌ {name}: Error loading page: {e}")
                all_consistent = False
        
        return all_consistent
    
    def run_tests(self):
        """Run all tests"""
        print("🎨 Hero Section Design Enhancement Test")
        print("=" * 50)
        
        # Start server
        if not self.start_server():
            return False
        
        try:
            # Test CSS hero section styles
            css_test = self.test_hero_section_css_styles()
            
            # Test hero elements in pages
            elements_test = self.test_hero_elements_in_pages()
            
            # Test visual consistency
            consistency_test = self.test_visual_consistency()
            
            # Summary
            print(f"\n📊 Test Results:")
            print(f"   CSS Hero Styles: {'✅ PASS' if css_test else '❌ FAIL'}")
            print(f"   Hero Elements: {'✅ PASS' if elements_test else '❌ FAIL'}")
            print(f"   Visual Consistency: {'✅ PASS' if consistency_test else '❌ FAIL'}")
            
            if css_test and elements_test and consistency_test:
                print(f"\n🎉 All tests passed! Hero sections are enhanced with visual interest.")
                print(f"   ✨ Welcome section has gradient background with radial patterns")
                print(f"   ✨ Subtle animation adds life to the welcome section")
                print(f"   ✨ Premium card headers have enhanced backgrounds")
                print(f"   ✨ Gradient cards feature sophisticated multi-layer backgrounds")
                print(f"   ✨ All hero sections maintain purple brand consistency")
                print(f"   ✨ Visual depth added while preserving professional appearance")
                return True
            else:
                print(f"\n⚠️ Some tests failed. Hero section design needs attention.")
                return False
                
        finally:
            self.stop_server()

if __name__ == "__main__":
    tester = HeroSectionTester()
    success = tester.run_tests()
    sys.exit(0 if success else 1)
