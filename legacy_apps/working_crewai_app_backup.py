"""
Working CrewAI YouTube Research v2 App
Properly implemented CrewAI Channel DNA Analysis
"""

import os
import uuid
import json
from datetime import datetime
from typing import Dict, Any
from fastapi import FastAP<PERSON>, BackgroundTasks, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
import logging

# CrewAI imports
from crewai import Agent, Crew, Task, Process
from crewai.llm import LLM

# Load environment variables
from dotenv import load_dotenv
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(title="YouTube Research v2 - CrewAI")

# Enable CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Simple in-memory storage
analysis_store = {}

# Import our YouTube tools
from src.tools.youtube_tools import YouTube<PERSON>hannelAnalysisTool

# Pydantic models
class AnalysisRequest(BaseModel):
    channel_id: str
    max_videos: int = 20
    max_comments: int = 50

class AnalysisResponse(BaseModel):
    analysis_id: str
    status: str
    message: str

class MarketResearchCrewAI:
    """CrewAI implementation of Market Research for AI Channel Creation"""
    
    def __init__(self):
        # Initialize Gemini models
        gemini_api_key = os.getenv('GEMINI_API_KEY')
        if gemini_api_key:
            # Set environment variable for LiteLLM
            os.environ['GEMINI_API_KEY'] = gemini_api_key
            
            self.llm_pro = LLM(
                model="gemini/gemini-1.5-pro"
            )
            self.llm_flash = LLM(
                model="gemini/gemini-1.5-flash"
            )
            self.ai_enabled = True
        else:
            self.llm_pro = None
            self.llm_flash = None
            self.ai_enabled = False
            
        # Initialize YouTube tools
        youtube_api_key = os.getenv('YOUTUBE_API_KEY')
        if youtube_api_key:
            self.youtube_tool = YouTubeChannelAnalysisTool(
                youtube_api_key=youtube_api_key,
                supadata_api_key=os.getenv('SUPADATA_API_KEY')
            )
            self.tools_available = True
        else:
            self.youtube_tool = None
            self.tools_available = False
    
    def create_agents(self):
        """Create the 6 Market Research agents"""
        
        if not self.ai_enabled:
            # Create simple agents without LLM
            return [
                Agent(
                    role="Market Opportunity Scout",
                    goal="Find content gaps and underserved audiences for new AI-generated channels",
                    backstory="Expert in identifying market gaps and blue ocean opportunities in YouTube",
                    verbose=True,
                    allow_delegation=False
                ),
                Agent(
                    role="Content Strategy Architect",
                    goal="Extract viral patterns and formats for AI content creation",
                    backstory="Content strategy expert specializing in AI-producible viral content",
                    verbose=True,
                    allow_delegation=False
                ),
                Agent(
                    role="Audience Intelligence Analyst", 
                    goal="Mine comments for unmet needs and content ideas",
                    backstory="Behavioral analyst expert at finding audience pain points and desires",
                    verbose=True,
                    allow_delegation=False
                ),
                Agent(
                    role="Competitive Landscape Mapper",
                    goal="Benchmark performance by channel size and identify competitive positioning",
                    backstory="Competitive intelligence specialist with deep market analysis skills",
                    verbose=True,
                    allow_delegation=False
                ),
                Agent(
                    role="Production Intelligence Agent",
                    goal="Assess AI feasibility and production requirements for content opportunities",
                    backstory="AI production expert who understands technical feasibility for automated content",
                    verbose=True,
                    allow_delegation=False
                ),
                Agent(
                    role="Launch Strategy Synthesizer",
                    goal="Create go-to-market plans for new AI-generated YouTube channels",
                    backstory="Launch strategy expert specializing in new channel market entry",
                    verbose=True,
                    allow_delegation=False
                )
            ]
        
        # Create agents with LLM - NEW 6-AGENT MARKET RESEARCH SYSTEM
        return [
            Agent(
                role="Market Opportunity Scout",
                goal="Identify content gaps, underserved audiences, and blue ocean opportunities for new AI-generated YouTube channels",
                backstory="""You are a market research specialist with 10+ years finding untapped YouTube niches. 
                You excel at spotting content gaps where audience demand exists but supply is limited. Your focus 
                is finding opportunities perfect for AI content creation - areas where new channels can enter and 
                compete effectively against established creators.""",
                llm=self.llm_flash,
                verbose=True,
                allow_delegation=False
            ),
            Agent(
                role="Content Strategy Architect",
                goal="Extract viral patterns, content formats, and optimization strategies that can be replicated with AI tools",
                backstory="""You are a content strategy expert and former YouTube algorithm engineer specializing 
                in AI-producible content. You analyze viral videos to identify patterns that can be systematically 
                replicated using AI tools for script writing, voice generation, and video creation. You focus on 
                formats with high viral potential but low technical complexity.""",
                llm=self.llm_flash,
                verbose=True,
                allow_delegation=False
            ),
            Agent(
                role="Audience Intelligence Analyst",
                goal="Mine comment sections for unmet audience needs, content requests, and improvement opportunities",
                backstory="""You are a behavioral psychology PhD specializing in social media audience analysis. 
                You excel at reading between the lines in comment sections to find what audiences truly want. 
                You identify patterns like 'I wish someone would make...', criticism of existing content, and 
                unaddressed questions that represent content opportunities.""",
                llm=self.llm_pro,
                verbose=True,
                allow_delegation=False
            ),
            Agent(
                role="Competitive Landscape Mapper", 
                goal="Analyze competitive positioning, performance benchmarks, and market saturation by channel size and niche",
                backstory="""You are a competitive intelligence specialist and ex-BCG consultant who understands 
                market dynamics. You analyze how different sized channels perform in the same niche, identify 
                performance gaps, and determine optimal positioning strategies for new entrants. You excel at 
                finding 'David vs Goliath' opportunities where smaller channels can outperform larger ones.""",
                llm=self.llm_flash,
                verbose=True,
                allow_delegation=False
            ),
            Agent(
                role="Production Intelligence Agent",
                goal="Assess AI production feasibility, technical requirements, and ROI potential for identified content opportunities",
                backstory="""You are an AI content production expert with deep knowledge of current AI tools 
                (GPT-4, Claude, ElevenLabs, Runway, etc.). You evaluate content opportunities based on how easily 
                they can be produced with AI tools, estimate production costs/time, and calculate ROI potential. 
                You understand the current capabilities and limitations of AI content generation.""",
                llm=self.llm_pro,
                verbose=True,
                allow_delegation=False
            ),
            Agent(
                role="Launch Strategy Synthesizer",
                goal="Create comprehensive go-to-market strategies for new AI-generated YouTube channels targeting identified opportunities",
                backstory="""You are a digital marketing strategist and former startup advisor specializing in 
                YouTube channel launches. You synthesize insights from market research to create actionable 
                launch plans including content calendars, SEO strategies, thumbnail approaches, and growth tactics 
                specifically optimized for AI-generated content channels entering competitive markets.""",
                llm=self.llm_pro,
                verbose=True,
                allow_delegation=False
            )
        ]
    
    def create_tasks(self, agents, channel_data):
        """Create tasks for each agent - NEW 6-AGENT MARKET RESEARCH SYSTEM"""
        
        # Extract channel info for context
        channel_info = channel_data.get('channel', {})
        channel_name = channel_info.get('title', 'Unknown Channel')
        videos = channel_data.get('videos', [])
        comments = channel_data.get('comments', {})
        
        return [
            Task(
                description=f"""MARKET OPPORTUNITY ANALYSIS for competitive intelligence.
                
                Analyze this channel's market position to identify opportunities for NEW competing channels:
                Channel: {channel_name}
                Videos analyzed: {len(videos)}
                
                Your mission: Find gaps and opportunities for NEW AI-generated channels to compete.
                
                FOCUS ON:
                1. Content gaps - What topics/angles are missing from this channel's content?
                2. Audience complaints - What do viewers want that this channel doesn't provide?
                3. Format opportunities - Are there unexplored content formats?
                4. Blue ocean opportunities - Adjacent niches with less competition
                5. Improvement opportunities - How could someone make BETTER content in this space?
                
                CRITICAL: This is for CREATING NEW CHANNELS, not optimizing existing ones.
                
                Format as structured JSON with specific actionable opportunities.""",
                agent=agents[0],
                expected_output="JSON analysis of market opportunities for new channel creation with specific content gaps and improvement areas"
            ),
            Task(
                description=f"""CONTENT STRATEGY EXTRACTION for AI replication.
                
                Analyze viral content patterns that can be systematically replicated with AI tools:
                
                Video data: {json.dumps(videos[:3], indent=2)[:500]}...
                
                EXTRACT:
                1. Viral title patterns and hooks that can be templated
                2. Content formats with high engagement but low production complexity
                3. Thumbnail strategies and visual patterns
                4. Script structures and narrative frameworks
                5. AI-friendly content types (talking head, slideshow, animation, etc.)
                
                RATE each pattern for:
                - Viral potential (1-10)
                - AI production feasibility (1-10) 
                - Competition level (1-10)
                
                Format as structured JSON with replicable templates.""",
                agent=agents[1], 
                expected_output="JSON analysis of viral content patterns optimized for AI production with feasibility ratings"
            ),
            Task(
                description=f"""AUDIENCE INTELLIGENCE MINING for unmet needs.
                
                Mine comment data for content opportunities and audience pain points:
                Comment sample: {json.dumps(list(comments.values())[:2], indent=2)[:500]}...
                
                IDENTIFY:
                1. "I wish someone would make..." type requests
                2. Criticism of existing content (improvement opportunities)
                3. Unanswered questions and knowledge gaps
                4. Emotional triggers and engagement drivers
                5. Audience frustrations with current creators
                
                EXTRACT specific content ideas that address these needs.
                
                OUTPUT:
                - 20+ specific video ideas based on audience requests
                - Pain points that new channels could solve
                - Audience psychology insights for targeting
                
                Format as structured JSON with specific quotes and content ideas.""",
                agent=agents[2],
                expected_output="JSON analysis of audience needs with specific content opportunities and pain points from comment mining"
            ),
            Task(
                description=f"""COMPETITIVE LANDSCAPE MAPPING for market positioning.
                
                Analyze competitive dynamics and performance benchmarks:
                
                Channel data: {json.dumps(channel_info, indent=2)[:300]}...
                Performance metrics: Views, engagement rates, growth patterns
                
                ANALYZE:
                1. Performance benchmarks by content type
                2. Competitive positioning gaps
                3. Market saturation assessment
                4. "David vs Goliath" opportunities (where smaller channels can win)
                5. Optimal positioning for new entrants
                
                PROVIDE:
                - Performance baselines to beat
                - Competitive differentiation strategies
                - Market entry recommendations
                - Positioning against established players
                
                Format as structured JSON with competitive intelligence.""",
                agent=agents[3],
                expected_output="JSON competitive analysis with market positioning strategies and performance benchmarks for new channel entry"
            ),
            Task(
                description=f"""AI PRODUCTION FEASIBILITY ASSESSMENT for content opportunities.
                
                Evaluate identified opportunities for AI production viability:
                
                Based on previous agents' findings, assess:
                
                1. AI tool capabilities vs content requirements
                2. Production complexity scores (1-10)
                3. Time and cost estimates for AI production
                4. Technical requirements and tool recommendations
                5. ROI potential analysis (effort vs views/revenue)
                
                TOOLS TO CONSIDER:
                - Script: GPT-4, Claude
                - Voice: ElevenLabs, Murf
                - Video: Runway, Pictory, Invideo
                - Thumbnails: Midjourney, DALL-E
                
                RATE each opportunity:
                - Production difficulty (1-10)
                - AI tool suitability (1-10)
                - Expected ROI (1-10)
                
                Format as structured JSON with AI production assessments.""",
                agent=agents[4],
                expected_output="JSON analysis of AI production feasibility with tool recommendations and ROI assessments for identified opportunities"
            ),
            Task(
                description=f"""LAUNCH STRATEGY SYNTHESIS for new AI channel creation.
                
                Create a comprehensive go-to-market strategy for launching NEW AI-generated channels in this market:
                
                Channel to compete against: {channel_name}
                Market insights from previous agents: [Will be provided via context]
                
                DEVELOP:
                1. Channel positioning and unique value proposition
                2. Content calendar for first 90 days
                3. SEO and discovery optimization strategy
                4. Thumbnail and branding recommendations
                5. Growth tactics and competitive advantages
                6. Success metrics and milestones
                
                DELIVERABLES:
                - Complete launch plan for new AI channel
                - 30 specific video ideas ranked by priority
                - Competitive differentiation strategy
                - 90-day growth roadmap
                
                FORMAT: Structured JSON with actionable launch strategy.
                
                REMEMBER: This is for CREATING a NEW channel, not optimizing {channel_name}.""",
                agent=agents[5],
                expected_output=f"JSON launch strategy for new AI-generated YouTube channel competing in {channel_name}'s market with complete go-to-market plan",
                context=[
                    # This task will have access to outputs from previous tasks
                ]
            )
        ]
    
    def analyze_channel(self, channel_id: str, max_videos: int = 20, max_comments: int = 50):
        """Run the CrewAI Channel DNA analysis"""
        
        logger.info(f"Starting CrewAI Channel DNA analysis for {channel_id}")
        
        # Step 1: Gather data using our YouTube tools
        if not self.tools_available:
            raise Exception("YouTube API tools not available - check YOUTUBE_API_KEY")
        
        logger.info("Fetching channel data...")
        channel_data = self.youtube_tool._run(channel_id)
        
        if 'error' in channel_data:
            raise Exception(f"Failed to fetch channel data: {channel_data['error']}")
        
        # Skip this bypass - we want CrewAI to work!
        # if not self.ai_enabled:
        #     logger.info("AI disabled - processing data directly...")
        #     structured_results = self._process_data_without_ai(channel_data)
        #     return structured_results
        
        # Step 2: Create agents and tasks
        logger.info("Creating CrewAI agents...")
        agents = self.create_agents()
        
        logger.info("Creating analysis tasks...")
        tasks = self.create_tasks(agents, channel_data)
        
        # Step 3: Create and run the crew
        logger.info("Running CrewAI crew...")
        crew = Crew(
            agents=agents,
            tasks=tasks,
            process=Process.sequential,
            verbose=True,
            memory=False,  # Disable memory for now to avoid issues
            cache=False    # Disable cache for now
        )
        
        # Run the crew and store it for later access to task outputs
        result = crew.kickoff()
        self.last_crew = crew  # Store the crew to access task outputs later
        
        # Step 4: Process and structure the results
        logger.info("Processing crew results...")
        logger.info(f"CrewAI result type: {type(result)}")
        logger.info(f"CrewAI result preview: {str(result)[:200]}")
        structured_results = self._process_crew_results(result, channel_data)
        
        return structured_results
    
    def _process_data_without_ai(self, channel_data):
        """Process channel data without AI - just structure the YouTube data"""
        
        # Extract channel info for metadata
        channel_info = channel_data.get('channel', {})
        channel_name = channel_info.get('title', 'Unknown Channel')
        videos = channel_data.get('videos', [])
        comments = channel_data.get('comments', {})
        
        # Basic analytics without AI
        total_views = sum(int(v.get('statistics', {}).get('viewCount', 0)) for v in videos)
        total_videos = len(videos)
        total_comments = sum(len(c) for c in comments.values())
        avg_views = total_views / total_videos if total_videos > 0 else 0
        
        # Top performing videos
        top_videos = sorted(videos, key=lambda x: int(x.get('statistics', {}).get('viewCount', 0)), reverse=True)[:5]
        
        results = {
            'channel_profile': {
                'channel_name': channel_name,
                'channel_id': channel_info.get('id', ''),
                'subscriber_count': channel_info.get('statistics', {}).get('subscriberCount', 0),
                'total_videos': total_videos,
                'total_views': total_views,
                'average_views_per_video': int(avg_views),
                'analysis_type': 'Data Collection Only (No AI)',
                'data_quality': 'High - Real YouTube API Data'
            },
            'content_patterns': {
                'videos_analyzed': total_videos,
                'top_performing_videos': [
                    {
                        'title': v.get('snippet', {}).get('title', ''),
                        'views': int(v.get('statistics', {}).get('viewCount', 0)),
                        'likes': int(v.get('statistics', {}).get('likeCount', 0))
                    } for v in top_videos
                ]
            },
            'audience_psychology': {
                'total_comments_analyzed': total_comments,
                'comments_per_video': total_comments / total_videos if total_videos > 0 else 0,
                'engagement_indicator': 'Active community based on comment volume'
            },
            'performance_metrics': {
                'data_collection_success': True,
                'api_calls_successful': True,
                'youtube_api_status': 'Connected',
                'gemini_api_status': 'Not configured' if not self.ai_enabled else 'Available but not used'
            },
            'strategic_recommendations': {
                'next_steps': [
                    'Configure Gemini API for AI insights',
                    'Enable CrewAI multi-agent analysis',
                    'Review top performing content patterns',
                    'Analyze audience engagement trends'
                ],
                'data_quality_score': '95% - Comprehensive YouTube data collected'
            },
            'raw_data_summary': {
                'videos_fetched': len(videos),
                'comments_fetched': total_comments,
                'transcripts_attempted': len(channel_data.get('transcripts', {})),
                'api_response_quality': 'Excellent'
            }
        }
        
        return results
    
    def _process_crew_results(self, crew_result, channel_data):
        """Process and structure the crew results"""
        
        # Extract channel info for metadata
        channel_info = channel_data.get('channel', {})
        # Get channel name from the correct structure (channel.snippet.title or channel.title)
        if 'snippet' in channel_info:
            channel_name = channel_info['snippet'].get('title', 'Unknown Channel')
        else:
            channel_name = channel_info.get('title', 'Unknown Channel')
        
        # Log the actual channel data structure for debugging
        logger.info(f"Channel info keys: {list(channel_info.keys())}")
        if 'snippet' in channel_info:
            logger.info(f"Snippet keys: {list(channel_info['snippet'].keys())}")
        if 'statistics' in channel_info:
            logger.info(f"Statistics: {channel_info['statistics']}")
        
        logger.info(f"Processing CrewAI results for channel: {channel_name}")
        logger.info(f"CrewAI result type: {type(crew_result)}")
        
        # Extract actual CrewAI task results using the correct CrewAI approach
        task_outputs = []
        
        # Method 1: Access individual task outputs from the crew tasks
        if hasattr(self, 'last_crew') and self.last_crew and hasattr(self.last_crew, 'tasks'):
            logger.info(f"Found {len(self.last_crew.tasks)} tasks in crew")
            for i, task in enumerate(self.last_crew.tasks):
                if hasattr(task, 'output') and task.output:
                    if hasattr(task.output, 'raw_output'):
                        output = str(task.output.raw_output)
                    elif hasattr(task.output, 'raw'):
                        output = str(task.output.raw)
                    else:
                        output = str(task.output)
                    logger.info(f"Task {i} output preview: {output[:100]}...")
                    task_outputs.append(output)
                else:
                    task_outputs.append("Task output not available")
        
        # Method 2: Fallback to crew_result if no tasks available
        if not task_outputs:
            if hasattr(crew_result, 'raw_output'):
                task_outputs = [str(crew_result.raw_output)]
            elif hasattr(crew_result, 'raw'):
                task_outputs = [str(crew_result.raw)]
            elif isinstance(crew_result, str):
                task_outputs = [crew_result]
            else:
                task_outputs = [str(crew_result)]
        
        # Ensure we have 6 task outputs (one for each agent)
        while len(task_outputs) < 6:
            task_outputs.append("Analysis completed")
            
        # Parse JSON outputs from agents
        parsed_outputs = []
        for i, output in enumerate(task_outputs):
            try:
                # Extract JSON from the output (agents often wrap in ```json blocks)
                if '```json' in output:
                    json_start = output.find('```json') + 7
                    json_end = output.find('```', json_start)
                    if json_end == -1:  # No closing ```, take the rest
                        json_str = output[json_start:].strip()
                    else:
                        json_str = output[json_start:json_end].strip()
                    
                    logger.info(f"Parsing JSON for task {i}: {json_str[:200]}...")
                    parsed_json = json.loads(json_str)
                    parsed_outputs.append(parsed_json)
                else:
                    # Try to parse the entire output as JSON
                    try:
                        parsed_json = json.loads(output.strip())
                        parsed_outputs.append(parsed_json)
                    except:
                        parsed_outputs.append({"raw_output": output})
            except Exception as e:
                logger.warning(f"Failed to parse JSON for task {i}: {e}")
                parsed_outputs.append({"raw_output": output})
        
        # Get subscriber count from the correct structure
        if 'statistics' in channel_info:
            subscriber_count = int(channel_info['statistics'].get('subscriberCount', 0))
        else:
            subscriber_count = 0
        
        # Get channel ID from the correct structure
        if 'id' in channel_info:
            channel_id = channel_info['id']
        elif 'snippet' in channel_info:
            channel_id = channel_info['snippet'].get('channelId', channel_info.get('id', 'Unknown'))
        else:
            channel_id = 'Unknown'
        
        # Debug: Log what we parsed
        logger.info(f"Parsed outputs count: {len(parsed_outputs)}")
        for i, parsed in enumerate(parsed_outputs):
            if isinstance(parsed, dict):
                logger.info(f"Task {i} parsed keys: {list(parsed.keys())}")
            else:
                logger.info(f"Task {i} parsed type: {type(parsed)}")
        
        # Structure the results with actual CrewAI outputs - NEW 6-AGENT SYSTEM
        results = {
            'market_opportunities': {
                'channel_name': channel_name,
                'channel_id': channel_id,
                'subscriber_count': subscriber_count,
                'analysis_type': 'CrewAI Market Research Analysis',
                'crew_analysis': task_outputs[0][:2000] if task_outputs and task_outputs[0] else 'Market opportunity analysis completed',
                'analysis_agent': 'Market Opportunity Scout',
                'parsed_data': parsed_outputs[0] if len(parsed_outputs) > 0 else None,
                'data_summary': {
                    'videos_analyzed': len(channel_data.get('videos', [])),
                    'comments_analyzed': sum(len(c) for c in channel_data.get('comments', {}).values()),
                    'transcripts_available': len(channel_data.get('transcripts', {}))
                }
            },
            'content_strategy': {
                'total_videos': len(channel_data.get('videos', [])),
                'crew_analysis': task_outputs[1][:2000] if len(task_outputs) > 1 and task_outputs[1] else 'Content strategy extraction completed by CrewAI',
                'analysis_agent': 'Content Strategy Architect',
                'parsed_data': parsed_outputs[1] if len(parsed_outputs) > 1 else None
            },
            'audience_intelligence': {
                'total_comments': sum(len(c) for c in channel_data.get('comments', {}).values()),
                'crew_analysis': task_outputs[2][:2000] if len(task_outputs) > 2 and task_outputs[2] else 'Audience intelligence mining completed by CrewAI',
                'analysis_agent': 'Audience Intelligence Analyst',
                'parsed_data': parsed_outputs[2] if len(parsed_outputs) > 2 else None
            },
            'competitive_landscape': {
                'analysis_method': 'CrewAI Competitive Analysis',
                'crew_analysis': task_outputs[3][:2000] if len(task_outputs) > 3 and task_outputs[3] else 'Competitive landscape mapping completed by CrewAI',
                'analysis_agent': 'Competitive Landscape Mapper',
                'parsed_data': parsed_outputs[3] if len(parsed_outputs) > 3 else None
            },
            'production_intelligence': {
                'source': 'CrewAI AI Production Assessment',
                'crew_analysis': task_outputs[4][:2000] if len(task_outputs) > 4 and task_outputs[4] else 'AI production feasibility analysis completed by CrewAI',
                'analysis_agent': 'Production Intelligence Agent',
                'parsed_data': parsed_outputs[4] if len(parsed_outputs) > 4 else None
            },
            'launch_strategy': {
                'source': 'CrewAI Launch Strategy',
                'crew_analysis': task_outputs[5] if len(task_outputs) > 5 else 'Launch strategy synthesis completed by CrewAI',
                'analysis_agent': 'Launch Strategy Synthesizer',
                'parsed_data': parsed_outputs[5] if len(parsed_outputs) > 5 else None
            },
            'crewai_metadata': {
                'agents_used': 6,
                'process_type': 'sequential',
                'ai_enabled': self.ai_enabled,
                'tools_available': self.tools_available,
                'analysis_focus': 'Market Research for New AI Channel Creation',
                'raw_result': str(crew_result) if crew_result else None
            }
        }
        
        return results

# Initialize CrewAI instance
crew_ai = None

def get_crewai():
    """Get or create CrewAI instance"""
    global crew_ai
    if crew_ai is None:
        crew_ai = MarketResearchCrewAI()
    return crew_ai

# Routes
@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve web interface"""
    return """
<!DOCTYPE html>
<html>
<head>
    <title>YouTube Research v2 - CrewAI</title>
    <style>
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif; 
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
            color: #f1f5f9; 
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        .header { 
            text-align: center; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            padding: 30px; 
            border-radius: 15px; 
            margin-bottom: 30px; 
        }
        /* Modern Card System */
        .card { 
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(71, 85, 105, 0.3);
            padding: 30px; 
            border-radius: 16px; 
            margin: 24px 0; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        /* Insight Cards - Modular Design */
        .insight-card {
            background: rgba(15, 23, 42, 0.9);
            border: 1px solid rgba(71, 85, 105, 0.2);
            border-radius: 16px;
            padding: 24px;
            margin: 16px 0;
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .insight-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #8b5cf6, #06b6d4);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }
        
        .insight-card:hover {
            border-color: rgba(139, 92, 246, 0.4);
            box-shadow: 0 12px 40px rgba(139, 92, 246, 0.15);
            transform: translateY(-2px);
        }
        
        .insight-card:hover::before {
            transform: scaleX(1);
        }
        
        /* Card Headers */
        .card-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 16px;
            border-bottom: 1px solid rgba(71, 85, 105, 0.2);
        }
        
        .card-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-right: 16px;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
        }
        
        .card-title {
            flex: 1;
        }
        
        .card-title h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #f1f5f9;
        }
        
        .card-title p {
            margin: 4px 0 0 0;
            font-size: 14px;
            color: #94a3b8;
        }
        
        /* Content Grid System */
        .content-grid {
            display: grid;
            gap: 24px;
            margin-top: 32px;
        }
        
        .grid-2 { grid-template-columns: repeat(2, 1fr); }
        .grid-3 { grid-template-columns: repeat(3, 1fr); }
        .grid-4 { grid-template-columns: repeat(4, 1fr); }
        
        @media (max-width: 1024px) {
            .grid-2, .grid-3, .grid-4 { grid-template-columns: 1fr; }
        }
        
        @media (max-width: 768px) {
            .container { padding: 12px; }
            .insight-card { padding: 20px; }
        }
        input { 
            padding: 12px; 
            margin: 8px; 
            width: 300px; 
            border: 1px solid #4a4a4a; 
            border-radius: 8px; 
            background: #3a3a3a; 
            color: white; 
            font-size: 16px;
        }
        input:focus { 
            outline: none; 
            border-color: #667eea; 
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2); 
        }
        button { 
            padding: 12px 24px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            border: none; 
            border-radius: 8px; 
            cursor: pointer; 
            margin: 8px; 
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        button:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3); 
        }
        button:disabled { 
            background: #666; 
            cursor: not-allowed; 
            transform: none; 
            box-shadow: none; 
        }
        .result { 
            background: #2d2d2d; 
            padding: 20px; 
            margin: 15px 0; 
            border-radius: 10px; 
            border-left: 4px solid #667eea; 
        }
        .error { border-left-color: #ef4444; background: #2d1a1a; color: #fca5a5; }
        .success { border-left-color: #22c55e; background: #1a2d1a; color: #86efac; }
        .warning { border-left-color: #f59e0b; background: #2d2a1a; color: #fcd34d; }
        .progress { 
            width: 100%; 
            background: #3a3a3a; 
            border-radius: 8px; 
            margin: 15px 0; 
            height: 8px;
        }
        .progress-bar { 
            height: 100%; 
            background: linear-gradient(90deg, #667eea, #764ba2); 
            border-radius: 8px; 
            width: 0%; 
            transition: width 0.3s ease; 
        }
        
        /* DNA Analysis Progress Modal */
        .analysis-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        .analysis-card {
            background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            border: 1px solid #334155;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
            max-width: 500px;
            width: 90%;
        }
        
        /* DNA Helix Animation */
        .dna-container {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }
        
        .dna-helix {
            width: 80px;
            height: 80px;
            position: relative;
            animation: dna-glow 2s ease-in-out infinite alternate;
        }
        
        .dna-strand {
            position: absolute;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            opacity: 0.8;
        }
        
        .strand-1 {
            background: linear-gradient(45deg, #8b5cf6, #06b6d4);
            top: 0;
            left: 0;
            animation: dna-rotate-1 3s linear infinite;
        }
        
        .strand-2 {
            background: linear-gradient(45deg, #06b6d4, #8b5cf6);
            top: 20px;
            right: 0;
            animation: dna-rotate-2 3s linear infinite;
        }
        
        @keyframes dna-glow {
            0% { filter: drop-shadow(0 0 10px rgba(139, 92, 246, 0.5)); }
            100% { filter: drop-shadow(0 0 20px rgba(139, 92, 246, 0.8)); }
        }
        
        @keyframes dna-rotate-1 {
            0% { transform: rotate(0deg) translateX(20px) rotate(0deg); }
            100% { transform: rotate(360deg) translateX(20px) rotate(-360deg); }
        }
        
        @keyframes dna-rotate-2 {
            0% { transform: rotate(180deg) translateX(20px) rotate(-180deg); }
            100% { transform: rotate(540deg) translateX(20px) rotate(-540deg); }
        }
        
        /* Modern Progress Bar */
        .modern-progress {
            width: 100%;
            height: 8px;
            background: #334155;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .modern-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #8b5cf6, #06b6d4, #334155);
            border-radius: 10px;
            width: 0%;
            transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .modern-progress-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        /* Agent Checklist */
        .agent-list {
            text-align: left;
            margin: 30px 0;
        }
        
        .agent-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            transition: all 0.3s ease;
        }
        
        .agent-status {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            margin-right: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        
        .agent-completed {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }
        
        .agent-active {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
            animation: agent-pulse 1.5s ease-in-out infinite;
        }
        
        .agent-pending {
            background: #334155;
            border: 2px solid #475569;
            color: #94a3b8;
        }
        
        @keyframes agent-pulse {
            0%, 100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.7); }
            50% { transform: scale(1.1); box-shadow: 0 0 0 10px rgba(139, 92, 246, 0); }
        }
        
        .agent-text {
            flex: 1;
            font-size: 16px;
            font-weight: 500;
        }
        
        .agent-completed .agent-text {
            color: #d1fae5;
        }
        
        .agent-active .agent-text {
            color: #e0e7ff;
        }
        
        .agent-pending .agent-text {
            color: #94a3b8;
        }
        
        /* Progress Stats */
        .progress-stats {
            color: #94a3b8;
            font-size: 18px;
            margin: 20px 0;
            font-weight: 500;
        }
        
        .progress-percentage {
            color: #06b6d4;
            font-weight: 700;
        }
        
        /* Estimated Time */
        .estimated-time {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-top: 30px;
            padding: 15px;
            background: rgba(139, 92, 246, 0.1);
            border-radius: 10px;
            border: 1px solid rgba(139, 92, 246, 0.2);
        }
        
        .time-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            border-radius: 50%;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        
        /* DYNAMIC v1-INSPIRED COMPONENTS */
        .metric-box {
            background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 16px;
            padding: 24px;
            text-align: center;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }
        
        .metric-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(139, 92, 246, 0.1), transparent);
            transition: left 0.6s ease;
        }
        
        .metric-box:hover {
            border-color: rgba(139, 92, 246, 0.6);
            background: linear-gradient(135deg, rgba(30, 41, 59, 0.9) 0%, rgba(51, 65, 85, 0.7) 100%);
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
        }
        
        .metric-box:hover::before {
            left: 100%;
        }
        
        .metric-value {
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4, #10b981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
            animation: glow-pulse 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow-pulse {
            0% { filter: drop-shadow(0 0 5px rgba(139, 92, 246, 0.4)); }
            100% { filter: drop-shadow(0 0 15px rgba(139, 92, 246, 0.8)); }
        }
        
        .metric-label {
            font-size: 14px;
            color: #e2e8f0;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        /* PROFESSIONAL TAB SYSTEM */
        .professional-tabs {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 32px;
            padding: 8px;
            background: rgba(15, 23, 42, 0.4);
            border-radius: 16px;
            border: 1px solid rgba(71, 85, 105, 0.3);
        }
        
        .tab-button {
            padding: 12px 20px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .tab-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.6s ease;
        }
        
        .tab-button:hover::before {
            transform: translateX(100%);
        }
        
        .tab-active {
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            color: white;
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
        }
        
        .tab-inactive {
            background: rgba(71, 85, 105, 0.3);
            color: #94a3b8;
            border: 1px solid rgba(71, 85, 105, 0.4);
        }
        
        .tab-inactive:hover {
            background: rgba(139, 92, 246, 0.2);
            color: #e2e8f0;
            border-color: rgba(139, 92, 246, 0.4);
            transform: translateY(-1px);
        }
        
        /* ENGAGING CONTENT SECTIONS */
        .analysis-section {
            background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.8) 100%);
            border: 1px solid rgba(139, 92, 246, 0.2);
            border-radius: 20px;
            padding: 32px;
            margin: 24px 0;
            position: relative;
            overflow: hidden;
        }
        
        .analysis-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #8b5cf6, #06b6d4, #10b981);
        }
        
        .section-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(71, 85, 105, 0.3);
        }
        
        .section-icon {
            width: 56px;
            height: 56px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            margin-right: 20px;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }
        
        .section-title {
            flex: 1;
        }
        
        .section-title h3 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
            color: #f1f5f9;
            background: linear-gradient(135deg, #f1f5f9, #94a3b8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .section-title p {
            margin: 8px 0 0 0;
            font-size: 16px;
            color: #64748b;
            font-weight: 500;
        }
        
        /* Tag System */
        .tag {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin: 4px;
            transition: all 0.2s ease;
        }
        
        .tag-primary { 
            background: rgba(139, 92, 246, 0.2); 
            color: #a78bfa; 
            border: 1px solid rgba(139, 92, 246, 0.3);
        }
        
        .tag-success { 
            background: rgba(34, 197, 94, 0.2); 
            color: #4ade80; 
            border: 1px solid rgba(34, 197, 94, 0.3);
        }
        
        .tag-warning { 
            background: rgba(251, 191, 36, 0.2); 
            color: #fbbf24; 
            border: 1px solid rgba(251, 191, 36, 0.3);
        }
        
        .tag-info { 
            background: rgba(6, 182, 212, 0.2); 
            color: #22d3ee; 
            border: 1px solid rgba(6, 182, 212, 0.3);
        }
        
        .tag:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
        }
        
        /* Data List Component */
        .data-list {
            background: rgba(15, 23, 42, 0.4);
            border-radius: 12px;
            padding: 0;
            margin: 16px 0;
            overflow: hidden;
        }
        
        .data-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            border-bottom: 1px solid rgba(71, 85, 105, 0.2);
            transition: background 0.2s ease;
        }
        
        .data-item:last-child {
            border-bottom: none;
        }
        
        .data-item:hover {
            background: rgba(139, 92, 246, 0.1);
        }
        
        .data-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            font-size: 16px;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
        }
        
        .data-content {
            flex: 1;
        }
        
        .data-title {
            font-weight: 600;
            color: #f1f5f9;
            margin-bottom: 4px;
        }
        
        .data-subtitle {
            font-size: 14px;
            color: #94a3b8;
        }
        
        .data-value {
            font-weight: 600;
            color: #06b6d4;
        }
        
        /* Expandable Sections */
        .expandable {
            cursor: pointer;
            user-select: none;
        }
        
        .expandable-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 0;
            border-bottom: 1px solid rgba(71, 85, 105, 0.2);
        }
        
        .expandable-icon {
            transition: transform 0.3s ease;
        }
        
        .expandable.expanded .expandable-icon {
            transform: rotate(180deg);
        }
        
        .expandable-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }
        
        .expandable.expanded .expandable-content {
            max-height: 1000px;
            padding: 20px 0;
        }
        pre { 
            background: #1a1a1a; 
            padding: 15px; 
            border-radius: 8px; 
            overflow-x: auto; 
            font-size: 12px; 
            color: #d1d5db;
            border: 1px solid #3a3a3a;
        }
        h1, h2, h3, h4, h5 { color: white; }
        h1 { font-size: 2.5rem; margin-bottom: 10px; }
        h2 { font-size: 2rem; margin-bottom: 15px; }
        h3 { font-size: 1.5rem; margin-bottom: 12px; }
        
        /* Grid layouts */
        .grid { display: grid; gap: 20px; }
        .grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
        .grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
        .grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
        
        @media (max-width: 768px) {
            .grid-cols-2, .grid-cols-4 { grid-template-columns: 1fr; }
            input { width: 100%; margin: 8px 0; }
        }
        
        /* Text colors for metrics */
        .text-purple-400 { color: #c084fc; }
        .text-blue-400 { color: #60a5fa; }
        .text-green-400 { color: #4ade80; }
        .text-yellow-400 { color: #facc15; }
        .text-cyan-400 { color: #22d3ee; }
        .text-gray-400 { color: #9ca3af; }
        .text-gray-300 { color: #d1d5db; }
        .text-white { color: white; }
        
        /* Spacing utilities */
        .mb-2 { margin-bottom: 8px; }
        .mb-4 { margin-bottom: 16px; }
        .mb-6 { margin-bottom: 24px; }
        .space-y-6 > * + * { margin-top: 24px; }
        .space-y-4 > * + * { margin-top: 16px; }
        .space-y-1 > * + * { margin-top: 4px; }
        .mt-2 { margin-top: 8px; }
        .p-4 { padding: 16px; }
        .rounded { border-radius: 8px; }
        .bg-gray-800 { background-color: #1f2937; }
        .font-semibold { font-weight: 600; }
        .font-medium { font-weight: 500; }
        .font-bold { font-weight: 700; }
        .text-lg { font-size: 1.125rem; }
        .text-sm { font-size: 0.875rem; }
        .gap-4 { gap: 16px; }
        
        /* MODERN INSIGHT CARDS */
        .insight-card {
            background: linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%);
            border: 1px solid rgba(139, 92, 246, 0.2);
            border-radius: 24px;
            padding: 32px;
            margin: 24px 0;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .insight-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #8b5cf6, #06b6d4, #10b981, #f59e0b);
        }
        
        .insight-card:hover {
            border-color: rgba(139, 92, 246, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 25px 50px rgba(139, 92, 246, 0.15);
        }
        
        /* CONTENT GRID SYSTEM */
        .content-grid {
            display: grid;
            gap: 20px;
            margin: 24px 0;
        }
        
        .grid-2 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
        .grid-3 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
        .grid-4 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }
        
        @media (max-width: 768px) {
            .grid-2, .grid-3, .grid-4 {
                grid-template-columns: 1fr;
            }
        }
        
        /* RICH ANALYTICS COMPONENTS */
        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin: 24px 0;
        }
        
        .trend-card {
            background: linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.8) 100%);
            border: 1px solid rgba(34, 197, 94, 0.3);
            border-radius: 16px;
            padding: 24px;
            position: relative;
            overflow: hidden;
        }
        
        .trend-card.trending-up {
            border-color: rgba(34, 197, 94, 0.4);
        }
        
        .trend-card.trending-down {
            border-color: rgba(239, 68, 68, 0.4);
        }
        
        .trend-indicator {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
        }
        
        .trend-up {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }
        
        .trend-down {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }
        
        .trend-title {
            font-size: 18px;
            font-weight: 600;
            color: #f1f5f9;
            margin-bottom: 8px;
        }
        
        .trend-value {
            font-size: 32px;
            font-weight: 800;
            background: linear-gradient(135deg, #8b5cf6, #06b6d4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 4px;
        }
        
        .trend-subtitle {
            font-size: 14px;
            color: #64748b;
        }
        
        /* PROFESSIONAL STATS */
        .stats-bar {
            background: rgba(15, 23, 42, 0.6);
            border-radius: 8px;
            padding: 16px;
            margin: 12px 0;
        }
        
        .stats-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(71, 85, 105, 0.2);
        }
        
        .stats-row:last-child {
            border-bottom: none;
        }
        
        .stats-label {
            font-size: 14px;
            color: #94a3b8;
            font-weight: 500;
        }
        
        .stats-value {
            font-size: 16px;
            font-weight: 600;
            color: #f1f5f9;
        }
        
        .stats-trend {
            font-size: 12px;
            font-weight: 600;
            padding: 2px 8px;
            border-radius: 12px;
            margin-left: 8px;
        }
        
        .stats-trend.positive {
            background: rgba(34, 197, 94, 0.2);
            color: #4ade80;
        }
        
        .stats-trend.negative {
            background: rgba(239, 68, 68, 0.2);
            color: #f87171;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 YouTube Market Research v2</h1>
            <h2>AI Channel Creation Intelligence</h2>
            <p>6-Agent CrewAI Pipeline for Market Opportunities & AI Content Strategy</p>
        </div>
        
        <div class="card">
            <div class="card-header">
                <div class="card-icon">🎯</div>
                <div class="card-title">
                    <h3>Competitive Intelligence Analysis</h3>
                    <p>Analyze competitor channels to find market opportunities for NEW AI-generated channels</p>
                </div>
            </div>
            
            <div style="display: flex; gap: 16px; flex-wrap: wrap; align-items: end; margin-bottom: 20px;">
                <div style="flex: 1; min-width: 300px;">
                    <label style="display: block; margin-bottom: 8px; color: #94a3b8; font-weight: 500;">Channel to Analyze</label>
                    <input type="text" id="channelId" placeholder="Channel ID or @handle (e.g. UCX6OQ3DkcsbYNE6H8uQQuVA or @MrBeast)" style="width: 100%; margin: 0;" />
                </div>
                <div>
                    <label style="display: block; margin-bottom: 8px; color: #94a3b8; font-weight: 500;">Videos</label>
                    <input type="number" id="maxVideos" value="20" min="5" max="50" placeholder="Max Videos" style="width: 80px; margin: 0;" />
                </div>
                <div>
                    <label style="display: block; margin-bottom: 8px; color: #94a3b8; font-weight: 500;">Comments</label>
                    <input type="number" id="maxComments" value="50" min="10" max="100" placeholder="Max Comments" style="width: 80px; margin: 0;" />
                </div>
                <button onclick="startAnalysis()" id="analyzeBtn" style="margin: 0;">🔍 Start Market Research</button>
            </div>
            
            <div id="status"></div>
            <div id="results"></div>
        </div>

        <div class="card">
            <div class="card-header">
                <div class="card-icon">📊</div>
                <div class="card-title">
                    <h3>Recent Analyses</h3>
                    <p>Previously analyzed channels and their market research results</p>
                </div>
            </div>
            <div id="recent"></div>
            <button onclick="loadRecent()" style="margin-top: 16px;">🔄 Refresh</button>
        </div>
    </div>

    <!-- Dynamic Progress Modal -->
    <div id="analysisModal" class="analysis-modal" onclick="closeModalOnBackdrop(event)">
        <div class="analysis-card" onclick="event.stopPropagation()">
            <div class="dna-container">
                <div class="dna-helix">
                    <div class="dna-strand strand-1"></div>
                    <div class="dna-strand strand-2"></div>
                </div>
            </div>
            
            <h2 id="modalTitle" style="color: white; margin-bottom: 10px; font-size: 2rem;">Analyzing Market Intelligence</h2>
            
            <div class="progress-stats">
                <span class="progress-percentage" id="modalPercentage">0%</span> Complete • 
                <span id="modalAgentCount">0/6</span> Agents
            </div>
            
            <div class="modern-progress">
                <div class="modern-progress-bar" id="modalProgressBar"></div>
            </div>
            
            <div id="modalDataSummary" style="color: #94a3b8; font-size: 14px; margin: 15px 0;">
                Initializing market research analysis...
            </div>
            
            <div class="agent-list" id="modalAgentList">
                <div class="agent-item">
                    <div class="agent-status agent-pending" id="agent-0">1</div>
                    <div class="agent-text">🎯 Market Opportunity Scout</div>
                </div>
                <div class="agent-item">
                    <div class="agent-status agent-pending" id="agent-1">2</div>
                    <div class="agent-text">📈 Content Strategy Architect</div>
                </div>
                <div class="agent-item">
                    <div class="agent-status agent-pending" id="agent-2">3</div>
                    <div class="agent-text">🧠 Audience Intelligence Analyst</div>
                </div>
                <div class="agent-item">
                    <div class="agent-status agent-pending" id="agent-3">4</div>
                    <div class="agent-text">🗺️ Competitive Landscape Mapper</div>
                </div>
                <div class="agent-item">
                    <div class="agent-status agent-pending" id="agent-4">5</div>
                    <div class="agent-text">🤖 Production Intelligence Agent</div>
                </div>
                <div class="agent-item">
                    <div class="agent-status agent-pending" id="agent-5">6</div>
                    <div class="agent-text">🚀 Launch Strategy Synthesizer</div>
                </div>
            </div>
            
            <div class="estimated-time">
                <div class="time-icon">⏱</div>
                <span id="modalEstimatedTime">Estimated completion: 2-3 minutes</span>
            </div>
        </div>
    </div>

    <script>
        let currentAnalysisId = null;
        let statusInterval = null;
        
        // NEW FORMATTING FUNCTIONS FOR 6-AGENT SYSTEM
        
        function formatMarketOpportunities(data) {
            if (!data) return '<div class="text-gray-400">No market opportunities data available.</div>';
            return formatGenericAnalysis(data, 'Market Opportunities', '🎯');
        }
        
        function formatContentStrategy(data) {
            if (!data) return '<div class="text-gray-400">No content strategy data available.</div>';
            return formatGenericAnalysis(data, 'Content Strategy', '📈');
        }
        
        function formatAudienceIntelligence(data) {
            if (!data) return '<div class="text-gray-400">No audience intelligence data available.</div>';
            return formatGenericAnalysis(data, 'Audience Intelligence', '🧠');
        }
        
        function formatCompetitiveLandscape(data) {
            if (!data) return '<div class="text-gray-400">No competitive landscape data available.</div>';
            return formatGenericAnalysis(data, 'Competitive Landscape', '🗺️');
        }
        
        function formatProductionIntelligence(data) {
            if (!data) return '<div class="text-gray-400">No production intelligence data available.</div>';
            return formatGenericAnalysis(data, 'AI Production Intelligence', '🤖');
        }
        
        function formatLaunchStrategy(data) {
            if (!data) return '<div class="text-gray-400">No launch strategy data available.</div>';
            return formatGenericAnalysis(data, 'Launch Strategy', '🚀');
        }
        
        function formatGenericAnalysis(data, title, emoji) {
            if (!data) return '<div style="color: #94a3b8; text-align: center; padding: 20px;">No analysis data available</div>';
            
            // Extract and format AI analysis content
            function formatAIContent(content) {
                if (!content) return 'Analysis completed successfully';
                
                if (typeof content === 'string') {
                    // Try to extract JSON from CrewAI output
                    const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/);
                    if (jsonMatch) {
                        try {
                            const parsed = JSON.parse(jsonMatch[1]);
                            return formatStructuredData(parsed);
                        } catch (e) {
                            return formatTextContent(content);
                        }
                    }
                    return formatTextContent(content);
                } else if (typeof content === 'object') {
                    return formatStructuredData(content);
                }
                
                return String(content);
            }
            
            // Format structured JSON data into modern components
            function formatStructuredData(obj) {
                if (!obj || typeof obj !== 'object') return String(obj);
                
                const entries = Object.entries(obj);
                return entries.map(([key, value]) => {
                    const cleanKey = key.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1');
                    const titleKey = cleanKey.charAt(0).toUpperCase() + cleanKey.slice(1);
                    
                    if (Array.isArray(value)) {
                        return `
                            <div class="expandable" onclick="toggleExpandable(this)">
                                <div class="expandable-header">
                                    <h4 style="color: #f1f5f9; margin: 0; font-weight: 600;">${titleKey}</h4>
                                    <span class="expandable-icon" style="color: #8b5cf6;">▼</span>
                                </div>
                                <div class="expandable-content">
                                    <div class="data-list">
                                        ${value.map((item, index) => `
                                            <div class="data-item">
                                                <div class="data-icon">${index + 1}</div>
                                                <div class="data-content">
                                                    <div class="data-title">${typeof item === 'object' ? item.title || Object.keys(item)[0] : item}</div>
                                                    ${typeof item === 'object' ? `<div class="data-subtitle">${item.description || Object.values(item)[0]}</div>` : ''}
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        `;
                    } else if (typeof value === 'object' && value !== null) {
                        return `
                            <div class="expandable" onclick="toggleExpandable(this)">
                                <div class="expandable-header">
                                    <h4 style="color: #f1f5f9; margin: 0; font-weight: 600;">${titleKey}</h4>
                                    <span class="expandable-icon" style="color: #8b5cf6;">▼</span>
                                </div>
                                <div class="expandable-content">
                                    ${formatStructuredData(value)}
                                </div>
                            </div>
                        `;
                    } else {
                        return `
                            <div class="data-item">
                                <div class="data-icon">${emoji}</div>
                                <div class="data-content">
                                    <div class="data-title">${titleKey}</div>
                                    <div class="data-subtitle">${value}</div>
                                </div>
                            </div>
                        `;
                    }
                }).join('');
            }
            
            // Format plain text content
            function formatTextContent(text) {
                const lines = text.split('\\n').filter(line => line.trim());
                return `
                    <div class="data-list">
                        ${lines.map((line, index) => `
                            <div class="data-item">
                                <div class="data-icon">${index + 1}</div>
                                <div class="data-content">
                                    <div class="data-title">${line.trim()}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
            
            // Main analysis display
            const analysisContent = data.parsed_data || data.crew_analysis;
            
            return `
                <div style="margin-top: 16px;">
                    <!-- Agent Info -->
                    <div class="content-grid grid-2" style="margin-bottom: 24px;">
                        <div class="metric-box">
                            <div class="metric-value" style="font-size: 16px;">${data.analysis_agent || title}</div>
                            <div class="metric-label">Analysis Agent</div>
                        </div>
                        <div class="metric-box">
                            <div class="metric-value" style="font-size: 16px;">AI Channel Creation</div>
                            <div class="metric-label">Focus Area</div>
                        </div>
                    </div>
                    
                    <!-- Analysis Content -->
                    <div>
                        ${formatAIContent(analysisContent)}
                    </div>
                </div>
            `;
        }
        
        // Toggle function for expandable sections
        function toggleExpandable(element) {
            element.classList.toggle('expanded');
        }

        function formatChannelProfile(data) {
            if (!data) return '<div class="text-gray-400">No channel profile available.</div>';
            
            console.log('Channel Profile Data:', data); // Debug log
            
            // Enhanced function to properly extract and format CrewAI JSON analysis
            function formatCrewAIContent(content) {
                if (!content) return '';
                
                // Function to extract JSON from CrewAI ```json blocks
                function extractJSONFromCrewAI(text) {
                    if (typeof text !== 'string') return text;
                    
                    console.log('Extracting JSON from:', text.substring(0, 100) + '...');
                    
                    // First, convert literal \\n to actual newlines for proper parsing
                    const normalizedText = text.replace(/\\\\n/g, String.fromCharCode(10));
                    
                    // Look for ```json blocks with correct regex for real newlines
                    const jsonBlockMatch = normalizedText.match(/```json\s*([\s\S]*?)\s*```/);
                    if (jsonBlockMatch) {
                        console.log('Found JSON block, attempting to parse...');
                        try {
                            const jsonStr = jsonBlockMatch[1].trim();
                            console.log('JSON string to parse:', jsonStr.substring(0, 100) + '...');
                            return JSON.parse(jsonStr);
                        } catch (e) {
                            console.log('Failed to parse extracted JSON:', e);
                        }
                    }
                    
                    // Try to parse the entire string as JSON
                    try {
                        return JSON.parse(normalizedText);
                    } catch (e) {
                        console.log('Failed to parse as direct JSON:', e);
                        // Return original text if not JSON
                        return text;
                    }
                }
                
                // Extract JSON if it's wrapped in code blocks
                const extracted = extractJSONFromCrewAI(content);
                
                // If it's an object now, format it beautifully
                if (typeof extracted === 'object' && extracted !== null) {
                    return formatJSONObject(extracted);
                }
                
                // If it's still a string, format as text
                if (typeof extracted === 'string') {
                    return formatPlainText(extracted);
                }
                
                return String(content);
            }
            
            // Function to format JSON objects beautifully
            function formatJSONObject(obj, level = 0) {
                if (!obj || typeof obj !== 'object') return String(obj);
                
                const indent = level * 20;
                const entries = Object.entries(obj);
                
                return entries.map(([key, value]) => {
                    const cleanKey = key.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1').toLowerCase();
                    const titleKey = cleanKey.charAt(0).toUpperCase() + cleanKey.slice(1);
                    
                    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                        return `
                            <div class="mb-4" style="margin-left: ${indent}px;">
                                <h4 class="text-blue-400 font-semibold mb-2">${titleKey}</h4>
                                <div class="bg-gray-800 p-3 rounded">
                                    ${formatJSONObject(value, level + 1)}
                                </div>
                            </div>
                        `;
                    } else if (Array.isArray(value)) {
                        return `
                            <div class="mb-3" style="margin-left: ${indent}px;">
                                <strong class="text-purple-400">${titleKey}:</strong>
                                <div class="ml-4 mt-1">
                                    ${value.map(item => `<div class="text-gray-300 mb-1">• ${item}</div>`).join('')}
                                </div>
                            </div>
                        `;
                    } else {
                        return `
                            <div class="mb-2" style="margin-left: ${indent}px;">
                                <strong class="text-purple-400">${titleKey}:</strong> 
                                <span class="text-gray-300">${value}</span>
                            </div>
                        `;
                    }
                }).join('');
            }
            
            // Function to format plain text nicely
            function formatPlainText(text) {
                if (!text || typeof text !== 'string') return '';
                
                return text.split('\\n').map(line => line.trim()).filter(line => line).map(line => {
                    if (line.includes(':')) {
                        const [key, ...valueParts] = line.split(':');
                        const value = valueParts.join(':').trim();
                        return `<div class="mb-2"><strong class="text-purple-400">${key.trim()}:</strong> <span class="text-gray-300">${value}</span></div>`;
                    } else {
                        return `<div class="text-gray-300 mb-1">${line}</div>`;
                    }
                }).join('');
            }
            
            // Display channel metadata at the top
            let html = `
                <div style="background: #1a1a1a; border-radius: 8px; padding: 16px;">
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div><span class="text-gray-400">Channel Name:</span> <span class="text-white font-bold">${data.channel_name || 'Unknown'}</span></div>
                        <div><span class="text-gray-400">Channel ID:</span> <span class="text-purple-400 font-mono text-sm">${data.channel_id || 'Unknown'}</span></div>
                        <div><span class="text-gray-400">Subscribers:</span> <span class="text-green-400 font-bold">${data.subscriber_count?.toLocaleString() || 'Unknown'}</span></div>
                        <div><span class="text-gray-400">Analysis Type:</span> <span class="text-yellow-400">${data.analysis_type || 'CrewAI Analysis'}</span></div>
                    </div>
            `;
            
            // Show data summary if available
            if (data.data_summary) {
                html += `
                    <div class="mb-4">
                        <h5 class="text-blue-400 font-semibold mb-2">📊 Data Summary</h5>
                        <div class="grid grid-cols-3 gap-4 text-sm">
                            <div><span class="text-gray-400">Videos analyzed:</span> <span class="text-green-400 font-bold">${data.data_summary.videos_analyzed || 0}</span></div>
                            <div><span class="text-gray-400">Comments analyzed:</span> <span class="text-blue-400 font-bold">${data.data_summary.comments_analyzed || 0}</span></div>
                            <div><span class="text-gray-400">Transcripts available:</span> <span class="text-purple-400 font-bold">${data.data_summary.transcripts_available || 0}</span></div>
                        </div>
                    </div>
                `;
            }
            
            // Format and display the CrewAI analysis
            if (data.crew_analysis) {
                html += `
                    <div class="mb-4">
                        <h5 class="text-green-400 font-semibold mb-3">🤖 AI Channel Profile Analysis</h5>
                        <div class="bg-gray-800 p-4 rounded">
                            ${formatCrewAIContent(data.crew_analysis)}
                        </div>
                    </div>
                `;
            }
            
            // Handle parsed_data if available
            if (data.parsed_data && !data.crew_analysis) {
                html += `
                    <div class="mb-4">
                        <h5 class="text-green-400 font-semibold mb-3">🤖 Parsed Analysis Data</h5>
                        <div class="bg-gray-800 p-4 rounded">
                            ${formatCrewAIContent(data.parsed_data)}
                        </div>
                    </div>
                `;
            }
            
            html += '</div>';
            return html;
        }

        function formatContentPatterns(data) {
            if (!data) return '<div class="text-gray-400">No content patterns available.</div>';
            
            // Enhanced function to properly extract and format CrewAI JSON analysis
            function formatCrewAIContent(content) {
                if (!content) return '';
                
                // Function to extract JSON from CrewAI ```json blocks
                function extractJSONFromCrewAI(text) {
                    if (typeof text !== 'string') return text;
                    
                    console.log('Extracting JSON from:', text.substring(0, 100) + '...');
                    
                    // First, convert literal \\n to actual newlines for proper parsing
                    const normalizedText = text.replace(/\\\\n/g, String.fromCharCode(10));
                    
                    // Look for ```json blocks with correct regex for real newlines
                    const jsonBlockMatch = normalizedText.match(/```json\\s*([\\s\\S]*?)\\s*```/);
                    if (jsonBlockMatch) {
                        console.log('Found JSON block, attempting to parse...');
                        try {
                            const jsonStr = jsonBlockMatch[1].trim();
                            console.log('JSON string to parse:', jsonStr.substring(0, 100) + '...');
                            return JSON.parse(jsonStr);
                        } catch (e) {
                            console.log('Failed to parse extracted JSON:', e);
                        }
                    }
                    
                    // Try to parse the entire string as JSON
                    try {
                        return JSON.parse(normalizedText);
                    } catch (e) {
                        console.log('Failed to parse as direct JSON:', e);
                        // Return original text if not JSON
                        return text;
                    }
                }
                
                // Extract JSON if it's wrapped in code blocks
                const extracted = extractJSONFromCrewAI(content);
                
                // If it's an object now, format it beautifully
                if (typeof extracted === 'object' && extracted !== null) {
                    return formatJSONObject(extracted);
                }
                
                // If it's still a string, format as text
                if (typeof extracted === 'string') {
                    return formatPlainText(extracted);
                }
                
                return String(content);
            }
            
            // Function to format JSON objects beautifully
            function formatJSONObject(obj, level = 0) {
                if (!obj || typeof obj !== 'object') return String(obj);
                
                const indent = level * 20;
                const entries = Object.entries(obj);
                
                return entries.map(([key, value]) => {
                    const cleanKey = key.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1').toLowerCase();
                    const titleKey = cleanKey.charAt(0).toUpperCase() + cleanKey.slice(1);
                    
                    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                        return `
                            <div class="mb-4" style="margin-left: ${indent}px;">
                                <h4 class="text-blue-400 font-semibold mb-2">${titleKey}</h4>
                                <div class="bg-gray-800 p-3 rounded">
                                    ${formatJSONObject(value, level + 1)}
                                </div>
                            </div>
                        `;
                    } else if (Array.isArray(value)) {
                        return `
                            <div class="mb-3" style="margin-left: ${indent}px;">
                                <strong class="text-purple-400">${titleKey}:</strong>
                                <div class="ml-4 mt-1">
                                    ${value.map(item => `<div class="text-gray-300 mb-1">• ${item}</div>`).join('')}
                                </div>
                            </div>
                        `;
                    } else {
                        return `
                            <div class="mb-2" style="margin-left: ${indent}px;">
                                <strong class="text-purple-400">${titleKey}:</strong> 
                                <span class="text-gray-300">${value}</span>
                            </div>
                        `;
                    }
                }).join('');
            }
            
            // Function to format plain text nicely
            function formatPlainText(text) {
                if (!text || typeof text !== 'string') return '';
                
                return text.split('\\n').map(line => line.trim()).filter(line => line).map(line => {
                    if (line.includes(':')) {
                        const [key, ...valueParts] = line.split(':');
                        const value = valueParts.join(':').trim();
                        return `<div class="mb-2"><strong class="text-purple-400">${key.trim()}:</strong> <span class="text-gray-300">${value}</span></div>`;
                    } else {
                        return `<div class="text-gray-300 mb-1">${line}</div>`;
                    }
                }).join('');
            }
            
            return `
                <div style="background: #1a1a1a; border-radius: 8px; padding: 16px;">
                    <div class="mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div><span class="text-gray-400">Total Videos:</span> <span class="text-blue-400 font-bold">${data.total_videos || 'Unknown'}</span></div>
                            <div><span class="text-gray-400">Analysis Agent:</span> <span class="text-purple-400">${data.analysis_agent || 'Content Pattern Analyzer'}</span></div>
                        </div>
                    </div>
                    
                    ${data.parsed_data ? `
                    <div class="mb-4">
                        <h5 class="text-green-400 font-semibold mb-3">🎯 AI Content Analysis</h5>
                        <div class="bg-gray-800 p-4 rounded">
                            ${formatCrewAIContent(data.parsed_data)}
                        </div>
                    </div>
                    ` : ''}
                    
                    ${data.crew_analysis && !data.parsed_data ? `
                    <div class="mb-4">
                        <h5 class="text-green-400 font-semibold mb-2">🤖 CrewAI Analysis</h5>
                        <div class="bg-gray-800 p-4 rounded" style="max-height: 300px; overflow-y: auto;">
                            ${formatCrewAIContent(data.crew_analysis)}
                        </div>
                    </div>
                    ` : ''}
                </div>
            `;
        }

        function formatAudiencePsychology(data) {
            if (!data) return '<div class="text-gray-400">No audience psychology available.</div>';
            
            // Enhanced function to properly extract and format CrewAI JSON analysis
            function formatCrewAIContent(content) {
                if (!content) return '';
                
                // Function to extract JSON from CrewAI ```json blocks
                function extractJSONFromCrewAI(text) {
                    if (typeof text !== 'string') return text;
                    
                    console.log('Extracting JSON from:', text.substring(0, 100) + '...');
                    
                    // First, convert literal \\n to actual newlines for proper parsing
                    const normalizedText = text.replace(/\\\\n/g, String.fromCharCode(10));
                    
                    // Look for ```json blocks with correct regex for real newlines
                    const jsonBlockMatch = normalizedText.match(/```json\\s*([\\s\\S]*?)\\s*```/);
                    if (jsonBlockMatch) {
                        console.log('Found JSON block, attempting to parse...');
                        try {
                            const jsonStr = jsonBlockMatch[1].trim();
                            console.log('JSON string to parse:', jsonStr.substring(0, 100) + '...');
                            return JSON.parse(jsonStr);
                        } catch (e) {
                            console.log('Failed to parse extracted JSON:', e);
                        }
                    }
                    
                    // Try to parse the entire string as JSON
                    try {
                        return JSON.parse(normalizedText);
                    } catch (e) {
                        console.log('Failed to parse as direct JSON:', e);
                        // Return original text if not JSON
                        return text;
                    }
                }
                
                // Extract JSON if it's wrapped in code blocks
                const extracted = extractJSONFromCrewAI(content);
                
                // If it's an object now, format it beautifully
                if (typeof extracted === 'object' && extracted !== null) {
                    return formatJSONObject(extracted);
                }
                
                // If it's still a string, format as text
                if (typeof extracted === 'string') {
                    return formatPlainText(extracted);
                }
                
                return String(content);
            }
            
            // Function to format JSON objects beautifully
            function formatJSONObject(obj, level = 0) {
                if (!obj || typeof obj !== 'object') return String(obj);
                
                const indent = level * 20;
                const entries = Object.entries(obj);
                
                return entries.map(([key, value]) => {
                    const cleanKey = key.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1').toLowerCase();
                    const titleKey = cleanKey.charAt(0).toUpperCase() + cleanKey.slice(1);
                    
                    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                        return `
                            <div class="mb-4" style="margin-left: ${indent}px;">
                                <h4 class="text-blue-400 font-semibold mb-2">${titleKey}</h4>
                                <div class="bg-gray-800 p-3 rounded">
                                    ${formatJSONObject(value, level + 1)}
                                </div>
                            </div>
                        `;
                    } else if (Array.isArray(value)) {
                        return `
                            <div class="mb-3" style="margin-left: ${indent}px;">
                                <strong class="text-purple-400">${titleKey}:</strong>
                                <div class="ml-4 mt-1">
                                    ${value.map(item => `<div class="text-gray-300 mb-1">• ${item}</div>`).join('')}
                                </div>
                            </div>
                        `;
                    } else {
                        return `
                            <div class="mb-2" style="margin-left: ${indent}px;">
                                <strong class="text-purple-400">${titleKey}:</strong> 
                                <span class="text-gray-300">${value}</span>
                            </div>
                        `;
                    }
                }).join('');
            }
            
            // Function to format plain text nicely
            function formatPlainText(text) {
                if (!text || typeof text !== 'string') return '';
                
                return text.split('\\n').map(line => line.trim()).filter(line => line).map(line => {
                    if (line.includes(':')) {
                        const [key, ...valueParts] = line.split(':');
                        const value = valueParts.join(':').trim();
                        return `<div class="mb-2"><strong class="text-purple-400">${key.trim()}:</strong> <span class="text-gray-300">${value}</span></div>`;
                    } else {
                        return `<div class="text-gray-300 mb-1">${line}</div>`;
                    }
                }).join('');
            }
            
            return `
                <div style="background: #1a1a1a; border-radius: 8px; padding: 16px;">
                    <div class="mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div><span class="text-gray-400">Total Comments:</span> <span class="text-green-400 font-bold">${data.total_comments || 'Unknown'}</span></div>
                            <div><span class="text-gray-400">Analysis Agent:</span> <span class="text-purple-400">${data.analysis_agent || 'Audience Psychologist'}</span></div>
                        </div>
                    </div>
                    
                    ${data.parsed_data ? `
                    <div class="mb-4">
                        <h5 class="text-green-400 font-semibold mb-3">👥 AI Audience Analysis</h5>
                        <div class="bg-gray-800 p-4 rounded">
                            ${formatCrewAIContent(data.parsed_data)}
                        </div>
                    </div>
                    ` : ''}
                    
                    ${data.crew_analysis && !data.parsed_data ? `
                    <div class="mb-4">
                        <h5 class="text-green-400 font-semibold mb-2">🤖 CrewAI Analysis</h5>
                        <div class="bg-gray-800 p-4 rounded" style="max-height: 300px; overflow-y: auto;">
                            ${formatCrewAIContent(data.crew_analysis)}
                        </div>
                    </div>
                    ` : ''}
                </div>
            `;
        }

        function formatPerformanceMetrics(data) {
            if (!data) return '<div class="text-gray-400">No performance metrics available.</div>';
            
            // Enhanced function to properly extract and format CrewAI JSON analysis
            function formatCrewAIContent(content) {
                if (!content) return '';
                
                // Function to extract JSON from CrewAI ```json blocks
                function extractJSONFromCrewAI(text) {
                    if (typeof text !== 'string') return text;
                    
                    console.log('Extracting JSON from:', text.substring(0, 100) + '...');
                    
                    // First, convert literal \\n to actual newlines for proper parsing
                    const normalizedText = text.replace(/\\\\n/g, String.fromCharCode(10));
                    
                    // Look for ```json blocks with correct regex for real newlines
                    const jsonBlockMatch = normalizedText.match(/```json\\s*([\\s\\S]*?)\\s*```/);
                    if (jsonBlockMatch) {
                        console.log('Found JSON block, attempting to parse...');
                        try {
                            const jsonStr = jsonBlockMatch[1].trim();
                            console.log('JSON string to parse:', jsonStr.substring(0, 100) + '...');
                            return JSON.parse(jsonStr);
                        } catch (e) {
                            console.log('Failed to parse extracted JSON:', e);
                        }
                    }
                    
                    // Try to parse the entire string as JSON
                    try {
                        return JSON.parse(normalizedText);
                    } catch (e) {
                        console.log('Failed to parse as direct JSON:', e);
                        // Return original text if not JSON
                        return text;
                    }
                }
                
                // Extract JSON if it's wrapped in code blocks
                const extracted = extractJSONFromCrewAI(content);
                
                // If it's an object now, format it beautifully
                if (typeof extracted === 'object' && extracted !== null) {
                    return formatJSONObject(extracted);
                }
                
                // If it's still a string, format as text
                if (typeof extracted === 'string') {
                    return formatPlainText(extracted);
                }
                
                return String(content);
            }
            
            // Function to format JSON objects beautifully
            function formatJSONObject(obj, level = 0) {
                if (!obj || typeof obj !== 'object') return String(obj);
                
                const indent = level * 20;
                const entries = Object.entries(obj);
                
                return entries.map(([key, value]) => {
                    const cleanKey = key.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1').toLowerCase();
                    const titleKey = cleanKey.charAt(0).toUpperCase() + cleanKey.slice(1);
                    
                    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                        return `
                            <div class="mb-4" style="margin-left: ${indent}px;">
                                <h4 class="text-blue-400 font-semibold mb-2">${titleKey}</h4>
                                <div class="bg-gray-800 p-3 rounded">
                                    ${formatJSONObject(value, level + 1)}
                                </div>
                            </div>
                        `;
                    } else if (Array.isArray(value)) {
                        return `
                            <div class="mb-3" style="margin-left: ${indent}px;">
                                <strong class="text-purple-400">${titleKey}:</strong>
                                <div class="ml-4 mt-1">
                                    ${value.map(item => `<div class="text-gray-300 mb-1">• ${item}</div>`).join('')}
                                </div>
                            </div>
                        `;
                    } else {
                        return `
                            <div class="mb-2" style="margin-left: ${indent}px;">
                                <strong class="text-purple-400">${titleKey}:</strong> 
                                <span class="text-gray-300">${value}</span>
                            </div>
                        `;
                    }
                }).join('');
            }
            
            // Function to format plain text nicely
            function formatPlainText(text) {
                if (!text || typeof text !== 'string') return '';
                
                return text.split('\\n').map(line => line.trim()).filter(line => line).map(line => {
                    if (line.includes(':')) {
                        const [key, ...valueParts] = line.split(':');
                        const value = valueParts.join(':').trim();
                        return `<div class="mb-2"><strong class="text-purple-400">${key.trim()}:</strong> <span class="text-gray-300">${value}</span></div>`;
                    } else {
                        return `<div class="text-gray-300 mb-1">${line}</div>`;
                    }
                }).join('');
            }
            
            return `
                <div style="background: #1a1a1a; border-radius: 8px; padding: 16px;">
                    <div class="mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div><span class="text-gray-400">Analysis Method:</span> <span class="text-blue-400">${data.analysis_method || 'CrewAI Performance Analysis'}</span></div>
                            <div><span class="text-gray-400">Analysis Agent:</span> <span class="text-purple-400">${data.analysis_agent || 'Performance Analyst'}</span></div>
                        </div>
                    </div>
                    
                    ${data.parsed_data ? `
                    <div class="mb-4">
                        <h5 class="text-green-400 font-semibold mb-3">📊 AI Performance Analysis</h5>
                        <div class="bg-gray-800 p-4 rounded">
                            ${formatCrewAIContent(data.parsed_data)}
                        </div>
                    </div>
                    ` : ''}
                    
                    ${data.crew_analysis && !data.parsed_data ? `
                    <div class="mb-4">
                        <h5 class="text-green-400 font-semibold mb-2">🤖 CrewAI Analysis</h5>
                        <div class="bg-gray-800 p-4 rounded" style="max-height: 300px; overflow-y: auto;">
                            ${formatCrewAIContent(data.crew_analysis)}
                        </div>
                    </div>
                    ` : ''}
                </div>
            `;
        }

        function formatStrategicRecommendations(data) {
            if (!data) return '<div class="text-gray-400">No strategic recommendations available.</div>';
            
            // Enhanced function to properly extract and format CrewAI JSON analysis
            function formatCrewAIContent(content) {
                if (!content) return '';
                
                // Function to extract JSON from CrewAI ```json blocks
                function extractJSONFromCrewAI(text) {
                    if (typeof text !== 'string') return text;
                    
                    console.log('Extracting JSON from:', text.substring(0, 100) + '...');
                    
                    // First, convert literal \\n to actual newlines for proper parsing
                    const normalizedText = text.replace(/\\\\n/g, String.fromCharCode(10));
                    
                    // Look for ```json blocks with correct regex for real newlines
                    const jsonBlockMatch = normalizedText.match(/```json\\s*([\\s\\S]*?)\\s*```/);
                    if (jsonBlockMatch) {
                        console.log('Found JSON block, attempting to parse...');
                        try {
                            const jsonStr = jsonBlockMatch[1].trim();
                            console.log('JSON string to parse:', jsonStr.substring(0, 100) + '...');
                            return JSON.parse(jsonStr);
                        } catch (e) {
                            console.log('Failed to parse extracted JSON:', e);
                        }
                    }
                    
                    // Try to parse the entire string as JSON
                    try {
                        return JSON.parse(normalizedText);
                    } catch (e) {
                        console.log('Failed to parse as direct JSON:', e);
                        // Return original text if not JSON
                        return text;
                    }
                }
                
                // Extract JSON if it's wrapped in code blocks
                const extracted = extractJSONFromCrewAI(content);
                
                // If it's an object now, format it beautifully
                if (typeof extracted === 'object' && extracted !== null) {
                    return formatJSONObject(extracted);
                }
                
                // If it's still a string, format as text
                if (typeof extracted === 'string') {
                    return formatPlainText(extracted);
                }
                
                return String(content);
            }
            
            // Function to format JSON objects beautifully
            function formatJSONObject(obj, level = 0) {
                if (!obj || typeof obj !== 'object') return String(obj);
                
                const indent = level * 20;
                const entries = Object.entries(obj);
                
                return entries.map(([key, value]) => {
                    const cleanKey = key.replace(/_/g, ' ').replace(/([A-Z])/g, ' $1').toLowerCase();
                    const titleKey = cleanKey.charAt(0).toUpperCase() + cleanKey.slice(1);
                    
                    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                        return `
                            <div class="mb-4" style="margin-left: ${indent}px;">
                                <h4 class="text-blue-400 font-semibold mb-2">${titleKey}</h4>
                                <div class="bg-gray-800 p-3 rounded">
                                    ${formatJSONObject(value, level + 1)}
                                </div>
                            </div>
                        `;
                    } else if (Array.isArray(value)) {
                        return `
                            <div class="mb-3" style="margin-left: ${indent}px;">
                                <strong class="text-purple-400">${titleKey}:</strong>
                                <div class="ml-4 mt-1">
                                    ${value.map(item => `<div class="text-gray-300 mb-1">• ${item}</div>`).join('')}
                                </div>
                            </div>
                        `;
                    } else {
                        return `
                            <div class="mb-2" style="margin-left: ${indent}px;">
                                <strong class="text-purple-400">${titleKey}:</strong> 
                                <span class="text-gray-300">${value}</span>
                            </div>
                        `;
                    }
                }).join('');
            }
            
            // Function to format plain text nicely
            function formatPlainText(text) {
                if (!text || typeof text !== 'string') return '';
                
                return text.split('\\n').map(line => line.trim()).filter(line => line).map(line => {
                    if (line.includes(':')) {
                        const [key, ...valueParts] = line.split(':');
                        const value = valueParts.join(':').trim();
                        return `<div class="mb-2"><strong class="text-purple-400">${key.trim()}:</strong> <span class="text-gray-300">${value}</span></div>`;
                    } else {
                        return `<div class="text-gray-300 mb-1">${line}</div>`;
                    }
                }).join('');
            }
            
            return `
                <div style="background: #1a1a1a; border-radius: 8px; padding: 16px;">
                    <div class="mb-4">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div><span class="text-gray-400">Source:</span> <span class="text-blue-400">${data.source || 'CrewAI Strategy Synthesis'}</span></div>
                            <div><span class="text-gray-400">Analysis Agent:</span> <span class="text-purple-400">${data.analysis_agent || 'Strategy Synthesizer'}</span></div>
                        </div>
                    </div>
                    
                    ${data.parsed_data ? `
                    <div class="mb-4">
                        <h5 class="text-green-400 font-semibold mb-3">🎯 AI Strategic Analysis</h5>
                        <div class="bg-gray-800 p-4 rounded">
                            ${formatCrewAIContent(data.parsed_data)}
                        </div>
                    </div>
                    ` : ''}
                    
                    ${data.crew_analysis && !data.parsed_data ? `
                    <div class="mb-4">
                        <h5 class="text-green-400 font-semibold mb-2">🤖 CrewAI Strategic Analysis</h5>
                        <div class="bg-gray-800 p-4 rounded" style="max-height: 300px; overflow-y: auto;">
                            ${formatCrewAIContent(data.crew_analysis)}
                        </div>
                    </div>
                    ` : ''}
                </div>
            `;
        }

        // Modal Control Functions
        function showAnalysisModal() {
            const modal = document.getElementById('analysisModal');
            modal.style.display = 'flex';
            resetModalProgress();
        }
        
        function hideAnalysisModal() {
            const modal = document.getElementById('analysisModal');
            modal.style.display = 'none';
        }
        
        function closeModalOnBackdrop(event) {
            // Only close if clicking on the backdrop (not the card itself)
            if (event.target === event.currentTarget) {
                hideAnalysisModal();
            }
        }
        
        function resetModalProgress() {
            // Reset progress bar
            document.getElementById('modalProgressBar').style.width = '0%';
            document.getElementById('modalPercentage').textContent = '0%';
            document.getElementById('modalAgentCount').textContent = '0/6';
            
            // Reset all agents to pending
            for (let i = 0; i < 6; i++) {
                const agent = document.getElementById(`agent-${i}`);
                agent.className = 'agent-status agent-pending';
                agent.textContent = (i + 1).toString();
            }
            
            // Reset data summary
            document.getElementById('modalDataSummary').textContent = 'Initializing market research analysis...';
        }
        
        function updateModalProgress(status) {
            const progress = status.progress || 0;
            const currentStep = status.current_step || 'Initializing...';
            
            // Update progress bar and percentage
            document.getElementById('modalProgressBar').style.width = `${progress}%`;
            document.getElementById('modalPercentage').textContent = `${Math.round(progress)}%`;
            
            // Calculate current agent (rough approximation)
            const currentAgent = Math.min(Math.floor(progress / 16.67), 5); // 100/6 ≈ 16.67
            const completedAgents = Math.max(0, currentAgent);
            
            document.getElementById('modalAgentCount').textContent = `${completedAgents}/6`;
            
            // Update agent statuses
            for (let i = 0; i < 6; i++) {
                const agent = document.getElementById(`agent-${i}`);
                if (i < completedAgents) {
                    agent.className = 'agent-status agent-completed';
                    agent.textContent = '✓';
                } else if (i === completedAgents && progress < 100) {
                    agent.className = 'agent-status agent-active';
                    agent.textContent = '⚡';
                } else {
                    agent.className = 'agent-status agent-pending';
                    agent.textContent = (i + 1).toString();
                }
            }
            
            // Update data summary with current step and data summary if available
            const dataSummary = status.data_summary || currentStep;
            document.getElementById('modalDataSummary').textContent = dataSummary;
            
            // Update estimated time based on progress
            const remainingTime = Math.max(0, Math.round((100 - progress) * 0.03)); // Rough estimate: 3 seconds per percent
            if (remainingTime > 60) {
                document.getElementById('modalEstimatedTime').textContent = `Estimated completion: ${Math.round(remainingTime/60)} minutes`;
            } else if (remainingTime > 0) {
                document.getElementById('modalEstimatedTime').textContent = `Estimated completion: ${remainingTime} seconds`;
            } else {
                document.getElementById('modalEstimatedTime').textContent = 'Finalizing analysis...';
            }
        }

        async function startAnalysis() {
            const channelId = document.getElementById('channelId').value.trim();
            const maxVideos = document.getElementById('maxVideos').value;
            const maxComments = document.getElementById('maxComments').value;
            
            if (!channelId) {
                showMessage('Please enter a channel ID', 'error');
                return;
            }
            
            try {
                document.getElementById('analyzeBtn').disabled = true;
                document.getElementById('analyzeBtn').textContent = '⏳ Starting Market Research...';
                
                // Show the beautiful progress modal
                showAnalysisModal();
                
                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        channel_id: channelId,
                        max_videos: parseInt(maxVideos),
                        max_comments: parseInt(maxComments)
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    currentAnalysisId = data.analysis_id;
                    showMessage(`Market research analysis started! ID: ${data.analysis_id}`, 'success');
                    startStatusPolling();
                } else {
                    hideAnalysisModal();
                    throw new Error(data.detail);
                }
                
            } catch (error) {
                hideAnalysisModal();
                showMessage(`Error: ${error.message}`, 'error');
                document.getElementById('analyzeBtn').disabled = false;
                document.getElementById('analyzeBtn').textContent = '🔍 Start Market Research';
            }
        }
        
        function startStatusPolling() {
            statusInterval = setInterval(async () => {
                if (!currentAnalysisId) return;
                
                try {
                    const response = await fetch(`/api/status/${currentAnalysisId}`);
                    const status = await response.json();
                    
                    // Update the beautiful modal with progress
                    updateModalProgress(status);
                    
                    // Also update the old status display (for fallback)
                    document.getElementById('status').innerHTML = `
                        <div class="result">
                            <strong>Status:</strong> ${status.status}<br>
                            <strong>Step:</strong> ${status.current_step}<br>
                            <div class="progress">
                                <div class="progress-bar" style="width: ${status.progress || 0}%"></div>
                            </div>
                            Progress: ${status.progress || 0}%
                        </div>
                    `;
                    
                    if (status.status === 'completed') {
                        // Show completion state in modal briefly
                        setTimeout(() => {
                            updateModalProgress({progress: 100, current_step: 'Analysis complete! Loading results...'});
                        }, 500);
                        
                        // Hide modal after showing completion
                        setTimeout(() => {
                            hideAnalysisModal();
                        }, 2000);
                        
                        clearInterval(statusInterval);
                        await loadResults(currentAnalysisId);
                        loadRecent();
                        document.getElementById('analyzeBtn').disabled = false;
                        document.getElementById('analyzeBtn').textContent = '🔍 Start Market Research';
                        
                        // Scroll to results
                        document.getElementById('results').scrollIntoView({behavior: 'smooth'});
                        
                    } else if (status.status === 'failed') {
                        clearInterval(statusInterval);
                        hideAnalysisModal();
                        showMessage('Market research analysis failed', 'error');
                        document.getElementById('analyzeBtn').disabled = false;
                        document.getElementById('analyzeBtn').textContent = '🔍 Start Market Research';
                    }
                    
                } catch (error) {
                    console.error('Status error:', error);
                }
            }, 2000); // Reduced to 2 seconds for more responsive updates
        }
        
        async function loadResults(analysisId) {
            try {
                const response = await fetch(`/api/results/${analysisId}`);
                const results = await response.json();
                
                document.getElementById('results').innerHTML = `
                    <div style="margin-top: 32px;">
                        <!-- Success Header -->
                        <div class="insight-card" style="background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05)); border-color: rgba(34, 197, 94, 0.3);">
                            <div style="text-align: center;">
                                <div style="font-size: 48px; margin-bottom: 16px;">✅</div>
                                <h2 style="color: #f1f5f9; margin: 0 0 8px 0; font-size: 24px; font-weight: 700;">Market Research Analysis Complete</h2>
                                <p style="color: #94a3b8; margin: 0; font-size: 16px;">AI-powered competitive intelligence for new channel creation</p>
                            </div>
                        </div>
                        
                        <!-- Market Overview Metrics -->
                        <div class="content-grid grid-4">
                            <div class="metric-box">
                                <div class="metric-value">${results.market_opportunities?.channel_name || 'Unknown'}</div>
                                <div class="metric-label">Target Channel</div>
                            </div>
                            <div class="metric-box">
                                <div class="metric-value">${results.market_opportunities?.data_summary?.videos_analyzed || 0}</div>
                                <div class="metric-label">Videos Analyzed</div>
                            </div>
                            <div class="metric-box">
                                <div class="metric-value">${results.market_opportunities?.data_summary?.comments_analyzed || 0}</div>
                                <div class="metric-label">Comments Mined</div>
                            </div>
                            <div class="metric-box">
                                <div class="metric-value">${results.crewai_metadata?.agents_used || 6}</div>
                                <div class="metric-label">AI Agents</div>
                            </div>
                        </div>
                        
                        <!-- Analysis Sections -->
                        <div class="content-grid" style="grid-template-columns: 1fr; gap: 24px;">
                            <!-- Market Opportunities -->
                            <div class="insight-card">
                                <div class="card-header">
                                    <div class="card-icon">🎯</div>
                                    <div class="card-title">
                                        <h3>Market Opportunities</h3>
                                        <p>Content gaps and blue ocean opportunities for new AI channels</p>
                                    </div>
                                </div>
                                <div id="market-opportunities-content"></div>
                            </div>
                            
                            <!-- Content Strategy -->
                            <div class="insight-card">
                                <div class="card-header">
                                    <div class="card-icon">📈</div>
                                    <div class="card-title">
                                        <h3>Content Strategy Intelligence</h3>
                                        <p>Viral patterns and formats optimized for AI production</p>
                                    </div>
                                </div>
                                <div id="content-strategy-content"></div>
                            </div>
                            
                            <!-- Audience Intelligence -->
                            <div class="insight-card">
                                <div class="card-header">
                                    <div class="card-icon">🧠</div>
                                    <div class="card-title">
                                        <h3>Audience Intelligence</h3>
                                        <p>Unmet audience needs and content requests from comment mining</p>
                                    </div>
                                </div>
                                <div id="audience-intelligence-content"></div>
                            </div>
                            
                            <!-- Competitive Landscape -->
                            <div class="insight-card">
                                <div class="card-header">
                                    <div class="card-icon">🗺️</div>
                                    <div class="card-title">
                                        <h3>Competitive Landscape</h3>
                                        <p>Market positioning and performance benchmarks for new entrants</p>
                                    </div>
                                </div>
                                <div id="competitive-landscape-content"></div>
                            </div>
                            
                            <!-- Production Intelligence -->
                            <div class="insight-card">
                                <div class="card-header">
                                    <div class="card-icon">🤖</div>
                                    <div class="card-title">
                                        <h3>AI Production Intelligence</h3>
                                        <p>Feasibility assessment and ROI analysis for AI content creation</p>
                                    </div>
                                </div>
                                <div id="production-intelligence-content"></div>
                            </div>
                            
                            <!-- Launch Strategy -->
                            <div class="insight-card">
                                <div class="card-header">
                                    <div class="card-icon">🚀</div>
                                    <div class="card-title">
                                        <h3>Launch Strategy</h3>
                                        <p>Complete go-to-market plan for new AI-generated YouTube channel</p>
                                    </div>
                                </div>
                                <div id="launch-strategy-content"></div>
                            </div>
                            
                            <!-- System Analysis Details -->
                            <div class="insight-card">
                                <div class="card-header">
                                    <div class="card-icon">⚙️</div>
                                    <div class="card-title">
                                        <h3>System Analysis Details</h3>
                                        <p>Technical configuration and AI agent pipeline information</p>
                                    </div>
                                </div>
                                
                                <div class="content-grid grid-2">
                                    <div class="data-list">
                                        <div class="data-item">
                                            <div class="data-icon">🤖</div>
                                            <div class="data-content">
                                                <div class="data-title">AI Enabled</div>
                                                <div class="data-subtitle">${results.crewai_metadata?.ai_enabled ? '✅ Yes' : '❌ No'}</div>
                                            </div>
                                        </div>
                                        <div class="data-item">
                                            <div class="data-icon">🎯</div>
                                            <div class="data-content">
                                                <div class="data-title">Analysis Focus</div>
                                                <div class="data-subtitle">${results.crewai_metadata?.analysis_focus || 'Market Research'}</div>
                                            </div>
                                        </div>
                                        <div class="data-item">
                                            <div class="data-icon">🔢</div>
                                            <div class="data-content">
                                                <div class="data-title">Research Agents</div>
                                                <div class="data-subtitle">${results.crewai_metadata?.agents_used || 6} Agents</div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="data-list">
                                        <div class="data-item">
                                            <div class="data-icon">1</div>
                                            <div class="data-content">
                                                <div class="data-title">🎯 Market Opportunity Scout</div>
                                            </div>
                                        </div>
                                        <div class="data-item">
                                            <div class="data-icon">2</div>
                                            <div class="data-content">
                                                <div class="data-title">📈 Content Strategy Architect</div>
                                            </div>
                                        </div>
                                        <div class="data-item">
                                            <div class="data-icon">3</div>
                                            <div class="data-content">
                                                <div class="data-title">🧠 Audience Intelligence Analyst</div>
                                            </div>
                                        </div>
                                        <div class="data-item">
                                            <div class="data-icon">4</div>
                                            <div class="data-content">
                                                <div class="data-title">🗺️ Competitive Landscape Mapper</div>
                                            </div>
                                        </div>
                                        <div class="data-item">
                                            <div class="data-icon">5</div>
                                            <div class="data-content">
                                                <div class="data-title">🤖 Production Intelligence Agent</div>
                                            </div>
                                        </div>
                                        <div class="data-item">
                                            <div class="data-icon">6</div>
                                            <div class="data-content">
                                                <div class="data-title">🚀 Launch Strategy Synthesizer</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                // Format all sections properly for new 6-agent system
                document.getElementById('market-opportunities-content').innerHTML = formatMarketOpportunities(results.market_opportunities);
                document.getElementById('content-strategy-content').innerHTML = formatContentStrategy(results.content_strategy);
                document.getElementById('audience-intelligence-content').innerHTML = formatAudienceIntelligence(results.audience_intelligence);
                document.getElementById('competitive-landscape-content').innerHTML = formatCompetitiveLandscape(results.competitive_landscape);
                document.getElementById('production-intelligence-content').innerHTML = formatProductionIntelligence(results.production_intelligence);
                document.getElementById('launch-strategy-content').innerHTML = formatLaunchStrategy(results.launch_strategy);
                
            } catch (error) {
                showMessage(`Failed to load results: ${error.message}`, 'error');
            }
        }
        
        async function loadRecent() {
            try {
                const response = await fetch('/api/recent');
                const analyses = await response.json();
                
                let html = '';
                analyses.forEach(analysis => {
                    html += `
                        <div class="result">
                            <strong>${analysis.channel_name}</strong> (${analysis.channel_id})<br>
                            Status: ${analysis.status} • ${new Date(analysis.created_at).toLocaleString()}
                        </div>
                    `;
                });
                
                document.getElementById('recent').innerHTML = html || 'No recent analyses';
                
            } catch (error) {
                document.getElementById('recent').innerHTML = 'Error loading recent analyses';
            }
        }
        
        function showMessage(message, type) {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            document.getElementById('status').appendChild(div);
        }
        
        window.onload = loadRecent;
    </script>
</body>
</html>
"""

@app.get("/health")
async def health():
    """Health check"""
    crew = get_crewai()
    return {
        "status": "healthy",
        "crewai_available": True,
        "ai_enabled": crew.ai_enabled,
        "tools_available": crew.tools_available,
        "timestamp": datetime.utcnow().isoformat()
    }

@app.post("/api/analyze", response_model=AnalysisResponse)
async def analyze(request: AnalysisRequest, background_tasks: BackgroundTasks):
    """Start CrewAI analysis"""
    analysis_id = str(uuid.uuid4())
    
    analysis_store[analysis_id] = {
        "analysis_id": analysis_id,
        "channel_id": request.channel_id,
        "status": "pending",
        "created_at": datetime.utcnow(),
        "progress": 0,
        "current_step": "Initializing CrewAI...",
        "results": None,
        "channel_name": f"Channel {request.channel_id}"
    }
    
    background_tasks.add_task(run_crewai_analysis, analysis_id, request)
    
    return AnalysisResponse(
        analysis_id=analysis_id,
        status="pending", 
        message=f"CrewAI analysis started for {request.channel_id}"
    )

@app.get("/api/status/{analysis_id}")
async def get_status(analysis_id: str):
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    return analysis_store[analysis_id]

@app.get("/api/results/{analysis_id}")
async def get_results(analysis_id: str):
    if analysis_id not in analysis_store:
        raise HTTPException(status_code=404, detail="Analysis not found")
    
    analysis = analysis_store[analysis_id]
    if analysis["status"] != "completed":
        raise HTTPException(status_code=400, detail="Analysis not completed")
    
    return analysis["results"]

@app.get("/api/recent")
async def get_recent():
    recent = sorted(analysis_store.values(), key=lambda x: x["created_at"], reverse=True)[:10]
    return [{
        "analysis_id": r["analysis_id"],
        "channel_id": r["channel_id"],
        "channel_name": r.get("channel_name", "Unknown"),
        "status": r["status"],
        "created_at": r["created_at"]
    } for r in recent]

async def run_crewai_analysis(analysis_id: str, request: AnalysisRequest):
    """Run CrewAI analysis in background with enhanced progress tracking"""
    import asyncio
    
    try:
        analysis_store[analysis_id]["status"] = "in_progress"
        analysis_store[analysis_id]["current_step"] = "🎯 Initializing Market Research Agents..."
        analysis_store[analysis_id]["progress"] = 5
        await asyncio.sleep(1)
        
        # Get CrewAI instance
        crew = get_crewai()
        
        analysis_store[analysis_id]["current_step"] = "📊 Collecting YouTube data..."
        analysis_store[analysis_id]["progress"] = 15
        analysis_store[analysis_id]["data_summary"] = f"Analyzing videos and comments for {request.max_videos} videos"
        await asyncio.sleep(0.5)
        
        analysis_store[analysis_id]["current_step"] = "🎯 Market Opportunity Scout analyzing..."
        analysis_store[analysis_id]["progress"] = 25
        await asyncio.sleep(0.5)
        
        analysis_store[analysis_id]["current_step"] = "📈 Content Strategy Architect extracting patterns..."
        analysis_store[analysis_id]["progress"] = 40
        await asyncio.sleep(0.5)
        
        analysis_store[analysis_id]["current_step"] = "🧠 Audience Intelligence Analyst mining comments..."
        analysis_store[analysis_id]["progress"] = 55
        await asyncio.sleep(0.5)
        
        analysis_store[analysis_id]["current_step"] = "🗺️ Competitive Landscape Mapper benchmarking..."
        analysis_store[analysis_id]["progress"] = 70
        await asyncio.sleep(0.5)
        
        analysis_store[analysis_id]["current_step"] = "🤖 Production Intelligence Agent assessing AI feasibility..."
        analysis_store[analysis_id]["progress"] = 85
        await asyncio.sleep(0.5)
        
        analysis_store[analysis_id]["current_step"] = "🚀 Launch Strategy Synthesizer creating go-to-market plan..."
        analysis_store[analysis_id]["progress"] = 95
        
        # Run the analysis
        results = crew.analyze_channel(request.channel_id, request.max_videos, request.max_comments)
        
        # Extract channel name
        channel_name = results.get('market_opportunities', {}).get('channel_name', f"Channel {request.channel_id}")
        
        analysis_store[analysis_id]["current_step"] = "✅ Market research analysis complete!"
        analysis_store[analysis_id]["progress"] = 100
        analysis_store[analysis_id]["status"] = "completed"
        analysis_store[analysis_id]["channel_name"] = channel_name
        analysis_store[analysis_id]["results"] = results
        
        logger.info(f"Market research analysis completed for {analysis_id}")
        
    except Exception as e:
        logger.error(f"Market research analysis failed for {analysis_id}: {e}")
        analysis_store[analysis_id]["status"] = "failed"
        analysis_store[analysis_id]["current_step"] = f"❌ Error: {str(e)}"

if __name__ == "__main__":
    import uvicorn
    
    print("🔍 YouTube Market Research v2 - AI Channel Creation Intelligence")
    print("=" * 60)
    
    # Check environment
    youtube_key = os.getenv('YOUTUBE_API_KEY')
    gemini_key = os.getenv('GEMINI_API_KEY')
    
    print(f"🔑 YouTube API: {'✅' if youtube_key else '❌'}")
    print(f"🤖 Gemini API: {'✅' if gemini_key else '❌'}")
    print(f"🚀 CrewAI: ✅ 6-Agent Market Research Pipeline")
    
    if not youtube_key:
        print("\n⚠️  Need YouTube API key for data collection")
    
    print("\n🚀 Starting CrewAI server...")
    print("🌐 Open http://localhost:8001")
    print("⌨️  Press Ctrl+C to stop")
    print("-" * 60)
    
    uvicorn.run(app, host="0.0.0.0", port=8001, log_level="info")