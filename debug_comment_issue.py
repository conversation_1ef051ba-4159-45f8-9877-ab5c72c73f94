#!/usr/bin/env python3
"""
Debug comment issue by checking the video data directly
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from working_crewai_app_tabbed import get_video_ai

def debug_comment_data():
    """Debug comment data in video analysis"""
    
    # Get the video AI instance
    video_ai = get_video_ai()
    
    # Test with <PERSON> video
    video_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
    
    print(f"🧪 Testing comment data extraction from: {video_url}")
    
    # Get video data directly
    video_data = video_ai._get_video_data_from_url(video_url)
    
    if 'error' in video_data:
        print(f"❌ Error getting video data: {video_data['error']}")
        return
    
    print(f"📊 Video: {video_data.get('title', 'Unknown')}")
    print(f"📊 Views: {video_data.get('views', 0):,}")
    print(f"📊 Comments (metadata): {video_data.get('comments', 0):,}")
    
    # Check comment data
    comment_data = video_data.get('comment_data', [])
    print(f"💬 Comment data: {len(comment_data)} comments")
    
    if comment_data:
        print("✅ Comments found in video data!")
        first_comment = comment_data[0]
        print(f"   First comment keys: {list(first_comment.keys())}")
        print(f"   First comment text: {first_comment.get('text', 'No text')[:100]}...")
        
        # Test the comment agent directly
        print("\n🤖 Testing comment agent directly...")
        
        # Create dummy data for other agents
        dummy_script_analysis = {'analysis': 'dummy script analysis'}
        dummy_performance_data = {'analysis': 'dummy performance data'}
        dummy_seo_data = {'analysis': 'dummy seo data'}
        dummy_psychology_data = {'analysis': 'dummy psychology data'}
        
        comment_result = video_ai._run_comment_agent(
            video_data, 
            dummy_script_analysis, 
            dummy_performance_data, 
            dummy_script_analysis, 
            dummy_seo_data, 
            dummy_psychology_data
        )
        
        if 'analysis' in comment_result:
            analysis = comment_result['analysis']
            if "No comment data available" in analysis:
                print("❌ Comment agent reports no data despite comments being present")
            else:
                print("✅ Comment agent produced analysis!")
                print(f"   Analysis length: {len(analysis)} chars")
                print(f"   Analysis preview: {analysis[:200]}...")
        else:
            print("❌ No analysis in comment result")
            print(f"   Comment result keys: {list(comment_result.keys())}")
    else:
        print("❌ No comments in video data")
        print(f"   Video data keys: {list(video_data.keys())}")

if __name__ == "__main__":
    debug_comment_data()